/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_ota_config.h
 * @description: OTA configuration management header.
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025-01-19
 */

#ifndef __SK_OTA_CONFIG_H__
#define __SK_OTA_CONFIG_H__

#include <stdbool.h>
#include <stdint.h>

// Forward declarations - avoid including heavy headers
typedef void* SkOtaHandle;
struct SkOtaConfig;

#ifdef __cplusplus
extern "C" {
#endif

// Common OTA constants
#define SK_OTA_DEFAULT_SERVER_URL "http://************:8070/"
#define SK_OTA_DEFAULT_FIRMWARE_PATH "Desktop/sk_terminal.bin"
#define SK_OTA_DEFAULT_TIMEOUT_MS 30000
#define SK_OTA_CHECK_INTERVAL_MS (2 * 60 * 1)  // 60 minutes (deprecated, for compatibility)
#define SK_OTA_IDLE_TRIGGER_TIME_SEC (3 * 60)     // 1 hour idle time to trigger OTA check
#define SK_OTA_CHECK_INTERVAL_SEC (6 * 60 * 60)    // 6 hours minimum interval between checks
#define SK_OTA_MIN_FREE_HEAP 50000  // Minimum free heap for OTA
#define SK_OTA_TASK_STACK_SIZE 8192
#define SK_OTA_TASK_PRIORITY 5

// Common string lengths
#define SK_OTA_URL_MAX_LEN 128

/**
 * @brief Static OTA configuration structure for HTTP-only updates
 * Simplified version without HTTPS/certificate support
 * Memory usage: 128 + 4 + 1 + 4 + 1 = 138 bytes (vs 2184 bytes with certificate)
 */
typedef struct {
    char url[SK_OTA_URL_MAX_LEN];        // 128 bytes
    uint32_t timeoutMs;                  // 4 bytes
    bool bulkFlashErase;                 // 1 byte
    uint32_t bufferCaps;                 // 4 bytes
    bool isValid;                        // 1 byte
} SkOtaConfigStatic;

// Common utility macros
#define SK_OTA_SAFE_STRCPY(dst, src, size) do { \
    strncpy(dst, src, size - 1); \
    dst[size - 1] = '\0'; \
} while(0)

#define SK_OTA_ENSURE_INIT(ctrl, init_func) do { \
    if ((ctrl).initialized != true) { \
        init_func(); \
    } \
} while(0)

// Common validation macros
#define SK_OTA_CHECK_INIT(ctrl, error_return) do { \
    if ((ctrl)->initialized == false) { \
        ESP_LOGE(TAG, "Module not initialized"); \
        return error_return; \
    } \
} while(0)

#define SK_OTA_CHECK_NULL(ptr, error_return) do { \
    if ((ptr) == NULL) { \
        ESP_LOGE(TAG, "NULL parameter: " #ptr); \
        return error_return; \
    } \
} while(0)

#define SK_OTA_CHECK_PARAMS(condition, error_return) do { \
    if (!(condition)) { \
        ESP_LOGE(TAG, "Invalid parameters: " #condition); \
        return error_return; \
    } \
} while(0)

/**
 * @brief Initialize OTA configuration
 * @return 0 on success, error code otherwise
 */
int SkOtaConfigInit(void);

/**
 * @brief Get OTA server URL
 * @return OTA server URL string
 */
const char* SkOtaConfigGetServer(void);

/**
 * @brief Set OTA server URL
 * @param server OTA server URL
 * @return 0 on success, error code otherwise
 */
int SkOtaConfigSetServer(const char* server);

/**
 * @brief Get auto check enable status
 * @return true if auto check is enabled, false otherwise
 */
bool SkOtaConfigGetAutoCheck(void);

/**
 * @brief Set auto check enable status
 * @param enable true to enable, false to disable
 * @return 0 on success, error code otherwise
 */
int SkOtaConfigSetAutoCheck(bool enable);

/**
 * @brief Get skip same version setting
 * @return true if should skip same version, false otherwise
 */
bool SkOtaConfigGetSkipSameVersion(void);

/**
 * @brief Set skip same version setting
 * @param skip true to skip same version, false to allow
 * @return 0 on success, -1 on error
 */
int SkOtaConfigSetSkipSameVersion(bool skip);

/**
 * @brief Save OTA configuration to NVS
 * @return 0 on success, error code otherwise
 */
int SkOtaConfigSave(void);

/**
 * @brief Load OTA configuration from NVS
 * @return 0 on success, error code otherwise
 */
int SkOtaConfigLoad(void);

/**
 * @brief Validate static OTA configuration parameters
 * @param config Static configuration to validate
 * @return 0 on success, error code otherwise
 */
int SkOtaConfigValidateStatic(const SkOtaConfigStatic *config);

/**
 * @brief Create default OTA configuration
 * @param config Output configuration structure
 * @return 0 on success, -1 on error
 */
int SkOtaConfigCreateDefault(SkOtaConfigStatic *config);


int SkOtaConfigSetUrl(const char *firmwareUrl);

#ifdef __cplusplus
}
#endif

#endif /* __SK_OTA_CONFIG_H__ */
