/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_ota.h
 * @description: OTA update module header file.
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025-01-19
 */

#ifndef __SK_OTA_H__
#define __SK_OTA_H__

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"
#include "sk_ota_api.h"  // 正确的依赖关系：实现依赖API
#include "sk_ota_config.h"  // 需要 SkOtaConfigStatic 定义

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Version information structure
 */
typedef struct {
    int major;                        ///< Major version number
    int minor;                        ///< Minor version number
    int patch;                        ///< Patch version number
} SkVersionInfo;

/**
 * @brief OTA progress information
 */
typedef struct {
    uint32_t totalSize;               ///< Total firmware size
    uint32_t downloadedSize;          ///< Downloaded size
    uint8_t percentage;               ///< Download percentage (0-100)
    SkOtaStatus status;               ///< Current status
    SkOtaError error;                 ///< Error code if failed
} SkOtaProgress;

/**
 * @brief OTA progress callback function type
 * @param progress Pointer to progress information
 * @param userData User data passed to callback
 */
typedef void (*OtaProgressCallback_t)(const SkOtaProgress *progress, void *userData);

/**
 * @brief Initialize OTA module
 * @return SK_OTA_ERR_OK on success, error code otherwise
 */
SkOtaError SkOtaInit(void);

/**
 * @brief Deinitialize OTA module
 * @return SK_OTA_ERR_OK on success, error code otherwise
 */
SkOtaError SkOtaDeinit(void);

/**
 * @brief Start OTA update
 * @param progressCb Progress callback function (can be NULL)
 * @param userData User data for callback (can be NULL)
 * @return SK_OTA_ERR_OK on success, error code otherwise
 */
SkOtaError SkOtaStart(OtaProgressCallback_t progressCb, void *userData);

/**
 * @brief Stop OTA update
 * @return SK_OTA_ERR_OK on success, error code otherwise
 */
SkOtaError SkOtaStop(void);

/**
 * @brief Get current OTA status
 * @return Current OTA status
 */
SkOtaStatus SkOtaGetStatus(void);

/**
 * @brief Get current OTA progress
 * @param progress Pointer to store progress information
 * @return SK_OTA_ERR_OK on success, error code otherwise
 */
SkOtaError SkOtaGetProgress(SkOtaProgress *progress);

/**
 * @brief Check if application needs rollback verification
 * @return true if verification needed, false otherwise
 */
bool SkOtaNeedsVerification(void);

/**
 * @brief Mark current application as valid (cancel rollback)
 * @return SK_OTA_ERR_OK on success, error code otherwise
 */
SkOtaError SkOtaMarkAppValid(void);

/**
 * @brief Mark current application as invalid and trigger rollback
 * @return SK_OTA_ERR_OK on success, error code otherwise
 */
SkOtaError SkOtaMarkAppInvalidAndRollback(void);

/**
 * @brief Parse version string into version information structure
 * @param versionStr Version string (e.g., "v1.2.3", "1.2.3", "1.2.3-beta")
 * @param versionInfo Pointer to store parsed version information
 * @return SK_OTA_ERR_OK on success, error code otherwise
 */
SkOtaError SkOtaGetVersionInfo(const char *versionStr, SkVersionInfo *versionInfo);

/**
 * @brief Get running partition information
 * @param partitionName Buffer to store partition name (min 32 bytes)
 * @param version Buffer to store app version (min 32 bytes)
 * @return SK_OTA_ERR_OK on success, error code otherwise
 */
SkOtaError SkOtaGetRunningInfo(char *partitionName, char *version);

#ifdef __cplusplus
}
#endif

#endif /* __SK_OTA_H__ */
