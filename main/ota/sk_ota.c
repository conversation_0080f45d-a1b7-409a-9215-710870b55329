/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_ota.c
 * @description: OTA update module implementation.
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025-01-19
 */

#include "sk_ota.h"
#include "sk_ota_config.h"
#include "esp_log.h"
#include "esp_ota_ops.h"
#include "esp_https_ota.h"
#include "esp_http_client.h"
#include "esp_app_desc.h"
#include "esp_efuse.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include <string.h>

static const char *TAG = "SkOta";

/**
 * @brief OTA control structure
 */
typedef struct {
    SkOtaStatus status;
    SkOtaError error;
    SkOtaProgress progress;
    OtaProgressCallback_t progressCb;
    void *userData;
    TaskHandle_t taskHandle;
    bool initialized;
    bool stopRequested;
} SkOtaCtrl;

static SkOtaCtrl g_otaCtrl = {0};

// Remove global config buffer - use stack allocation instead

// Forward declarations
static void UpdateProgress(SkOtaStatus status, uint32_t downloaded, uint32_t total, SkOtaError error);

/**
 * @brief Common error handling for ESP errors
 */
static esp_err_t HandleEspError(esp_err_t err, const char* operation, SkOtaStatus status, SkOtaError otaError){
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "%s failed: %s", operation, esp_err_to_name(err));
        UpdateProgress(status, 0, 0, otaError);
        return ESP_FAIL;
    }
    return ESP_OK;
}

/**
 * @brief Parse version string into version information structure
 * @param versionStr Version string (e.g., "v1.2.3", "1.2.3", "1.2.3-beta")
 * @param versionInfo Pointer to store parsed version information
 * @return SK_OTA_ERR_OK on success, error code otherwise
 */
SkOtaError SkOtaGetVersionInfo(const char *versionStr, SkVersionInfo *versionInfo) {
    if (versionStr == NULL || versionInfo == NULL) {
        return SK_OTA_ERR_INVALID_PARAM;
    }

    // Initialize version info
    versionInfo->major = 0;
    versionInfo->minor = 0;
    versionInfo->patch = 0;

    // Copy version string to temporary buffer
    char tempVersion[32];
    strncpy(tempVersion, versionStr, sizeof(tempVersion) - 1);
    tempVersion[sizeof(tempVersion) - 1] = '\0';

    // Remove 'v' or 'V' prefix if present
    char *versionPtr = (tempVersion[0] == 'v' || tempVersion[0] == 'V') ? tempVersion + 1 : tempVersion;

    // Parse major.minor.patch format
    sscanf(versionPtr, "%d.%d.%d", &versionInfo->major, &versionInfo->minor, &versionInfo->patch);

    return SK_OTA_ERR_OK;
}

/**
 * @brief Compare two version strings
 * @param version1 First version string (e.g., "1.2.3")
 * @param version2 Second version string (e.g., "1.2.4")
 * @return -1 if version1 < version2, 0 if equal, 1 if version1 > version2
 */
static int CompareVersions(const char *version1, const char *version2){
    if (version1 == NULL || version2 == NULL) {
        return 0;  // Treat as equal if either is NULL
    }

    // If versions are identical strings, return equal
    if (strcmp(version1, version2) == 0) {
        return 0;
    }

    // Parse version information using the extracted function
    SkVersionInfo versionInfo1, versionInfo2;

    if (SkOtaGetVersionInfo(version1, &versionInfo1) != SK_OTA_ERR_OK ||
        SkOtaGetVersionInfo(version2, &versionInfo2) != SK_OTA_ERR_OK) {
        return 0;  // Treat as equal if parsing fails
    }

    // Compare major version
    if (versionInfo1.major != versionInfo2.major) {
        return (versionInfo1.major > versionInfo2.major) ? 1 : -1;
    }

    // Compare minor version
    if (versionInfo1.minor != versionInfo2.minor) {
        return (versionInfo1.minor > versionInfo2.minor) ? 1 : -1;
    }

    // Compare patch version
    if (versionInfo1.patch != versionInfo2.patch) {
        return (versionInfo1.patch > versionInfo2.patch) ? 1 : -1;
    }

    return 0;  // Versions are equal
}

/**
 * @brief Validate image header
 */
static esp_err_t ValidateImageHeader(const esp_app_desc_t *newAppInfo){
    if (newAppInfo == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    const esp_app_desc_t *runningAppInfo = esp_app_get_description();
    ESP_LOGI(TAG, "Version check: %s -> %s", runningAppInfo->version, newAppInfo->version);

    // Compare versions
    int versionCompare = CompareVersions(runningAppInfo->version, newAppInfo->version);

    if (versionCompare > 0) {
        // Current version is higher than new version - prevent downgrade
        ESP_LOGW(TAG, "Downgrade detected (%s -> %s), rejecting update",
                 runningAppInfo->version, newAppInfo->version);
        return ESP_FAIL;
    } else if (versionCompare == 0) {
        // Same version
        if (SkOtaConfigGetSkipSameVersion()) {
            ESP_LOGW(TAG, "Same version detected, skipping update");
            return ESP_FAIL;
        } else {
            ESP_LOGI(TAG, "Same version update allowed by configuration");
        }
    } else {
        // New version is higher - allow upgrade
        ESP_LOGI(TAG, "Upgrade detected (%s -> %s), proceeding",
                 runningAppInfo->version, newAppInfo->version);
    }

#ifdef CONFIG_BOOTLOADER_APP_ANTI_ROLLBACK
    const uint32_t hwSecVersion = esp_efuse_read_secure_version();
    if (newAppInfo->secure_version < hwSecVersion) {
        ESP_LOGW(TAG, "New firmware security version is less than eFuse programmed, %lu < %lu",
                 newAppInfo->secure_version, hwSecVersion);
        return ESP_FAIL;
    }
#endif

    return ESP_OK;
}

/**
 * @brief Update progress and call callback
 */
static void UpdateProgress(SkOtaStatus status, uint32_t downloaded, uint32_t total, SkOtaError error){
    SkOtaCtrl *ctrl = &g_otaCtrl;

    ctrl->status = status;
    ctrl->error = error;
    ctrl->progress.status = status;
    ctrl->progress.error = error;
    ctrl->progress.downloadedSize = downloaded;
    ctrl->progress.totalSize = total;

    if (total > 0) {
        ctrl->progress.percentage = (uint8_t)((downloaded * 100) / total);
    } else {
        ctrl->progress.percentage = 0;
    }

    if (ctrl->progressCb) {
        ctrl->progressCb(&ctrl->progress, ctrl->userData);
    }
}

/**
 * @brief Validate OTA prerequisites
 */
static esp_err_t ValidateOtaPrerequisites(const SkOtaConfigStatic *config){
    // Validate input parameters
    if (config == NULL || strlen(config->url) == 0) {
        ESP_LOGE(TAG, "Invalid OTA configuration");
        UpdateProgress(SK_OTA_STATUS_FAILED, 0, 0, SK_OTA_ERR_INVALID_PARAM);
        return ESP_FAIL;
    }

    // Check available memory
    size_t free_heap = esp_get_free_heap_size();
    if (free_heap < SK_OTA_MIN_FREE_HEAP) {
        ESP_LOGE(TAG, "Insufficient memory for OTA update");
        UpdateProgress(SK_OTA_STATUS_FAILED, 0, 0, SK_OTA_ERR_MEMORY);
        return ESP_FAIL;
    }

    return ESP_OK;
}

/**
 * @brief Initialize OTA connection
 */
static esp_err_t InitializeOtaConnection(const SkOtaConfigStatic *config, esp_https_ota_handle_t *httpsOtaHandle){
    UpdateProgress(SK_OTA_STATUS_CONNECTING, 0, 0, SK_OTA_ERR_OK);

    // Configure HTTP client with explicit initialization
    esp_http_client_config_t httpConfig = {0};  // Zero-initialize all fields
    httpConfig.url = config->url;
    httpConfig.cert_pem = NULL;  // HTTP-only, no certificate
    httpConfig.timeout_ms = config->timeoutMs;
    httpConfig.keep_alive_enable = true;
    httpConfig.skip_cert_common_name_check = true;
    httpConfig.use_global_ca_store = false;
    httpConfig.buffer_size = 4096;  // Set explicit buffer size
    httpConfig.buffer_size_tx = 4096;  // Set explicit TX buffer size

    // Configure HTTPS OTA
    esp_https_ota_config_t otaConfig = {0};  // Zero-initialize
    otaConfig.http_config = &httpConfig;
    otaConfig.bulk_flash_erase = config->bulkFlashErase;
    otaConfig.buffer_caps = config->bufferCaps;

    // Begin OTA with error handling
    esp_err_t err = esp_https_ota_begin(&otaConfig, httpsOtaHandle);
    if (HandleEspError(err, "OTA begin", SK_OTA_STATUS_FAILED, SK_OTA_ERR_NETWORK) != ESP_OK) {
        return ESP_FAIL;
    }

    return ESP_OK;
}

/**
 * @brief Verify OTA image
 */
static esp_err_t VerifyOtaImage(esp_https_ota_handle_t httpsOtaHandle){
    // Get and validate image description
    UpdateProgress(SK_OTA_STATUS_VERIFYING, 0, 0, SK_OTA_ERR_OK);
    esp_app_desc_t appDesc;
    esp_err_t err = esp_https_ota_get_img_desc(httpsOtaHandle, &appDesc);
    if (HandleEspError(err, "Get image description", SK_OTA_STATUS_FAILED, SK_OTA_ERR_VERIFY) != ESP_OK) {
        return ESP_FAIL;
    }

    err = ValidateImageHeader(&appDesc);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Image header verification failed");
        UpdateProgress(SK_OTA_STATUS_FAILED, 0, 0, SK_OTA_ERR_SECURITY);
        return ESP_FAIL;
    }

    return ESP_OK;
}

/**
 * @brief Download OTA firmware
 */
static esp_err_t DownloadOtaFirmware(esp_https_ota_handle_t httpsOtaHandle){
    // Download and install firmware
    UpdateProgress(SK_OTA_STATUS_DOWNLOADING, 0, 0, SK_OTA_ERR_OK);

    esp_err_t err;
    while (1) {
        SkOtaCtrl *ctrl = &g_otaCtrl;
        if (ctrl->stopRequested) {
            ESP_LOGI(TAG, "OTA stop requested");
            UpdateProgress(SK_OTA_STATUS_FAILED, 0, 0, SK_OTA_ERR_OK);
            return ESP_FAIL;
        }

        err = esp_https_ota_perform(httpsOtaHandle);
        if (err != ESP_ERR_HTTPS_OTA_IN_PROGRESS) {
            break;
        }

        // Update progress
        int imageLen = esp_https_ota_get_image_len_read(httpsOtaHandle);
        int totalLen = esp_https_ota_get_image_size(httpsOtaHandle);
        UpdateProgress(SK_OTA_STATUS_DOWNLOADING, imageLen, totalLen, SK_OTA_ERR_OK);

        vTaskDelay(pdMS_TO_TICKS(10)); // Allow other tasks to run
    }

    if (HandleEspError(err, "OTA perform", SK_OTA_STATUS_FAILED, SK_OTA_ERR_DOWNLOAD) != ESP_OK) {
        return ESP_FAIL;
    }

    // Check if complete data was received
    if (esp_https_ota_is_complete_data_received(httpsOtaHandle) != true) {
        ESP_LOGE(TAG, "Complete data was not received");
        UpdateProgress(SK_OTA_STATUS_FAILED, 0, 0, SK_OTA_ERR_DOWNLOAD);
        return ESP_FAIL;
    }

    return ESP_OK;
}

/**
 * @brief Install OTA firmware
 */
static esp_err_t InstallOtaFirmware(esp_https_ota_handle_t *httpsOtaHandle){
    // Finish OTA
    UpdateProgress(SK_OTA_STATUS_INSTALLING, 0, 0, SK_OTA_ERR_OK);
    esp_err_t otaFinishErr = esp_https_ota_finish(*httpsOtaHandle);
    if (otaFinishErr != ESP_OK) {
        if (otaFinishErr == ESP_ERR_OTA_VALIDATE_FAILED) {
            ESP_LOGE(TAG, "Image validation failed, image is corrupted");
            UpdateProgress(SK_OTA_STATUS_FAILED, 0, 0, SK_OTA_ERR_VERIFY);
        } else {
            ESP_LOGE(TAG, "ESP_HTTPS_OTA upgrade failed: %s", esp_err_to_name(otaFinishErr));
            UpdateProgress(SK_OTA_STATUS_FAILED, 0, 0, SK_OTA_ERR_INSTALL);
        }
        return ESP_FAIL;
    }

    // Set handle to NULL after successful finish to prevent double cleanup
    *httpsOtaHandle = NULL;
    return ESP_OK;
}

static bool OtaGetConfig(SkOtaConfigStatic *staticConfig) {
    if (SkOtaConfigCreateDefault(staticConfig)) {
        return false;
    }

    if (staticConfig->isValid == false) {
        return false;
    }
    ESP_LOGE(TAG, "Invalid static configuration");

    UpdateProgress(SK_OTA_STATUS_FAILED, 0, 0, SK_OTA_ERR_INVALID_PARAM);

    return true;
}

static bool OtaUpdateProc(SkOtaConfigStatic *staticConfig) {
    esp_https_ota_handle_t httpsOtaHandle = NULL;

    // Step 1: Validate prerequisites
    if (ValidateOtaPrerequisites(staticConfig) != ESP_OK) {
        return false;
    }

    ESP_LOGI(TAG, "OTA update started from: %s", staticConfig->url);

    // Step 2: Initialize OTA connection
    if (InitializeOtaConnection(staticConfig, &httpsOtaHandle) != ESP_OK) {
        return false;
    }

    // Step 3: Verify OTA image
    if (VerifyOtaImage(httpsOtaHandle) != ESP_OK) {
        esp_https_ota_abort(httpsOtaHandle);
        return false;
    }

    // Step 4: Download firmware
    if (DownloadOtaFirmware(httpsOtaHandle) != ESP_OK) {
        // Cleanup OTA handle only (no need to free static config)
        esp_https_ota_abort(httpsOtaHandle);
        return false;
    }

    // Step 5: Install firmware
    if (InstallOtaFirmware(&httpsOtaHandle) != ESP_OK) {
        return false;
    }

    return true;
}

/**
 * @brief OTA task implementation
 */
static void OtaTask(void *pvParameter){
    SkOtaConfigStatic staticConfig;

    if (!OtaGetConfig(&staticConfig)) {
        return;
    }

    if (OtaUpdateProc(&staticConfig)) {
        ESP_LOGI(TAG, "ESP_HTTPS_OTA upgrade successful. Notifying system for reboot...");
        UpdateProgress(SK_OTA_STATUS_SUCCESS, 0, 0, SK_OTA_ERR_OK);
    } else {
        UpdateProgress(SK_OTA_STATUS_FAILED, 0, 0, SK_OTA_ERR_OK);
    }

    g_otaCtrl.taskHandle = NULL;
    vTaskDelete(NULL);
}

SkOtaError SkOtaInit(void){
    SkOtaCtrl *ctrl = &g_otaCtrl;

    if (ctrl->initialized) {
        return SK_OTA_ERR_OK;
    }

    memset(ctrl, 0, sizeof(*ctrl));
    ctrl->status = SK_OTA_STATUS_IDLE;
    ctrl->error = SK_OTA_ERR_OK;
    ctrl->initialized = true;

    ESP_LOGI(TAG, "OTA module initialized");
    return SK_OTA_ERR_OK;
}

SkOtaError SkOtaDeinit(void){
    SkOtaCtrl *ctrl = &g_otaCtrl;

    if (ctrl->initialized == false) {
        return SK_OTA_ERR_OK;
    }

    SkOtaStop();
    ctrl->initialized = false;

    ESP_LOGI(TAG, "OTA module deinitialized");
    return SK_OTA_ERR_OK;
}

SkOtaError SkOtaStart(OtaProgressCallback_t progressCb, void *userData){
    SkOtaCtrl *ctrl = &g_otaCtrl;

    if (ctrl->initialized == false) {
        return SK_OTA_ERR_INVALID_PARAM;
    }

    if (ctrl->taskHandle != NULL) {
        ESP_LOGW(TAG, "OTA task already running");
        return SK_OTA_ERR_OK;
    }

    ctrl->progressCb = progressCb;
    ctrl->userData = userData;
    ctrl->stopRequested = false;

    // Create OTA task with heap-allocated config
    BaseType_t ret = xTaskCreate(OtaTask, "ota_task", SK_OTA_TASK_STACK_SIZE, NULL, SK_OTA_TASK_PRIORITY, &ctrl->taskHandle);
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create OTA task");
        return SK_OTA_ERR_MEMORY;
    }

    return SK_OTA_ERR_OK;
}

SkOtaError SkOtaStop(void){
    SkOtaCtrl *ctrl = &g_otaCtrl;

    if (ctrl->initialized == false) {
        return SK_OTA_ERR_INVALID_PARAM;
    }

    if (ctrl->taskHandle != NULL) {
        ctrl->stopRequested = true;
        // Wait for task to finish
        while (ctrl->taskHandle) {
            vTaskDelay(pdMS_TO_TICKS(100));
        }
    }

    return SK_OTA_ERR_OK;
}

SkOtaStatus SkOtaGetStatus(void){
    SkOtaCtrl *ctrl = &g_otaCtrl;
    return ctrl->status;
}

SkOtaError SkOtaGetProgress(SkOtaProgress *progress){
    SkOtaCtrl *ctrl = &g_otaCtrl;

    if (progress == NULL) {
        return SK_OTA_ERR_INVALID_PARAM;
    }

    *progress = ctrl->progress;
    return SK_OTA_ERR_OK;
}

bool SkOtaNeedsVerification(void){
    const esp_partition_t *running = esp_ota_get_running_partition();
    esp_ota_img_states_t otaState;
    
    if (esp_ota_get_state_partition(running, &otaState) == ESP_OK) {
        return (otaState == ESP_OTA_IMG_PENDING_VERIFY);
    }
    
    return false;
}

SkOtaError SkOtaMarkAppValid(void){
    esp_err_t err = esp_ota_mark_app_valid_cancel_rollback();
    if (err == ESP_OK) {
        ESP_LOGI(TAG, "Application marked as valid, rollback cancelled");
        return SK_OTA_ERR_OK;
    } else {
        ESP_LOGE(TAG, "Failed to mark app as valid: %s", esp_err_to_name(err));
        return SK_OTA_ERR_ROLLBACK;
    }
}

SkOtaError SkOtaMarkAppInvalidAndRollback(void){
    ESP_LOGW(TAG, "Marking app as invalid and triggering rollback");
    esp_ota_mark_app_invalid_rollback_and_reboot();
    // This function should not return
    return SK_OTA_ERR_OK;
}

SkOtaError SkOtaGetRunningInfo(char *partitionName, char *version){
    if (partitionName == NULL || version == NULL) {
        return SK_OTA_ERR_INVALID_PARAM;
    }

    const esp_partition_t *running = esp_ota_get_running_partition();
    if (running != NULL) {
        strncpy(partitionName, running->label, 31);
        partitionName[31] = '\0';
    } else {
        strcpy(partitionName, "unknown");
    }

    const esp_app_desc_t *appDesc = esp_app_get_description();
    if (appDesc) {
        strncpy(version, appDesc->version, 31);
        version[31] = '\0';
    } else {
        strcpy(version, "unknown");
    }

    return SK_OTA_ERR_OK;
}

const char* SkOtaErrorToString(SkOtaError error){
    switch (error) {
        case SK_OTA_ERR_OK:             return "No error";
        case SK_OTA_ERR_INVALID_PARAM:  return "Invalid parameter";
        case SK_OTA_ERR_NETWORK:        return "Network error";
        case SK_OTA_ERR_SERVER:         return "Server error";
        case SK_OTA_ERR_DOWNLOAD:       return "Download error";
        case SK_OTA_ERR_VERIFY:         return "Verification error";
        case SK_OTA_ERR_INSTALL:        return "Installation error";
        case SK_OTA_ERR_ROLLBACK:       return "Rollback error";
        case SK_OTA_ERR_TIMEOUT:        return "Timeout error";
        case SK_OTA_ERR_MEMORY:         return "Memory error";
        case SK_OTA_ERR_SECURITY:       return "Security version error";
        default:                        return "Unknown error";
    }
}

const char* SkOtaStatusToString(SkOtaStatus status){
    switch (status) {
        case SK_OTA_STATUS_IDLE:        return "Idle";
        case SK_OTA_STATUS_CONNECTING:  return "Connecting";
        case SK_OTA_STATUS_DOWNLOADING: return "Downloading";
        case SK_OTA_STATUS_VERIFYING:   return "Verifying";
        case SK_OTA_STATUS_INSTALLING:  return "Installing";
        case SK_OTA_STATUS_SUCCESS:     return "Success";
        case SK_OTA_STATUS_FAILED:      return "Failed";
        case SK_OTA_STATUS_ROLLBACK:    return "Rollback";
        default:                        return "Unknown";
    }
}
