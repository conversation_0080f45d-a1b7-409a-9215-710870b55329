/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_ota_config.c
 * @description: OTA configuration management implementation.
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025-01-19
 */

#include "sk_ota.h"
#include "sk_ota_config.h"
#include "esp_log.h"
#include "esp_https_ota.h"
#include "nvs_flash.h"
#include "nvs.h"
#include <string.h>
#include <stdlib.h>

static const char *TAG = "SkOtaConfig";

#define NVS_NAMESPACE "SkOtaConfig"

typedef struct {
    char serverUrl[SK_OTA_URL_MAX_LEN];
    bool autoCheck;
    bool skipSameVersion;
    bool initialized;
} SkOtaConfigCtrl;

int SkOtaConfigValidateHttpUrl(const char *url);

static SkOtaConfigCtrl g_otaConfig = {0};

/**
 * @brief Common NVS error handling
 */
static bool HandleNvsError(esp_err_t err, const char* operation, nvs_handle_t handle){
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to %s: %s", operation, esp_err_to_name(err));
        if (handle != 0) {
            nvs_close(handle);
        }
        return false;
    }
    return true;
}

int SkOtaConfigInit(void){
    SkOtaConfigCtrl *ctrl = &g_otaConfig;

    if (ctrl->initialized) {
        return 0;
    }

    // Set default values
    SK_OTA_SAFE_STRCPY(ctrl->serverUrl, SK_OTA_DEFAULT_SERVER_URL, sizeof(ctrl->serverUrl));
    ctrl->autoCheck = true;  // Enable by default for HTTP server
    ctrl->skipSameVersion = true;  // Skip same version by default
    ctrl->initialized = true;

    // Load configuration from NVS
    SkOtaConfigLoad();

    ESP_LOGI(TAG, "OTA config: %s, AutoCheck: %s, SkipSame: %s",
             ctrl->serverUrl,
             ctrl->autoCheck ? "ON" : "OFF",
             ctrl->skipSameVersion ? "ON" : "OFF");

    return 0;
}

const char* SkOtaConfigGetServer(void){
    SkOtaConfigCtrl *ctrl = &g_otaConfig;
    SK_OTA_ENSURE_INIT(*ctrl, SkOtaConfigInit);
    return ctrl->serverUrl;
}

int SkOtaConfigSetServer(const char* server){
    SkOtaConfigCtrl *ctrl = &g_otaConfig;

    if (server == NULL) {
        return -1;
    }

    if (SkOtaConfigValidateHttpUrl(server)) {
        return -1;
    }

    SK_OTA_ENSURE_INIT(*ctrl, SkOtaConfigInit);
    SK_OTA_SAFE_STRCPY(ctrl->serverUrl, server, sizeof(ctrl->serverUrl));

    ESP_LOGI(TAG, "OTA server updated: %s", server);
    return 0;
}

bool SkOtaConfigGetAutoCheck(void){
    SkOtaConfigCtrl *ctrl = &g_otaConfig;
    SK_OTA_ENSURE_INIT(*ctrl, SkOtaConfigInit);
    return ctrl->autoCheck;
}

int SkOtaConfigSetAutoCheck(bool enable){
    SkOtaConfigCtrl *ctrl = &g_otaConfig;
    SK_OTA_ENSURE_INIT(*ctrl, SkOtaConfigInit);
    ctrl->autoCheck = enable;
    return 0;
}

bool SkOtaConfigGetSkipSameVersion(void){
    SkOtaConfigCtrl *ctrl = &g_otaConfig;
    SK_OTA_ENSURE_INIT(*ctrl, SkOtaConfigInit);
    return ctrl->skipSameVersion;
}

int SkOtaConfigSetSkipSameVersion(bool skip){
    SkOtaConfigCtrl *ctrl = &g_otaConfig;
    SK_OTA_ENSURE_INIT(*ctrl, SkOtaConfigInit);
    ctrl->skipSameVersion = skip;
    return 0;
}

int SkOtaConfigSave(void){
    SkOtaConfigCtrl *ctrl = &g_otaConfig;

    if (ctrl->initialized != true) {
        return -1;
    }

    nvs_handle_t nvsHandle;
    esp_err_t err = nvs_open(NVS_NAMESPACE, NVS_READWRITE, &nvsHandle);
    if (HandleNvsError(err, "open NVS handle", 0) != true) {
        return -1;
    }

    // Save all settings with unified error handling
    bool success = true;
    success &= HandleNvsError(nvs_set_str(nvsHandle, "serverUrl", ctrl->serverUrl),
                             "save server URL", nvsHandle);
    success &= HandleNvsError(nvs_set_u8(nvsHandle, "autoCheck", ctrl->autoCheck ? 1 : 0),
                             "save auto check setting", nvsHandle);
    success &= HandleNvsError(nvs_set_u8(nvsHandle, "skipSameVersion", ctrl->skipSameVersion ? 1 : 0),
                             "save skip same version setting", nvsHandle);
    success &= HandleNvsError(nvs_commit(nvsHandle), "commit NVS changes", nvsHandle);

    nvs_close(nvsHandle);
    return success ? 0 : -1;
}

int SkOtaConfigLoad(void){
    SkOtaConfigCtrl *ctrl = &g_otaConfig;

    if (ctrl->initialized != true) {
        return -1;
    }

    nvs_handle_t nvsHandle;
    esp_err_t err = nvs_open(NVS_NAMESPACE, NVS_READONLY, &nvsHandle);
    if (err != ESP_OK) {
        return 0;  // Use defaults
    }

    // Load server URL with fallback to default
    size_t length = sizeof(ctrl->serverUrl);
    if (nvs_get_str(nvsHandle, "serverUrl", ctrl->serverUrl, &length) != ESP_OK) {
        SK_OTA_SAFE_STRCPY(ctrl->serverUrl, SK_OTA_DEFAULT_SERVER_URL, sizeof(ctrl->serverUrl));
    }

    // Load boolean settings with defaults
    uint8_t value;
    ctrl->autoCheck = (nvs_get_u8(nvsHandle, "autoCheck", &value) == ESP_OK) ? (value != 0) : true;
    ctrl->skipSameVersion = (nvs_get_u8(nvsHandle, "skipSameVersion", &value) == ESP_OK) ? (value != 0) : true;

    nvs_close(nvsHandle);
    return 0;
}

int SkOtaConfigValidateStatic(const SkOtaConfigStatic *config){
    if (config == NULL) {
        ESP_LOGE(TAG, "Config is NULL");
        return -1;
    }

    // URL validation using common function
    if (SkOtaConfigValidateHttpUrl(config->url) != 0) {
        return -1;
    }

    // Timeout validation
    if (config->timeoutMs < 1000 || config->timeoutMs > 300000) {
        ESP_LOGE(TAG, "Invalid timeout: %d ms, must be between 1000-300000", config->timeoutMs);
        return -1;
    }

    // Buffer capabilities validation
    if (config->bufferCaps == 0) {
        ESP_LOGE(TAG, "Invalid buffer capabilities");
        return -1;
    }

    // Valid flag check
    if (!config->isValid) {
        ESP_LOGE(TAG, "Config marked as invalid");
        return -1;
    }

    ESP_LOGD(TAG, "OTA static config validation passed");
    return 0;
}

int SkOtaConfigBuildFirmwareUrl(char *url, size_t urlSize, const char *serverUrl){
    if (url == NULL || serverUrl == NULL || urlSize == 0) {
        ESP_LOGE(TAG, "Invalid parameters for BuildFirmwareUrl");
        if (url != NULL && urlSize > 0) {
            url[0] = '\0';
        }
        return -1;
    }

    // Ensure server URL ends with '/' for proper concatenation
    size_t serverLen = strlen(serverUrl);
    if (serverLen == 0) {
        ESP_LOGE(TAG, "Empty server URL");
        url[0] = '\0';
        return -1;
    }

    if (serverUrl[serverLen - 1] == '/') {
        snprintf(url, urlSize, "%s%s", serverUrl, SK_OTA_DEFAULT_FIRMWARE_PATH);
    } else {
        snprintf(url, urlSize, "%s/%s", serverUrl, SK_OTA_DEFAULT_FIRMWARE_PATH);
    }

    ESP_LOGD(TAG, "Built firmware URL: %s", url);
    return 0;
}

int SkOtaConfigValidateHttpUrl(const char *url){
    if (url == NULL || strlen(url) == 0) {
        ESP_LOGE(TAG, "URL is NULL or empty");
        return -1;
    }

    // URL protocol validation - only support HTTP for simplicity
    if (strncmp(url, "http://", 7) != 0) {
        ESP_LOGE(TAG, "Invalid URL protocol, only http:// is supported");
        return -1;
    }

    // URL length validation
    if (strlen(url) >= SK_OTA_URL_MAX_LEN) {
        ESP_LOGE(TAG, "URL too long, max %d characters", SK_OTA_URL_MAX_LEN - 1);
        return -1;
    }

    return 0;
}

int SkOtaConfigCreateDefault(SkOtaConfigStatic *config){
    SkOtaConfigCtrl *ctrl = &g_otaConfig;

    if (config == NULL) {
        ESP_LOGE(TAG, "Invalid parameters for CreateDefault");
        return -1;
    }
    if (strlen(ctrl->serverUrl) == 0) {
        ESP_LOGE(TAG, "Empty server URL");
        return -1;
    }

    // Initialize with default values first
    memset(config, 0, sizeof(SkOtaConfigStatic));
    config->timeoutMs = SK_OTA_DEFAULT_TIMEOUT_MS;
    config->bulkFlashErase = true;  // Enable for faster update
    config->bufferCaps = MALLOC_CAP_INTERNAL;  // Use internal RAM for better performance
    config->isValid = true;

    // Build firmware URL after initialization
    if (SkOtaConfigBuildFirmwareUrl(config->url, sizeof(config->url), ctrl->serverUrl) != 0) {
        ESP_LOGE(TAG, "Failed to build firmware URL");
        return -1;
    }
    ESP_LOGD(TAG, "Created default OTA config for URL: %s", config->url);
    return 0;
}
