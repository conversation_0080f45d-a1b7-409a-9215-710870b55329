/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_ota_manager.c
 * @description: OTA manager implementation for automatic updates.
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025-01-19
 */

#include "esp_log.h"
#include "esp_app_desc.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>
#include "sk_ota_api.h"
#include "sk_ota.h"
#include "sk_ota_config.h"

static const char *TAG = "SkOtaManager";

// Use common constants from sk_ota_config.h

// HTTP-only OTA implementation - no certificate support needed

typedef struct {
    bool initialized;
    bool autoCheckEnabled;
    uint32_t lastOtaCheckTime;    // 上次OTA检查时间戳
    SkOtaStateCallback_t stateCb;
} SkOtaManagerCtrl;


static SkOtaManagerCtrl g_otaManager = {0};

/**
 * @brief OTA progress callback
 */
static void SkOtaProgressCallback(const SkOtaProgress *progress, void *userData){
    SkOtaManagerCtrl *ctrl = userData;
    switch (progress->status) {
        case SK_OTA_STATUS_SUCCESS:
            ESP_LOGI(TAG, "OTA completed successfully, rebooting...");
            break;

        case SK_OTA_STATUS_FAILED:
            ESP_LOGE(TAG, "OTA failed: %s", SkOtaErrorToString(progress->error));
            break;

        case SK_OTA_STATUS_DOWNLOADING:
            if (progress->percentage % 20 == 0) {  // Log every 20%
                ESP_LOGI(TAG, "Download: %d%% (%lu/%lu bytes)",
                         progress->percentage, progress->downloadedSize, progress->totalSize);
            }
            break;

        default:
            break;
    }
    if (ctrl->stateCb != NULL) {
        ctrl->stateCb(progress->status);
    }
}

void SkOtaRegStateCallback(SkOtaStateCallback_t cb) {
    g_otaManager.stateCb = cb;
}

/**
 * @brief Called on system idle tick to check if OTA update should be triggered
 * @param idleTimeSeconds System idle time in seconds
 * @return true if OTA check was triggered, false otherwise
 */
bool SkOtaManagerOnIdleTick(uint32_t idleTimeSeconds){
    SkOtaManagerCtrl *ctrl = &g_otaManager;

    if (ctrl->initialized != true || ctrl->autoCheckEnabled != true) {
        return false;
    }

    // 空闲指定时间后触发OTA检查
    if (idleTimeSeconds >= SK_OTA_IDLE_TRIGGER_TIME_SEC) {
        uint32_t currentTime = xTaskGetTickCount() / (configTICK_RATE_HZ);

        // 避免频繁检查，至少间隔指定时间
        if (currentTime - ctrl->lastOtaCheckTime >= SK_OTA_CHECK_INTERVAL_SEC) {
            ESP_LOGI(TAG, "System idle for %u seconds, triggering OTA check", idleTimeSeconds);
            ctrl->lastOtaCheckTime = currentTime;

            SkOtaManagerCheckUpdate();
            return true;
        }
    }

    return false;
}



SkOtaError SkOtaManagerInit(void){
    SkOtaManagerCtrl *ctrl = &g_otaManager;

    if (ctrl->initialized) {
        return SK_OTA_ERR_OK;
    }

    // Initialize OTA configuration
    if (SkOtaConfigInit() != 0) {
        ESP_LOGE(TAG, "Failed to initialize OTA configuration");
        return SK_OTA_ERR_INVALID_PARAM;
    }

    // Initialize OTA module
    SkOtaError err = SkOtaInit();
    if (err != SK_OTA_ERR_OK) {
        ESP_LOGE(TAG, "Failed to initialize OTA module: %s", SkOtaErrorToString(err));
        return err;
    }

    // Check if we need to verify the current application after OTA
    if (SkOtaNeedsVerification()) {
        ESP_LOGI(TAG, "Application needs verification after OTA update");
        
        // Perform basic self-test (you can customize this)
        bool diagnosticPassed = true;
        
        // Add your diagnostic tests here
        // For example: check critical peripherals, network connectivity, etc.
        if (diagnosticPassed) {
            ESP_LOGI(TAG, "Diagnostic passed, marking app as valid");
            err = SkOtaMarkAppValid();
            if (err != SK_OTA_ERR_OK) {
                ESP_LOGE(TAG, "Failed to mark app as valid: %s", SkOtaErrorToString(err));
            }
        } else {
            ESP_LOGE(TAG, "Diagnostic failed, triggering rollback");
            SkOtaMarkAppInvalidAndRollback();
        }
    }

    // Initialize manager structure
    memset(ctrl, 0, sizeof(*ctrl));
    ctrl->initialized = true;
    ctrl->autoCheckEnabled = SkOtaConfigGetAutoCheck();
    ctrl->lastOtaCheckTime = 0;  // 初始化检查时间

    ESP_LOGI(TAG, "OTA manager initialized successfully");
    SkOtaManagerSetSkipSameVersion(true);

    return SK_OTA_ERR_OK;
}

SkOtaError SkOtaManagerCheckUpdate(void) {
    SkOtaManagerCtrl *ctrl = &g_otaManager;

    if (ctrl->initialized == false) {
        return SK_OTA_ERR_INVALID_PARAM;
    }

    // Check if auto update is enabled
    if (ctrl->autoCheckEnabled == false) {
        ESP_LOGI(TAG, "OTA auto check is disabled");
        return SK_OTA_ERR_OK;
    }

    // Create OTA config using common function
    SkOtaConfigStatic config;
    if (SkOtaConfigCreateDefault(&config) != 0) {
        ESP_LOGE(TAG, "Failed to create OTA configuration");
        return SK_OTA_ERR_INVALID_PARAM;
    }

    ESP_LOGI(TAG, "Starting OTA update from: %s", config.url);
    return SkOtaStart(SkOtaProgressCallback, ctrl);
}

SkOtaError SkOtaManagerStop(void){
    SkOtaManagerCtrl *ctrl = &g_otaManager;

    if (ctrl->initialized == false) {
        return SK_OTA_ERR_INVALID_PARAM;
    }

    ESP_LOGI(TAG, "Stopping OTA update");
    return SkOtaStop();
}

SkOtaStatus SkOtaManagerGetStatus(void){
    SkOtaManagerCtrl *ctrl = &g_otaManager;

    if (ctrl->initialized == false) {
        return SK_OTA_STATUS_IDLE;
    }

    return SkOtaGetStatus();
}

SkOtaError SkOtaManagerSetServer(const char *serverUrl){
    SkOtaManagerCtrl *ctrl = &g_otaManager;

    if (ctrl->initialized == false || serverUrl == NULL) {
        return SK_OTA_ERR_INVALID_PARAM;
    }

    // Validate HTTP-only URL using common function
    if (SkOtaConfigSetServer(serverUrl) != 0) {
        ESP_LOGE(TAG, "Invalid server URL");
        return SK_OTA_ERR_INVALID_PARAM;
    }

    // Update configuration (no need to store locally)
    SkOtaConfigSave();

    ESP_LOGI(TAG, "OTA server updated: %s", serverUrl);
    return SK_OTA_ERR_OK;
}

void SkOtaManagerSetAutoCheck(bool enable){
    SkOtaManagerCtrl *ctrl = &g_otaManager;

    if (ctrl->initialized == false) {
        return;
    }

    ctrl->autoCheckEnabled = enable;

    // Update configuration
    SkOtaConfigSetAutoCheck(enable);
    SkOtaConfigSave();

    ESP_LOGI(TAG, "Idle-based OTA check %s", enable ? "enabled" : "disabled");
}

void SkOtaManagerSetSkipSameVersion(bool skip){
    SkOtaManagerCtrl *ctrl = &g_otaManager;

    if (ctrl->initialized == false) {
        return;
    }

    // Update configuration
    SkOtaConfigSetSkipSameVersion(skip);
    SkOtaConfigSave();

    ESP_LOGI(TAG, "Skip same version %s", skip ? "enabled" : "disabled");
}
