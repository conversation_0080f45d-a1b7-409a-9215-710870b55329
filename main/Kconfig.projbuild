menu "SK Configuration"
    config SK_CPU_USAGE_SHOW
        bool "CPU usage statistic, only for debug."
        default "n"

    choice BOARD_SELECT
        prompt "Board type"
        default ESP32_S3_BOX3
        help
            Select destination for application trace: JTAG or none (to disable).

        config ESP32_S3_BOX3
            bool "Standard demo board ESP32-S3-BOX3"
            help
                Select standard demo board ESP32-S3-BOX3.

        config OUTSOURCE_BOARD_V_0_1
            bool "Outsorce board"
            help
                Select outsorce board.

        config DEMO_BOARD_V_0_1
            bool "Demo board designed by FineTuning."
            help
                Select demo board designed by FineTuning.
    endchoice

    config SK_BOARD_BOX3
        int
        default 1

    config SK_BOARD_V_0_1
        int
        default 2

    config FT_BOARD_V_0_1
        int
        default 3
    
    # 映射用户选择到符号常量值
    config BOARD_TYPE
        int
        default SK_BOARD_BOX3 if ESP32_S3_BOX3
        default SK_BOARD_V_0_1 if OUTSOURCE_BOARD_V_0_1
        default FT_BOARD_V_0_1 if DEMO_BOARD_V_0_1

endmenu
