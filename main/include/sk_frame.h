/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_frame.h
 * @description: 协议帧格式定义.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_FRAME_H
#define SK_FRAME_H

#include <stdint.h>

#define FRAME_HEAD_FLAG (0x1981)
#define DPKT_DATA_LENGTH (256)

enum {
    FRAME_NULL = 0,
    FRAME_CMSG_TERM_TO_CTRL = 1,
    FRAME_CMSG_CTRL_TO_TERM = 2,
    FRAME_CMSG_CTRL_TO_RELAY = 3,
    FRAME_CMSG_RELAY_TO_CTRL = 4,
    FRAME_CMSG_TERM_TO_RELAY = 5,
    FRAME_DPKT_TERM_AND_RELAY = 6,
    FRAME_CMSG_CTRL_TO_AGENT = 7,
    FRAME_CMSG_AGENT_TO_CTRL = 8,
    FRAME_CMSG_TERM_AND_TERM = 9,
    FRAME_CMSG_AGENT_TO_RELAY = 10,
    FRAME_CMSG_AGENT_AND_TERM = 11,
    FRAME_DPKT_AGENT_AND_RELAY = 12,
    FRAME_NOT_FINISHED = 13,
    FRAME_LAST = 14,
    FRAME_MAX = 15,
    FRAME_CMSG_DFX_KEEPALIVE = 24,
    FRAME_CMSG_DFX_AND_TERM = 25,
};

enum {
    // FRAME_CMSG_TERM_TO_CTRL
    MSG_TYPE_INVALID = 0,
    MSG_TERM_TO_CTRL_REGISTER_REQUEST = 1,
    MSG_TERM_TO_CTRL_LOGIN_REQUEST = 2,
    MSG_TERM_TO_CTRL_OFF_LINE_NOTICE = 3,
    MSG_TERM_TO_CTRL_CALLER_REQUEST = 10,
    MSG_TERM_TO_CTRL_CALLER_CALLING_NOTICE = 11,
    MSG_TERM_TO_CTRL_CALL_FINISH_NOTICE = 12,
    MSG_TERM_TO_CTRL_CALL_IDLE_NOTICE = 13,

    // FRAME_CMSG_CTRL_TO_TERM
    MSG_CTRL_TO_TERM_REGISTER_OK = 21,
    MSG_CTRL_TO_TERM_REGISTER_REFUSE = 22,
    MSG_CTRL_TO_TERM_IDLE_OK = 26,
    MSG_CTRL_TO_TERM_ALIVE_OK = 27,
    MSG_CTRL_TO_TERM_CALLING_OK = 28,
    MSG_CTRL_TO_TERM_CALLER_REQUEST_OK = 31,
    MSG_CTRL_TO_TERM_CALLER_REQUEST_REFUSED = 32,
    MSG_CTRL_TO_TERM_CONNECT_RELAY_AS_CALLER_REQUEST = 40,
    MSG_CTRL_TO_TERM_CONNECT_RELAY_AS_CALLEE_REQUEST = 41,
    MSG_CTRL_TO_TERM_FRIEND_LIST = 42,
    MSG_CTRL_TO_TERM_MUSIC_LIST = 43,
    MSG_CTRL_TO_TERM_STORY_LIST = 44,

    // FRAME_CMSG_TERM_TO_RELAY
    MSG_TERM_TO_RELAY_CALLER_REQUEST = 50,
    MSG_TERM_TO_RELAY_CALLEE_REQUEST = 51,

    // FRAME_CMSG_TERM_AND_TERM
    MSG_TERM_TO_TERM_OFFHOOK = 52,
    MSG_TERM_TO_TERM_ONHOOK = 53,
    MSG_TERM_TO_TERM_ALIGN_AUDIO = 54,
    MSG_TERM_TO_TERM_ALIGN_AUDIO_REPLY = 55,

    // FRAME_CMSG_TERM_TO_CLOUD_AI
    MSG_TERM_TO_CLOUD_AI_CALLEE_MATCH_REQUEST = 1,
    MSG_TERM_TO_CLOUD_AI_CALLEE_MATCH_REQUEST_RESP = 2,
    MSG_CLOUD_AI_TO_TERM_CALLEE_VOL_UP = 3,
    MSG_CLOUD_AI_TO_TERM_CALLEE_VOL_DOWN = 4,
    MSG_CLOUD_AI_TO_TERM_DIST_CHAT = 5,

    // FRAME_CMSG_DFX_AND_TERM
    MSG_DFX_AND_TERM_GET_PARAM = 0,
    MSG_DFX_AND_TERM_SET_PARAM = 1,
    MSG_DFX_AND_TERM_KEEPALIVE = 2,
    MSG_DFX_AND_TERM_PCM1 = 3,          //  单声道
    MSG_DFX_AND_TERM_PCM2 = 4,          //  双声道
    MSG_DFX_AND_TERM_OPUS = 5,          //  opus
    MSG_DFX_AND_TERM_TIME_RECORD = 6,
    MSG_DFX_AND_TERM_SENSOR_DATA = 7,
    MSG_DFX_AND_AGENT_TIME_RECORD = 8,
    MSG_DFX_AND_TERM_DATE_INFO = 9,
    MSG_DFX_TO_TERM_ENABLE_REPORT_DEVINFO = 10,
    MSG_DFX_TO_TERM_ENABLE_REPORT_TIME_RECORD = 11,
    MSG_DFX_TO_TERM_DISABLE_REPORT_DEVINFO = 12,
    MSG_DFX_TO_TERM_DISABLE_REPORT_TIME_RECORD = 13,
    MSG_DFX_TO_TERM_ENABLE_REPORT_AUDIO = 14,
    MSG_DFX_TO_TERM_DISABLE_REPORT_AUDIO = 15,
    MSG_DFX_TO_TERM_RESET_GYRO = 16,
};

enum {
    SESSION_TYPE_INVALID = 0,
    SESSION_TYPE_CALLER_REQUEST = 1,
    SESSION_TYPE_RELAY_RESOURCE_REQUEST = 2,
    SESSION_TYPE_RELAY_ID_DISTRIBUTE_TO_CALLER = 3,
    SESSION_TYPE_RELAY_ID_DISTRIBUTE_TO_CALLEE = 4,
    SESSION_TYPE_CALL_FINISH_CMD = 5,
    SESSION_TYPE_REGISTER = 6,
    SESSION_TYPE_RELAY_REQUEST = 7,
    SESSION_TYPE_CALLEE_MATCH = 8,
    SESSION_TYPE_LAST = 9,
};

typedef struct __attribute__((packed)) {
    uint16_t headFlag;      // 用于识别消息的开始； 比0x1981
    uint8_t frameType;      // 消息类型, 1，用于终端      ->   控制器消息类型，
                            //          2 用于控制器     ->   终端发消息， 
                            //          3 用于控制器     ->   relay服务器发消息；
                            //          4 用于Relay服务器->   控制服务器发消息
                            //          5 用于终端       ->   Relay之间的控制消息；
                            //          6 用于终端       ->   relay之间的数据报文
                            //          7 控制器         ->  智能体 控制报文
                            //          8 智能体         ->  控制器 控制报文
    uint8_t msgType;        // 子消息类型              
    uint16_t headLen;       // headlen，用于帧头的长度定义； 不同的版本可能长度不同；
    uint16_t payloadLen;    // 负荷内容，目前控制消息固定是256，数据报文使用同一个帧头，长度不固定；
    uint16_t seqID;         // 消息的序号；
    uint8_t resv[4];        // 保留位4个位置；
    uint16_t crc;           // 校验
} FrameHead;

typedef struct __attribute__((packed)) {
    uint32_t encDoneTick;   // ENC侧编码完成时间
    uint32_t encTxTick;     // ENC侧进入Tx Socket时间
    uint32_t relayRxTick;   // Relay接收时间
    uint32_t relayTxTick;   // Relay发送时间
    uint32_t decRxTick;     // DEC侧从TCP流中解析出时间
    uint32_t decDoneTick;   // DEC侧解码完成时间
} AudioTickRecord;

typedef struct __attribute__((packed)) {
    uint32_t sessionID;         // 会话ID
    uint8_t type;               // 消息类型
    uint8_t version;            // 消息版本
    uint8_t headLen;            // 头部长度
    uint8_t msgIndexInSession;  // 会话中的第几个消息
} SessionHead;

typedef struct __attribute__((packed)) {
    uint8_t msgType;
    uint8_t msgLen;
    uint32_t controllerInfo;

    uint8_t callerTerminalID[6];
    uint8_t callerRoleID[6];
    uint8_t callerHwVersion;
    uint8_t callerSwVersion;
    uint8_t callerIP[4];
    uint16_t callerReserve;
    
    uint8_t calleeTerminalID[6];
    uint8_t calleeRoleID[6];
    uint8_t calleeHwVersion;
    uint8_t calleeSwVersion;
    uint8_t calleeIP[4];
    uint16_t calleeReserve;

    uint8_t callStartTime[6];

    uint32_t relayResourceID;
    uint8_t relayIP[4];
    uint16_t relayPort;
    uint8_t chatID[16];
} CtrlMsg;

typedef struct __attribute__((packed)) {
    uint8_t msgType;
    uint8_t matchResult;
    uint16_t msgLen;

    uint32_t controllerInfo;

    uint8_t callerTerminalID[6];
    uint8_t callerRoleID[6];
    uint8_t callerIP[4];
    uint8_t callerHwVersion;
    uint8_t callerSwVersion;

    uint8_t startTime[6];
    uint8_t requestID[16];
    uint8_t calleeNameList[800];
} CtrlMsgCalleeMatch;

typedef struct __attribute__((packed)) {
    FrameHead frameHead;
    SessionHead sessionHead;
    CtrlMsg ctrlMsg;
} CtrlMsgFrame;

typedef struct __attribute__((packed)) {
    FrameHead frameHead;
    uint32_t timeRecord[8];
    uint8_t data[DPKT_DATA_LENGTH];
} DataFrame;

typedef struct __attribute__((packed)) {
    FrameHead frameHead;
    SessionHead sessionHead;
    CtrlMsgCalleeMatch ctrlMsg;
} CtrlFrameCalleeMatch;

typedef struct __attribute__((packed)) {
    int8_t ctrlServer[64];
    uint16_t port;
    uint8_t spkInitVol;
    uint8_t micInitVol;
    int8_t callAgentIp[16];
    uint16_t callAgentPort;
    uint8_t mnThd;
    uint8_t resv;
} DbgSysConfig;

typedef struct __attribute__((packed)) {
    uint8_t paramId;
    uint8_t resv;
    uint16_t length;
    uint8_t data[];
} DbgParamData;

typedef struct __attribute__((packed)) {
    FrameHead frameHead;
    DbgParamData dbgParamData;
} DbgFrame;

#ifdef __cplusplus
extern "C" {
#endif

void EncodeFrameHead(FrameHead *frameHead, uint16_t frameType, uint16_t msgType, uint16_t payloadLen);
void EncodeRegisterReqMsg(CtrlMsgFrame *frame);
void EncodeStateReportMsg(CtrlMsgFrame *frame, uint8_t msgType, uint8_t chatId[16]);
void EncodeCallRequestMsg(CtrlMsgFrame *frame, uint8_t calleeRole);
void EncodeRelayReqMsg(CtrlMsgFrame *frame, uint8_t msgType);
void EncodeTermOnhookOffhookMsg(CtrlMsgFrame *frame, uint8_t msgType);
void EncodeRelayAlignAudioMsg(CtrlMsgFrame *frame);
void EncodeRelayDataMsg(DataFrame *frame, uint16_t payloadLen);
void EncodeTermToCloudAiMsg(CtrlFrameCalleeMatch *frame, uint8_t msgType);

#ifdef __cplusplus
}
#endif

#endif