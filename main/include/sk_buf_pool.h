/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_buf_pool.h
 * @description: 内存池功能.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_BUF_POOL_H
#define SK_BUF_POOL_H

#include <stddef.h>
#include <stdbool.h>
#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"
#include "sk_list.h"

typedef struct {
    SkListNode_t node;
    uint8_t *data;
    uint32_t size;          // data空间大小
    uint32_t head;          // data实际使用的起始位置
    uint32_t tail;          // data实际使用的结束位置
} SkBufPoolNode;

typedef struct {
    SkList_t freeList;
    SkList_t usedList;

    SkList_t freeTempIn;    // 一次从freeList取出多个，用于临时存储
    SkList_t usedTempIn;    // 缓存到usedTemp, 一次放入usedList

    uint32_t usedTempCnt;
    uint32_t usedTempMax;
    uint32_t freeTempMax;
    uint32_t deep;

    SemaphoreHandle_t mutexFree;
    SemaphoreHandle_t mutexUsed;
    SkBufPoolNode *nodeArray;
} SkBufPool;

#ifdef __cplusplus
extern "C" {
#endif

int32_t SkBufPoolInit(SkBufPool *array, uint32_t deep, uint32_t size, uint32_t usedTempMax, uint32_t freeTempMax);
void SkBufPoolDeinit(SkBufPool *array);
SkBufPoolNode* SkBufPoolGetFree(SkBufPool *array);
void SkBufPoolPutData(SkBufPool *array, SkBufPoolNode *node);
SkBufPoolNode* SkBufPoolGetData(SkBufPool *array);
void SkBufPoolPutFree(SkBufPool *array, SkBufPoolNode *node);

#ifdef __cplusplus
}
#endif

#endif // SK_BUF_POOL_H