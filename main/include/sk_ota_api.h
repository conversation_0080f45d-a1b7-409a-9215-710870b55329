/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_ota_api.h
 * @description: OTA API header for main program integration.
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025-01-19
 */

#ifndef __SK_OTA_API_H__
#define __SK_OTA_API_H__

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief OTA update status
 */
typedef enum {
    SK_OTA_STATUS_IDLE = 0,           ///< OTA is idle
    SK_OTA_STATUS_CONNECTING,         ///< Connecting to server
    SK_OTA_STATUS_DOWNLOADING,        ///< Downloading firmware
    SK_OTA_STATUS_VERIFYING,          ///< Verifying firmware
    SK_OTA_STATUS_INSTALLING,         ///< Installing firmware
    SK_OTA_STATUS_SUCCESS,            ///< OTA completed successfully
    SK_OTA_STATUS_FAILED,             ///< OTA failed
    SK_OTA_STATUS_ROLLBACK            ///< Rollback in progress
} SkOtaStatus;

/**
 * @brief OTA error codes
 */
typedef enum {
    SK_OTA_ERR_OK = 0,                ///< No error
    SK_OTA_ERR_INVALID_PARAM,         ///< Invalid parameter
    SK_OTA_ERR_NETWORK,               ///< Network error
    SK_OTA_ERR_SERVER,                ///< Server error
    SK_OTA_ERR_DOWNLOAD,              ///< Download error
    SK_OTA_ERR_VERIFY,                ///< Verification error
    SK_OTA_ERR_INSTALL,               ///< Installation error
    SK_OTA_ERR_ROLLBACK,              ///< Rollback error
    SK_OTA_ERR_TIMEOUT,               ///< Timeout error
    SK_OTA_ERR_MEMORY,                ///< Memory error
    SK_OTA_ERR_SECURITY               ///< Security version error
} SkOtaError;

typedef void (*SkOtaStateCallback_t)(const SkOtaStatus state);

SkOtaError SkOtaManagerInit(void);

SkOtaError SkOtaManagerCheckUpdate(void);

/**
 * @brief Stop current OTA update
 * @return SK_OTA_ERR_OK on success, error code otherwise
 */
SkOtaError SkOtaManagerStop(void);

/**
 * @brief Get current OTA manager status
 * @return Current OTA status
 */
SkOtaStatus SkOtaManagerGetStatus(void);

/**
 * @brief Set OTA server configuration (HTTP-only)
 * @param serverUrl OTA server base URL (must be HTTP)
 * @return SK_OTA_ERR_OK on success, error code otherwise
 */
SkOtaError SkOtaManagerSetServer(const char *serverUrl);

/**
 * @brief Called on system idle tick to check if OTA should be triggered
 * @param idleTimeSeconds System idle time in seconds
 * @return true if OTA check was triggered, false otherwise
 */
bool SkOtaManagerOnIdleTick(uint32_t idleTimeSeconds);

/**
 * @brief Enable/disable idle-based OTA check capability
 * @param enable true to enable idle-based checks, false to disable
 */
void SkOtaManagerSetAutoCheck(bool enable);

void SkOtaManagerSetSkipSameVersion(bool skip);

const char* SkOtaStatusToString(SkOtaStatus status);

const char* SkOtaErrorToString(SkOtaError error);

void SkOtaRegStateCallback(SkOtaStateCallback_t cb);

#ifdef __cplusplus
}
#endif

#endif /* __SK_OTA_API_H__ */
