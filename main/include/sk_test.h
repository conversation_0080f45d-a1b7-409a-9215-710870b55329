/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_test.h
 * @description: UT测试用例框架头文件
 * @author: <PERSON>
 * @date: 2025-07-18
 */

#ifndef __SK_TEST_H__
#define __SK_TEST_H__

#include "sk_common.h"

#ifdef __cplusplus
extern "C" {
#endif

#define TC_WEBSOCKET    1
#define TC_OTA          2

#if (TESTCASE_ENABLED == TC_WEBSOCKET)
#define SkTestMain() SkTestWebSocketMain()
#else
#define SkTestMain()
#endif

void SkTestWebSocketMain();

#ifdef __cplusplus
}
#endif

#endif /* __SK_TEST_H__ */