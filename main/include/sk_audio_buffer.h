/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_audio_buffer.h
 * @description: 音频使用的Buffer队列定义.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_AUDIO_BUFFER_H
#define SK_AUDIO_BUFFER_H

#include <stdint.h>
#include <stdbool.h>
#include "sk_common.h"
#include "sk_list.h"


#ifdef __cplusplus
extern "C" {
#endif

enum {
    SK_AUDIO_BUF_STATE_IN_FREE = 0,
    SK_AUDIO_BUF_STATE_OUT_FREE = 1,
    SK_AUDIO_BUF_STATE_IN_DATA = 2,
    SK_AUDIO_BUF_STATE_OUT_DATA = 3
};

typedef struct {
    SkListNode_t listNode;              // list item 必须放在开始位置, 以便于使用链表操作
    uint8_t *data;
    uint16_t size;                      // data指向空间大小, 队列创建时指定
    uint16_t offset;                    // data指向空间偏移, 队列创建时指定, offset之前用于报文组装时预留
    uint16_t length;                    // data实际数据长度，由用户设置
    uint16_t index;                     // data数据块索引, 创建时生成
    uint16_t status;                    // data状态, 0: free, 1: in use
    uint16_t sessionId;
    uint16_t speechFlag;
    uint16_t voiceType;                 // 1: local, 0: remote
    uint32_t dataFlag;                  // 两个相邻的模块间传递，相互之间定义
    uint8_t *addrBackup;
    uint32_t timeRecord[8];
} SkAudioBuf;

typedef void* SkAudioQueueHandler;

void* SkCreateAudioQueue(uint32_t queueSize, size_t bufferSize, size_t offset);
void SkDesctoryAudioQueue(void *handler);

SkAudioBuf* SkAudioBufferGetFree(void *handler, uint32_t timeout);
int SkAudioBufferPutFree(void *handler, SkAudioBuf *buf);
SkAudioBuf* SkAudioBufferGetData(void *handler, uint32_t timeout);
int SkAudioBufferPutData(void *handler, SkAudioBuf *buf);
void AudioQueueDisplay(void *handler);
bool SkAudioBufferHasFree(void *handler);
bool SkAudioBufferHasData(void *handler);

#ifdef __cplusplus
}
#endif

#endif /* SK_AUDIO_BUF_H */