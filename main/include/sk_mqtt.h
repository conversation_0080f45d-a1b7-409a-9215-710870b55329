/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_mqtt.h
 * @description: MQTT适配层
 * @author: <PERSON>
 * @date: 2025-07-16
 */
#ifndef SK_MQTT_H
#define SK_MQTT_H
#include "sk_common.h"

#ifdef __cplusplus
extern "C" {
#endif

#define SK_MQTT_EVENT_MASK(x) (1UL << (x))
#define SK_MQTT_TOPIC_MAX_LEN 64

enum {
    SK_MQTT_EVENT_NULL,
    SK_MQTT_EVENT_CONNECT,
    SK_MQTT_EVENT_DISCONNECT,
    SK_MQTT_EVENT_DATA,
    SK_MQTT_EVENT_DATA_PARTIAL,
    SK_MQTT_EVENT_ERROR
};

typedef struct {
    char* topic;
    uint32_t topicLen;
    uint8_t* data;
    uint32_t dataLen;
    uint32_t dataTotalLen;
} SkMqttDataInfo;

typedef void (*SkMqttEventCb)(void *arg, int32_t event);
typedef void (*SkMqttDataCb)(void *arg, SkMqttDataInfo *dataInfo);

void SkMqttClientInit();
void SkMqttClientDeinit();
void SkMqttRegEventCb(uint32_t eventMask, SkMqttEventCb cb, void *arg);
void SkMqttRegDataCb(SkMqttDataCb cb, void *arg);
void SkMqttConnect(const char* brokerAddr, int brokerPort, const char* clientId, const char* userName, const char* password);
void SkMqttDisconnect();
bool SkMqttPublish(const char *topic, const char *payload, uint32_t payloadLen, int qos);
bool SkMqttSubscribe(const char *topic, int qos);
bool SkMqttUnsubscribe(const char *topic);
bool SkMqttIsConnected();
void SkMqttGetTopic(char *topic, uint32_t topicLen, SkMqttDataInfo *dataInfo);

#ifdef __cplusplus
}
#endif

#endif // SK_MQTT_H