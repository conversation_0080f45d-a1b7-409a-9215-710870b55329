/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_log.h
 * @description: 日志模块接口.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_LOG_H
#define SK_LOG_H

#include "esp_log.h"

#define SK_LOGE(tag, format, ...) ESP_LOGE(tag, format, ##__VA_ARGS__)
#define SK_LOGW(tag, format, ...) ESP_LOGW(tag, format, ##__VA_ARGS__)
#define SK_LOGI(tag, format, ...) ESP_LOGI(tag, format, ##__VA_ARGS__)
#define SK_LOGD(tag, format, ...) ESP_LOGD(tag, format, ##__VA_ARGS__)

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
}
#endif

#endif
