/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_wifi.h
 * @description: WIFI接口封装.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_WIFI_H
#define SK_WIFI_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

enum {
    SK_WIFI_EVENT_NULL = 0,
    SK_WIFI_EVENT_AP_STOP,
    SK_WIFI_EVENT_AP_START,
    SK_WIFI_EVENT_STA_STOP,
    SK_WIFI_EVENT_STA_SCAN,
    SK_WIFI_EVENT_STA_CONNECTING,
    SK_WIFI_EVENT_STA_CONNECTED,
};

typedef void (*SkWifiEventCb)(uint32_t event);

void SkWifiInit();
void SkWifiDeinit();
int32_t SkWifiStartAp();
int32_t SkWifiStartSta();
int32_t SkWifiStop();
void SkWifiGetIp(uint32_t wifiMode, char *ipStr, int32_t len);
void SkWifiRegEventCb(SkWifiEventCb cb);

#ifdef __cplusplus
}
#endif

#endif