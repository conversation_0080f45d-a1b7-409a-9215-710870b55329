/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_queue.h
 * @description: 线程安全的队列接口, 非中断使用.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_QUEUE_H
#define SK_QUEUE_H

#include <stddef.h>
#include <stdbool.h>
#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"
#include "sk_list.h"

#ifdef __cplusplus
extern "C" {
#endif

#define SK_TIMEOUT_MAX 0xFFFFFFFFU

typedef struct SkQueue {
    SkList_t list;
    uint32_t count;
    SemaphoreHandle_t mutex;
    SemaphoreHandle_t dataValid;
} SkQueue_t;

void SkQueueInit(SkQueue_t *queue, uint32_t size);
uint32_t SkQueuePut(SkQueue_t *queue, SkListNode_t *item);
SkListNode_t *SkQueueGet(SkQueue_t *queue, uint32_t timeout);

#ifdef __cplusplus
}
#endif

#endif