/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_dfx.h
 * @description: DFX功能接口
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_DFX_H
#define SK_DFX_H

#include <stdint.h>
#include <freertos/FreeRTOS.h>
#include "sk_common.h"
#include "sk_board.h"
#include "sk_frame.h"                          // for msgType

#ifdef __cplusplus
extern "C" {
#endif

#define CONFIG_SK_DEBUG_FUNC                (CONFIG_SK_DFX_NONE)
#define CONFIG_SK_DFX_NONE                      (0)

typedef struct {
    uint16_t msgType;
    size_t size;
    size_t pos;
    size_t minUnitSize;
    EventGroupHandle_t eventGroup;
    uint8_t *buffer;
} SkDfxCtrl_t;

// Reporter接口
void SkDfxInit(SkDfxCtrl_t *dfxCtrl, uint8_t msgType, size_t size, size_t minUnitSize);
void SkDfxStart(SkDfxCtrl_t *dfxCtrl);
void SkDfxStop(SkDfxCtrl_t *dfxCtrl);
void SkDfxRecordData(SkDfxCtrl_t *dfxCtrl, uint8_t *data, size_t len);
void SkDfxProcCall(SkDfxCtrl_t *dfxCtrl);

// Debug Server接口
void SkDfxLinkStart();
void SkDfxLinkStop();
void SkDfxLinkSendMsg(uint16_t msgType, uint8_t *buffer, uint32_t bytes);
void SkDfxLinkSendAudio(uint16_t msgType, uint8_t *buffer, uint32_t bytes);

#ifdef __cplusplus
}
#endif

#endif /* SK_DFX_H */
