/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_list.h
 * @description: 列表通用功能. 非线程安全接口。
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_LIST_H
#define SK_LIST_H

#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>

struct SkListNode;

// 链表节点（私有，不暴露给外部）
typedef struct SkListNode {
    struct SkListNode* prev;
    struct SkListNode* next;
} SkListNode_t;

// 链表容器（对外暴露的句柄）
typedef struct {
    SkListNode_t head;          // 头哨兵节点（不存储数据）
    SkListNode_t tail;          // 尾哨兵节点（不存储数据）
} SkList_t;

#ifdef __cplusplus
extern "C" {
#endif

static inline void SkListInit(SkList_t* list) {
    // 初始化哨兵节点关系
    list->head.next = &list->tail;
    list->head.prev = NULL;
    list->tail.prev = &list->head;
    list->tail.next = NULL;
    return;
}

static inline void SkListInsertEnd(SkList_t* list, SkListNode_t* node) {
    SkListNode_t *lastNode = list->tail.prev;
    
    // 调整指针关系
    node->prev = lastNode;
    node->next = &list->tail;
    lastNode->next = node;
    list->tail.prev = node;

    return;
}

static inline SkListNode_t* SkListGetFront(SkList_t* list) {
    if (list->head.next == &list->tail) {
        return NULL;
    }

    return list->head.next;
}

static inline void SkListRemove(SkList_t* list, SkListNode_t* node) {
    if (node == &list->head || node == &list->tail) {
        return;
    }
    
    // 调整相邻节点指针
    node->prev->next = node->next;
    node->next->prev = node->prev;
    
    return;
}

static inline bool SkListIsEmpty(SkList_t* list) {
    return (list->head.next == &list->tail);
}

#ifdef __cplusplus
}
#endif

#endif