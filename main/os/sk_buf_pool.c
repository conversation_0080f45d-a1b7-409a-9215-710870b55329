/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_buf_pool.c
 * @description: 内存池模块.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <stdlib.h>
#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"
#include "esp_log.h"
#include "sk_os.h"
#include "sk_common.h"
#include "sk_buf_pool.h"

#define TAG "SkBufPool"

int32_t SkBufPoolInit(SkBufPool *array, uint32_t deep, uint32_t size, uint32_t usedTempMax, uint32_t freeTempMax) {
    uint32_t i;
    SkBufPoolNode *nodeTemp = NULL;

    SkListInit(&array->freeList);
    SkListInit(&array->usedList);
    SkListInit(&array->freeTempIn);
    SkListInit(&array->usedTempIn);
    array->usedTempCnt = 0;
    array->usedTempMax = usedTempMax;
    array->freeTempMax = freeTempMax;
    array->mutexFree = xSemaphoreCreateMutex();
    array->mutexUsed = xSemaphoreCreateMutex();
    array->nodeArray = (SkBufPoolNode *)malloc(sizeof(SkBufPoolNode) * deep);
    array->deep = deep;
    if (array->nodeArray == NULL) {
        ESP_LOGE(TAG, "SkBufPoolInit: malloc failed");
        return SK_RET_FAIL;
    }
    for (i = 0; i < deep; i++) {
        nodeTemp = &array->nodeArray[i];
        nodeTemp->data = (uint8_t *)SkOsAllocPsram(size, sizeof(uint32_t));
        if (nodeTemp->data == NULL) {
            ESP_LOGE(TAG, "SkBufPoolInit: malloc failed");
            return SK_RET_FAIL;
        }
        nodeTemp->size = size;
        nodeTemp->head = 0;
        nodeTemp->tail = 0;
        SkListInsertEnd(&array->freeList, &nodeTemp->node);
        ESP_LOGD(TAG, "SkBufPoolInit: %d %d", i, nodeTemp->size);
    }

    return SK_RET_SUCCESS;
}

void SkBufPoolDeinit(SkBufPool *array) {
    SkBufPoolNode *nodeTemp = NULL;
    if (array == NULL) {
        return;
    }
    SkListInit(&array->freeList);
    SkListInit(&array->usedList);
    SkListInit(&array->freeTempIn);
    SkListInit(&array->usedTempIn);
    if (array->mutexFree != NULL) {
        vSemaphoreDelete(array->mutexFree);
        array->mutexFree = NULL;
    }
    if (array->mutexUsed != NULL) {
        vSemaphoreDelete(array->mutexUsed);
        array->mutexUsed = NULL;
    }
    if (array->nodeArray == NULL) {
        return;
    }
    for (int i = 0; i < array->deep; i++) {
        nodeTemp = &array->nodeArray[i];
        free(nodeTemp->data);
        nodeTemp->data = NULL;
    }
    free(array->nodeArray);

    return;
}

void BufArrayUpdateFree(SkBufPool *array) {
    uint32_t i;
    xSemaphoreTake(array->mutexFree, portMAX_DELAY);
    if (SkListIsEmpty(&array->freeList)) {
        xSemaphoreGive(array->mutexFree);
        return;
    }
    for (i = 0; i < array->freeTempMax; i++) {
        SkBufPoolNode *nodeTemp = (SkBufPoolNode *)SkListGetFront(&array->freeList);
        if (nodeTemp == NULL) {
            break;
        }
        SkListRemove(&array->freeList, &nodeTemp->node);
        SkListInsertEnd(&array->freeTempIn, &nodeTemp->node);
    }
    xSemaphoreGive(array->mutexFree);
    ESP_LOGI(TAG, "BufArrayUpdateFree: %d %d", array->freeTempMax, i);

    return;
}

void BufArrayUpdateUsed(SkBufPool *array) {
    int32_t i;

    xSemaphoreTake(array->mutexUsed, portMAX_DELAY);
    for (i = 0; i < array->usedTempMax; i++) {
        SkBufPoolNode *nodeTemp = (SkBufPoolNode *)SkListGetFront(&array->usedTempIn);
        if (nodeTemp == NULL) {
            break;
        }
        array->usedTempCnt = array->usedTempCnt != 0 ? array->usedTempCnt - 1 : 0;
        SkListRemove(&array->usedTempIn, &nodeTemp->node);
        SkListInsertEnd(&array->usedList, &nodeTemp->node);
    }
    xSemaphoreGive(array->mutexUsed);
    return;
}

SkBufPoolNode* SkBufPoolGetFree(SkBufPool *array) {
    if (SkListIsEmpty(&array->freeTempIn)) {
        BufArrayUpdateFree(array);
    }
    SkBufPoolNode *nodeTemp = (SkBufPoolNode *)SkListGetFront(&array->freeTempIn);
    if (nodeTemp != NULL) {
        SkListRemove(&array->freeTempIn, &nodeTemp->node);
    }
    return nodeTemp;
}

void SkBufPoolPutData(SkBufPool *array, SkBufPoolNode *node) {
    SkListInsertEnd(&array->usedTempIn, &node->node);
    array->usedTempCnt++;
    if (array->usedTempCnt >= array->usedTempMax) {
        BufArrayUpdateUsed(array);
    }
    return;
}

SkBufPoolNode* SkBufPoolGetData(SkBufPool *array) {
    SkBufPoolNode *nodeTemp = (SkBufPoolNode *)SkListGetFront(&array->usedList);
    if (nodeTemp != NULL) {
        SkListRemove(&array->usedList, &nodeTemp->node);
    }
    return nodeTemp;
}

void SkBufPoolPutFree(SkBufPool *array, SkBufPoolNode *node) {
    SkListInsertEnd(&array->freeList, &node->node);
    return;
}
