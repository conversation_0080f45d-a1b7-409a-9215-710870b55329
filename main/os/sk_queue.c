/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_queue.c
 * @description: 队列模块.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <stdlib.h>
#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"
#include "esp_log.h"
#include "sk_os.h"
#include "sk_common.h"
#include "sk_queue.h"

#define TAG "SkQueue"

// 初始化全局资源
void SkQueueInit(SkQueue_t *queue, uint32_t size) {
    SkListInit(&queue->list);
    queue->mutex = xSemaphoreCreateMutex();
    queue->dataValid = xSemaphoreCreateCounting(size, 0);
    queue->count = 0;

    return;
}

uint32_t SkQueuePut(SkQueue_t *queue, SkListNode_t *item) {
    if (xSemaphoreTake(queue->mutex, portMAX_DELAY) == pdTRUE) {
        SkListInsertEnd(&queue->list, item);
        queue->count++;
        xSemaphoreGive(queue->mutex);
        xSemaphoreGive(queue->dataValid);
    }
    return 0;
}

SkListNode_t *SkQueueGet(SkQueue_t *queue, uint32_t timeout) {
    SkListNode_t *item = NULL;
    TickType_t ticks = pdMS_TO_TICKS(timeout);
    
    if (xSemaphoreTake(queue->dataValid, ticks) == pdTRUE) {
        if (xSemaphoreTake(queue->mutex, portMAX_DELAY) == pdTRUE) {
            if (SkListIsEmpty(&queue->list)) {
                xSemaphoreGive(queue->mutex);
                return 0;
            }
            item = SkListGetFront(&queue->list);
            if (item != NULL) {
                SkListRemove(&queue->list, item);
                queue->count--;
            } else {
                queue->count = 0;
            }
            xSemaphoreGive(queue->mutex);
        }
    }
    return item;
}

uint32_t SkQueueGetValidCnt(SkQueue_t *queue) {
    return queue->count;
}
