/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_sr.c
 * @description: AFE处理模块,包含两个子模块语音识别的AFE和语音通信的AFE.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_process_sdkconfig.h"
#include "esp_wn_iface.h"
#include "esp_wn_models.h"
#include "esp_afe_sr_iface.h"
#include "esp_afe_sr_models.h"
#include "esp_mn_iface.h"
#include "esp_mn_models.h"
#include "esp_mn_speech_commands.h"
#include "esp_log.h"
#include "model_path.h"
#include "sk_common.h"
#include "sk_board.h"
#include "sk_audio.h"
#include "sk_sm.h"
#include "sk_afe.h"
#include "sk_os.h"
#include "sk_board.h"
#include "sk_dfx.h"
#include "sk_frame.h"

#define MAX_BUFFER_SIZE 8192
#define TAG "SkSr"

#define SK_AFE_ENABLE_BIT   BIT0
#define SK_AFE_STOP_BIT     BIT1

void SrInitCommands();

typedef struct {
    // 控制标志
    bool runFlag;                       // SR和VC任务是否执行
    bool cmdInputMode;                  // 是否进入语音指令识别状态
    bool mnRunStatus;                   // MN是否在执行，用于标记是否需要清空数据.每次使用前需要清空数据.
    bool updateThdFlag;                 // 预留调试接口, 用于标志需要刷新语音识别门限
    bool pmMode;                        // PowerSave Mode
    // 外界控制和同步使用的事件
    EventGroupHandle_t vcEventGroup;
    EventGroupHandle_t srEventGroup;
    // AFE接口
    esp_afe_sr_iface_t* srAfeHandle;
    esp_afe_sr_iface_t* vcAfeHandle;
    // AFE控制数据
    esp_afe_sr_data_t* srAfeData;
    esp_afe_sr_data_t* vcAfeData;
    int srChunkSize;                    // SR AFE每个Chunk的Sample数
    int vcChunkSize;                    // VC AFE每个Chunk的Sample数
    // 语音数据缓存
    uint32_t memSize;                   // 数据缓存的大小
    int16_t *srBuffer;
    size_t bufferWritePos;              // 写入srBuffer的数据位置
    size_t vcReadPos;                   // VC读取的数据位置
    size_t srReadPos;                   // SR读取的数据位置
    // 语音识别部分
    esp_mn_iface_t* multinet;           // 语音识别使用的接口
    model_iface_data_t *model_data;     // 语音识别的控制数据
    SkSpeechMapItem *speechMap;         // 指令映射表
    int32_t mapLen;                     // 指令映射表长度
    uint32_t silenteTime;               // 激活后静音时长
    float mnThd;                        // 更改的语音识别门限
    // SR通道和VC通道回调函数, 将指令和数据交给上层处理
    SkFuncSrCmdCallback cmdCallback;
    SkVcAfeDataCallback dataCallback;
    // 用于调试的统计数据
    uint32_t srAfeFeedCnt;
    uint32_t srAfeFetchCnt;
    uint32_t vcAfeFeedCnt;
    uint32_t vcAfeFetchCnt;
    uint32_t vcToCodecCnt;
    uint32_t silenceToCodecCnt;
} SkSrCtrl;

static SkSrCtrl g_skSrCtrl;

void SkSrSendSpeechData(SkSrCtrl *ctrl, int16_t *buff, int bufferSize, uint32_t dataMode);

void SkSrRegister(SkSpeechMapItem *map, size_t len, SkFuncSrCmdCallback func) {
    g_skSrCtrl.speechMap = map;
    g_skSrCtrl.mapLen = len;
    g_skSrCtrl.cmdCallback = func;
}

void SkSrInit(int32_t samplePerChunk, int32_t recChanNum, int32_t inputBytesPerSample, int32_t srChanNum) {
    int mu_chunksize;
    char *mn_name;
    SkSrCtrl *ctrl = &g_skSrCtrl;
    srmodel_list_t* models;
    esp_mn_iface_t* multinet;
    model_iface_data_t *model_data;

    models = SkSrGetModels();
    mn_name = esp_srmodel_filter(models, ESP_MN_PREFIX, ESP_MN_CHINESE);
    multinet = esp_mn_handle_from_name(mn_name);
    model_data = multinet->create(mn_name, 4000);
    esp_mn_commands_update_from_sdkconfig(multinet, model_data); // Add speech commands from sdkconfig
    mu_chunksize = multinet->get_samp_chunksize(model_data);
    assert(mu_chunksize == samplePerChunk);

    ctrl->vcEventGroup = xEventGroupCreate();
    ctrl->srEventGroup = xEventGroupCreate();
    ctrl->memSize = mu_chunksize * sizeof(uint16_t) * srChanNum;
    ctrl->bufferWritePos = 0;
    ctrl->vcReadPos = 0;
    ctrl->srReadPos = 0;
    ctrl->srBuffer = (int16_t *)malloc(ctrl->memSize);
    ESP_LOGI(TAG, "srBuffer:%p size:%d", ctrl->srBuffer, ctrl->memSize);
    memset(ctrl->srBuffer, 0x00, ctrl->memSize);

    ctrl->srChunkSize = samplePerChunk;
    ctrl->srAfeHandle = SkSrGetAfeHandle();
    ctrl->srAfeData = SkSrGetAfeData();
#ifdef CONFIG_VC_AFE_ENABLED
    ctrl->vcAfeHandle = SkVcGetAfeHandle();
    ctrl->vcAfeData = SkVcGetAfeData();
    ctrl->vcChunkSize = ctrl->vcAfeHandle->get_feed_chunksize(ctrl->vcAfeData);
#endif
    ctrl->multinet = multinet;
    ctrl->model_data = model_data;
    ESP_LOGI(TAG, "multinet:%s srAfeData:%p", mn_name, ctrl->srAfeData);
    ctrl->vcAfeFeedCnt = 0;
    ctrl->vcAfeFetchCnt = 0;
    ctrl->srAfeFeedCnt = 0;
    ctrl->srAfeFetchCnt = 0;
    ctrl->mnThd = 0.1;
    ctrl->runFlag = true;
    ctrl->mnRunStatus = false;
    ctrl->updateThdFlag = false;
    ctrl->cmdInputMode = false;
    ctrl->pmMode = false;
    ctrl->silenteTime = 0;
    SrInitCommands();

    return;
}

void SrInitCommands() {
    esp_err_t ret;
    SkSrCtrl *ctrl = &g_skSrCtrl;

    ret = esp_mn_commands_clear();
    ESP_LOGI(TAG, "Clear all default commands ret value:%d", ret);
    for (int i = 0; i < ctrl->mapLen; i++) {
        ret = esp_mn_commands_add(i + 1, ctrl->speechMap[i].command);
        if (ret != ESP_OK) {
            ESP_LOGI(TAG, "Add command %d %s error ret:%d", ctrl->speechMap[i].commandId, ctrl->speechMap[i].command, ret);
        }
    }
    esp_mn_commands_update();

    return;
}

void SkSrUninit() {
    SkSrCtrl *ctrl = &g_skSrCtrl;

    if (ctrl->model_data != NULL) {
        ctrl->multinet->destroy(ctrl->model_data);
    }
    ctrl->multinet = NULL;
    ctrl->model_data = NULL;
    ctrl->srAfeHandle = NULL;
    ctrl->srAfeData = NULL;
    return;
}

void SrWackeCheck(SkSrCtrl *ctrl, afe_fetch_result_t* res) {
    if (res->wakeup_state == WAKENET_DETECTED) {
        SkSmPostEvent(SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_START_VOICE_INPUT, 0, 0);
        ESP_LOGI(TAG, "WAKEWORD DETECTED");
        ctrl->multinet->clean(ctrl->model_data);
        ctrl->cmdInputMode = true;
        ctrl->silenteTime = 0;
        //if (ctrl->pmMode) {
            SkSmOnWakeup();
        //}
    } else if (res->wakeup_state == WAKENET_CHANNEL_VERIFIED) {
        ESP_LOGI(TAG, "AFE_FETCH_CHANNEL_VERIFIED, channel index: %d", res->trigger_channel_id);
    }
    return;
}

void SrProcessCmd(SkSrCtrl *ctrl, uint16_t cmd) {
    if (cmd == 0) {
        ESP_LOGI(TAG, "no command");
        return;
    }
    cmd--;
    if (cmd < ctrl->mapLen) {
        if (ctrl->cmdCallback != NULL) {
            ctrl->cmdCallback(ctrl->speechMap[cmd].commandId);
        }
        return;
    }

    ESP_LOGI(TAG, "command not found");
    return;
}

void SrDetectCmd(SkSrCtrl *ctrl, afe_fetch_result_t *res) {
    esp_mn_state_t mn_state;
    esp_mn_results_t *mn_result;

    mn_state = ctrl->multinet->detect(ctrl->model_data, res->data);
    if (mn_state == ESP_MN_STATE_DETECTING) {
        return;
    }

    // 完成命令检测, 或者检测失败, 切换为唤醒模式
    if (mn_state == ESP_MN_STATE_DETECTED) {
        mn_result = ctrl->multinet->get_results(ctrl->model_data);
        for (int i = 0; i < mn_result->num; i++) {
            ESP_LOGI(TAG, "TOP %d, command_id: %d, phrase_id: %d, string:%s prob: %f", 
                i+1, mn_result->command_id[i], mn_result->phrase_id[i], mn_result->string, mn_result->prob[i]);
        }
        SrProcessCmd(ctrl, mn_result->command_id[0]);
        ctrl->multinet->clean(ctrl->model_data);
        //SkOpusDecUnmuteRemote();
        return;
    }

    if (mn_state == ESP_MN_STATE_TIMEOUT) {
        ESP_LOGI(TAG, "SR timeout");
        ctrl->cmdInputMode = false;
        SkSmPostEvent(SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_END_VOICE_INPUT, 0, 0);
        return;
    }
}

void SkSrSendSpeechData(SkSrCtrl *ctrl, int16_t *buff, int bufferSize, uint32_t dataMode) {
    if (ctrl->dataCallback != NULL) {
        ctrl->dataCallback((uint16_t *)buff, bufferSize, dataMode, SkOsGetTickCnt());
    }
}

void SkSrSetSendFunc(SkVcAfeDataCallback func) {
    g_skSrCtrl.dataCallback = func;
}

void SkSrVadProc(SkSrCtrl *ctrl, afe_fetch_result_t *res) {
    if (!ctrl->cmdInputMode) {
        return;
    }
    if ((uint8_t)res->vad_state == (uint8_t)AFE_VAD_SPEECH) {
        ctrl->silenteTime = 0;
        return;
    }
    
    ctrl->silenteTime++;
    if (ctrl->silenteTime == 32) {
        SkSmOnUserAck();
        ESP_LOGI(TAG, "Detect silence after wakeup, action user by speech");
        return;
    }

    return;
}

void SkSrAfeProc(SkSrCtrl *ctrl) {
    afe_fetch_result_t* res;

    ctrl->srAfeFetchCnt++;
    res = ctrl->srAfeHandle->fetch(ctrl->srAfeData);
    if (!res || res->ret_value == ESP_FAIL) {
        ESP_LOGI(TAG, "fetch error");
        return;
    }
#if (CONFIG_PCM_DEBUG == CONFIG_PCM_DBG_POS_SR_AFE)
    SkDfxLinkSendAudio(MSG_DFX_AND_TERM_PCM1, (uint8_t *)res->data, res->data_size);
#endif
    SrWackeCheck(ctrl, res);
    if (ctrl->pmMode) {
        return;
    }
    SkSrVadProc(ctrl, res);
    if (ctrl->cmdInputMode) {
        if (!ctrl->mnRunStatus) {
            ESP_LOGI(TAG, "mn clear");
            ctrl->mnRunStatus = true;
            ctrl->multinet->clean(ctrl->model_data);
        }
        SrDetectCmd(ctrl, res);
    } else {
        ctrl->mnRunStatus = true;
    }
    return;
}

void SkVcAfeProc(SkSrCtrl *ctrl) {
    afe_fetch_result_t* res;
    uint16_t dataFlag;

    ctrl->vcAfeFetchCnt++;
    res = ctrl->vcAfeHandle->fetch(ctrl->vcAfeData);
    if (!res || res->ret_value == ESP_FAIL) {
        ESP_LOGI(TAG, "AudioProcess task fetch error");
        return;
    }
#if (CONFIG_PCM_DEBUG == CONFIG_PCM_DBG_POS_VC_AFE)
    SkDfxLinkSendAudio(MSG_DFX_AND_TERM_PCM1, (uint8_t *)res->data, res->data_size);
#endif
    dataFlag = 0;        
    if ((uint8_t)res->vad_state == (uint8_t)AFE_VAD_SPEECH) {
        dataFlag |= SK_ENC_DATA_FLAG_VAD;
        ctrl->vcToCodecCnt++;
    } else {
        ctrl->silenceToCodecCnt++;
    }
    SkSrSendSpeechData(ctrl, res->data, res->data_size, dataFlag);

    return;
}

void SkSrTask(void *arg) {
    SkSrCtrl *ctrl = &g_skSrCtrl;

    ESP_LOGI(TAG, "Speech recognize start");
    ctrl->cmdInputMode = false;
    while (ctrl->runFlag) {
        xEventGroupWaitBits(ctrl->srEventGroup, SK_AFE_ENABLE_BIT | SK_AFE_STOP_BIT, pdFALSE, pdFALSE, portMAX_DELAY);
        SkSrAfeProc(ctrl);
    }
    ESP_LOGI(TAG, "Speech recognize exit");

    vTaskDelete(NULL);
}

void SkSrVcTask(void *arg) {
    SkSrCtrl *ctrl = &g_skSrCtrl;

    ESP_LOGI(TAG, "AudioProcess task start");
    while (ctrl->runFlag) {
        xEventGroupWaitBits(ctrl->vcEventGroup, SK_AFE_ENABLE_BIT | SK_AFE_STOP_BIT, pdFALSE, pdFALSE, portMAX_DELAY);
        SkVcAfeProc(ctrl);
    }
    ESP_LOGI(TAG, "AudioProcess task exit");

    vTaskDelete(NULL);
}

void SkSrDataIn(int16_t *buffer, int sampleCnt) {
    int i, wPos, startPos;
    SkSrCtrl *ctrl = &g_skSrCtrl;

    startPos = ctrl->bufferWritePos;
    if (startPos + sampleCnt > ctrl->srChunkSize) {
        ESP_LOGE(TAG, "SkSrDataIn error %d %d", startPos, sampleCnt);
        return;
    }

    wPos = startPos;
    for (i = 0; i < sampleCnt; i++) {
#ifdef CONFIG_NONCODEC_DEV
        // 奇数位置为参考信号，初始化已经设置为0
        ctrl->srBuffer[2 * wPos] = buffer[i] << 5;
#else
        ctrl->srBuffer[2 * wPos] = buffer[2 * i] << 2;
        ctrl->srBuffer[2 * wPos + 1] = buffer[2 * i + 1];
#endif
        wPos++;
    }
    ctrl->bufferWritePos += sampleCnt;

#ifdef CONFIG_NONCODEC_DEV
#if (CONFIG_PCM_DEBUG == CONFIG_PCM_DBG_POS_REC)
    SkDfxLinkSendAudio(MSG_DFX_AND_TERM_PCM1, (uint8_t *)&ctrl->srBuffer[2 * startPos], sampleCnt * sizeof(int16_t) * 2);
#endif
#endif

    return;
}

#ifndef CONFIG_VC_AFE_ENABLED
void SkDataToEncode(uint16_t *buffer, int sampleCnt) {
    SkSrCtrl *ctrl = &g_skSrCtrl;
    int i;

    for (i = 0; i < sampleCnt; i++) {
#ifdef CONFIG_NONCODEC_DEV
        buffer[i] = buffer[i] << 5;
#else
        buffer[i] = buffer[2*i] << 2;
#endif
    }
    if (SkBspReadUserInput() == SK_KEY_PRESS_UP) {
#ifdef CONFIG_VC_DTX_CHECK
        if (SkOpusDecGetPlayState() == SK_OPUS_STATE_DTX) {
            // 解码静音, 正常编码发送
#endif
            SkSrSendSpeechData(ctrl, buffer, sampleCnt * sizeof(int16_t), SK_ENC_DATA_FLAG_VAD);
            ctrl->vcToCodecCnt++;
#ifdef CONFIG_VC_DTX_CHECK
        } else {
            // 解码播放时，本段先不进行编码发送，只能发送静音数据
            SkSrSendSpeechData(ctrl, buffer, sampleCnt * sizeof(int16_t), 0);
            ctrl->silenceToCodecCnt++;
        }
#endif
    }
}
#endif

void SkVcProcessEnable(bool enable) {
    SkSrCtrl *ctrl = &g_skSrCtrl;
    if (enable) {
        xEventGroupSetBits(ctrl->vcEventGroup, SK_AFE_ENABLE_BIT);
    } else {
        xEventGroupClearBits(ctrl->vcEventGroup, SK_AFE_ENABLE_BIT);
    }
    return;
}

void SkSrProcessEnable(bool enable) {
    SkSrCtrl *ctrl = &g_skSrCtrl;
    if (enable) {
        ESP_LOGI(TAG, "Enable sr.");
        xEventGroupSetBits(ctrl->srEventGroup, SK_AFE_ENABLE_BIT);
    } else {
        ESP_LOGI(TAG, "Disable sr.");
        xEventGroupClearBits(ctrl->srEventGroup, SK_AFE_ENABLE_BIT);
    }
    return;
}

static void SkVcAfeFeed(SkSrCtrl *ctrl) {
    size_t currCnt;
    
    if ((xEventGroupGetBits(ctrl->vcEventGroup) & SK_AFE_ENABLE_BIT) == 0) {
        return;
    }

    currCnt = ctrl->bufferWritePos - ctrl->vcReadPos;
    while (currCnt >= ctrl->vcChunkSize) {
        ctrl->vcAfeHandle->feed(ctrl->vcAfeData, &ctrl->srBuffer[ctrl->vcReadPos * 2]);
        ctrl->vcAfeFeedCnt++;
        ctrl->vcReadPos += ctrl->vcChunkSize;
        if (ctrl->vcReadPos >= ctrl->srChunkSize) {
            break;
        }
    }
    ctrl->vcReadPos = 0;
    return;
}

static void SkSrAfeFeed(SkSrCtrl *ctrl) {
    size_t currCnt;

    if ((xEventGroupGetBits(ctrl->srEventGroup) & SK_AFE_ENABLE_BIT) == 0) {
        ctrl->bufferWritePos = 0;
        return;
    }
    
    currCnt = ctrl->bufferWritePos - ctrl->srReadPos;
    if (currCnt >= ctrl->srChunkSize) {
        ctrl->srAfeHandle->feed(ctrl->srAfeData, ctrl->srBuffer);
        ctrl->srAfeFeedCnt++;
        ctrl->bufferWritePos = 0;
    }
    return;
}

#if (CONFIG_PCM_DEBUG == CONFIG_PCM_DBG_POS_REC_CH0)
void SkAudioDumpRecData(uint16_t *buffer, int sampleCnt, int channelNum) {
#ifdef CONFIG_VC_AFE_ENABLED
    int i;

    if (channelNum == 2) {
        for (i = 0; i < sampleCnt; i++) {
            buffer[i] = buffer[2*i] << 2;
        }
    }
#endif
    // 已经完成移位, 直接导出即可
    SkDfxLinkSendAudio(MSG_DFX_AND_TERM_PCM1, (uint8_t *)buffer, sampleCnt * sizeof(int16_t));
}
#endif

void SkAudioProcessFeed(int16_t *buffer, int sampleCnt, int channelNum) {
    SkSrCtrl *ctrl = &g_skSrCtrl;

#if (CONFIG_PCM_DEBUG == CONFIG_PCM_DBG_POS_REC)
    if (channelNum == 2) {
        SkDfxLinkSendAudio(MSG_DFX_AND_TERM_PCM2, (uint8_t *)buffer, sampleCnt * sizeof(int16_t) * channelNum);
    } else {
        SkDfxLinkSendAudio(MSG_DFX_AND_TERM_PCM1, (uint8_t *)buffer, sampleCnt * sizeof(int16_t) * channelNum);
    }
#endif

    SkSrDataIn(buffer, sampleCnt);
#ifdef CONFIG_VC_AFE_ENABLED
    SkVcAfeFeed(ctrl);
#endif
    SkSrAfeFeed(ctrl);

#ifndef CONFIG_VC_AFE_ENABLED
    SkDataToEncode(buffer, sampleCnt);
#endif

#if (CONFIG_PCM_DEBUG == CONFIG_PCM_DBG_POS_REC_CH0)
    SkAudioDumpRecData(buffer, sampleCnt, channelNum);
#endif

    return;
}

void SkSrShowStat() {
    SkSrCtrl *ctrl = &g_skSrCtrl;
    ESP_LOGI(TAG, "VCAFE: FeedCnt=%u, FetchCnt=%u, SilentCnt=%u, VcCnt=%u", 
        ctrl->vcAfeFeedCnt, ctrl->vcAfeFetchCnt, ctrl->silenceToCodecCnt, ctrl->vcToCodecCnt);
    ESP_LOGI(TAG, "SRAFE: FeedCnt=%u, FetchCnt=%u", ctrl->srAfeFeedCnt, ctrl->srAfeFetchCnt);
    
    return;
}

void SkSrSetPm(bool flag) {
    SkSrCtrl *ctrl = &g_skSrCtrl;

    ctrl->pmMode = flag;
#if CONFIG_SK_PM_SLEEP_MODE == CONFIG_SK_PM_SLEEP_MODE_LIGHT2
    ctrl->taskFlag = !flag;
    if (flag) {
        xEventGroupSetBits(ctrl->srEventGroup, SK_AFE_STOP_BIT);
        xEventGroupSetBits(ctrl->vcEventGroup, SK_AFE_STOP_BIT);
    } else {
        xEventGroupClearBits(ctrl->srEventGroup, SK_AFE_STOP_BIT);
        xEventGroupClearBits(ctrl->vcEventGroup, SK_AFE_STOP_BIT);
    }
#endif
    
    return;
}

void SkSrSetMnThd(uint8_t thd) {
    SkSrCtrl *ctrl = &g_skSrCtrl;

    ctrl->mnThd = thd * 0.01f;
    ESP_LOGI(TAG, "Set Mn Thd: %f", ctrl->mnThd);
    ctrl->updateThdFlag = true;

    return;
}