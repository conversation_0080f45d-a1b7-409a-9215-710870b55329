/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_recorder.c
 * @description: 录音模块，将声音从底层接收, 并交给AFE模块数据.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdbool.h>
#include <string.h>
#include "sk_common.h"
#include "sk_log.h"
#include "sk_audio.h"
#include "sk_board.h"
#include "sk_afe.h"

#define MAX_AUDIO_CNT 10
#define TAG "SkRecorder"

#define RECORDER_EVENT_RUN (1 << 0)

typedef struct {
    EventGroupHandle_t eventGroup;
    int32_t bytesPerSample;
    int32_t samplePerChunk;
    int32_t channelNum;
    bool pauseFlag;
    int keyInput;
    bool taskFlag;
    int bufferSize;
    int16_t *buff;
    uint8_t *debugBuf;
    int32_t debugBufSize;
    int32_t debugBufPos;
    int32_t audioIndex;
    int32_t audioPos;
    int32_t audioCnt;
    SkAudioSrcItem audioSrcList[MAX_AUDIO_CNT];
} SkRecorderCtrl;

SkRecorderCtrl g_recorderCtrl;

uint32_t SkRecorderInit(int32_t bytesPerSample, int32_t samplePerChunk, int32_t channelNum) {
    SkRecorderCtrl *ctrl = &g_recorderCtrl;
    int bufferSize = samplePerChunk * bytesPerSample * channelNum;
    int16_t *buff = NULL;

    ESP_LOGI(TAG, "samplePerChunk:%d, bufferSize:%d", samplePerChunk, bufferSize);
    buff = malloc(bufferSize);
    if (buff == NULL) {
        ESP_LOGI(TAG, "malloc buff failed");
        return SK_RET_FAIL;
    }
    ESP_LOGI(TAG, "Recorder buffer %p size %d", buff, bufferSize);
    ctrl->bufferSize = bufferSize;
    ctrl->buff = buff;
    ctrl->taskFlag = true;
    ctrl->samplePerChunk = samplePerChunk;
    ctrl->bytesPerSample = bytesPerSample;
    ctrl->channelNum = channelNum;
    ctrl->eventGroup = xEventGroupCreate();

    return SK_RET_SUCCESS;
}

void SkRecorderStop() {
    SkRecorderCtrl *ctrl = &g_recorderCtrl;
    ctrl->taskFlag = false;
    if (ctrl->eventGroup != NULL) {
        xEventGroupSetBits(ctrl->eventGroup, RECORDER_EVENT_RUN);
    }
}

void SkRecorderPause() {
    if (g_recorderCtrl.eventGroup != NULL) {
        xEventGroupClearBits(g_recorderCtrl.eventGroup, RECORDER_EVENT_RUN);
    }
}

void SkRecorderResume() {
    if (g_recorderCtrl.eventGroup != NULL) {
        xEventGroupSetBits(g_recorderCtrl.eventGroup, RECORDER_EVENT_RUN);
    }
}

void SkRecorderDeInit() {
    SkRecorderCtrl *ctrl = &g_recorderCtrl;

    if (ctrl->buff) {
        free(ctrl->buff);
    }
    ctrl->buff = NULL;

    return;
}

void SkRecorderTask(void *arg)
{
    SkRecorderCtrl *ctrl = &g_recorderCtrl;

    ESP_LOGI(TAG, "Task start");
    while (ctrl->taskFlag) {
        xEventGroupWaitBits(ctrl->eventGroup, RECORDER_EVENT_RUN,
            pdFALSE, pdFALSE, portMAX_DELAY);
        SkBspReadAudio(ctrl->buff, ctrl->bufferSize);
        SkAudioProcessFeed(ctrl->buff, ctrl->samplePerChunk, ctrl->channelNum);                
    }
    ESP_LOGI(TAG, "Task exit");

    vTaskDelete(NULL);
}

void SkRecorderSetPm(bool flag) {
    SkRecorderCtrl *ctrl = &g_recorderCtrl;

    ctrl->pauseFlag = flag;
#if CONFIG_SK_PM_SLEEP_MODE == CONFIG_SK_PM_SLEEP_MODE_LIGHT2
    ctrl->taskFlag = !flag;
#endif

    return;
}
