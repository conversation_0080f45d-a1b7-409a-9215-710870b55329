/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_afe.c
 * @description: 音频前端管理.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdbool.h>
#include "esp_process_sdkconfig.h"
#include "esp_log.h"
#include "sk_audio.h"
#include "esp_wn_iface.h"
#include "esp_wn_models.h"
#include "esp_afe_config.h"
#include "sk_common.h"
#include "sk_afe.h"
#include "sk_os.h"
#include "sk_board.h"

#define TAG "SkAudioCfg"

static esp_afe_sr_iface_t *g_afe_handle = NULL;
static esp_afe_sr_data_t *g_afe_data = NULL;
srmodel_list_t *g_models = NULL;
static esp_afe_sr_iface_t *g_afeVC = NULL;
static esp_afe_sr_data_t *g_afeDataVC = NULL;

TaskHandle_t g_playTaskHandle;
TaskHandle_t g_recordTaskHandle;
TaskHandle_t g_srTaskHandle;
TaskHandle_t g_srVcTaskHandle;

esp_afe_sr_iface_t* SkSrGetAfeHandle() {
    return g_afe_handle;
}

esp_afe_sr_data_t* SkSrGetAfeData() {
    return g_afe_data;
}

esp_afe_sr_iface_t* SkVcGetAfeHandle() {
    return g_afeVC;
}

esp_afe_sr_data_t* SkVcGetAfeData() {
    return g_afeDataVC;
}

srmodel_list_t* SkSrGetModels() {
    return g_models;
}

int32_t SkAudioGetChunkSize() {
    return g_afe_handle->get_fetch_chunksize(g_afe_data);
}
#define ESP_SR_V2 1

int32_t SkAudioInitSrAfe() {
    int32_t chunkSize;
    int32_t fetchSize;
#if ESP_SR_V2
    afe_config_t* afeConfig = afe_config_init("MR", g_models, AFE_TYPE_SR, AFE_MODE_HIGH_PERF);
    afeConfig->memory_alloc_mode = AFE_MEMORY_ALLOC_MORE_PSRAM;
    afeConfig->afe_mode = AFE_MODE_HIGH_PERF;
    afeConfig->aec_mode = AEC_MODE_SR_HIGH_PERF;
    afeConfig->afe_ringbuf_size = 50;
    afeConfig->fixed_first_channel = true;
    afeConfig->aec_init = true;
    afeConfig->vad_init = false;
    //afe_config_print(afeConfig);
    g_afe_handle = esp_afe_handle_from_config(afeConfig);
    g_afe_data = g_afe_handle->create_from_config(afeConfig);
    chunkSize = g_afe_handle->get_feed_chunksize(g_afe_data);
    fetchSize = g_afe_handle->get_fetch_chunksize(g_afe_data);
#else
    g_afe_handle = (esp_afe_sr_iface_t *)&ESP_AFE_SR_HANDLE;
    afe_config_t afeConfig = AFE_CONFIG_DEFAULT();
    afeConfig.wakenet_model_name = esp_srmodel_filter(g_models, ESP_WN_PREFIX, NULL);
    afeConfig.pcm_config.total_ch_num = 2;
    afeConfig.pcm_config.mic_num = 1;
    afeConfig.pcm_config.ref_num = 1;
    afeConfig.wakenet_mode = DET_MODE_90;
    g_afe_data = g_afe_handle->create_from_config(&afeConfig);
    chunkSize = g_afe_handle->get_feed_chunksize(g_afe_data);
    fetchSize = g_afe_handle->get_fetch_chunksize(g_afe_data);
#endif
    ESP_LOGI(TAG, "SR AFE chunk size: %d, fetch size: %d", chunkSize, fetchSize);

    return chunkSize;
}

int32_t SkAudioInitVoiceAfe() {
    int32_t chunkSize;
    int32_t fetchSize;
    
#if ESP_SR_V2
    afe_config_t* afeConfig = afe_config_init("MR", NULL, AFE_TYPE_VC, AFE_MODE_HIGH_PERF);
    afeConfig->memory_alloc_mode = AFE_MEMORY_ALLOC_MORE_PSRAM;
    afeConfig->aec_mode = AEC_MODE_VOIP_HIGH_PERF;
    afeConfig->afe_ringbuf_size = 50;
    afeConfig->fixed_first_channel = true;
    afeConfig->vad_mode = VAD_MODE_3;
    afeConfig->afe_perferred_priority = 1;
    afeConfig->agc_init = false;
    afeConfig->agc_mode = AFE_AGC_MODE_WEBRTC;
    //afe_config_print(afeConfig);
    g_afeVC = esp_afe_handle_from_config(afeConfig);
    g_afeDataVC = g_afeVC->create_from_config(afeConfig);
    chunkSize = g_afeVC->get_feed_chunksize(g_afeDataVC);
    fetchSize = g_afeVC->get_fetch_chunksize(g_afeDataVC);
#else
    g_afeVC = (esp_afe_sr_iface_t *)&ESP_AFE_VC_HANDLE;
    afe_config_t afeConfig = AFE_CONFIG_DEFAULT();
    afeConfig.wakenet_init = false;
    afeConfig.voice_communication_init = true;
    afeConfig.voice_communication_agc_init = true;
    afeConfig.voice_communication_agc_gain = 10;
    afeConfig.pcm_config.total_ch_num = 2;
    afeConfig.pcm_config.mic_num = 1;
    afeConfig.pcm_config.ref_num = 1;
    afeConfig.afe_mode = SR_MODE_LOW_COST;
#if 1
    afeConfig.vad_init = true;
    afeConfig.aec_init = true;
    afeConfig.se_init = true;
    afeConfig.voice_communication_agc_init = true;
#endif
    g_afeDataVC = g_afeVC->create_from_config(&afeConfig);
    chunkSize = g_afeVC->get_feed_chunksize(g_afeDataVC);
    fetchSize = g_afeVC->get_fetch_chunksize(g_afeDataVC);
#endif
    ESP_LOGI(TAG, "VC AFE %p chunk size: %d, fetch size: %d",
        g_afeDataVC, chunkSize, fetchSize);

    return chunkSize;
}

void SkAudioStartTasks() {
    // 创建录音任务并绑定到CPU核心0
    xTaskCreatePinnedToCore(SkRecorderTask, "SkRecorderTask", 8192, (void*)g_afe_data, 5, &g_recordTaskHandle, 1);
    ESP_LOGI("Audio-Rec", "stack base %p", pxTaskGetStackStart(g_recordTaskHandle));
    SK_OS_MODULE_MEM_STAT("SkRecorderTask", true);
    // 创建检测任务并绑定到CPU核心1
    xTaskCreatePinnedToCore(&SkSrTask, "SkSrTask", 8192, (void*)g_afe_data, 5, &g_srTaskHandle, 1);
    ESP_LOGI("Audio-Sr", "stack base %p", pxTaskGetStackStart(g_srTaskHandle));
    SK_OS_MODULE_MEM_STAT("SkSrTask", true);
#ifdef CONFIG_VC_AFE_ENABLED
    xTaskCreatePinnedToCore(&SkSrVcTask, "SkSrVcTask", 8192, (void*)g_afeDataVC, 5, &g_srVcTaskHandle, 0);
    ESP_LOGI("Audio-Vc", "stack base %p", pxTaskGetStackStart(g_srVcTaskHandle));
    SK_OS_MODULE_MEM_STAT("SkVcTask", true);
#endif
    // 创建播放任务
    xTaskCreate(SkPlayerTask, "SkPlayerTask", 4096, NULL, 5, &g_playTaskHandle);
    ESP_LOGI("Audio-Play", "stack base %p", pxTaskGetStackStart(g_playTaskHandle));
    SK_OS_MODULE_MEM_STAT("SkPlayerTask", true);
    return;
}

void SkAudioInit(int32_t bytesPerSample, int32_t playerSamplePerChunk)
{
    int32_t chunkSize;
#ifdef CONFIG_NONCODEC_DEV
    int32_t recChannelNum = 1;
#else
    int32_t chunkSizeVc;
    int32_t recChannelNum = 2;
#endif
    int32_t afeChannelNum = 2;

    // 初始化语音识别模型
    SK_OS_MODULE_MEM_STAT("Sr", false);
    g_models = esp_srmodel_init("model");
    SK_OS_MODULE_MEM_STAT("Sr", true);
    chunkSize = SkAudioInitSrAfe();
    SK_OS_MODULE_MEM_STAT("AFE-SR", true);
#ifdef CONFIG_VC_AFE_ENABLED
    chunkSizeVc = SkAudioInitVoiceAfe();
    ESP_LOGI(TAG, "Audio SRChunkSize %d, VcChunkSize %d", chunkSize, chunkSizeVc);
    SK_OS_MODULE_MEM_STAT("AFE-VC", true);
#endif
    SkSrInit(chunkSize, recChannelNum, bytesPerSample, afeChannelNum);
    SK_OS_MODULE_MEM_STAT("SkSr", true);
    SkPlayerInit(bytesPerSample, playerSamplePerChunk);
    SK_OS_MODULE_MEM_STAT("SkPlayer", true);
    SkRecorderInit(bytesPerSample, chunkSize, recChannelNum);
    SK_OS_MODULE_MEM_STAT("SkRecorder", true);

    SkAudioStartTasks();
}
