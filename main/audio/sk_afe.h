/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_afe.h
 * @description: AFE模块内部接口
 * @author: <PERSON>
 * @date: 2025-06-30
 */

#ifndef SK_AFE_H
#define SK_AFE_H

#include "esp_afe_sr_iface.h"
#include "esp_afe_sr_models.h"
#include "model_path.h"

#ifdef __cplusplus
extern "C" {
#endif

esp_afe_sr_iface_t* SkSrGetAfeHandle();
esp_afe_sr_data_t* SkSrGetAfeData();
esp_afe_sr_iface_t* SkVcGetAfeHandle();
esp_afe_sr_data_t* SkVcGetAfeData();

srmodel_list_t* SkSrGetModels();
void SkSrTask(void *arg);
void SkSrVcTask(void *arg);
void SkPlayerTask(void *args);
void SkRecorderTask(void *arg);

#ifdef __cplusplus
}
#endif

#endif