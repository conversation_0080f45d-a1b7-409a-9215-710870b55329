/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_mqtt.c
 * @description: 通过ESP-MQTT-Client实现MQTT协议
 * @author: <PERSON>
 * @date: 2025-07-16
 */
#include <mqtt_client.h>
#include <esp_crt_bundle.h>
#include "sk_log.h"
#include "sk_mqtt.h"

static const char *TAG = "SkMqtt";

typedef struct {
    bool connected;
    esp_mqtt_client_handle_t client;

    SkMqttDataCb dataCb;
    void *dataCbArg;

    SkMqttEventCb eventCb;
    uint32_t eventCbMask;
    void *eventCbArg;
} SkMqttClient_t;

void SkMqttEventCallback(void *arg, esp_event_base_t base, int32_t eventId, void *eventData);

SkMqttClient_t g_mqttClient;

void SkMqttClientInit() {
    SkMqttClient_t *mqttClient = &g_mqttClient;

    if (mqttClient->client != NULL) {
        return;
    }
    mqttClient->connected = false;
    mqttClient->client = NULL;

    mqttClient->dataCb = NULL;
    mqttClient->dataCbArg = NULL;
    
    mqttClient->eventCb = NULL;
    mqttClient->eventCbMask = 0;
    mqttClient->eventCbArg = NULL;

    return;
}

void SkMqttClientDeinit() {
    SkMqttDisconnect();
}

void SkMqttRegEventCb(uint32_t eventMask, SkMqttEventCb cb, void *arg) {
    SkMqttClient_t *mqttClient = &g_mqttClient;

    mqttClient->eventCb = cb;
    mqttClient->eventCbArg = arg;
    mqttClient->eventCbMask = eventMask;

    return;
}

void SkMqttRegDataCb(SkMqttDataCb cb, void *arg) {
    SkMqttClient_t *mqttClient = &g_mqttClient;
    mqttClient->dataCb = cb;
    mqttClient->dataCbArg = arg;
    return;
}

void SkMqttConnect(const char* brokerAddr, int brokerPort, const char* clientId, const char* userName, const char* password) {
    SkMqttClient_t *mqttClient = &g_mqttClient;

    if (mqttClient->client != NULL) {
        SkMqttDisconnect();
    }

    esp_mqtt_client_config_t config = {};
    config.broker.address.hostname = brokerAddr;
    config.broker.address.port = brokerPort;
    if (brokerPort == 8883) {
        config.broker.address.transport = MQTT_TRANSPORT_OVER_SSL;
        config.broker.verification.crt_bundle_attach = esp_crt_bundle_attach;
    } else {
        config.broker.address.transport = MQTT_TRANSPORT_OVER_TCP;
    }
    config.credentials.client_id = clientId;
    config.credentials.username = userName;
    config.credentials.authentication.password = password;
    config.session.keepalive = 60;

    mqttClient->client = esp_mqtt_client_init(&config);
    esp_mqtt_client_register_event(mqttClient->client, MQTT_EVENT_ANY, SkMqttEventCallback, mqttClient);
    esp_mqtt_client_start(mqttClient->client);

    return;
}

void SkMqttDisconnect() {
    SkMqttClient_t *mqttClient = &g_mqttClient;

    mqttClient->connected = false;
    if (mqttClient->client != NULL) {
        esp_mqtt_client_stop(mqttClient->client);
        esp_mqtt_client_destroy(mqttClient->client);
    }
    mqttClient->client = NULL;
    return;
}

void SkMqttGetTopic(char *topic, uint32_t topicLen, SkMqttDataInfo *dataInfo) {
    memcpy(topic, dataInfo->topic, SK_MIN(topicLen, dataInfo->topicLen));
    topic[SK_MIN(topicLen, dataInfo->topicLen)] = '\0';
    return;
}

void SkMqttEventCallback(void *arg, esp_event_base_t base, int32_t eventId, void *eventData) {
    uint32_t eventInner = SK_MQTT_EVENT_NULL;
    uint32_t eventBit;
    SkMqttClient_t *mqttClient = arg;
    esp_mqtt_event_t *event = (esp_mqtt_event_t*)eventData;

    switch (eventId) {
        case MQTT_EVENT_CONNECTED:
            mqttClient->connected = true;
            eventInner = SK_MQTT_EVENT_CONNECT;
            SK_LOGI(TAG, "MQTT connected");
            break;
        case MQTT_EVENT_DISCONNECTED:
            mqttClient->connected = false;
            eventInner = SK_MQTT_EVENT_DISCONNECT;
            SK_LOGI(TAG, "MQTT disconnected");
            break;
        case MQTT_EVENT_DATA: 
            if (mqttClient->dataCb != NULL) {    
                SkMqttDataInfo dataInfo;

                dataInfo.topic = event->topic;
                dataInfo.topicLen = event->topic_len;
                dataInfo.data = (uint8_t*)event->data;
                dataInfo.dataLen = event->data_len;
                dataInfo.dataTotalLen = event->total_data_len;
                mqttClient->dataCb(mqttClient->dataCbArg, &dataInfo);
            }
            SK_LOGI(TAG, "MQTT data received: topic_len=%d, payloadLen=%d callback %p",
                event->topic_len, event->data_len, mqttClient->dataCb);
            break;
        case MQTT_EVENT_BEFORE_CONNECT:
            SK_LOGI(TAG, "MQTT before connect");
            break;
        case MQTT_EVENT_SUBSCRIBED:
            SK_LOGI(TAG, "MQTT subscribed");
            break;
        case MQTT_EVENT_ERROR:
            eventInner = SK_MQTT_EVENT_ERROR;
            ESP_LOGI(TAG, "MQTT error occurred: %s", esp_err_to_name(event->error_handle->esp_tls_last_esp_err));
            break;
        default:
            ESP_LOGI(TAG, "Unhandled event id %ld", eventId);
            break;
    }

    eventBit = SK_MQTT_EVENT_MASK(eventInner);
    if (mqttClient->eventCb != NULL && (eventBit & mqttClient->eventCbMask)) {
        mqttClient->eventCb(mqttClient->eventCbArg, eventInner);
    }
    return;
}

bool SkMqttPublish(const char *topic, const char *payload, uint32_t payloadLen, int qos) {
    int32_t ret;
    SkMqttClient_t *mqttClient = &g_mqttClient;

    if (!mqttClient->connected) {
        return false;
    }
    ret = esp_mqtt_client_publish(mqttClient->client, topic, payload, payloadLen, qos, 0);

    return  (ret == 0) ? true : false;
}

bool SkMqttSubscribe(const char *topic, int qos) {
    int32_t ret;
    SkMqttClient_t *mqttClient = &g_mqttClient;

    if (!mqttClient->connected) {
        return false;
    }
    ret = esp_mqtt_client_subscribe_single(mqttClient->client, topic, qos);

    return  (ret == 0) ? true : false;
}

bool SkMqttUnsubscribe(const char *topic) {
    int32_t ret;
    SkMqttClient_t *mqttClient = &g_mqttClient;

    if (!mqttClient->connected) {
        return false;
    }
    ret = esp_mqtt_client_unsubscribe(mqttClient->client, topic);

    return  (ret == 0) ? true : false;
}

bool SkMqttIsConnected() {
    return g_mqttClient.connected;
}
