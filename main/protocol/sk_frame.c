/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: frame.c
 * @description: 单个的组帧和解帧.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <string.h>
#include <arpa/inet.h>
#include "sk_common.h"
#include "sk_config.h"
#include "sk_frame.h"

uint8_t g_chatID[16];
uint8_t g_termID[6] = {'T', '0', '0', '0', '0', '1'};
uint8_t g_roleID[6] = {'T', '0', '0', '0', '0', '1'};
uint8_t g_agentRoleID[2][6] = {
    {'A', 'I', '-', '0', '0', '0'}, // 智能体角色-小博士
    {'A', 'I', '-', '0', '0', '1'}, // 智能体角色-本体角色
};

uint8_t g_hwVersion = 1;
uint8_t g_swVersion = 1;
uint8_t g_protocolVersion = 1;
uint32_t g_sessionID = 0;

void EncodeFrameHead(FrameHead *frameHead, uint16_t frameType, uint16_t msgType, uint16_t payloadLen) {
    frameHead->headFlag = htons(FRAME_HEAD_FLAG);
    frameHead->frameType = frameType;
    frameHead->msgType = msgType;
    frameHead->headLen = htons(sizeof(FrameHead)); // 暂时不使用校验；
    frameHead->payloadLen = htons(payloadLen);
    frameHead->seqID = htons(0); // 暂时不使用序号；
    frameHead->resv[0] = 0;
    frameHead->resv[1] = 0;
    frameHead->resv[2] = 0;
    frameHead->resv[3] = 0;
    frameHead->crc = htons(0); // 暂时不使用校验；
}

void EncodeSessionHead(SessionHead *head, uint32_t sessionType) {
    head->sessionID = htonl(g_sessionID);
    head->type = sessionType;
    head->version = g_protocolVersion;
    head->headLen = sizeof(SessionHead);
    head->msgIndexInSession = 0; // 暂时不使用序号；
}

void GenCtrlMsgBasicInfo(CtrlMsg *ctrlMsg) {
    ctrlMsg->msgLen = sizeof(CtrlMsg);
    SkConfigGetSelfTermId(ctrlMsg->callerTerminalID, sizeof(ctrlMsg->callerTerminalID));
    SkConfigGetSelfTermId(ctrlMsg->callerRoleID, sizeof(ctrlMsg->callerRoleID));
    memcpy(ctrlMsg->chatID, g_chatID, 16);
    ctrlMsg->callerHwVersion = g_hwVersion;
    ctrlMsg->callerSwVersion = g_swVersion;
}

void EncodeRegisterReqMsg(CtrlMsgFrame *frame) {
    memset(frame, 0, sizeof(CtrlMsgFrame));
    GenCtrlMsgBasicInfo(&frame->ctrlMsg);
    frame->ctrlMsg.msgType = MSG_TERM_TO_CTRL_REGISTER_REQUEST;
    EncodeSessionHead(&frame->sessionHead, SESSION_TYPE_REGISTER);
    EncodeFrameHead(&frame->frameHead, FRAME_CMSG_TERM_TO_CTRL, MSG_TERM_TO_CTRL_REGISTER_REQUEST,
        sizeof(CtrlMsgFrame) - sizeof(FrameHead));
}

void EncodeStateReportMsg(CtrlMsgFrame *frame, uint8_t msgType, uint8_t chatId[16]) {
    memset(frame, 0, sizeof(CtrlMsgFrame));
    GenCtrlMsgBasicInfo(&frame->ctrlMsg);
    frame->ctrlMsg.msgType = msgType;
    memcpy(frame->ctrlMsg.chatID, chatId, 16);
    EncodeSessionHead(&frame->sessionHead, SESSION_TYPE_REGISTER);
    EncodeFrameHead(&frame->frameHead, FRAME_CMSG_TERM_TO_CTRL, msgType,
        sizeof(CtrlMsgFrame) - sizeof(FrameHead));
}

void EncodeCallRequestMsg(CtrlMsgFrame *frame, uint8_t calleeRole) {
    memset(frame, 0, sizeof(CtrlMsgFrame));
    GenCtrlMsgBasicInfo(&frame->ctrlMsg);
    frame->ctrlMsg.msgType = MSG_TERM_TO_CTRL_CALLER_REQUEST;
    memcpy(frame->ctrlMsg.calleeRoleID, g_agentRoleID[calleeRole], 6);
    EncodeSessionHead(&frame->sessionHead, SESSION_TYPE_CALLER_REQUEST);
    EncodeFrameHead(&frame->frameHead, FRAME_CMSG_TERM_TO_CTRL, MSG_TERM_TO_CTRL_CALLER_REQUEST,
        sizeof(CtrlMsgFrame) - sizeof(FrameHead));
}

void EncodeRelayReqMsg(CtrlMsgFrame *frame, uint8_t msgType) {
    frame->ctrlMsg.msgType = msgType;
    EncodeSessionHead(&frame->sessionHead, SESSION_TYPE_RELAY_REQUEST);
    EncodeFrameHead(&frame->frameHead, FRAME_CMSG_TERM_TO_RELAY, msgType,
        sizeof(CtrlMsgFrame) - sizeof(FrameHead));
}

void EncodeTermOnhookOffhookMsg(CtrlMsgFrame *frame, uint8_t msgType) {
    memset(frame, 0, sizeof(CtrlMsgFrame));
    GenCtrlMsgBasicInfo(&frame->ctrlMsg);
    frame->ctrlMsg.msgType = msgType;
    EncodeSessionHead(&frame->sessionHead, SESSION_TYPE_RELAY_REQUEST);
    EncodeFrameHead(&frame->frameHead, FRAME_CMSG_TERM_AND_TERM, msgType,
        sizeof(CtrlMsgFrame) - sizeof(FrameHead));
}

void EncodeRelayDataMsg(DataFrame *frame, uint16_t payloadLen) {
    EncodeFrameHead(&frame->frameHead, FRAME_DPKT_TERM_AND_RELAY, MSG_TYPE_INVALID, payloadLen);
}

void EncodeRelayAlignAudioMsg(CtrlMsgFrame *frame) {
    memset(frame, 0, sizeof(CtrlMsgFrame));
    GenCtrlMsgBasicInfo(&frame->ctrlMsg);
    frame->ctrlMsg.msgType = MSG_TERM_TO_TERM_ALIGN_AUDIO;
    EncodeSessionHead(&frame->sessionHead, SESSION_TYPE_RELAY_REQUEST);
    EncodeFrameHead(&frame->frameHead, FRAME_CMSG_TERM_AND_TERM, MSG_TERM_TO_TERM_ALIGN_AUDIO, 
        sizeof(CtrlMsgFrame) - sizeof(FrameHead));
}

void EncodeTermToCloudAiMsg(CtrlFrameCalleeMatch *frame, uint8_t msgType) {
    CtrlMsgCalleeMatch *ctrlMsg = &frame->ctrlMsg;

    memset(frame, 0, sizeof(CtrlFrameCalleeMatch));
    ctrlMsg->msgLen = sizeof(CtrlMsgCalleeMatch);
    ctrlMsg->msgType = msgType;
    ctrlMsg->callerHwVersion = g_hwVersion;
    ctrlMsg->callerSwVersion = g_swVersion;
    SkConfigGetSelfTermId(ctrlMsg->callerTerminalID, sizeof(ctrlMsg->callerTerminalID));
    SkConfigGetSelfTermId(ctrlMsg->callerRoleID, sizeof(ctrlMsg->callerRoleID));
    SkConfigGetCalleeList(ctrlMsg->calleeNameList, sizeof(ctrlMsg->calleeNameList));
    EncodeSessionHead(&frame->sessionHead, SESSION_TYPE_CALLEE_MATCH);
    EncodeFrameHead(&frame->frameHead, FRAME_CMSG_AGENT_AND_TERM, msgType,
        sizeof(CtrlFrameCalleeMatch) - sizeof(FrameHead));

    return;
}