/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sm_config.c
 * @description: 配置状态机, 负责和配置用的微信小程序进行通信.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <string.h>
#include "sk_common.h"
#include "sk_sm.h"
#include "sk_board.h"
#include "sk_audio.h"
#include "sk_opus_dec.h"
#include "sk_opus_enc.h"
#include "sk_config.h"
#include "sk_wifi.h"
#include "sm.h"
#include "sk_clink.h"

#define TAG "SkSmConfig"

typedef struct {
    SkStateHandler handler;
    SkSmStateEndCallback endCb;
} SmConfigCtrl;

SmConfigCtrl g_smConfigCtrl;

void SmConfigStartServer() {
    uint8_t audioList[2];

    SkConfigStartServer();
    audioList[0] = AUDIO_IDX_CONFIGSTART;
    SkSmPlayLocalNotice(audioList, 1, true);

    return;
}

void SmCallCmdCallback(int32_t commandId) {
    SkSmSendEvent(g_smConfigCtrl.handler, SM_EVENT_CMD, SPEECH_CMD_EVENT_CALL, commandId, 0);
    return;
}

void SmConfigInnerStop() {
    uint8_t audioList[16];

    SkConfigStopServer();
    SkSmSetReboot();
    if (SkSmGetNetState() != NETWORK_STATE_STA_CONNECTED) {
        audioList[0] = AUDIO_IDX_CONFIGEND;
        audioList[1] = AUDIO_IDX_REBOOT;
        SkSmPlayLocalNotice(audioList, 2, true);
    } else {
        audioList[0] = AUDIO_IDX_CONFIGEND;
        SkSmPlayLocalNotice(audioList, 1, true);
    }
}

int32_t SmConfigStart(SkSubStateInfo *info, const SkSmEvent *event) {
    int32_t netState = SkSmGetNetState();
    SkRledSetEvent(SK_LED_EVENT_CONFIG);
    SkClinkSetFunFlag(CLINK_RUN_FLAG_IDLE);
    SkSrSetSendFunc(NULL);
    SkOpusEncStopTask();
    vTaskDelay(pdMS_TO_TICKS(100));
    if (netState != NETWORK_STATE_STA_STOP) {
        SkWifiStop();
    }
    SkWifiStartAp();
    return SK_RET_SUCCESS;
}

int32_t SmConfigStop(SkSubStateInfo *info) {
    SmConfigInnerStop();
    return SK_RET_SUCCESS;
}

int32_t SmConfigEventProc(SkSubStateInfo *info, const SkSmEvent *event) {
    if (event->event == SM_EVENT_CMD) {
        if (event->subEvent == SPEECH_CMD_EVENT_QUIT) {
            SmConfigInnerStop();
            if (g_smConfigCtrl.endCb != NULL) {
                g_smConfigCtrl.endCb(g_smConfigCtrl.handler, STATE_CONFIG);
            }
        }
    }
    return SK_RET_SUCCESS;
}

int32_t SkSmConfigInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler) {
    item->info.subState = 0;
    item->info.privateData = NULL;
    item->startProc = SmConfigStart;
    item->stopProc = SmConfigStop;
    item->eventProc = SmConfigEventProc;
    g_smConfigCtrl.endCb = endCb;
    g_smConfigCtrl.handler = handler;
    return SK_RET_SUCCESS;
}
