/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sm_call.c
 * @description: 聊天的子状态机, 负责主菜单/AI聊天/AI分布式聊天/好友呼叫业务.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include "sk_common.h"
#include "sk_log.h"
#include "sk_board.h"
#include "sk_os.h"
#include "sk_audio.h"
#include "sk_clink.h"
#include "sk_rlink.h"
#include "sk_opus_dec.h"
#include "sk_opus_enc.h"
#include "sm.h"

#define TAG "SmCall"

enum {
    SM_CALL_STATE_IDLE,
    SM_CALL_STATE_WAIT_START,
    SM_CALL_STATE_WAIT_CONTROLLER,
    SM_CALL_STATE_WAIT_AGENT,
    SM_CALL_STATE_CALLING,
};

enum {
    SM_SESSION_MODE_AGENT,
    SM_SESSION_MODE_HUMAN,
    SM_SESSION_MODE_SYS,
};

typedef struct {
    int16_t currSessionId;  // 内部的SessionID, Clink， Rlink可见.
    int32_t tickCnt;
    int32_t state;
    int32_t role;
    int32_t sessionMode;
    int32_t matchResultValid;
    int32_t matchResult;
    int32_t audioEnd;
    bool waitLocalPlayEnd;
    SkStateHandler handler;
    SkSmStateEndCallback endCb;
} SmChatCtrl;

typedef int32_t (*SmCallEventFunc)(SmChatCtrl *ctrl, const SkSmEvent *event);

int32_t SmCallInitParam(SmChatCtrl *ctrl, const SkSmEvent *event);
int32_t SmCallStartLink(SmChatCtrl *ctrl);
void SmCallInitModules(SmChatCtrl *ctrl);

SmChatCtrl g_callSmCtrl;

static inline void SmCallSetState(SmChatCtrl *ctrl, int32_t state) {
    ctrl->state = state;
}

int32_t SmCallStart(SkSubStateInfo *info, const SkSmEvent *event) {
    int32_t nextState;
    SmChatCtrl *ctrl = (SmChatCtrl *)info->privateData;

    if (SmCallInitParam(ctrl, event) != SK_RET_SUCCESS) {
        return SK_RET_FAIL;
    }
    if (SkSmGetRlinkState(ctrl->handler) == SM_RLINK_STATE_DISCONNECT) {
        SmCallInitModules(ctrl);
        nextState = SmCallStartLink(ctrl);
    } else {
        nextState = SM_CALL_STATE_WAIT_START;
    }
    SmCallSetState(ctrl, nextState);
    return SK_RET_SUCCESS;
}

int32_t SmCallInitParam(SmChatCtrl *ctrl, const SkSmEvent *event) {
    ctrl->currSessionId = SkSmNewSession(ctrl->handler);
    ctrl->tickCnt = 0;
    ctrl->matchResultValid = 0;
    ctrl->matchResult = 0;
    ctrl->audioEnd = 0;
    ctrl->waitLocalPlayEnd = false;
    if (event->event == SM_EVENT_LINK) {
        if (event->subEvent == SM_EVENT_CLINK_CALL_REQUEST) {
            ctrl->role = SK_ROLE_MODE_CALLEE;
            ctrl->sessionMode = SM_SESSION_MODE_HUMAN;
        }
    } else if (event->event == SM_EVENT_CMD) {
        if (event->subEvent == SPEECH_CMD_EVENT_CALL_CALLEE) {
            SkRledSetEvent(SK_LED_EVENT_CALL_OUTGOING);
            ctrl->role = SK_ROLE_MODE_CALLER;
            ctrl->sessionMode = SM_SESSION_MODE_HUMAN;
        } else if (event->subEvent == SPEECH_CMD_EVENT_CHAT) {
            SkRledSetEvent(SK_LED_EVENT_CALL_OUTGOING);
            ctrl->role = SK_ROLE_MODE_CALLER;
            ctrl->sessionMode = SM_SESSION_MODE_AGENT;
        }
    } else if (event->event == SM_EVENT_SYSTEM) {
        if (event->subEvent == SM_EVENT_SYSTEM_ENTER_MENU || event->subEvent == SM_EVENT_SYSTEM_WAKEUP) {
            SkRledSetEvent(SK_LED_EVENT_CHAT);
            ctrl->sessionMode = SM_SESSION_MODE_SYS;
        }
    } else {
        SK_LOGE(TAG, "Unknown event %d", event->event);
        return SK_RET_FAIL;
    }
    SK_LOGE(TAG, "Start call as %d, local session id %d", ctrl->role, ctrl->currSessionId);
    SkOsShowCurrTime();
    
    return SK_RET_SUCCESS;
}

void SmCallInitModules(SmChatCtrl *ctrl) {
    uint8_t audioList[2];

    SkPlayerResume();
    audioList[0] = AUDIO_IDX_WAIT;
    SkSmPlayLocalNotice(audioList, 1, false);
    SkRecorderResume();
    SkVcProcessEnable(true);
    SkSrProcessEnable(false);
    SkOpusDecUnmuteRemote(); // 解除静音

    return;
}

int32_t SmCallStartLink(SmChatCtrl *ctrl) {
    int32_t nextState = ctrl->state;
    if (ctrl->sessionMode == SM_SESSION_MODE_SYS) {
        //SkRlinkEventNotify(RLINK_EVENT_LINK_CALL_AGENT, ctrl->currSessionId);
        nextState = SM_CALL_STATE_CALLING;
    } else if (ctrl->sessionMode == SM_SESSION_MODE_AGENT) {
        SkClinkEventNotify(CLINK_EVENT_CALL_AGENT, ctrl->currSessionId, 0, 0);
        nextState = SM_CALL_STATE_WAIT_CONTROLLER;       
    } else {
        /* ctrl->sessionMode == SM_SESSION_MODE_HUMAN */
        if (ctrl->role == SK_ROLE_MODE_CALLEE) {
            SkClinkEventNotify(CLINK_EVENT_CALL_TERM_OK, ctrl->currSessionId, 0, 0);
            nextState = SM_CALL_STATE_WAIT_AGENT;
        } else {
            SkClinkEventNotify(CLINK_EVENT_CALL_TERM, ctrl->currSessionId, 0, 0);
            nextState = SM_CALL_STATE_WAIT_CONTROLLER;
        }
    }
    return nextState;
}

void SmCallReset(SmChatCtrl *ctrl) {
    SkRlinkEventNotify(RLINK_EVENT_STOP_CALL, 0);
    SkClinkEventNotify(CLINK_EVENT_FINISH_CALL, ctrl->currSessionId, 0, 0);
    SkRecorderResume();
    SkVcProcessEnable(false);
    SkSrProcessEnable(true);
    SmCallSetState(ctrl, SM_CALL_STATE_IDLE);
    return;
}

int32_t SmCallStop(SkSubStateInfo *info) {
    SmChatCtrl *ctrl = (SmChatCtrl *)info->privateData;
    SmCallReset(ctrl);
    SK_LOGE(TAG, "Stop Call");
    return SK_RET_SUCCESS;
}

void SmCallInnerStop(SmChatCtrl *ctrl) {
    SmCallReset(ctrl);
    if (ctrl->endCb != NULL) {
        ctrl->endCb(ctrl->handler, STATE_CALL);
    }
}

int32_t SmCallEventProcCalleeMatch(SmChatCtrl *ctrl, const SkSmEvent *event) {
    if (event->param1 != ctrl->currSessionId) {
        return SK_RET_SUCCESS;
    }
    ctrl->matchResult = event->param2;
    ctrl->matchResultValid = 1;

    // 提示音结束之后，本状态才结束
    if (ctrl->audioEnd != 1) {
        return SK_RET_SUCCESS;
    }
    SmCallInnerStop(ctrl);
    if (ctrl->matchResult == 1) {
        SkSmSendEvent(ctrl->handler, SM_EVENT_CMD, SPEECH_CMD_EVENT_CALL_CALLEE, 0, 0);
    }
    return SK_RET_SUCCESS;
}

int32_t SmCallEventProcAudioEnd(SmChatCtrl *ctrl, const SkSmEvent *event) {
    if (ctrl->sessionMode != SM_SESSION_MODE_SYS) {
        return SK_RET_SUCCESS;
    }
    if (event->param1 != ctrl->currSessionId) {
        return SK_RET_SUCCESS;
    }
    ctrl->audioEnd = 1;
    if (ctrl->matchResultValid != 1) {
        return SK_RET_SUCCESS;
    }
    SmCallInnerStop(ctrl);
    if (ctrl->matchResult == 1) {
        SkSmSendEvent(ctrl->handler, SM_EVENT_CMD, SPEECH_CMD_EVENT_CALL_CALLEE, 0, 0);
    }
    return SK_RET_SUCCESS;
}

int32_t SmCallEventProcWaitStart(SmChatCtrl *ctrl, const SkSmEvent *event) {
    int32_t nextState = ctrl->state;

    if (event->subEvent == SM_EVENT_RLINK_DISCONNECT) {
        SmCallInitModules(ctrl);
        nextState = SmCallStartLink(ctrl);
        SmCallSetState(ctrl, nextState);
    } else if (event->subEvent == SM_EVENT_SYSTEM_TICK) {
        if (SkSmGetRlinkState(ctrl->handler) == SM_RLINK_STATE_DISCONNECT) {
            SmCallInitModules(ctrl);
            nextState = SmCallStartLink(ctrl);
            SmCallSetState(ctrl, nextState);
        } else {
            ctrl->tickCnt++;
            if (ctrl->tickCnt >= 30) {
                SK_LOGE(TAG, "Timeout, stop chat");
                SmCallInnerStop(ctrl);
            }
        }
    }         
    return SK_RET_SUCCESS;
}

int32_t SmCallEventProcWaitController(SmChatCtrl *ctrl, const SkSmEvent *event) {
    int32_t ret = SK_RET_SUCCESS;
    if (event->subEvent == SM_EVENT_CLINK_RELAY_OK) {
        if (event->param1 == ctrl->currSessionId) {
            SmCallSetState(ctrl, SM_CALL_STATE_WAIT_AGENT);
        }
    } else if (event->subEvent == SM_EVENT_CLINK_RELAY_FAIL) {
        SmCallInnerStop(ctrl);
    } else if (event->subEvent == SM_EVENT_SYSTEM_TICK) {
        ctrl->tickCnt++;
        if (ctrl->tickCnt >= 30) {
            SK_LOGE(TAG, "Timeout, stop chat");
            SmCallInnerStop(ctrl);
        }
    }
    return ret;
}

int32_t SmCallEventProcWaitAgentState(SmChatCtrl *ctrl, const SkSmEvent *event) {
    int32_t ret = SK_RET_SUCCESS;
    if (event->subEvent == SM_EVENT_RLINK_PEER_CONNECT) {
        if (event->param1 == ctrl->currSessionId) {
            SmCallSetState(ctrl, SM_CALL_STATE_CALLING);
            SkRledSetEvent(SK_LED_EVENT_CALLING);
        }
    } else if (event->subEvent == SM_EVENT_RLINK_DISCONNECT) {
        if (event->param1 != ctrl->currSessionId) {
            SK_LOGE(TAG, "Session id not match rlink close: need %u, rpt %u.",
                ctrl->currSessionId, event->param1);
        }
        SmCallInnerStop(ctrl);
        SkClinkEventNotify(CLINK_EVENT_FINISH_CALL, 0, 0, 0);
    } else if (event->subEvent == SM_EVENT_CLINK_DISCONNECT) {
        SmCallInnerStop(ctrl);
    } else if (event->subEvent == SM_EVENT_SYSTEM_TICK) {
        ctrl->tickCnt++;
        if (ctrl->tickCnt >= 30) {
            SK_LOGE(TAG, "Timeout, stop chat");
            SmCallInnerStop(ctrl);
        }
    }
    return ret;
}

int32_t SmCallEventProcCalling(SmChatCtrl *ctrl, const SkSmEvent *event) {
    int32_t ret = SK_RET_SUCCESS;
    if (event->subEvent == SM_EVENT_SYSTEM_START_VOICE_INPUT) {
        SkOpusDecMuteRemote();
    } else if (event->subEvent == SM_EVENT_SYSTEM_END_VOICE_INPUT) {
        SkOpusDecUnmuteRemote();
    } else if (event->subEvent == SM_EVENT_CLINK_DISCONNECT) {
        SmCallInnerStop(ctrl);
    } else if (event->subEvent == SM_EVENT_RLINK_DISCONNECT) {
        if (event->param1 != ctrl->currSessionId) {
            SK_LOGE(TAG, "Session id not match rlink close: need %u, rpt %u.",
                ctrl->currSessionId, event->param1);
        }
        SmCallInnerStop(ctrl);
        SkClinkEventNotify(CLINK_EVENT_FINISH_CALL, 0, 0, 0);
    } else if (event->subEvent == SM_EVENT_RLINK_CALLEE_MATCH) {
        SmCallEventProcCalleeMatch(ctrl, event);
    } else if (event->subEvent == SM_EVENT_RLINK_SESSION_AUDIO_END) {
        SmCallEventProcAudioEnd(ctrl, event);
    } else if (event->subEvent == SM_EVENT_RLINK_ALIGN_REPLY) {
        // 收到对齐回复
        SkSmActionDone();
        SkOpusDecUnmuteRemote(); // 解除静音
    } else if (event->subEvent == SM_EVENT_SYSTEM_WORK_FINISH) {
        if (ctrl->waitLocalPlayEnd) {
            SkRecorderResume();
            SkOpusEncSetFlag(1);
            SkVcProcessEnable(true);
            ctrl->waitLocalPlayEnd = false;
        }
    } else if (event->subEvent == SM_EVENT_RLINK_VOLUP) {
        // 远程调整音量, 为了避免本地提示音AEC不完整, 导致远端错误, 因此暂停录音
        if (SkSmVolUp() == SK_RET_SUCCESS) {
            SkOpusEncSetFlag(0);
            SkRecorderPause();
            SkVcProcessEnable(false);
            ctrl->waitLocalPlayEnd = true;
        }
    } else if (event->subEvent == SM_EVENT_RLINK_VOLDOWN) {
        // 远程调整音量, 为了避免本地提示音AEC不完整, 导致远端错误, 因此暂停录音
        if (SkSmVolDown() == SK_RET_SUCCESS) {
            SkOpusEncSetFlag(0);
            SkRecorderPause();
            SkVcProcessEnable(false);
            ctrl->waitLocalPlayEnd = true;
        }
    }
    return ret;
}

int32_t SmCallCmdProc(SmChatCtrl *ctrl, const SkSmEvent *event) {
    switch (event->subEvent) {
        case SPEECH_CMD_EVENT_PAUSE:
            SkPlayerPause();
            SkRecorderPause();
            SK_LOGI(TAG, "Pause");
            break;
        case SPEECH_CMD_EVENT_RESUME:
            SkPlayerResume();
            SkRecorderResume();
            SK_LOGI(TAG, "Resume");
            break;
        case SPEECH_CMD_EVENT_QUIT:
            SmCallInnerStop(ctrl);
            SK_LOGI(TAG, "Quit");
            break;
        default:
            SK_LOGE(TAG, "Unknown cmd %d", event->subEvent);
            return SK_RET_FAIL;
    }
    return SK_RET_SUCCESS;
}

int32_t SmCallEventProc(SkSubStateInfo *info, const SkSmEvent *event) {
    SmChatCtrl *ctrl = (SmChatCtrl *)info->privateData;
    const static SmCallEventFunc eventProcs[] = {
        NULL,
        SmCallEventProcWaitStart,
        SmCallEventProcWaitController,
        SmCallEventProcWaitAgentState,
        SmCallEventProcCalling,
    };

    if (event->event == SM_EVENT_CMD) {
        return SmCallCmdProc(ctrl, event);
    }

    if (ctrl->state >= ARRAY_SIZE(eventProcs)) {
        SK_LOGE(TAG, "Invalid state when proc event.");
        return SK_RET_FAIL;
    }
    if (eventProcs[ctrl->state] != NULL) {
        return eventProcs[ctrl->state](ctrl, event);
    }

    return SK_RET_SUCCESS;
}

int32_t SkSmCallInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler) {
    g_callSmCtrl.state = SM_CALL_STATE_IDLE;
    g_callSmCtrl.currSessionId = 0;
    g_callSmCtrl.endCb = endCb;
    g_callSmCtrl.handler = handler;
    item->info.subState = 0;
    item->info.privateData = &g_callSmCtrl;
    item->startProc = SmCallStart;
    item->stopProc = SmCallStop;
    item->eventProc = SmCallEventProc;
    return SK_RET_SUCCESS;
}
