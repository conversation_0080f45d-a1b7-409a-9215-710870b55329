/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sm_ota.c
 * @description: OTA升级子状态.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <string.h>
#include "sk_common.h"
#include "sk_log.h"
#include "sk_audio.h"
#include "sk_sm.h"
#include "sm.h"
#include "sk_board.h"
#include "sk_ota_api.h"
#include "sk_ota_config.h"

#define TAG "SmOta"

typedef struct {
    SkStateHandler handler;
    SkSmStateEndCallback endCb;
    bool otaStarted;
} SmOtaCtrl;

SmOtaCtrl g_otaSmCtrl;

int32_t SmOtaStart(SkSubStateInfo *info, const SkSmEvent *event) {
    SkRledSetEvent(SK_LED_EVENT_OTA);
    SmOtaCtrl *ctrl = (SmOtaCtrl *)info->privateData;

    SK_LOGI(TAG, "OTA upgrade started");

    // Check current OTA status
    SkOtaStatus status = SkOtaManagerGetStatus();
    if (status != SK_OTA_STATUS_IDLE) {
        SK_LOGI(TAG, "OTA already in progress: %s", SkOtaStatusToString(status));
        ctrl->otaStarted = true;
        return SK_RET_SUCCESS;
    }

    // Start OTA check/update
    SkOtaError err = SkOtaManagerCheckUpdate();
    if (err == SK_OTA_ERR_OK) {
        ctrl->otaStarted = true;
        SK_LOGI(TAG, "OTA update initiated successfully");
    } else {
        SK_LOGE(TAG, "Failed to start OTA update: %s", SkOtaErrorToString(err));
        ctrl->otaStarted = false;
    }

    return SK_RET_SUCCESS;
}

int32_t SmOtaStop(SkSubStateInfo *info) {
    SmOtaCtrl *ctrl = (SmOtaCtrl *)info->privateData;

    SK_LOGI(TAG, "Stop OTA process");

    if (ctrl->otaStarted) {
        SkOtaError err = SkOtaManagerStop();
        if (err == SK_OTA_ERR_OK) {
            SK_LOGI(TAG, "OTA update stopped successfully");
        } else {
            SK_LOGE(TAG, "Failed to stop OTA update: %s", SkOtaErrorToString(err));
        }
        ctrl->otaStarted = false;
    }

    return SK_RET_SUCCESS;
}

int32_t SmOtaEventProc(SkSubStateInfo *info, const SkSmEvent *event) {
    SmOtaCtrl *ctrl = (SmOtaCtrl *)info->privateData;

    // Check OTA status and handle events
    if (ctrl->otaStarted) {
        SkOtaStatus status = SkOtaManagerGetStatus();

        switch (status) {
            case SK_OTA_STATUS_SUCCESS:
                SK_LOGI(TAG, "OTA update completed successfully, device will reboot");
                ctrl->otaStarted = false;
                break;

            case SK_OTA_STATUS_FAILED:
                SK_LOGE(TAG, "OTA update failed");
                ctrl->otaStarted = false;
                break;

            case SK_OTA_STATUS_DOWNLOADING:
                // Continue monitoring download progress
                break;

            case SK_OTA_STATUS_IDLE:
                // OTA completed or stopped
                ctrl->otaStarted = false;
                break;

            default:
                // Other statuses, continue monitoring
                break;
        }
    }

    // Handle state machine events
    if (ctrl->endCb != NULL) {
        SK_LOGI(TAG, "End OTA state");
        ctrl->endCb(ctrl->handler, STATE_OTA);
    }

    return SK_RET_SUCCESS;
}

int32_t SkSmOtaInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler) {
    // Initialize control structure
    memset(&g_otaSmCtrl, 0, sizeof(g_otaSmCtrl));
    g_otaSmCtrl.endCb = endCb;
    g_otaSmCtrl.handler = handler;
    g_otaSmCtrl.otaStarted = false;

    // Initialize state machine item
    item->info.subState = 0;
    item->info.privateData = &g_otaSmCtrl;
    item->startProc = SmOtaStart;
    item->stopProc = SmOtaStop;
    item->eventProc = SmOtaEventProc;

    SK_LOGI(TAG, "OTA state machine initialized");
    return SK_RET_SUCCESS;
}

void SkSmOtaOnStateChange(const SkOtaStatus state) {
    switch (state) {
        case SK_OTA_STATUS_SUCCESS:
            SkSmPostEvent(SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_OTA_COMPLETE, 0, 0);
            break;

        case SK_OTA_STATUS_FAILED:
            SkSmPostEvent(SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_OTA_FAILED, 0, 0);
            break;

        default:
            break;
    }
    return;
}