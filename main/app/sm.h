/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sm.h
 * @description: 状态机内部头文件.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SM_H
#define SM_H

#include <stdint.h>
#include "sk_sm.h"

enum {
    STATE_INIT = 0,
    STATE_CONNECTING = 1,
    STATE_OTA = 2,
    STATE_IDLE = 3,
    STATE_ERROR = 4,
    STATE_CHAT = 5,
    STATE_CALL = 6,
    STATE_MUSIC = 7,
    STATE_CONFIG = 8,
    STATE_QUERY = 9,
    STATE_HELP = 10,
    STATE_REBOOT = 11,
    STATE_PM = 12,
    STATE_MAX,
};

enum {
    NETWORK_STATE_STA_CONNECTING,
    NETWORK_STATE_STA_CONNECTED,
    NETWORK_STATE_STA_STOP,
    NETWORK_STATE_AP_ENABLE,
    NETWORK_STATE_AP_STOP,
};

enum SmClinkState_e {
    SM_CLINK_STATE_DISCONNECT,
    SM_CLINK_STATE_CONNECTING,
    SM_CLINK_STATE_CONNECTED,
};

enum SmRlinkState_e {
    SM_RLINK_STATE_DISCONNECT,
    SM_RLINK_STATE_CONNECTING,
    SM_RLINK_STATE_CONNECTED,
};

typedef void (*SkSpeechCmdProc)(int32_t event, int32_t subEvent, int32_t param1, int32_t param2);
typedef void (*SkSmStateEndCallback)(SkStateHandler handler, int32_t state);

typedef struct {
    int32_t cmd;
    SkSpeechCmdProc proc;
} SkCommandProcItem;

typedef struct {
    int32_t subState;
    void *privateData;
} SkSubStateInfo;

typedef int32_t (*SkSmStartProc)(SkSubStateInfo *info, const SkSmEvent *event);
typedef int32_t (*SkSmStopProc)(SkSubStateInfo *info);
typedef int32_t (*SkSmEventProc)(SkSubStateInfo *info, const SkSmEvent *event);

typedef struct {
    SkSubStateInfo info;
    SkSmStartProc startProc;
    SkSmStopProc stopProc;
    SkSmEventProc eventProc;
} SkSmItem;

#ifdef __cplusplus
extern "C" {
#endif

int32_t SkSmIdleInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler);
int32_t SkSmCallInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler);
int32_t SkSmMusicInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler);
int32_t SkSmConfigInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler);
int32_t SkSmQueryInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler);
int32_t SkSmHelpInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler);
int32_t SkSmConnectingInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler);
int32_t SkSmOtaInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler);
int32_t SkSmPmInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler);
void SkSmStateChange(int32_t event, int32_t subEvent, int32_t param1, int32_t param2);
int16_t SkSmNewSession(SkStateHandler handler);
int32_t SkSmGenAudioList(int32_t wifiMode, uint8_t firstIdx, uint8_t audioList[16]);
int32_t SkSmGetNetState(void);
void SkSmSetReboot();
void SkSmPlayLocalNotice(uint8_t *audioList, uint8_t len, bool waitFlag);
uint8_t SkSmGetClinkState(SkStateHandler handler);
uint8_t SkSmGetRlinkState(SkStateHandler handler);
bool SkSmIsPlaying(void);
void SkSmActionDone();
void SkSmActionOk();
int32_t SkSmVolUp();
int32_t SkSmVolDown();

#ifdef __cplusplus
}
#endif

#endif /* SM_H */
