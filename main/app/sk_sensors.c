/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_sensors.c
 * @description: 探测器应用任务，从探测器中读取数据将状态上报给状态处理模块.
 * @author: <PERSON>
 * @date: 2025-06-30
 */

#include <stdint.h>
#include <stdlib.h>
#include <stdio.h>
#include "sdkconfig.h"
#include <string.h>
#include <unistd.h>
#include <math.h>
#include "sk_os.h"
#include "sk_board.h"
#include "sk_dfx.h"
#include "sk_log.h"
#include "sk_frame.h"
#include "sm.h"

#define CONFIG_SK_SENSOR_BATTERY_ENABLE     1
#define CONFIG_SK_GYRO_ENABLE               1
#define CONFIG_SK_THS_ENABLE                1
#define CONFIG_SK_VIBRATION_EABLE           1
#define CONFIG_SK_DEBUG_SENSOR_DATA         1

static const char *TAG = "SmartKid";

typedef struct {
    EventGroupHandle_t eventGroup;
    SkStateHandler smHandler;
    uint32_t stationaryCnt;
    int32_t prevAccel;
    SkDfxCtrl_t dfxCtrl;
} SkPeripheralCtrl_t;

#define SK_PERIPHERAL_TASK_STOP     (1UL << 0)
#define SK_PERIPHERAL_RESET_GYRO    (1UL << 1)

SkPeripheralCtrl_t g_skPeripheralCtrl;

uint32_t SkSensorState() {
    return g_skPeripheralCtrl.stationaryCnt;
}

int32_t SkSensorDataProcess(SkSensorData_t *data) {
    float accelScale = 2.0f / 32768.0f;
    float gyroScale = 250.0f / 32768.0f;
    float accelTotal, accelX, accelY, accelZ;
    int32_t accelIntValue;

    // 转换为物理量（±2g量程）
    accelX = (float)data->accel[0] * accelScale;
    accelY = (float)data->accel[1] * accelScale;
    accelZ = (float)data->accel[2] * accelScale;
    accelTotal = sqrt(accelX * accelX + accelY * accelY + accelZ * accelZ) - 1.0f;
    accelIntValue = (int32_t)(accelTotal * 10);
    SK_LOGD(TAG, "Accel: X=%.3fg, Y=%.3fg, Z=%.3fg, total-1.0=%.3fg/%d",
        accelX, accelY, accelZ, accelTotal, accelIntValue);
    // 转换为物理量（±250dps量程）
    SK_LOGD(TAG, "Gyro: X=%.2f°/s, Y=%.2f°/s, Z=%.2f°/s",
        data->gyro[0] * gyroScale, data->gyro[1] * gyroScale, data->gyro[2] * gyroScale);
    if (accelIntValue > 10) {
        SK_LOGI(TAG, "Accel: X=%.3fg, Y=%.3fg, Z=%.3fg, total-1.0=%.3fg/%d",
            accelX, accelY, accelZ, accelTotal, accelIntValue);
    }

    return accelIntValue;
}

void SkSensorDetectState(SkPeripheralCtrl_t *ctrl, int32_t accelValue) {
    if (accelValue < 2 && accelValue > -2) {
        ctrl->stationaryCnt++;
    } else {
        ctrl->stationaryCnt = 0;
    }
    if (ctrl->prevAccel < 2 && ctrl->prevAccel > -2) {
        if ((accelValue > 10) || (accelValue < -10)) {
            SkSmSendEvent(ctrl->smHandler, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_MOVED, 0, 0);
        }
    }
    ctrl->prevAccel = accelValue;
    return;
}

void SkSensorProcess(SkPeripheralCtrl_t *ctrl, SkSensorData_t *data) {
    int32_t ret;

    ret = SkGyroReadData(data);
    if (ret != SK_RET_SUCCESS) {
        return;
    }
    ret = SkSensorDataProcess(data);
    SkSensorDetectState(ctrl, ret);

    return;
}

void SkTHSProcess(SkTHSData_t *thsData) {
    if (SkTHSReadData(thsData) != SK_RET_SUCCESS) {
        return;
    }
    SK_LOGD(TAG, "THS: temp=%d°C, humi=%d%%", thsData->temp, thsData->humi);
    return;
}

void SkSensorCopyGyroDataToDevInfo(SkDeviceInfoRecord *devInfo, SkSensorData_t *data) {
    devInfo->timestamp = data->timestamp;
    devInfo->accel[0] = data->accel[0];
    devInfo->accel[1] = data->accel[1];
    devInfo->accel[2] = data->accel[2];
    devInfo->gyro[0] = data->gyro[0];
    devInfo->gyro[1] = data->gyro[1];
    devInfo->gyro[2] = data->gyro[2];
    devInfo->ff = data->ff;
    devInfo->temp = data->temp;
    devInfo->step = data->step;
    devInfo->stepState = data->stepState;

    return;
}

void SkPeripheralTask(void *arg) {
#if CONFIG_SK_SENSOR_BATTERY_ENABLE
    int32_t battery;
#endif
    SkPeripheralCtrl_t *ctrl = (SkPeripheralCtrl_t *)arg;
#if CONFIG_SK_GYRO_ENABLE
    SkSensorData_t sensorData;
#endif
#if CONFIG_SK_THS_ENABLE
    SkTHSData_t thsData;
#endif
#if CONFIG_SK_DEBUG_SENSOR_DATA
    SkDeviceInfoRecord devInfo;

    memset(&devInfo, 0x00, sizeof(SkDeviceInfoRecord));
#endif

    SkBspInitSensor();
    while ((xEventGroupGetBits(ctrl->eventGroup) & SK_PERIPHERAL_TASK_STOP) == 0) {
#if CONFIG_SK_GYRO_ENABLE
        if ((xEventGroupGetBits(ctrl->eventGroup) & SK_PERIPHERAL_RESET_GYRO) != 0) {
            xEventGroupClearBits(g_skPeripheralCtrl.eventGroup, SK_PERIPHERAL_RESET_GYRO);
            SkGyroReset();
            SK_LOGI(TAG, "Gyro reset");
        }
        SkSensorProcess(ctrl, &sensorData);
#endif
#if CONFIG_SK_THS_ENABLE
        SkTHSProcess(&thsData);
#endif
#if CONFIG_SK_SENSOR_BATTERY_ENABLE
        battery = SkBspGetBattery();
#endif
#if CONFIG_SK_DEBUG_SENSOR_DATA
        SkSensorCopyGyroDataToDevInfo(&devInfo, &sensorData);
#if CONFIG_SK_SENSOR_BATTERY_ENABLE
        devInfo.battery = battery;
#endif
#if CONFIG_SK_THS_ENABLE
        devInfo.humi = thsData.humi;
        devInfo.temp = thsData.temp;
#endif
        SkDfxRecordData(&ctrl->dfxCtrl, (uint8_t *)&devInfo, sizeof(SkDeviceInfoRecord));
        SkDfxProcCall(&ctrl->dfxCtrl);
#endif
        vTaskDelay(pdMS_TO_TICKS(500));
    }
    vTaskDelete(NULL);

    return;
}

void SkPeripheralStop(void) {
    if (g_skPeripheralCtrl.eventGroup != NULL) {
        xEventGroupSetBits(g_skPeripheralCtrl.eventGroup, SK_PERIPHERAL_TASK_STOP);
    }
    
    return;
}

void SkPeripheralResetGyro(void) {
    if (g_skPeripheralCtrl.eventGroup != NULL) {
        xEventGroupSetBits(g_skPeripheralCtrl.eventGroup, SK_PERIPHERAL_RESET_GYRO);
    }
    
    return;
}

void SkPeripheralInit(SkStateHandler smHandler) {
    g_skPeripheralCtrl.eventGroup = xEventGroupCreate();
    g_skPeripheralCtrl.smHandler = smHandler;

#if CONFIG_SK_DEBUG_SENSOR_DATA
    SkDfxInit(&g_skPeripheralCtrl.dfxCtrl, MSG_DFX_AND_TERM_SENSOR_DATA, 512, sizeof(SkDeviceInfoRecord));
#endif

    xTaskCreate(SkPeripheralTask, "PeripheralTask", 4096, &g_skPeripheralCtrl, 2, NULL);
    return;
}

void SkPeripheralEnableReport(bool flag) {
#if CONFIG_SK_DEBUG_SENSOR_DATA
    if (flag) {
        SkDfxStart(&g_skPeripheralCtrl.dfxCtrl);
    } else {
        SkDfxStop(&g_skPeripheralCtrl.dfxCtrl);
    }
#endif
    return;
}

void SkPeripheralSetPm(bool flag) {
    if (flag) {
        SkPeripheralStop();
    } else {
        xEventGroupClearBits(g_skPeripheralCtrl.eventGroup, SK_PERIPHERAL_TASK_STOP);
        xTaskCreate(SkPeripheralTask, "PeripheralTask", 4096, &g_skPeripheralCtrl, 2, NULL);
    }
    return;
}