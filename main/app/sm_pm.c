/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sm_pm.c
 * @description: 低功耗子状态.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <string.h>
#include "sk_common.h"
#include "sk_log.h"
#include "sk_board.h"
#include "sk_sm.h"
#include "sk_audio.h"
#include "sk_config.h"
#include "sk_wifi.h"
#include "sm.h"
#include "sk_opus.h"
#include "sk_opus_dec.h"
#include "sk_opus_enc.h"
#include "sk_os.h"
#include "sk_clink.h"
#include "sk_rlink.h"
#include "sk_dfx.h"

#define TAG "SmPm"

#define SK_MODEL_CLINK  (0x00000001UL << 0)
#define SK_MODEL_WIFI   (0x00000001UL << 1)
#define SK_MODEL_SR     (0x00000001UL << 2)
#define SK_MODEL_PLAYER (0x00000001UL << 3)

typedef struct {
    uint32_t modelState;
    uint32_t pmTimeCnt;
    SkSmStateEndCallback endCb;
    SkStateHandler handler;
} SmPmCtrl;

SmPmCtrl g_smPmCtrl;

void SkPeripheralSetPm(bool flag);
void SkSmPmExit(SmPmCtrl *ctrl) {
    SK_LOGI(TAG, "Exit pm mode");
    SkBoardSetPm(false);
    SkPeripheralSetPm(false);
    SkClinkSetPm(false);
    SK_LOGI(TAG, "Exit light sleep mode");
    SkPlayerSetPm(false);
    SkRecorderSetPm(false);
    SkRecorderResume();
    SkSrSetPm(false);
#if CONFIG_SK_PM_SLEEP_MODE == CONFIG_SK_PM_SLEEP_MODE_LIGHT2
    SkAudioStartTasks();
#endif
    SK_LOGI(TAG, "Exit sr pm mode");
    SkOpusSetPm(false);
    SK_LOGI(TAG, "Exit opus pm mode");
    SkWifiStartSta();
    SK_LOGI(TAG, "Start wifi sta mode");
    SkSrProcessEnable(true);
    if (ctrl->endCb != NULL) {
        ctrl->endCb(ctrl->handler, STATE_PM);
    }

    return;
}

int32_t SkSmPmStart(SkSubStateInfo *info, const SkSmEvent *event) {
    SmPmCtrl *ctrl = (SmPmCtrl*)info->privateData;

    SK_LOGI(TAG, "Enter pm mode");
    ctrl->pmTimeCnt = 0;

    const int wakeupSec = 600;
    SK_LOGI(TAG, "Enabling timer wakeup, %ds", wakeupSec);
    SkBspPmTimeWakeup(wakeupSec);
    SkRecorderSetPm(true);
    SkPeripheralSetPm(true);

    SK_LOGI(TAG, "Clink pm mode");
    SkClinkSetPm(true);
    SK_LOGI(TAG, "Vc pm mode");
    SkVcProcessEnable(false);
    SK_LOGI(TAG, "Audio pm mode");
    SkSrSetPm(true);
    SkRecorderPause();
    SkSrProcessEnable(false);
    ctrl->modelState = SK_MODEL_CLINK | SK_MODEL_WIFI | SK_MODEL_PLAYER;

    return SK_RET_SUCCESS;
}

int32_t SkSmPmStop(SkSubStateInfo *info) {
    return SK_RET_SUCCESS;
}

esp_err_t SkPmEnterCb(int64_t sleep_time_us, void *arg) {
    SK_LOGI(TAG, "Enter pm mode cb.");
    return ESP_OK;
}

esp_err_t SkPmExitCb(int64_t sleep_time_us, void *arg) {
    SK_LOGI(TAG, "Exit pm mode cb.");
    return ESP_OK;
}

int32_t SkSmPmEventProc(SkSubStateInfo *info, const SkSmEvent *event) {
    SmPmCtrl *ctrl = (SmPmCtrl*)info->privateData;

    if (ctrl->modelState == 0) {
        // 检查是否退出低功耗模式
        if (event->event == SM_EVENT_SYSTEM && event->subEvent == SM_EVENT_SYSTEM_WAKEUP) {
            SkSmPmExit(ctrl);
            return SK_RET_SUCCESS;
        } else if (event->event == SM_EVENT_SYSTEM && event->subEvent == SM_EVENT_SYSTEM_TICK) {   
            if (ctrl->pmTimeCnt == 0) {
                SK_LOGI(TAG, "Enter light sleep mode");
                SkBoardSetPm(true);
                SkOsShowSysInfo();
                SkBspEnterSleep();  // 进入睡眠模式, CPU不运转，等待唤醒事件
                SK_LOGI(TAG, "Backfrom light sleep mode"); // Light Sleep进入这里就被唤醒了。
            }
            ctrl->pmTimeCnt++;
            if (ctrl->pmTimeCnt >= 10) {
                SkSmPmExit(ctrl);
            }
        }
        return SK_RET_SUCCESS;
    }
    if (event->event == SM_EVENT_SYSTEM && event->subEvent == SM_EVENT_SYSTEM_TICK) {
        if ((SkSmGetClinkState(ctrl->handler) == 0) && ((ctrl->modelState & SK_MODEL_CLINK) != 0)) {
            SK_LOGI(TAG, "Stop wifi");
            ctrl->modelState = ctrl->modelState & ~SK_MODEL_CLINK;
            // 关闭WiFi模块
            SkWifiStop();
        }
        if ((SkSmGetNetState() == NETWORK_STATE_STA_STOP) && ((ctrl->modelState & SK_MODEL_WIFI) != 0)) {
            SK_LOGI(TAG, "Notify enter pm mode");
            ctrl->modelState = ctrl->modelState & ~SK_MODEL_WIFI;
            // 播放待机提醒
            uint8_t audioList[2];
            audioList[0] = AUDIO_IDX_ENTER_PM;
            SkSmPlayLocalNotice(audioList, 1, true);
        }
        if ((ctrl->modelState == SK_MODEL_PLAYER) && (!SkSmIsPlaying())) {
            SK_LOGI(TAG, "Opus enter pm mode");
            SkPlayerSetPm(true);
            ctrl->modelState = ctrl->modelState & ~SK_MODEL_PLAYER;
            // 进入省电模式
            SkOpusSetPm(true);
        }
    }

    return SK_RET_SUCCESS;
}

int32_t SkSmPmInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler) {
    item->info.subState = 0;
    item->info.privateData = &g_smPmCtrl;
    item->startProc = SkSmPmStart;
    item->stopProc = SkSmPmStop;
    item->eventProc = SkSmPmEventProc;
    g_smPmCtrl.handler = handler;
    g_smPmCtrl.endCb = endCb;
    return SK_RET_SUCCESS;
}
