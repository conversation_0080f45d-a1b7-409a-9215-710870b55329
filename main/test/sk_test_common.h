/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_test_common.h
 * @description: 测试模块公用头文件
 * @author: <PERSON>
 * @date: 2025-07-19
 */
#ifndef SK_TEST_COMMON_H
#define SK_TEST_COMMON_H

#include "sk_common.h"
#include "sk_log.h"

#ifdef __cplusplus
extern "C" {
#endif

bool SkTcWaitConditionSec(bool *cond, uint32_t timeout);

#ifdef __cplusplus
}
#endif

#endif // SK_TEST_COMMON_H
