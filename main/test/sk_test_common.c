/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_test_common.c
 * @description: 
 * @author: 
 * @date: 2025-07-19
 */
#include <stdint.h>
#include <stdbool.h>
#include <freertos/FreeRTOS.h>
#include "sk_test_common.h"

bool SkTcWaitConditionSec(bool *cond, uint32_t timeout) {
    uint32_t cnt = 0;
    while(!(*cond)) {
        if (cnt > timeout) {
            SK_LOGD("TC", "Wait condition timeout");
            return false;
        }
        vTaskDelay(1000 / portTICK_PERIOD_MS);
        cnt++;
    }
    SK_LOGD("TC", "Wait condition success");
    return true;
}
