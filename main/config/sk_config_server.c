/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_config_server.c
 * @description: 小程序配置时，配置服务器端.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <sys/socket.h>
#include <cJSON.h>
#include "sk_os.h"
#include "sk_log.h"
#include "sk_sm.h"
#include "sk_config.h"

#define CONFIG_SK_CFG_PORT 9528

static const char *TAG = "ConfigServer";

typedef struct {
    bool running;
    TaskHandle_t taskHandle;
} SkConfigServer_t;

void SkConfigServerProc(void *arg);

SkConfigServer_t g_skConfigServer;

void SkConfigStartServer() {
    SkConfigServer_t *ctrl = &g_skConfigServer;

    if (ctrl->taskHandle != NULL) {
        return;
    }
    ctrl->running = true;
    xTaskCreate(SkConfigServerProc, "CfgServer", 4096, ctrl, 5, &ctrl->taskHandle);

    return;
}

void SkConfigStopServer() {
    SkConfigServer_t *ctrl = &g_skConfigServer;
    ctrl->running = false;

    return;
}

static int32_t SkConfigParseSsid(cJSON *json) {
    cJSON *ssid_item = cJSON_GetObjectItemCaseSensitive(json, "ssid");
    cJSON *password_item = cJSON_GetObjectItemCaseSensitive(json, "password");

    if (!cJSON_IsString(ssid_item) || (ssid_item->valuestring == NULL)) {
        cJSON_Delete(json);
        return ESP_OK;
    }

    if (password_item != NULL && cJSON_IsString(password_item) && password_item->valuestring != NULL) {
        SkConfigAddSsid((int8_t *)ssid_item->valuestring, (int8_t *)password_item->valuestring);
    } else {
        SkConfigAddSsid((int8_t *)ssid_item->valuestring, NULL);
    }
    return ESP_OK;
}

void SkConfigReportAck(const int sock) {
    char *ack = "{\"operation\":\"ack\"}\r\n";
    send(sock, ack, strlen(ack), 0);
    return;
}

void SKConfigReportSsidList(const int sock) {
    int32_t ret;
    uint8_t *ssidList = malloc(512);

    if (ssidList == NULL) {
        return;
    }

    ret = SkConfigGetSsidList(ssidList, 512);
    if (ret == SK_RET_SUCCESS) {
        SK_LOGI(TAG, "ssid list: %s", ssidList);
        send(sock, ssidList, strlen((char *)ssidList), 0);
    }
    free(ssidList);
    return;
}

void SkConfigServerProcessJson(const int sock, uint8_t *buf) {
    cJSON *json = cJSON_Parse((char *)buf);
    cJSON *operate = NULL;

    if (json == NULL) {
        return;
    }
    operate = cJSON_GetObjectItemCaseSensitive(json, "operation");
    if (!cJSON_IsString(operate) || (operate->valuestring == NULL)) {
        cJSON_Delete(json);
        return;
    }
    if (strcmp(operate->valuestring, "add_ssid") == 0) {
        SkConfigParseSsid(json);
        SkConfigReportAck(sock);
    } else if (strcmp(operate->valuestring, "reset") == 0) {
        SkConfigReportAck(sock);
        SkSmOnReboot();
    } else if (strcmp(operate->valuestring, "get_ssid") == 0) {
        SKConfigReportSsidList(sock);
    } else if (strcmp(operate->valuestring, "clear_ssid") == 0) {
        SkConfigDelSsid();
        SkConfigReportAck(sock);
    }

    cJSON_Delete(json);
    return;
}

static void SkConfigProcess(SkConfigServer_t *ctrl, const int sock) {
    int len;
    char rx_buffer[128];

    do {
        len = recv(sock, rx_buffer, sizeof(rx_buffer) - 1, 0);
        if (len < 0) {
            SK_LOGE(TAG, "Error occurred during receiving: errno %d", errno);
        } else if (len == 0) {
            SK_LOGW(TAG, "Connection closed");
        } else {
            rx_buffer[len] = 0; // Null-terminate whatever is received and treat it like a string
            SK_LOGI(TAG, "Received %d bytes: %s", len, rx_buffer);
            SkConfigServerProcessJson(sock, (uint8_t *)rx_buffer);
            break;
        }
    } while (len > 0 && ctrl->running);
}

void SkConfigServerProc(void *arg) {
    SkConfigServer_t *ctrl = (SkConfigServer_t *)arg;
    int opt = 1;
    int err;
    int listenSock, sock;
    socklen_t addrLen;
    struct sockaddr_storage destAddr;
    struct sockaddr_in *dest_addr_ip4 = (struct sockaddr_in *)&destAddr;
    struct timeval timeout;

    dest_addr_ip4->sin_addr.s_addr = htonl(INADDR_ANY);
    dest_addr_ip4->sin_family = AF_INET;
    dest_addr_ip4->sin_port = htons(CONFIG_SK_CFG_PORT);

    listenSock = socket(AF_INET, SOCK_STREAM, IPPROTO_IP);
    if (listenSock < 0) {
        SK_LOGE(TAG, "Unable to create socket: errno %d", errno);
        vTaskDelete(NULL);
        return;
    }

    timeout.tv_sec = 5;  // 超时时间5秒
    timeout.tv_usec = 0;
    setsockopt(listenSock, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));
    setsockopt(listenSock, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout));

    err = bind(listenSock, (struct sockaddr *)&destAddr, sizeof(destAddr));
    if (err != 0) {
        SK_LOGE(TAG, "Socket unable to bind: errno %d", errno);
        goto CLEAN_UP;
    }

    err = listen(listenSock, 1);
    if (err != 0) {
        SK_LOGE(TAG, "Error occurred during listen: errno %d", errno);
        goto CLEAN_UP;
    }

    SK_LOGI(TAG, "Socket listening");
    while (ctrl->running) {
        struct sockaddr_storage srcAddr;
        addrLen = sizeof(srcAddr);
        sock = -1;

        sock = accept(listenSock, (struct sockaddr *)&srcAddr, &addrLen);
        if (sock < 0) {
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                // 超时，继续等待
                continue;
            }
            SK_LOGE(TAG, "Unable to accept connection: errno %d", errno);
            break;
        }

        // Convert ip address to string
        SK_LOGI(TAG, "Socket accepted ip address: %u.%u.%u.%u", 
            ((struct sockaddr_in *)&srcAddr)->sin_addr.s_addr >> 0 & 0xFF,
            ((struct sockaddr_in *)&srcAddr)->sin_addr.s_addr >> 8 & 0xFF,
            ((struct sockaddr_in *)&srcAddr)->sin_addr.s_addr >> 16 & 0xFF,
            ((struct sockaddr_in *)&srcAddr)->sin_addr.s_addr >> 24 & 0xFF);
        SkConfigProcess(ctrl, sock);

        shutdown(sock, 0);
        close(sock);
    }

CLEAN_UP:
    close(listenSock);
    vTaskDelete(NULL);
    ctrl->taskHandle = NULL;

    SK_LOGI(TAG, "Server stopped");
    return;
}