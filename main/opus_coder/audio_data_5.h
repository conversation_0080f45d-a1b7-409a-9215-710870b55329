#ifndef AUDIO_DATA_5_H
#define AUDIO_DATA_5_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfo5[] = {0, 24, 49, 110, 230, 360, 491, 637, 794, 932, 1051};
const uint8_t g_audioData5[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x39, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x0A, 0xB4, 0xD6, 0x80, 0xC9, 0x8C, 0xB9, 0xBE, 0x30, 0x29, 0x10, 0x06, 0x1D, 0xCC, 0x7A, 0x7F, 0x13, 0xAB, 0x59, 0x6D, 0x6D, 0xA6, 0x97, 0xDE, 0xD1, 0x03, 0xE2, 0xE3, 0xFF, 0x71, 0x0D, 0x9A, 0x7B, 0x4E, 0x51, 0x45, 0x65, 0x39, 0x2D, 0x12, 0x4D, 0x2E, 0xF0, 0xB3, 0x3E, 0x63, 0xDB, 0x54, 0x70, 0x4F, 0x00, 0x00, 0x74, 0x58, 0xE0, 0x3B, 0x23, 0xA3, 0xF1, 0x9C, 0xF2, 0x07, 0x68, 0xD8, 0xFC, 0x5E, 0xD3, 0x33, 0xBE, 0x50, 0xEE, 0xF9, 0x0F, 0x9B, 0xCF, 0xD2, 0x3B, 0xBC, 0xC3, 0xCF, 0x20, 0x8E, 0xAE, 0x78, 0xE5, 0xD0, 0xB2, 0x77, 0xA5, 0xC4, 0x6F, 0x1B, 0x7E, 0xFB, 0x9A, 0x22, 0x90, 0x4D, 0x37, 0x9D, 0x8D, 0xB1, 0x14, 0x28, 0xE0, 0xFD, 0x63, 0x10, 0x3D, 0xD2, 0x61, 0x57, 0x3C, 0x31, 0xB2, 0xEE, 0x2A, 0xF4, 0x84, 0xA1, 0x50, 0xFF, 0xDC, 0x5D, 0x10, 0xC9, 0x05, 0x7D, 0x0C, 0x6E, 0x7C, 0x5A, 0x5D, 0xC3, 0x55, 0x51, 0xB5, 0x43, 0x42, 0xDD, 0xB7, 0xB3, 0x25, 0x35, 0x30, 0x0E, 0x7E, 0x6A, 0xCE, 0xB5, 0x7A, 0xF7, 0xEA, 0x10, 0xEB, 0x8F, 0x56, 0x6E, 0x1B, 0xFA, 0x5F, 0xED, 0x86, 0x1A, 0xD9, 0x84, 0xD3, 0x58, 0x16, 0x4F, 0x00, 0x00, 0x7E, 0x58, 0xE7, 0x07, 0xBB, 0x9F, 0x01, 0x61, 0xCE, 0x46, 0xC6, 0x65, 0x76, 0x5B, 0x91, 0xCE, 0xA1, 0x55, 0x76, 0x88, 0xFF, 0xD7, 0x53, 0xD3, 0x00, 0x80, 0x99, 0x52, 0x29, 0x54, 0x34, 0xBC, 0x41, 0xD8, 0x5B, 0x7D, 0x11, 0x97, 0x5B, 0x66, 0x49, 0x75, 0x48, 0xF9, 0x83, 0x79, 0x13, 0x7B, 0xF9, 0x6B, 0xB4, 0x5D, 0xC5, 0x6C, 0x4E, 0xE6, 0xB5, 0xF5, 0xBE, 0x55, 0x65, 0xF8, 0xE8, 0x4A, 0xC0, 0xA1, 0x4E, 0x19, 0x4F, 0x14, 0x51, 0xBB, 0x9E, 0xE1, 0x29, 0x77, 0x32, 0x04, 0x99, 0xAB, 0x65, 0x64, 0xCA, 0xF1, 0xF1, 0x4C, 0xF4, 0x51, 0x5C, 0x1E, 0x0D, 0x31, 0x24, 0x31, 0xDC, 0xEF, 0xCA, 0xCB, 0xC0, 0x48, 0x1C, 0x7D, 0xCE, 0xEA, 0xA1, 0x26, 0xA2, 0x8D, 0xE1, 0x97, 0x40, 0xC5, 0xD1, 0xF0, 0x5B, 0xC4, 0x4D, 0x80, 0xEF, 0xC4, 0x92, 0xA7, 0x94, 0x92, 0x1A, 0xC7, 0x5B, 0x4F, 0x00, 0x00, 0x7F, 0x58, 0xE7, 0x74, 0x84, 0x2E, 0x65, 0xFD, 0xFB, 0x50, 0x3F, 0x7A, 0x12, 0xA8, 0xAB, 0x1B, 0x5C, 0x9F, 0x2A, 0x55, 0x51, 0x5F, 0x4C, 0x33, 0x08, 0x0E, 0x98, 0xA0, 0x91, 0x2A, 0x1E, 0x5D, 0xB8, 0xC8, 0xE2, 0x52, 0x85, 0x97, 0x89, 0xCA, 0x00, 0x1D, 0xAF, 0xCE, 0x17, 0x6B, 0xAE, 0x09, 0xE8, 0x13, 0x89, 0x0E, 0x27, 0xEA, 0x53, 0x46, 0x3B, 0x4F, 0xEC, 0xFA, 0x87, 0x4B, 0xB6, 0x38, 0xA7, 0xBB, 0x7E, 0x34, 0x42, 0xF1, 0xB3, 0x77, 0xC1, 0x89, 0x43, 0xD6, 0xB9, 0x3D, 0x2A, 0x5A, 0x70, 0x1A, 0x02, 0x2B, 0xBC, 0xAE, 0xCB, 0x95, 0x27, 0xEE, 0x89, 0x4E, 0x6D, 0x28, 0x92, 0xF4, 0xD3, 0x12, 0x72, 0x08, 0x79, 0x08, 0x66, 0x96, 0x63, 0x46, 0xF0, 0xF5, 0x50, 0xBC, 0xA7, 0x88, 0xC1, 0x04, 0x9C, 0xDC, 0x2A, 0xC3, 0xA1, 0x0F, 0xE8, 0xF1, 0xA6, 0xC1, 0xB3, 0x41, 0xE6, 0x08, 0x4F, 0x00, 0x00, 0x8E, 0x58, 0xE8, 0x55, 0x27, 0x9C, 0x14, 0x8E, 0x63, 0xD0, 0x14, 0xE6, 0x2E, 0xE2, 0x63, 0x39, 0x54, 0xD3, 0x59, 0xA3, 0xAC, 0xE6, 0xB1, 0x5E, 0xA7, 0x90, 0xAF, 0xCB, 0x1A, 0xF3, 0xE3, 0xEF, 0xD2, 0x0B, 0xF0, 0xB8, 0x75, 0x1D, 0x66, 0x90, 0xFE, 0xE3, 0x6A, 0x80, 0x71, 0xC5, 0xB2, 0x48, 0x14, 0x9F, 0x37, 0x25, 0xCF, 0xBE, 0x55, 0xDD, 0xE8, 0xFE, 0xA0, 0x65, 0x38, 0x42, 0x3E, 0xB7, 0x78, 0x05, 0x26, 0x35, 0x8D, 0x25, 0xCC, 0x04, 0x5D, 0xEC, 0x13, 0x29, 0x0A, 0x53, 0x2D, 0x42, 0x05, 0x56, 0xD7, 0xE6, 0x32, 0x2C, 0x27, 0x3A, 0x0E, 0xCD, 0x1E, 0x8C, 0x9F, 0x8F, 0x2C, 0xDF, 0x83, 0xCC, 0xF3, 0x6B, 0xD0, 0x22, 0x10, 0xA0, 0x71, 0x54, 0xE0, 0x1B, 0x00, 0x19, 0x3E, 0x7D, 0x13, 0x37, 0x09, 0x4F, 0x7D, 0xC8, 0x2A, 0x87, 0x45, 0x13, 0x57, 0x71, 0x0E, 0xDF, 0x11, 0x5B, 0x16, 0x7F, 0xDF, 0xF2, 0xB2, 0x4A, 0x00, 0x70, 0x0C, 0xB8, 0x84, 0xF2, 0x63, 0x5C, 0xC8, 0x4F, 0x00, 0x00, 0x99, 0x58, 0xE9, 0xB2, 0x76, 0x69, 0xF5, 0x4D, 0x9A, 0x5D, 0x14, 0xD7, 0xCE, 0x4F, 0x52, 0x74, 0xA4, 0x0D, 0x99, 0x0C, 0xA6, 0x00, 0x1D, 0x91, 0xE7, 0x91, 0x9E, 0x9E, 0xD0, 0x44, 0x04, 0xF6, 0xBE, 0xEE, 0xEB, 0x46, 0x4D, 0x3D, 0x2F, 0x6F, 0xCA, 0x37, 0x8C, 0x1C, 0x39, 0x87, 0x4E, 0x7D, 0x40, 0xD2, 0x66, 0xFB, 0xEB, 0x32, 0x35, 0xD9, 0xA8, 0x1A, 0x44, 0xD8, 0x73, 0x49, 0x5D, 0x7A, 0x34, 0x6E, 0x81, 0x0F, 0x95, 0x6F, 0xB2, 0x1A, 0x2B, 0x63, 0x97, 0x62, 0x89, 0x40, 0x34, 0x05, 0xA5, 0x06, 0x98, 0x09, 0x62, 0xE3, 0x51, 0x01, 0xD2, 0x9A, 0xD8, 0x0E, 0xEB, 0xCC, 0xF3, 0xEC, 0xB3, 0x0A, 0x52, 0xDA, 0x8B, 0xC3, 0xBE, 0xDD, 0x2F, 0xC9, 0xA5, 0xFA, 0x58, 0x4B, 0xDA, 0x8F, 0xC0, 0xFE, 0xE6, 0x98, 0x32, 0xB9, 0xE0, 0xBE, 0x71, 0x06, 0xA7, 0x1E, 0xFE, 0xA0, 0x31, 0x84, 0x3F, 0x3E, 0x22, 0x2A, 0x60, 0x3E, 0x0D, 0x9A, 0x24, 0x30, 0xC5, 0x93, 0xCC, 0x7A, 0x89, 0xDD, 0x83, 0x84, 0x9C, 0xDE, 0xA4, 0x29, 0xC7, 0x76, 0x7B, 0xD7, 0x4F, 0x00, 0x00, 0x86, 0x58, 0xEC, 0xF7, 0xE2, 0x2E, 0x2A, 0x36, 0x12, 0xB9, 0xA8, 0x96, 0x08, 0x6D, 0x89, 0xDE, 0xDD, 0xA3, 0xB3, 0x3C, 0x3D, 0x25, 0x0A, 0xB8, 0xB7, 0xAC, 0x23, 0xCC, 0x34, 0xA0, 0xE1, 0x14, 0x7A, 0xAD, 0x02, 0x68, 0xDF, 0x77, 0x10, 0x8A, 0xB6, 0x24, 0xA4, 0x0C, 0xA7, 0x1E, 0x7B, 0x1D, 0x56, 0x72, 0xC8, 0xB4, 0x52, 0xBE, 0x04, 0x0B, 0x3E, 0xA0, 0x10, 0x4D, 0xAF, 0x0A, 0xB6, 0x59, 0x70, 0xF4, 0xBE, 0x88, 0x76, 0x41, 0x4E, 0xB8, 0xA1, 0x6A, 0x74, 0x7D, 0xC4, 0xB9, 0xC2, 0x13, 0x81, 0x46, 0x24, 0x81, 0xAE, 0x34, 0x86, 0x08, 0xDB, 0x89, 0xCF, 0x7A, 0xDF, 0xFC, 0xB4, 0x42, 0x5D, 0x97, 0xBE, 0x56, 0x37, 0x08, 0x04, 0xFA, 0x98, 0x04, 0xCB, 0x2C, 0x4B, 0xC5, 0x53, 0xAD, 0x5D, 0xFB, 0xB7, 0xC4, 0x55, 0xF8, 0x23, 0xAC, 0x69, 0x82, 0xCD, 0x7A, 0x82, 0x8E, 0x15, 0xB2, 0x64, 0xA3, 0x0B, 0xBA, 0x1A, 0xB0, 0x80, 0x4F, 0x00, 0x00, 0x73, 0x58, 0xC9, 0x25, 0xC0, 0xB7, 0xCA, 0xB6, 0x6D, 0x3F, 0x4A, 0x83, 0x4E, 0xDE, 0xB3, 0x2A, 0x34, 0x35, 0x84, 0x4D, 0xBD, 0x10, 0x65, 0xD1, 0x28, 0x72, 0x90, 0x53, 0xF0, 0x88, 0xC6, 0xAF, 0xD6, 0x5B, 0x90, 0xE1, 0x4B, 0xB7, 0x4D, 0xF6, 0xA9, 0x24, 0x42, 0x95, 0xD6, 0xBC, 0x0D, 0xEC, 0x61, 0x93, 0xB2, 0x6C, 0xDB, 0x30, 0xD5, 0x17, 0x7B, 0x5E, 0x57, 0x93, 0xEE, 0xC8, 0xCA, 0xCE, 0x1D, 0xAC, 0x5B, 0x87, 0x72, 0x0D, 0x6D, 0x3E, 0xDE, 0x04, 0xDE, 0x2B, 0x3D, 0xEA, 0xD1, 0x0A, 0xA3, 0xF4, 0x98, 0xE2, 0xBB, 0xD2, 0xE9, 0x25, 0xB6, 0xD6, 0x93, 0x04, 0xAA, 0x2E, 0xD6, 0xA0, 0x2A, 0x3B, 0xFE, 0x18, 0xB7, 0x89, 0x83, 0xEB, 0xCA, 0xFF, 0x3D, 0xBF, 0xCB, 0xBD, 0xA1, 0x6B, 0x51, 0xCD, 0x99, 0x18, 0x4F, 0x00, 0x00, 0x1F, 0x58, 0x02, 0xFE, 0xC3, 0x7B, 0x28, 0x06, 0x31, 0xF8, 0xD0, 0xD3, 0xC6, 0x55, 0xEA, 0x2F, 0x04, 0x59, 0xA2, 0xD8, 0x27, 0x09, 0xB4, 0xCB, 0x14, 0x89, 0x44, 0x29, 0x4B, 0xB0, 0xF1, 0x6F
};

#endif // AUDIO_DATA_5_H