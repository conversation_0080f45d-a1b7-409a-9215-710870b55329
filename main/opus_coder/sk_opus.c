/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_opus.c
 * @description: OPUS操作管理任务, 完成OPUS编码和解码的调度.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <string.h>
#include <esp_log.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/queue.h>
#include <arpa/inet.h>
#include "opus.h"
#include "sk_ring_buf.h"
#include "sk_opus_enc.h"
#include "sk_opus_dec.h"
#include "sk_common.h"
#include "sk_opus_inner.h"
#include "sk_board.h"

#define TAG "SkOpus"

#define SK_EVENT_GROUP_ENC_START (1 << 0)
#define SK_EVENT_GROUP_DEC_START (1 << 1)

typedef struct {
    EventGroupHandle_t eventGroup;
    QueueHandle_t msgQueue;
    SkOpusEncHandler encHandler;
    SkOpusDecHandler decHandler;
    TaskHandle_t taskHandle;
    bool taskFlag;
    bool pmMode;
    uint32_t procCnt;
    uint32_t noMsgCnt;
} SkOpusCtrl;

void SkCodecMsgProc(SkOpusCtrl *ctrl, TickType_t ticks);

SkOpusCtrl g_skOPusCtrl;

void SkOpusTask(void *arg) {
    SkOpusCtrl *ctrl = (SkOpusCtrl *)arg;
    bool decReady, encReady;
    TickType_t ticksWait, ticks;

    ESP_LOGI(TAG, "Opus Dec task on.");
    decReady = false;
    encReady = false;
    ticksWait = pdMS_TO_TICKS(100);
    while(ctrl->taskFlag) {
        xEventGroupWaitBits(ctrl->eventGroup,
            SK_EVENT_GROUP_ENC_START | SK_EVENT_GROUP_DEC_START,
            pdFALSE, pdFALSE, portMAX_DELAY);
        // 处理消息队列
        if (decReady || encReady) {
            ticks = 0;
        } else {
            ticks = ticksWait;
        }
        SkCodecMsgProc(ctrl, ticks);
        
        // 处理播放队列
        decReady = SkOpusDecProc(ctrl->decHandler);
        // 处理录音队列
        encReady = SkOpusEncProc(ctrl->encHandler);
    }
}

void SkCodecMsgProc(SkOpusCtrl *ctrl, TickType_t ticks) {
    SkOpusMsg msg;

    if (xQueueReceive(ctrl->msgQueue, &msg, ticks) != pdPASS) {
        ctrl->noMsgCnt++;
        return;
    }

    ctrl->procCnt++;
    switch (msg.event) {
        case SK_ENC_EVENT_PCM:
            SkOpusEncPutAudioIntoQueue(ctrl->encHandler, (SkAudioBuf *)msg.arg);
            break;
        case SK_ENC_EVENT_PKT:
            SkOpusEncSetLocal(ctrl->encHandler, msg.param1);
            break;
        case SK_OPUS_EVENT_PLAY_LOCAL:
            SkOpusDecStartLocalPlay(ctrl->decHandler);
            break;
        case SK_OPUS_EVENT_REMOTE_LONG:
        case SK_OPUS_EVENT_REMOTE_SHORT:
            SkOpusDecPutDataIntoQueue(ctrl->decHandler, (SkAudioBuf *)msg.arg);
            break;
        case SK_OPUS_EVENT_REMOTE_END:
            SkOpusDecPutDataIntoQueue(ctrl->decHandler, (SkAudioBuf *)msg.arg);
            break;
        case SK_OPUS_EVENT_MUTE_REMOTE:
            SkOpusDecEnableRemote(false);
            break;
        case SK_OPUS_EVENT_UNMUTE_REMOTE:
            SkOpusDecEnableRemote(true);
            break;
        case SK_OPUS_EVENT_DEC_OUTPUT:
            break;
        default:
            ESP_LOGE(TAG, "Unknown message type: %d", msg.event);
            break;
    }
    return;
}

void SkOpusStartTask() {
    esp_err_t err;
    SkOpusCtrl *ctrl = &g_skOPusCtrl;

    ctrl->taskFlag = true;
    ctrl->pmMode = false;
    err = xTaskCreate(SkOpusTask, "SkOpusEncDec", 32768, ctrl, 5, &ctrl->taskHandle);
    ESP_LOGI(TAG, "stack base %p", pxTaskGetStackStart(ctrl->taskHandle));
    if (err != pdPASS) {
        ESP_LOGE(TAG, "Create task failed: %d", err);
        return;
    }
}

void SkOpusInit(int sampleRate, int channels, int durationMs) {
    SkOpusCtrl *ctrl = &g_skOPusCtrl;

    ctrl->msgQueue = xQueueCreate(32, sizeof(SkOpusMsg));
    ctrl->eventGroup = xEventGroupCreate();
    ctrl->encHandler = SkOpusEncInit(sampleRate, channels, durationMs, ctrl->msgQueue);
    ctrl->decHandler = SkOpusDecInit(sampleRate, channels, durationMs, ctrl->msgQueue);

    SkOpusStartTask();
    xEventGroupSetBits(ctrl->eventGroup, SK_EVENT_GROUP_ENC_START | SK_EVENT_GROUP_DEC_START);

    return;
}

void SkOpusTaskShowStat() {
    SkOpusCtrl *ctrl = &g_skOPusCtrl;
    ESP_LOGI(TAG, "Opus task status: %d, procMsgCnt: %d, noMsgCnt: %d",
        ctrl->taskFlag, ctrl->procCnt, ctrl->noMsgCnt);
}

void SkOpusSetPm(bool flag) {
    SkOpusCtrl *ctrl = &g_skOPusCtrl;

    ctrl->pmMode = flag;
#if (CONFIG_SK_PM_SLEEP_MODE == CONFIG_SK_PM_SLEEP_MODE_LIGHT2)
    ctrl->taskFlag = !flag;
#endif

}