#ifndef AUDIO_DATA_2_H
#define AUDIO_DATA_2_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfo2[] = {0, 24, 49, 116, 225, 320, 434, 567, 641};
const uint8_t g_audioData2[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x3F, 0x58, 0x61, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1E, 0xEC, 0xE7, 0x90, 0xB3, 0x0A, 0x6D, 0x20, 0x06, 0xAE, 0x83, 0x95, 0x64, 0x92, 0xB3, 0x60, 0xD8, 0x90, 0x85, 0x7F, 0x38, 0xFF, 0x03, 0xC9, 0x10, 0x73, 0x14, 0xF6, 0xCB, 0xD8, 0x49, 0xDC, 0xAF, 0x82, 0x29, 0x0B, 0x5F, 0x87, 0x90, 0xAD, 0xFF, 0x2D, 0x4E, 0xD6, 0x9F, 0x2A, 0x0D, 0x44, 0x04, 0x8C, 0x7A, 0xEA, 0xB3, 0xC4, 0x1B, 0xF0, 0x4F, 0x00, 0x00, 0x69, 0x58, 0xED, 0xCE, 0x18, 0x8D, 0x4A, 0xB8, 0xCE, 0xE6, 0x80, 0xB5, 0x8A, 0x2A, 0xE1, 0xB5, 0xE1, 0xE0, 0x31, 0x82, 0x6B, 0xC9, 0x6F, 0x23, 0x74, 0xF2, 0x3F, 0x6D, 0xD1, 0x4C, 0x1C, 0x84, 0x68, 0x13, 0x46, 0xBE, 0x02, 0x9D, 0xDD, 0xEA, 0xEE, 0x37, 0x54, 0x9E, 0xB8, 0x03, 0xC3, 0xD5, 0x85, 0x96, 0xC8, 0xA8, 0xF3, 0x97, 0x17, 0xB8, 0x98, 0xE8, 0x65, 0xF6, 0xB8, 0x69, 0xE8, 0x0E, 0x2E, 0xDF, 0xE2, 0xB2, 0xFE, 0x9C, 0x29, 0x39, 0xBA, 0x08, 0xC0, 0xEB, 0x61, 0xEE, 0x43, 0xCD, 0x2B, 0x1C, 0xC2, 0x0F, 0x52, 0x50, 0xB8, 0xFA, 0x83, 0x78, 0xFC, 0xB8, 0x96, 0x41, 0x02, 0x66, 0x63, 0x3A, 0x92, 0xC8, 0xD2, 0xCC, 0xE3, 0xEB, 0x33, 0x80, 0x4F, 0x00, 0x00, 0x5B, 0x58, 0xED, 0xBC, 0x63, 0x80, 0xBE, 0x62, 0x4C, 0xC4, 0xFC, 0xE2, 0xEA, 0x51, 0x02, 0xC9, 0xA8, 0xD7, 0x82, 0x4F, 0xD0, 0x11, 0x9A, 0xDB, 0xF5, 0x9A, 0x76, 0xE4, 0x45, 0x25, 0x46, 0x6F, 0xCA, 0xA8, 0x0E, 0xEC, 0xFE, 0x5A, 0xC2, 0x56, 0x9B, 0x1B, 0x7D, 0xEE, 0x4F, 0x2C, 0xD7, 0x06, 0x94, 0xFB, 0x97, 0xF7, 0xC3, 0x58, 0x89, 0xB1, 0x3A, 0x83, 0xFD, 0xFB, 0x14, 0x11, 0xBA, 0x37, 0x33, 0x59, 0xA7, 0xF7, 0x9D, 0xF9, 0x47, 0xF6, 0x97, 0x02, 0x11, 0x5C, 0xA8, 0x20, 0xAF, 0x40, 0xBA, 0xFF, 0xF8, 0x3F, 0xA2, 0x28, 0xBC, 0x34, 0x16, 0x4C, 0x79, 0x3B, 0x4F, 0x00, 0x00, 0x6E, 0x58, 0xED, 0x3C, 0xD8, 0x26, 0xC0, 0xE0, 0xDD, 0xDB, 0x3A, 0x0F, 0x7F, 0xFE, 0x58, 0x95, 0xFC, 0xB2, 0x44, 0x13, 0x6A, 0xE9, 0x5F, 0xE0, 0x97, 0x7D, 0x10, 0x82, 0x49, 0xFF, 0x6B, 0x7F, 0x04, 0x39, 0x57, 0xE1, 0xDB, 0xD8, 0xD0, 0xBE, 0x5B, 0xB5, 0x6C, 0x71, 0x62, 0xF9, 0x13, 0xBB, 0x8F, 0xB3, 0x71, 0xB0, 0xDB, 0x57, 0xE7, 0x0D, 0xF4, 0xC2, 0xAA, 0x3C, 0xFF, 0x4E, 0xF9, 0xC0, 0x46, 0xE2, 0x30, 0x92, 0x33, 0x24, 0x2E, 0x96, 0x64, 0x94, 0xA4, 0x28, 0x3F, 0x93, 0xD3, 0x56, 0xE4, 0xB1, 0x48, 0xB9, 0xC6, 0x7C, 0x9F, 0x3C, 0x96, 0xA4, 0xC2, 0x07, 0x5D, 0xA5, 0xFA, 0x33, 0xA6, 0x9A, 0x96, 0xDA, 0x66, 0xFF, 0xFC, 0xC8, 0x2E, 0x68, 0x44, 0x01, 0x5D, 0x74, 0x88, 0x4F, 0x00, 0x00, 0x81, 0x58, 0xEA, 0xAA, 0x6C, 0xF5, 0xA5, 0x69, 0x85, 0x6A, 0xD3, 0x31, 0x45, 0xD9, 0x1F, 0xA0, 0xC0, 0x32, 0x65, 0xC3, 0x91, 0x87, 0x68, 0x34, 0x50, 0x18, 0x24, 0x1D, 0x29, 0x0A, 0xA4, 0x34, 0xAD, 0x89, 0x6D, 0x3B, 0xEB, 0xB9, 0x2A, 0xCD, 0x09, 0x31, 0x2E, 0x99, 0xE6, 0xB1, 0x60, 0xA2, 0x91, 0x8E, 0x6A, 0x01, 0xBA, 0xAA, 0x5A, 0xD0, 0xD8, 0xC7, 0xA5, 0x40, 0x66, 0x5C, 0xD3, 0xF8, 0xE2, 0xCB, 0x0D, 0x28, 0xA6, 0xAD, 0x0F, 0x72, 0x24, 0x1B, 0x5E, 0x64, 0xE0, 0xFF, 0xBF, 0x23, 0x45, 0xA0, 0xC1, 0x93, 0xB7, 0xE5, 0x89, 0x05, 0x76, 0x0E, 0xFE, 0x1E, 0xC9, 0x92, 0xA2, 0x5B, 0x15, 0xD9, 0xE9, 0x6D, 0x85, 0xB1, 0xB8, 0x61, 0x6C, 0x18, 0x60, 0xB8, 0xEF, 0x4E, 0x83, 0xA7, 0x48, 0x3A, 0xA0, 0x5B, 0xD5, 0x8B, 0x10, 0x24, 0x1B, 0x13, 0xAE, 0xF1, 0xB0, 0xC1, 0xD4, 0x96, 0xAD, 0x35, 0x4F, 0x00, 0x00, 0x46, 0x58, 0x08, 0x2C, 0x93, 0x68, 0x41, 0xC6, 0xD2, 0xA5, 0xB2, 0x61, 0xF0, 0xAE, 0x46, 0x30, 0x3D, 0x82, 0xD9, 0x9B, 0x0E, 0x8D, 0x10, 0x54, 0xAD, 0xF5, 0x8F, 0xAD, 0xF9, 0xBA, 0x3F, 0x36, 0x89, 0xD2, 0xD8, 0xE5, 0xB4, 0xF2, 0x14, 0xCA, 0xFC, 0x1F, 0x74, 0xBE, 0x8D, 0x29, 0xD3, 0xFC, 0x7D, 0xD1, 0x98, 0x9E, 0x6D, 0x66, 0x99, 0x17, 0x1D, 0xB4, 0xA1, 0x0E, 0x31, 0x30, 0x94, 0x84, 0x8A, 0xA0, 0x67, 0x2F, 0xEA, 0x66, 0x80, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20
};

#endif // AUDIO_DATA_2_H