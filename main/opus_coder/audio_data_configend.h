#ifndef AUDIO_DATA_CONFIGEND_H
#define AUDIO_DATA_CONFIGEND_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfoConfigEnd[] = {0, 24, 49, 113, 256, 382, 504, 646, 775, 906, 1042, 1176, 1300, 1429, 1567, 1689, 1813, 1965, 2103, 2179};
const uint8_t g_audioDataConfigEnd[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x3C, 0x58, 0x61, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1E, 0xD7, 0xBE, 0x1D, 0xB9, 0xEF, 0x55, 0x78, 0x07, 0x8C, 0xF4, 0x23, 0x18, 0x48, 0xD1, 0xF4, 0x6E, 0x34, 0xF2, 0x14, 0x39, 0x7E, 0x5A, 0x68, 0x29, 0xB5, 0xE4, 0xE5, 0xB5, 0x4D, 0x00, 0x66, 0x56, 0xB2, 0xF0, 0x7A, 0x3E, 0x7B, 0xD6, 0x1B, 0x18, 0x0F, 0x47, 0x75, 0x8A, 0x1A, 0xB3, 0x77, 0x43, 0x53, 0x9B, 0x14, 0xD0, 0x4F, 0x00, 0x00, 0x8B, 0x58, 0xE1, 0x08, 0xB9, 0xAE, 0x19, 0xAB, 0xED, 0x20, 0x40, 0x11, 0x3B, 0x4B, 0x6E, 0x15, 0x8A, 0x48, 0xD2, 0x5D, 0x6C, 0x39, 0x31, 0xB9, 0x90, 0xFB, 0x94, 0x49, 0x82, 0x82, 0x90, 0xD2, 0x16, 0xA1, 0x38, 0xAD, 0x55, 0xFC, 0xF3, 0x4C, 0x6C, 0xDA, 0x42, 0x01, 0x08, 0x42, 0x83, 0x6B, 0x79, 0xB5, 0x86, 0xB6, 0xBD, 0x6F, 0xAE, 0x35, 0x1E, 0xE5, 0xB5, 0x83, 0xB5, 0x88, 0xC5, 0x58, 0x0F, 0xF4, 0x0E, 0x2C, 0x6E, 0x16, 0x0A, 0x0C, 0x3C, 0xA8, 0xE0, 0x48, 0x0E, 0x21, 0xA8, 0x61, 0x7D, 0x31, 0x31, 0xF9, 0x86, 0x8C, 0xEC, 0x5D, 0x44, 0x5E, 0x53, 0xEB, 0x1E, 0x55, 0x92, 0x8F, 0x9C, 0x60, 0xE0, 0x4D, 0xD3, 0x71, 0xF2, 0x4B, 0x43, 0x71, 0x16, 0x56, 0x28, 0xC0, 0xEE, 0x3C, 0xDE, 0x21, 0xF1, 0x77, 0x28, 0x68, 0x94, 0xE3, 0xB6, 0x56, 0xA2, 0x72, 0x4E, 0xFD, 0x3B, 0x90, 0xB3, 0xF6, 0xC9, 0x0D, 0x3F, 0xB2, 0xAC, 0x60, 0x57, 0x42, 0xE9, 0xC0, 0x4F, 0x00, 0x00, 0x7A, 0x58, 0xEE, 0x2B, 0x75, 0x6E, 0xA4, 0x4A, 0xB5, 0x52, 0xE2, 0xCC, 0x26, 0x9F, 0x5C, 0xCF, 0xBA, 0x13, 0x2C, 0x93, 0x15, 0x65, 0xD7, 0xA6, 0x7F, 0x8F, 0x37, 0xBB, 0x85, 0x06, 0x38, 0xDB, 0xEA, 0xD4, 0x96, 0x7D, 0x3A, 0xD3, 0x7C, 0xED, 0x00, 0xA7, 0xF4, 0x3D, 0x89, 0x39, 0xCC, 0xD1, 0x0A, 0x55, 0x49, 0x30, 0xAB, 0xD2, 0x5B, 0x3B, 0x15, 0xD9, 0x72, 0x0F, 0x92, 0x3C, 0x1C, 0x76, 0x40, 0xE9, 0xFB, 0x17, 0xD8, 0x43, 0x63, 0xD4, 0xB0, 0x1A, 0xD6, 0x08, 0xD0, 0x12, 0x52, 0xB6, 0x0A, 0xBD, 0x9D, 0xAF, 0x42, 0xB0, 0xB3, 0xC6, 0x82, 0xB9, 0x79, 0xFA, 0x64, 0xC2, 0xD2, 0xB4, 0x03, 0xB9, 0xF9, 0x7F, 0xC4, 0x0B, 0x7C, 0x09, 0x96, 0x1A, 0x4D, 0x88, 0xA4, 0x9B, 0x23, 0xB5, 0x54, 0xC7, 0x4F, 0xDD, 0xA4, 0xD4, 0xFA, 0x4C, 0x7A, 0x58, 0x64, 0x4F, 0x00, 0x00, 0x76, 0x58, 0xED, 0xE9, 0xBD, 0x1B, 0x85, 0xE2, 0x48, 0xE4, 0xF0, 0xFC, 0x5B, 0x08, 0x01, 0x97, 0xF8, 0x9A, 0xA4, 0x3E, 0x04, 0x2A, 0x13, 0x05, 0x05, 0x4E, 0x8D, 0x37, 0xCC, 0xF0, 0x61, 0x12, 0x33, 0xC6, 0xBD, 0x05, 0x1B, 0xED, 0xD4, 0x9B, 0xF2, 0x88, 0xBF, 0xE6, 0x98, 0x56, 0x03, 0x45, 0xC3, 0xC3, 0x9A, 0xFC, 0xE9, 0x4D, 0xA2, 0x86, 0x23, 0x85, 0x71, 0xDD, 0x70, 0x4E, 0x0E, 0x90, 0x50, 0x93, 0x84, 0x5B, 0x61, 0x27, 0x14, 0x7D, 0xB1, 0x25, 0x1C, 0x6B, 0x5D, 0xC9, 0xA8, 0xDF, 0xBB, 0x09, 0x81, 0xE0, 0x63, 0xA9, 0xA2, 0x66, 0x72, 0xCB, 0x58, 0x8A, 0x8D, 0x03, 0xE6, 0x14, 0x49, 0x41, 0xC2, 0x86, 0x09, 0x10, 0x54, 0x6C, 0xF9, 0x8C, 0x08, 0xD9, 0x49, 0x45, 0x36, 0xBF, 0x98, 0x32, 0x62, 0xC6, 0x09, 0x65, 0x44, 0x4F, 0x00, 0x00, 0x8A, 0x58, 0xEB, 0x10, 0xFB, 0xD3, 0x42, 0xB0, 0x79, 0x6F, 0xAE, 0x5F, 0x75, 0xBB, 0x66, 0x72, 0x0D, 0x7E, 0x09, 0x26, 0x21, 0xC0, 0x66, 0xC3, 0x6A, 0x3C, 0x56, 0xA5, 0x83, 0x94, 0x93, 0x21, 0xF3, 0x1C, 0xF2, 0x7F, 0x2C, 0x6C, 0x67, 0x1E, 0x01, 0x39, 0x8E, 0xA3, 0xDE, 0x25, 0x34, 0x94, 0x80, 0xB4, 0x30, 0x8E, 0x75, 0xCC, 0x8D, 0xEA, 0xFC, 0xAD, 0x5A, 0x62, 0x14, 0x77, 0x92, 0x76, 0x8B, 0x1D, 0x55, 0xE0, 0xD1, 0x15, 0x4E, 0x9A, 0x19, 0x98, 0x0B, 0x53, 0x46, 0x83, 0x08, 0xF2, 0x64, 0x4F, 0xF9, 0x24, 0x65, 0x86, 0x78, 0x86, 0xA0, 0x79, 0xE5, 0x0D, 0xA4, 0x81, 0x12, 0xF6, 0x03, 0x57, 0x42, 0x8F, 0xD6, 0xB0, 0x83, 0x99, 0x1C, 0x94, 0x39, 0xEC, 0xB8, 0xE6, 0x63, 0xE1, 0xF2, 0x4C, 0xB0, 0x16, 0xD3, 0xDF, 0xCF, 0x66, 0x02, 0x80, 0xE5, 0x06, 0xAC, 0x17, 0xFA, 0x0C, 0x1C, 0xDE, 0x26, 0xEF, 0x49, 0x25, 0x10, 0xCF, 0xED, 0x0F, 0x80, 0x4F, 0x00, 0x00, 0x7D, 0x58, 0xE1, 0x57, 0xC8, 0xA4, 0xFF, 0xF2, 0x6C, 0x03, 0x09, 0x73, 0x2D, 0xEE, 0xA4, 0xDB, 0xC6, 0xD8, 0xEA, 0xAD, 0x73, 0x0D, 0xDE, 0x5F, 0x36, 0xB3, 0xAB, 0xC2, 0xFF, 0xE1, 0x7D, 0x14, 0x0C, 0x2F, 0x85, 0xFF, 0x8A, 0xAE, 0xDE, 0x1E, 0x48, 0xE0, 0x29, 0xAF, 0x40, 0x2E, 0x96, 0x47, 0x93, 0xBA, 0x43, 0xCA, 0x33, 0x75, 0xB2, 0x73, 0x62, 0x09, 0x92, 0xE8, 0x8D, 0x3B, 0x72, 0xEC, 0x8C, 0x79, 0x10, 0xF0, 0xBE, 0x8A, 0x6D, 0x58, 0xE5, 0xC7, 0xAE, 0x87, 0x0C, 0x4B, 0xB1, 0x66, 0x5A, 0xDB, 0x14, 0xE9, 0x27, 0x33, 0xD6, 0xEE, 0xAC, 0xC0, 0xF4, 0xFA, 0xE7, 0x20, 0x62, 0x1B, 0x40, 0x83, 0xC6, 0xC7, 0xEA, 0x93, 0x76, 0xF8, 0x4B, 0x27, 0x01, 0x1C, 0x22, 0x91, 0x60, 0x47, 0xB4, 0x09, 0x48, 0x4D, 0xB9, 0x6A, 0x90, 0x86, 0x65, 0xA8, 0x39, 0x31, 0x55, 0x60, 0x4F, 0x00, 0x00, 0x7F, 0x58, 0xED, 0x6B, 0x51, 0xD7, 0x05, 0x98, 0xFB, 0x37, 0x1D, 0x8D, 0xEB, 0xC8, 0x43, 0x6C, 0x5A, 0xBF, 0x52, 0xB1, 0x79, 0xB7, 0x61, 0x78, 0xEE, 0xBD, 0x0C, 0xBA, 0x1A, 0x24, 0xA7, 0xC3, 0x08, 0x60, 0x74, 0x2B, 0xAB, 0x00, 0xFA, 0x66, 0xC9, 0x69, 0x0C, 0x70, 0x39, 0xD5, 0x15, 0x89, 0xBF, 0xA1, 0x22, 0xCC, 0x24, 0x9C, 0xB1, 0x38, 0x4D, 0xC5, 0xD3, 0x29, 0xAE, 0x89, 0x00, 0xCE, 0x1B, 0xE2, 0x25, 0x33, 0x7A, 0xC1, 0xD3, 0x4B, 0xF5, 0x69, 0x7A, 0xD3, 0x31, 0x75, 0x7C, 0xDB, 0x70, 0x2E, 0x1E, 0xD5, 0xBB, 0x15, 0x9E, 0xDF, 0x3C, 0x95, 0x12, 0xD9, 0xE6, 0x6C, 0x51, 0x5D, 0xEF, 0x9B, 0x09, 0xE2, 0x8D, 0x18, 0x35, 0x65, 0x56, 0xEB, 0x6B, 0xD5, 0xA6, 0x15, 0x38, 0xF8, 0xC3, 0x1C, 0x16, 0xF2, 0x6F, 0x78, 0xAD, 0x4A, 0xD5, 0x5F, 0xE7, 0xE7, 0x52, 0xFB, 0xCE, 0xE0, 0x4F, 0x00, 0x00, 0x84, 0x58, 0xEA, 0xA9, 0x48, 0xA5, 0xD1, 0xD5, 0xA0, 0x0F, 0x43, 0xB0, 0xE6, 0x3A, 0xFC, 0x97, 0xE5, 0xAD, 0x08, 0x33, 0xBD, 0x86, 0x29, 0x40, 0xF7, 0x4B, 0x97, 0x91, 0x7B, 0xF6, 0x22, 0xD9, 0x58, 0x01, 0x41, 0x70, 0x3E, 0x0A, 0xF7, 0x4C, 0xCD, 0x24, 0x78, 0x1D, 0x76, 0x7A, 0x3A, 0xCD, 0x16, 0xAE, 0xCB, 0xB2, 0x08, 0x38, 0x02, 0x25, 0xB6, 0x1D, 0x46, 0xBB, 0x91, 0xDF, 0xB0, 0x6E, 0xCA, 0x0F, 0x68, 0x85, 0x27, 0x94, 0x5F, 0x30, 0xE0, 0x25, 0xD1, 0xE3, 0x1C, 0x1C, 0x2D, 0x5F, 0x97, 0x37, 0x0B, 0x94, 0x83, 0x1C, 0x12, 0x56, 0x4F, 0x84, 0x47, 0x46, 0x3C, 0xD0, 0x1D, 0xF3, 0x4A, 0xCC, 0x52, 0x21, 0x7E, 0x52, 0x46, 0xA9, 0x82, 0xCB, 0x75, 0x13, 0xE0, 0xE6, 0x7D, 0x18, 0xEE, 0x3E, 0x42, 0xEE, 0x6E, 0x69, 0x14, 0xDC, 0xA5, 0x58, 0xEA, 0x64, 0x1C, 0x21, 0x95, 0x9A, 0x87, 0x18, 0x23, 0x42, 0x62, 0x4F, 0x00, 0x00, 0x82, 0x58, 0xE1, 0x14, 0x81, 0xED, 0xEA, 0x04, 0xB8, 0x98, 0x90, 0xF2, 0xFE, 0x41, 0x7A, 0x9F, 0x6D, 0x4C, 0x50, 0x41, 0x94, 0x43, 0x59, 0xBD, 0x52, 0xED, 0xD0, 0x44, 0x5F, 0xCB, 0x4C, 0x2A, 0x1F, 0x78, 0xF1, 0x50, 0x63, 0xB1, 0x7D, 0x5F, 0x33, 0xC6, 0x5A, 0xA5, 0xE7, 0xF6, 0xF7, 0xDE, 0xBC, 0x32, 0x8C, 0x75, 0x7C, 0xE1, 0xB4, 0xC1, 0xBB, 0xCF, 0x0C, 0xBA, 0x7E, 0x0F, 0x69, 0x05, 0xA6, 0x5F, 0x60, 0xE3, 0x44, 0x64, 0x7B, 0xE6, 0xBA, 0x64, 0xF9, 0xEF, 0xAD, 0x57, 0x58, 0x46, 0x4D, 0x7D, 0x18, 0xE3, 0x0F, 0x3B, 0x96, 0x56, 0x02, 0xE3, 0x2F, 0x27, 0x72, 0x76, 0xD1, 0xE6, 0x83, 0xA6, 0x84, 0xAE, 0xC6, 0x4A, 0x67, 0x10, 0xFA, 0x48, 0x9B, 0xEC, 0xC3, 0x27, 0x42, 0x25, 0xA7, 0x02, 0x24, 0x78, 0x59, 0x27, 0xE6, 0xFE, 0x5D, 0xB6, 0x8C, 0xAF, 0x0E, 0xCE, 0x9F, 0x83, 0xA7, 0x42, 0x40, 0x4F, 0x00, 0x00, 0x78, 0x58, 0xED, 0x76, 0x32, 0xCB, 0x7E, 0xDD, 0x93, 0x7A, 0x7A, 0x8C, 0x58, 0x85, 0xE0, 0x5D, 0x18, 0x13, 0x1C, 0x77, 0xC0, 0x72, 0x9E, 0xDD, 0x44, 0x89, 0xFD, 0x38, 0xCB, 0x96, 0x35, 0x2D, 0xA2, 0x21, 0x62, 0xDB, 0x2D, 0x1E, 0x88, 0x71, 0xC5, 0xA1, 0xCA, 0xEC, 0xD7, 0x50, 0x92, 0x50, 0x6A, 0x8B, 0x37, 0xFF, 0x96, 0x62, 0xF6, 0xF3, 0x97, 0xD3, 0x53, 0xAF, 0x02, 0xAF, 0x81, 0x94, 0xAD, 0x8B, 0x2E, 0x20, 0xD6, 0xB7, 0x74, 0x6C, 0x20, 0x6E, 0xC9, 0xA6, 0x1E, 0xD8, 0x3A, 0x68, 0x9C, 0x13, 0x35, 0x33, 0x94, 0xC0, 0x53, 0x2D, 0xD8, 0x50, 0xC1, 0x42, 0x3B, 0x73, 0x9C, 0xF6, 0x86, 0xF6, 0x75, 0xEC, 0x8F, 0x79, 0x29, 0xB7, 0x18, 0x59, 0x18, 0x1C, 0xB4, 0x20, 0x4B, 0x84, 0xB8, 0x29, 0x45, 0xF8, 0x1C, 0x07, 0x69, 0xA3, 0x0D, 0x4F, 0x00, 0x00, 0x7D, 0x58, 0xED, 0x48, 0xC5, 0x92, 0x40, 0xE2, 0xC6, 0x5A, 0x3B, 0x1C, 0xA0, 0xBF, 0x57, 0x2B, 0x11, 0xBC, 0x07, 0x22, 0x94, 0x69, 0xD8, 0xCA, 0x56, 0x0E, 0x72, 0xE0, 0x93, 0x2C, 0xED, 0x5D, 0x30, 0x52, 0x1F, 0xC0, 0x3E, 0xCE, 0x52, 0xA7, 0x7C, 0x02, 0x73, 0x6B, 0xAC, 0x7D, 0x93, 0xC7, 0xB9, 0x91, 0xF9, 0x2F, 0x76, 0x0C, 0xB2, 0xDD, 0x4F, 0x84, 0xE4, 0xE1, 0x18, 0xC8, 0xAF, 0x47, 0xCA, 0x01, 0xAC, 0x7B, 0x1A, 0xB2, 0x98, 0x25, 0x09, 0xD8, 0xE6, 0x2F, 0xF5, 0xA4, 0xD7, 0xDB, 0xDA, 0x31, 0xDC, 0x87, 0x4C, 0x9E, 0xD8, 0x1E, 0x79, 0xE4, 0x99, 0xCF, 0x09, 0x52, 0x41, 0x33, 0x8F, 0x4F, 0x98, 0x85, 0x95, 0x26, 0x30, 0x80, 0x6D, 0x25, 0x6E, 0x4C, 0x0B, 0x48, 0x55, 0xF6, 0x8A, 0x23, 0x25, 0x90, 0xDA, 0x15, 0xF5, 0x91, 0xF5, 0x40, 0xB6, 0xF8, 0x52, 0x6C, 0x4F, 0x00, 0x00, 0x86, 0x58, 0xE5, 0x27, 0x8C, 0x80, 0x2D, 0xE3, 0xBB, 0xE2, 0xE0, 0x0E, 0x33, 0xE2, 0x84, 0x55, 0x0E, 0xE1, 0xE7, 0xC0, 0xCB, 0xED, 0x69, 0x49, 0xFA, 0x74, 0x67, 0xF6, 0x8E, 0x79, 0x0F, 0xBA, 0x25, 0xC3, 0x52, 0x37, 0x6A, 0x56, 0x61, 0x71, 0xC0, 0x06, 0x1B, 0xE0, 0x37, 0xFD, 0x60, 0xB6, 0xD2, 0x18, 0x23, 0x0B, 0x58, 0xD9, 0xF2, 0xFC, 0x7C, 0x2C, 0xC4, 0xA5, 0x76, 0x8D, 0x05, 0xD5, 0xE8, 0xDD, 0x42, 0xAA, 0x11, 0x6C, 0x05, 0x75, 0xB6, 0x96, 0xBB, 0xAB, 0xBC, 0xC3, 0x2C, 0xC2, 0x2F, 0xC4, 0x5E, 0x15, 0xA2, 0x17, 0x31, 0x7E, 0xB7, 0xEF, 0x40, 0x2A, 0x26, 0xE8, 0xD4, 0x86, 0x85, 0x90, 0x25, 0x99, 0x35, 0x6C, 0x3F, 0x23, 0x02, 0x63, 0xE6, 0xC2, 0x74, 0x1B, 0xEC, 0xEA, 0x17, 0x9A, 0x63, 0xC0, 0x94, 0x32, 0x57, 0x54, 0x0F, 0x83, 0x37, 0x69, 0x31, 0xB7, 0x78, 0xBF, 0xBF, 0xBD, 0x7E, 0xDD, 0xC0, 0x26, 0x64, 0x4F, 0x00, 0x00, 0x76, 0x58, 0xE5, 0x8B, 0xD3, 0x6A, 0x28, 0xD8, 0x2F, 0x92, 0x86, 0x5C, 0x1F, 0xF4, 0x00, 0x21, 0x3B, 0x49, 0x2A, 0xDC, 0x65, 0xF1, 0xAB, 0xA0, 0x39, 0xD4, 0x80, 0x6E, 0x1D, 0x7D, 0x76, 0x9B, 0x3E, 0xB8, 0x3D, 0x0F, 0xB6, 0x68, 0xB3, 0x1A, 0x68, 0xB9, 0x28, 0x65, 0xBC, 0x94, 0xFA, 0x19, 0x1C, 0x59, 0xB0, 0xC8, 0x45, 0x6B, 0x55, 0x38, 0x8C, 0x51, 0x59, 0x45, 0xBC, 0xE7, 0xFB, 0x4D, 0x81, 0x52, 0x61, 0x58, 0xDA, 0xED, 0x57, 0x9E, 0x0E, 0x3F, 0x76, 0x77, 0x24, 0xFE, 0x14, 0xFF, 0xA2, 0xCD, 0x30, 0xBC, 0x24, 0xE5, 0x80, 0xA7, 0x8D, 0x95, 0x84, 0x89, 0x2F, 0xFF, 0x46, 0xF2, 0xEC, 0x18, 0xB1, 0xA0, 0x69, 0x53, 0x4F, 0xFC, 0x96, 0x7F, 0x2E, 0x62, 0xF1, 0xA7, 0x4C, 0xC3, 0xD9, 0xCD, 0xDE, 0x03, 0x1B, 0x4C, 0x33, 0x4F, 0x00, 0x00, 0x78, 0x58, 0xED, 0x6E, 0x38, 0x5D, 0x33, 0x79, 0xC9, 0x44, 0xA7, 0x76, 0x61, 0x16, 0x43, 0xD8, 0x0A, 0x3C, 0xF4, 0xFD, 0x3C, 0xBB, 0x84, 0x93, 0xA5, 0x80, 0xB9, 0x31, 0x3E, 0x24, 0x1B, 0x7E, 0x5E, 0x5F, 0x85, 0x55, 0x3B, 0xD6, 0xB8, 0x92, 0x50, 0xDC, 0x0C, 0x46, 0x6C, 0xEA, 0x67, 0xF2, 0x47, 0xFF, 0x33, 0x6F, 0x42, 0xF4, 0xBE, 0x24, 0x02, 0xD5, 0x9E, 0x59, 0xAF, 0xC7, 0xD0, 0x26, 0x9F, 0x2B, 0x3C, 0xC7, 0xCE, 0x2C, 0x8C, 0x56, 0xC3, 0xA1, 0x6B, 0x01, 0xD3, 0x60, 0x6F, 0x0E, 0xE6, 0x98, 0xD9, 0x68, 0x2F, 0xBD, 0xFE, 0x31, 0x32, 0x27, 0x9C, 0xB7, 0x06, 0x96, 0x88, 0xA6, 0x5E, 0xCE, 0xDF, 0xA8, 0x5D, 0x1E, 0xDB, 0x1F, 0x44, 0x80, 0x77, 0xAF, 0xB1, 0x8D, 0xEF, 0x96, 0x2F, 0x2A, 0x7D, 0x02, 0x8D, 0x31, 0x94, 0x62, 0x40, 0x4F, 0x00, 0x00, 0x94, 0x58, 0xEC, 0xCD, 0xAF, 0x0F, 0xD2, 0x1E, 0xD4, 0x06, 0x12, 0x5F, 0x16, 0x90, 0xCA, 0x2D, 0x18, 0xB9, 0x8F, 0xA0, 0x09, 0x34, 0x56, 0xB8, 0x55, 0x76, 0x8E, 0xB9, 0x17, 0x52, 0xB1, 0x57, 0xB9, 0x75, 0x55, 0x01, 0x2B, 0xA0, 0xF5, 0xC2, 0x66, 0xB5, 0x8E, 0xB7, 0x44, 0xE7, 0xAB, 0x01, 0xEE, 0xA3, 0x45, 0x7A, 0xF6, 0xB6, 0x60, 0x5C, 0xB7, 0x1B, 0xCB, 0x5C, 0x7A, 0xFA, 0x0F, 0x5A, 0x14, 0xAB, 0xEC, 0x75, 0xE3, 0x4D, 0xCF, 0xF2, 0xAB, 0x72, 0x19, 0xD6, 0xD3, 0xD0, 0x06, 0x3E, 0x7C, 0x07, 0xC2, 0xAD, 0x43, 0x37, 0xA6, 0x83, 0x3C, 0x57, 0xC3, 0x7B, 0x55, 0xEF, 0xB3, 0x76, 0xB9, 0x0F, 0x07, 0x74, 0xB7, 0x1B, 0xA1, 0xCD, 0xD0, 0x94, 0x86, 0x1B, 0x53, 0x92, 0x79, 0xFA, 0x14, 0x31, 0xAA, 0x53, 0xAD, 0xE8, 0x87, 0x4B, 0xB9, 0xAE, 0x80, 0x0F, 0x5C, 0xBC, 0x11, 0x46, 0xFC, 0x73, 0x7D, 0xFE, 0xB2, 0xF7, 0x0E, 0x27, 0xD3, 0x9B, 0xBD, 0x81, 0x28, 0xB1, 0x66, 0xB8, 0x20, 0x79, 0x74, 0x02, 0xCC, 0x4F, 0x00, 0x00, 0x86, 0x58, 0xCD, 0x31, 0x38, 0x96, 0x59, 0x30, 0xC4, 0xBB, 0x25, 0xB8, 0x22, 0xA4, 0x2A, 0x82, 0xD1, 0xCA, 0x97, 0x3E, 0x86, 0x90, 0x17, 0x16, 0xF3, 0x13, 0x31, 0x31, 0x9D, 0xE5, 0xC0, 0x0A, 0x0F, 0x51, 0xAF, 0x46, 0xFC, 0xAE, 0xF2, 0x7C, 0x1F, 0xB1, 0x82, 0x35, 0x5A, 0xE5, 0xA0, 0xC1, 0xB2, 0x0D, 0x54, 0x99, 0x43, 0x16, 0x2C, 0x78, 0xC7, 0xD4, 0xE8, 0xFE, 0xD7, 0xBC, 0x69, 0xC1, 0x99, 0x12, 0x81, 0xAD, 0x23, 0xFF, 0x7A, 0x8F, 0x11, 0x79, 0x2A, 0x5A, 0xC5, 0xEE, 0x3D, 0xD9, 0x39, 0x68, 0xB9, 0x33, 0xD9, 0x29, 0x56, 0x4B, 0x8A, 0x7C, 0x22, 0xAA, 0xF9, 0xE3, 0x44, 0x44, 0x4F, 0x6B, 0x32, 0x9E, 0x13, 0x06, 0x57, 0x78, 0xE5, 0x0C, 0x19, 0x8B, 0x20, 0xB4, 0xCD, 0x2D, 0x02, 0x58, 0x1F, 0xDD, 0xE6, 0xB4, 0xC8, 0x8A, 0xCC, 0xB6, 0x95, 0xBC, 0xE3, 0x2A, 0x37, 0xF3, 0x9E, 0xEE, 0xD9, 0xC4, 0x6E, 0x05, 0x80, 0x4F, 0x00, 0x00, 0x48, 0x58, 0x0B, 0x2A, 0x5E, 0xB8, 0x43, 0x59, 0xDA, 0xF3, 0x36, 0x0F, 0x00, 0xFE, 0x52, 0xCD, 0x2D, 0xE7, 0x4F, 0x65, 0x29, 0x79, 0x40, 0x49, 0xCE, 0x7F, 0x7E, 0x88, 0x54, 0x44, 0x14, 0x14, 0x2A, 0x30, 0x85, 0x92, 0x36, 0xB6, 0x82, 0x35, 0xD4, 0x4F, 0x3A, 0x51, 0xE3, 0xD9, 0x7D, 0x25, 0x2E, 0x34, 0x38, 0x1F, 0xAE, 0x28, 0x52, 0x19, 0x7B, 0x27, 0x6E, 0x6D, 0x95, 0x9A, 0x66, 0xFE, 0x44, 0x9C, 0x98, 0xBC, 0xE3, 0x4F, 0x03, 0x93, 0xD0, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20
};

#endif // AUDIO_DATA_CONFIGEND_H