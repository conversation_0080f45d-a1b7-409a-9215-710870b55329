/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_opus_enc.c
 * @description: OPUS编码模块.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <string.h>
#include <esp_log.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/queue.h>
#include <arpa/inet.h>
#include "opus.h"
#include "sk_audio_buffer.h"
#include "sk_ring_buf.h"
#include "sk_opus_enc.h"
#include "sk_common.h"
#include "audio_data_hellofirst.h"
#include "audio_data_hello.h"
#include "audio_data_callreq.h"
#include "sk_opus_inner.h"
#include "sk_os.h"

#define TAG "SkOpusEnc"

#define MAX_AUDIO_CNT 4

#define AUDIO_SHORT_BUF_SIZE 256
#define AUDIO_PCM_BUF_SIZE 1024

typedef struct {
    uint8_t *data;
    uint8_t maxCnt;
    uint8_t sn;
    uint8_t wtIdx;
    uint8_t rdIdx;
} SkCmdAudioData;

typedef struct {
    struct OpusEncoder* audioEnc;
    QueueHandle_t msgQueue;
    int frameSize;
    int taskStatus;
    void *inQueue;
    void *cbArg;
    uint8_t *opusBuf;
    uint16_t opusBufSize;
    SkOpusEncCallback cb;
    SkRingBuf rb;
    uint32_t localEndFlag;
    int32_t audioCnt;
    int32_t audioIndex;
    int32_t newAudioIndex;
    int32_t frameIndex;
    int32_t encodeFlag; // 0: disable, 1: enable
    uint32_t codedBytes;
    uint32_t codedFrameCnt;
    SkCodedDataItem audioInfoList[MAX_AUDIO_CNT];

    uint32_t enQueueFrameCnt;
    uint32_t encodeFrameCnt;
    uint32_t encodeDtxFrameCnt;
    uint32_t encodeNoDtxFrameCnt;
    SkCmdAudioData cmdAudioData;
} SkOpusEncCtrl;

void SkOpusCmdDataPrepare(SkOpusEncCtrl *ctrl, uint8_t sn);
void SkOpusSaveCmdAudio(SkOpusEncCtrl *ctrl, uint8_t *data, uint16_t length);

SkOpusEncCtrl g_opusEncCtrl;

/**
 * @brief 解码 Opus 音频数据到 PCM 数据
 *
 * 使用 Opus 解码器将 Opus 音频数据解码为 PCM 数据。
 *
 * @param handler Opus 解码器的控制句柄
 * @param opusData Opus 音频数据指针
 * @param opusSize Opus 音频数据大小（字节）
 * @param pcmBuffer PCM 数据缓冲区指针
 * @param pcmSize PCM 采样个数
 *
 * @return 解码后的 PCM 数据长度（样本数），如果解码失败则返回 0
 */
int32_t SkOpusEncEncode(SkOpusEncCtrl *ctrl, int16_t* pcmBuffer, int32_t pcmSize, uint8_t* opusData, int opusSize) {
    int ret = 0;

    if (ctrl->audioEnc == NULL) {
        return 0;
    }

    ctrl->encodeFrameCnt++;
    ret = opus_encode(ctrl->audioEnc, pcmBuffer, pcmSize, opusData, opusSize);
    if (ret < 0) {
        ESP_LOGE(TAG, "Failed to encode audio, error code: %d", ret);
        return 0;
    }
    
    if (ret < 1) {
        ctrl->encodeDtxFrameCnt++;
    } else {
        ctrl->encodeNoDtxFrameCnt++;
    }

    return ret;
}

void SkOpusEncStop() {
    SkOpusEncCtrl *ctrl = &g_opusEncCtrl;
    if (ctrl->audioEnc != NULL) {
        opus_encoder_ctl(ctrl->audioEnc, OPUS_RESET_STATE);
    }
}

uint8_t* SkOpusEncLocalGetPkt(SkOpusEncCtrl *ctrl, uint16_t *pktSize) {
    SkCodedDataItem *item = NULL;
    SkOpusPktHdr *pktHdr = NULL;
    uint16_t pktOffset;
    uint8_t* dataPos = NULL;

    if (ctrl->audioIndex >= ctrl->audioCnt || ctrl->audioIndex > MAX_AUDIO_CNT) {
        //ESP_LOGE(TAG, "Invalid audio index: %d", ctrl->audioIndex);
        return NULL;
    }

    item = &ctrl->audioInfoList[ctrl->audioIndex];
    if (ctrl->frameIndex >= item->pktCnt) {
        ESP_LOGE(TAG, "Invalid packet index: %d", ctrl->frameIndex);
        return NULL;
    }
    pktOffset = item->pktOffsetList[ctrl->frameIndex];
    if (pktOffset >= item->length) {
        ESP_LOGE(TAG, "Invalid packet offset: %d", pktOffset);
        return NULL;
    }
    pktHdr = (SkOpusPktHdr *)(item->data + pktOffset);
    if (pktHdr->type != OPUS_PKT_FLAG) {
        ESP_LOGE(TAG, "Invalid packet type: %d", pktHdr->type);
        return NULL;
    }

    *pktSize = ntohs(pktHdr->payloadSize) + 4;
    if (ctrl->frameIndex + 1 < item->pktCnt) {
        ctrl->frameIndex++;
    } else {
        ctrl->frameIndex = 0;
        ctrl->localEndFlag = 0;
    }
    //ESP_LOGI(TAG, "Curr audio %p len %d byte, next audio %d packet: %d", 
    //    pktHdr->payload, *pktSize, ctrl->audioIndex, ctrl->frameIndex);
    dataPos = (uint8_t *)(item->data);
    dataPos += pktOffset;

    return dataPos;
}

uint16_t SkOpusEncData(SkOpusEncCtrl *ctrl, SkAudioBuf *inBuf) {
    uint16_t head, pcmCnt, pktSize;
    SkRingBuf *rb = &ctrl->rb;

#if (CONFIG_PCM_DEBUG == CONFIG_PCM_DBG_POS_RB_IN)
    SkDfxLinkSendAudio(MSG_DFX_AND_TERM_PCM1, (uint8_t *)inBuf->data, inBuf->length);
#endif
    pcmCnt = SkRBDataIn(rb, (uint16_t *)inBuf->data, inBuf->length/sizeof(uint16_t),
        inBuf->speechFlag & SK_ENC_DATA_FLAG_VAD);
    SkAudioBufferPutFree(ctrl->inQueue, inBuf);
    if (pcmCnt < ctrl->frameSize) {
        return 0;
    }

    head = SkRBGetHead(rb);
#if (CONFIG_PCM_DEBUG == CONFIG_PCM_DBG_POS_RB_OUT)
    SkDfxLinkSendAudio(MSG_DFX_AND_TERM_PCM1, (uint8_t *)(&rb->buffer[head]), ctrl->frameSize * sizeof(uint16_t));
#endif
    pktSize = SkOpusEncEncode(ctrl, (int16_t *)(&rb->buffer[head]),
        ctrl->frameSize, ctrl->opusBuf + 4, ctrl->opusBufSize - 4);
    ctrl->codedBytes += pktSize;
    ctrl->codedFrameCnt++;
    ctrl->opusBuf[0] = OPUS_PKT_FLAG;
    ctrl->opusBuf[1] = (SkRBGetVadFlag(rb) != 0) ? 1 : 0;
    ctrl->opusBuf[2] = (uint8_t)((pktSize >> 8) & 0xff);
    ctrl->opusBuf[3] = (uint8_t)(pktSize & 0xff);
    pktSize += 4;
    SkRBDataOut(rb);

    return pktSize;
}

void SkOpusEncPutAudioIntoQueue(SkOpusEncHandler handler, SkAudioBuf *inBuf) {
    SkOpusEncCtrl *ctrl = (SkOpusEncCtrl *)handler;

    if (inBuf == NULL) {
        return;
    }
    SkAudioBufferPutData(ctrl->inQueue, inBuf);
    return;
}

void SkOpusEncProcAudioBuf(SkOpusEncHandler handler, SkAudioBuf *inBuf) {
    SkOpusEncCtrl *ctrl = (SkOpusEncCtrl *)handler;
    uint16_t pktSize;
    uint8_t *pktData = NULL;
    uint32_t currTick;
    uint8_t dataMode;
    uint32_t timestamp;

    if (inBuf == NULL) {
        return;
    }
    
    timestamp = inBuf->timeRecord[0];
    currTick = SkOsGetTickCnt();
    if ((currTick - timestamp) > 100) {
        ESP_LOGI(TAG, "Audio buffer timeout, %u->%u tick", timestamp, currTick);
    }
    if (ctrl->localEndFlag == 1) {
        dataMode = inBuf->speechFlag & SK_ENC_DATA_FLAG_CMD;
        if (dataMode != 0) {
            SkOpusCmdDataPrepare(ctrl, inBuf->speechFlag >> 8);
        }
        pktSize = SkOpusEncData(ctrl, inBuf);
        if (pktSize == 0) {
            return;
        }
        pktData = ctrl->opusBuf;
        if (dataMode != 0) {
            SkOpusSaveCmdAudio(ctrl, pktData, pktSize);
        }
    } else {
        SkAudioBufferPutFree(ctrl->inQueue, inBuf);
        pktData = SkOpusEncLocalGetPkt(ctrl, &pktSize);
        if (pktData == NULL) {
            return;
        }
    }
    
    if (ctrl->cb != NULL) {
        ctrl->cb(ctrl->cbArg, pktData, pktSize, currTick);
    }

    return;
}

int32_t SkOpusEncSetLocal(SkOpusEncHandler handler, uint8_t audioIndex) {
    SkOpusEncCtrl *ctrl = (SkOpusEncCtrl *)handler;

    if (audioIndex >= ctrl->audioCnt || audioIndex > MAX_AUDIO_CNT) {
        return SK_RET_FAIL;
    }
    ctrl->audioIndex = audioIndex;
    ctrl->frameIndex = 0;
    ctrl->localEndFlag = 0;

    return SK_RET_SUCCESS;
}

int32_t SkOpusEncSendInner(uint8_t audioIndex) {
    SkOpusEncCtrl *ctrl = &g_opusEncCtrl;
    SkOpusMsg msg;

    msg.event = SK_ENC_EVENT_PKT;
    msg.param1 = audioIndex;
    xQueueSend(ctrl->msgQueue, &msg, portMAX_DELAY);

    return SK_RET_SUCCESS;
}

void SkOpusEncRegAudio(char* name, const uint8_t* data, int length, const uint16_t *pktOffset, int16_t pktCnt) {
    SkOpusEncCtrl *ctrl = &g_opusEncCtrl;
    if (ctrl->audioCnt >= MAX_AUDIO_CNT) {
        return;
    }
    ctrl->audioInfoList[ctrl->audioCnt].name = name;
    ctrl->audioInfoList[ctrl->audioCnt].data = (uint8_t *)data;
    ctrl->audioInfoList[ctrl->audioCnt].length = length;
    ctrl->audioInfoList[ctrl->audioCnt].pktOffsetList = pktOffset;
    ctrl->audioInfoList[ctrl->audioCnt].pktCnt = pktCnt;
    ctrl->audioCnt++;

    return;
}

SkOpusEncHandler SkOpusEncInit(int sampleRate, int channels, int durationMs, QueueHandle_t msgQueue) {
    SkOpusEncCtrl *ctrl = &g_opusEncCtrl;
    int error;

    ctrl->frameSize = sampleRate / 1000 * channels * durationMs;
    ctrl->inQueue = SkCreateAudioQueue(6, AUDIO_PCM_BUF_SIZE, 0);
    ctrl->msgQueue = msgQueue;
    ctrl->opusBufSize = AUDIO_CODED_BUF_SIZE;
    ctrl->opusBuf = (uint8_t *)malloc(ctrl->opusBufSize);
    ctrl->taskStatus = 1;
    ctrl->audioIndex = 0;
    ctrl->frameIndex = 0;
    ctrl->codedBytes = 0;
    ctrl->codedFrameCnt = 0;
    ctrl->localEndFlag = 1;
    ctrl->enQueueFrameCnt = 0;
    ctrl->encodeFrameCnt = 0;
    ctrl->encodeDtxFrameCnt = 0;
    ctrl->encodeNoDtxFrameCnt = 0;
    ctrl->encodeFlag = 1;
    ctrl->cmdAudioData.data = (uint8_t *)SkOsAllocPsram(
        ctrl->cmdAudioData.maxCnt * ctrl->opusBufSize, sizeof(uint32_t));
    ctrl->cmdAudioData.maxCnt = 100;
    ctrl->cmdAudioData.wtIdx = 0;
    ctrl->cmdAudioData.rdIdx = 0;
    ctrl->cmdAudioData.sn = 0xFF;

    SkRBInit(&ctrl->rb, ctrl->frameSize);

    ctrl->audioEnc = opus_encoder_create(sampleRate, channels, OPUS_APPLICATION_VOIP, &error);
    if (ctrl->audioEnc == NULL) {
        ESP_LOGE(TAG, "Failed to create audio decoder, error code: %d", error);
        return NULL;
    }
    opus_encoder_ctl(ctrl->audioEnc, OPUS_SET_DTX(1));
    opus_encoder_ctl(ctrl->audioEnc, OPUS_SET_COMPLEXITY(5));
    SkOpusEncRegAudio("CallReq", g_audioDataCallreq, sizeof(g_audioDataCallreq),
        g_audioInfoCallreq, sizeof(g_audioInfoCallreq) / sizeof(uint16_t));
    SkOpusEncRegAudio("Hello", g_audioDataHello, sizeof(g_audioDataHello), 
        g_audioInfoHello, sizeof(g_audioInfoHello) / sizeof(uint16_t));
    SkOpusEncRegAudio("HelloFirst", g_audioDataHelloFirst, sizeof(g_audioDataHelloFirst), 
        g_audioInfoHelloFirst, sizeof(g_audioInfoHelloFirst) / sizeof(uint16_t));

    return ctrl;
}

void SkOpusEncSetFlag(int flag) {
    SkOpusEncCtrl *ctrl = &g_opusEncCtrl;
    ctrl->encodeFlag = flag;
}

void SkOpusEncEnqueue(uint16_t *buff, size_t len, uint32_t dataMode, uint32_t tickCnt) {
    SkOpusEncCtrl *ctrl = &g_opusEncCtrl;
    uint16_t *codeBuf = NULL;
    int32_t ret;
    SkAudioBuf *audioBuf = NULL;
    SkOpusMsg msg;

    if (ctrl->encodeFlag == 0) {
        return;
    }

    audioBuf = SkAudioBufferGetFree(ctrl->inQueue, 0);
    if (audioBuf == NULL) {
        return;
    }

    codeBuf = (uint16_t *)audioBuf->data;
    if (len > audioBuf->size) {
        SkAudioBufferPutFree(ctrl->inQueue, audioBuf);
        return;
    }

    audioBuf->speechFlag = (uint16_t)dataMode;
    memcpy(codeBuf, buff, len);
    audioBuf->length = len;
    audioBuf->timeRecord[0] = tickCnt;

#if (CONFIG_PCM_DEBUG == CONFIG_PCM_DBG_POS_ENC_QUEUE)
    SkDfxLinkSendAudio(MSG_DFX_AND_TERM_OPUS, (uint8_t *)audioBuf->data, audioBuf->length);
#endif
    msg.arg = audioBuf;
    msg.event = SK_ENC_EVENT_PCM;
    msg.timestamp = tickCnt;
    ret = xQueueSend(ctrl->msgQueue, &msg, portMAX_DELAY);
    if (ret != pdPASS) {
        SkAudioBufferPutFree(ctrl->inQueue, audioBuf);
        return;
    }
    ctrl->enQueueFrameCnt++;
    
    return;
}

void SkOpusEncDeinit(SkOpusEncHandler handler) {
    SkOpusEncCtrl *ctrl = (SkOpusEncCtrl *)handler;
    if (ctrl->audioEnc != NULL) {
        opus_encoder_destroy(ctrl->audioEnc);
        ctrl->audioEnc = NULL;
    }
    return;
}

SkOpusEncHandler SkOpusEncGetHandler() {
    return &g_opusEncCtrl;
}

void SkOpusEncSetCallback(SkOpusEncCallback cb) {
    SkOpusEncCtrl *ctrl = &g_opusEncCtrl;

    ctrl->cb = cb;
    ctrl->cbArg = NULL;
    return;
}

void SkOpusEncShowStatus() {
    SkOpusEncCtrl *ctrl = &g_opusEncCtrl;
    ESP_LOGI(TAG, "ENC: EqCnt=%u EncCnt=%u DtxCnt=%u NoDtxCnt=%u CodeFrm=%u CodeBytes=%u",
        ctrl->enQueueFrameCnt, ctrl->encodeFrameCnt, ctrl->encodeDtxFrameCnt, ctrl->encodeNoDtxFrameCnt,
        ctrl->codedFrameCnt, ctrl->codedBytes);
    return;
}

void SkOpusEncStopTask() {
    g_opusEncCtrl.taskStatus = 0;
    return;
}

void SkOpusCmdDataPrepare(SkOpusEncCtrl *ctrl, uint8_t sn) {
    if (sn != ctrl->cmdAudioData.sn) {
        SkRBReset(&ctrl->rb);
        ctrl->cmdAudioData.sn = sn;
        ctrl->cmdAudioData.wtIdx = 0;
    }
    return;
}            

void SkOpusSaveCmdAudio(SkOpusEncCtrl *ctrl, uint8_t *data, uint16_t length) {
    SkCmdAudioData *cmdAudio = &ctrl->cmdAudioData;
    SkCmdData *cmdData = NULL;
    int wtIdx;

    if ((length > sizeof(SkCmdData)) || (length < 4)) {
        return;
    }

    if (cmdAudio->wtIdx >= cmdAudio->maxCnt) {
        return;
    }
    wtIdx = cmdAudio->wtIdx;
    cmdData = (SkCmdData *)&cmdAudio->data[wtIdx * sizeof(SkCmdData)];

    memcpy((void *)cmdData, data, length);
    // cmdData->length输入过来的时大端，这里修改为小段
    cmdData->frameSize = length - 4;
    cmdAudio->wtIdx++;

    return;
}

SkCmdData* SkOpusPopCmdAudio() {
    SkOpusEncCtrl *ctrl = &g_opusEncCtrl;
    SkCmdData *cmdData = NULL;

    if ((ctrl->cmdAudioData.wtIdx == 0) || (ctrl->cmdAudioData.rdIdx >= ctrl->cmdAudioData.wtIdx)) {
        return NULL;
    }
    
    cmdData = (SkCmdData *)&ctrl->cmdAudioData.data[ctrl->cmdAudioData.rdIdx * sizeof(SkCmdData)];
    ctrl->cmdAudioData.rdIdx++;

    return cmdData;
}

bool SkOpusEncProc(SkOpusEncHandler handler) {
    SkOpusEncCtrl *ctrl = (SkOpusEncCtrl *)handler;

    if (SkAudioBufferHasData(ctrl->inQueue)) {
        SkOpusEncProcAudioBuf((SkOpusEncHandler)ctrl, 
            SkAudioBufferGetData(ctrl->inQueue, 0));
    }

    return SkAudioBufferHasData(ctrl->inQueue);
}