/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_ring_buf.c
 * @description: 编码模块前的缓存队列，通过环形Buffer，缓存数据.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <string.h>
#include "esp_log.h"
#include "sk_ring_buf.h"
#include "sk_common.h"

#define TAG "SkRingBuf"

// 创建一个Ring Buffer
int32_t SkRBInit(SkRingBuf *rb, uint16_t frameSize) {
    uint16_t byteSize;

    if (rb == NULL) {
        return SK_RET_INVALID_PARAM;
    }

    rb->head = 0;
    rb->tail = 0;
    rb->frameSize = frameSize;
    rb->size = frameSize * 2;
    rb->dataFlag = 0;
    byteSize = rb->size * sizeof(uint16_t);
    rb->buffer = (uint16_t *)malloc(byteSize);
    if (rb->buffer == NULL) {
        return SK_RET_NO_MEMORY;
    }
    return SK_RET_SUCCESS;
}

// sampleCnt不超过2倍frameSize
uint16_t SkRBDataIn(SkRingBuf *rb, uint16_t *samples, uint16_t sampleCnt, uint32_t vadFlag) {
    uint16_t freeSpace, cnt, tailLeft;
    uint16_t head, tail, tailFlag, headFlag;

    cnt = SkRBSampleCnt(rb, 0, NULL);
    freeSpace = rb->size - cnt;
    if (freeSpace < sampleCnt) {
        return cnt;
    }

    head = rb->head & 0x7FFF;
    tail = rb->tail & 0x7FFF;
    tailFlag = rb->tail & 0x8000;
    headFlag = rb->head & 0x8000;
    if (tailFlag == headFlag) {
        tailLeft = rb->size - tail;
    } else {
        tailLeft = head - tail;
    }
    
    if (tailLeft < sampleCnt) {
        memcpy((void *)(rb->buffer + tail), samples, sizeof(uint16_t) * tailLeft);
        memcpy((void *)rb->buffer, samples + tailLeft, sizeof(uint16_t) * (sampleCnt - tailLeft));
    } else {
        memcpy((void *)(rb->buffer + tail), samples, sizeof(uint16_t) * sampleCnt);
    }
    tail += sampleCnt;
    if (tail >= rb->size) {
        tail -= rb->size;
        // 每次到达尾部，翻转标志位.
        tailFlag = (~rb->tail) & 0x8000;
    }
    rb->tail = tailFlag | tail;
    if (vadFlag != 0) {
        if (sampleCnt == 512) {
            rb->dataFlag = rb->dataFlag | (0xFF << (cnt / 64));
        } else {
            ESP_LOGE(TAG, "SkRBDataIn error sampleCnt %d", sampleCnt);
        }
    }

    return cnt + sampleCnt;
}

// 每次删除一个FrameSize，通过移动head，删除位置从0或者从偏移FrameSize开始的一个Frame数据。
uint16_t SkRBDataOut(SkRingBuf *rb) {
    uint16_t cnt, head, headFlag;

    cnt = SkRBSampleCnt(rb, 0, NULL);
    if (cnt <= rb->frameSize) {
        rb->head = 0;
        rb->tail = 0;
        rb->dataFlag = 0;
        return 0;
    }

    head = rb->head & 0x7FFF;
    headFlag = rb->head & 0x8000;
    head += rb->frameSize;
    if (head >= rb->size) {
        head -= rb->size;
        // 每次到达头部，翻转标志位.
        headFlag = (~headFlag) & 0x8000;
    }
    rb->head = headFlag | head;
    if (rb->frameSize == 960) {
        rb->dataFlag = rb->dataFlag >> 15;
    }
    return cnt - rb->frameSize;
}

void SkRBReset(SkRingBuf *rb) {
    rb->head = 0;
    rb->tail = 0;
    rb->dataFlag = 0;
    return;
}

// 获取数据个数，从tail-head数量
uint16_t SkRBSampleCnt(SkRingBuf *rb, uint32_t mask, uint32_t *vadValue) {
    uint16_t head, tail, cnt, hFlag, tFlag;
    
    hFlag = rb->head & 0x8000;
    tFlag = rb->tail & 0x8000;
    if (hFlag == tFlag) {
        cnt =rb->tail - rb->head;
    } else {
        head = rb->head & 0x7FFF;
        tail = rb->tail & 0x7FFF;
        if (head >= tail) {
            cnt = rb->size + tail - head;
        } else {
            ESP_LOGI("SkRB", "SkRBSampleCnt error hFlag %04x tFlag %04x head %d tail %d size %d",
                hFlag, tFlag, head, tail, rb->size);
            SkRBReset(rb);
            cnt = 0;
        }
    }
    if (vadValue != NULL) {
        *vadValue = rb->dataFlag & mask;
    }

    return cnt;
}
