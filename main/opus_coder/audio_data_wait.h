#ifndef AUDIO_DATA_WAIT_H
#define AUDIO_DATA_WAIT_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfoWait[] = {0, 105, 256, 413, 564, 723, 872, 1024, 1166, 1326, 1458, 1607, 1750, 1903, 2031, 2036, 2041, 2046, 2051, 2056, 2061, 2066, 2071};
const uint8_t g_audioDataWait[] = {
    0x4F, 0x00, 0x00, 0x65, 0x58, 0x60, 0xF5, 0x8B, 0xE3, 0x8F, 0x20, 0xA5, 0xA9, 0xB8, 0x53, 0xAF, 0xE4, 0x49, 0x44, 0x9A, 0x85, 0x5F, 0x8F, 0xCD, 0xA1, 0xCD, 0x80, 0x1C, 0x71, 0x04, 0x34, 0x5F, 0x4F, 0xDC, 0x6E, 0x4C, 0x7D, 0xD3, 0xD0, 0xD3, 0x02, 0x56, 0x8C, 0x4D, 0x78, 0x75, 0x49, 0x91, 0x7C, 0x8D, 0xAA, 0x07, 0x78, 0x98, 0x97, 0xBB, 0x19, 0x99, 0x58, 0x40, 0x56, 0xB9, 0x3F, 0xAF, 0x59, 0xCA, 0xAE, 0x1F, 0x7F, 0xEE, 0xBD, 0x1F, 0xAB, 0x69, 0x71, 0xE4, 0xD7, 0xCF, 0x06, 0x7F, 0x2A, 0x76, 0xAE, 0xD8, 0x5F, 0x7E, 0xF0, 0x67, 0x09, 0x70, 0x7B, 0x7D, 0x21, 0xD0, 0xEE, 0xC6, 0xF3, 0x63, 0x12, 0x86, 0x03, 0xCE, 0xD1, 0xA0, 0xE0, 0x4F, 0x00, 0x00, 0x93, 0x58, 0xE8, 0xE2, 0x03, 0x6F, 0xC8, 0x3A, 0x47, 0x10, 0xF6, 0xC7, 0x02, 0xBA, 0xEB, 0x6B, 0xE0, 0x38, 0x1E, 0xFE, 0xF4, 0x07, 0xB7, 0x1F, 0x77, 0x95, 0x9C, 0x92, 0x56, 0xB6, 0x5A, 0xD5, 0xDA, 0x34, 0x42, 0xD3, 0x0C, 0x31, 0x7A, 0xD0, 0x94, 0x0D, 0x14, 0x1E, 0x44, 0x59, 0x07, 0x77, 0x27, 0x53, 0xBA, 0xA8, 0x9D, 0x63, 0x8B, 0xAA, 0x1A, 0xC5, 0xD9, 0xDD, 0xB8, 0x78, 0x3B, 0x5C, 0x81, 0x0A, 0xA6, 0x2A, 0xA2, 0x68, 0xB4, 0x0A, 0x20, 0xDE, 0x30, 0x78, 0x07, 0xAD, 0x62, 0xD7, 0x72, 0x70, 0x30, 0x41, 0x08, 0x01, 0xA6, 0xF4, 0x23, 0x6B, 0x1D, 0x3D, 0x91, 0x75, 0x19, 0xC3, 0x28, 0x6E, 0xAB, 0x8B, 0xE1, 0x31, 0xB2, 0x18, 0xC3, 0xCD, 0x4A, 0x2E, 0x15, 0xE6, 0xF8, 0xC3, 0x53, 0x07, 0xE3, 0x27, 0x08, 0x41, 0x18, 0xA6, 0x32, 0x2C, 0xCF, 0x05, 0xF4, 0xFF, 0xD7, 0xAF, 0x88, 0x71, 0x22, 0x45, 0xBE, 0x52, 0x3C, 0xA3, 0xCD, 0x64, 0x92, 0x9A, 0x47, 0x9F, 0xA7, 0x08, 0xA0, 0x98, 0xEE, 0xA7, 0x4F, 0x00, 0x00, 0x99, 0x58, 0xE8, 0x1F, 0x48, 0x86, 0x12, 0x9A, 0x08, 0x72, 0x43, 0xA0, 0x0A, 0x4B, 0xE0, 0x62, 0x29, 0x2A, 0xF9, 0x98, 0x9A, 0x7E, 0xF4, 0xB5, 0xD6, 0x55, 0xB8, 0xEB, 0x5D, 0x31, 0x05, 0x37, 0xF1, 0xBE, 0x61, 0x42, 0x79, 0xCF, 0xC9, 0xA6, 0xE3, 0x73, 0x9B, 0x87, 0xBC, 0xD4, 0x49, 0x9E, 0x13, 0xAE, 0x22, 0x50, 0xF5, 0x7C, 0x28, 0x38, 0x65, 0xD5, 0xC2, 0xBD, 0x28, 0xCC, 0xB9, 0xCD, 0x85, 0x12, 0x66, 0xA3, 0x8D, 0x7D, 0x5F, 0xD0, 0x02, 0x6C, 0x99, 0x95, 0x11, 0x7A, 0xC9, 0xA6, 0xA4, 0xDF, 0x19, 0x6A, 0xD7, 0x90, 0x55, 0xAB, 0xEF, 0x5E, 0xC0, 0x3D, 0x12, 0x3A, 0x2F, 0x9E, 0xD8, 0x97, 0xD8, 0x04, 0x40, 0x54, 0x06, 0xAD, 0xCA, 0x64, 0x67, 0xB0, 0xD3, 0x75, 0x4F, 0x23, 0x83, 0x7E, 0x74, 0x78, 0x76, 0xE9, 0xF9, 0x66, 0x04, 0x42, 0xCD, 0x86, 0xB1, 0xA0, 0x71, 0x46, 0x69, 0x8C, 0xCF, 0x36, 0xBE, 0x5F, 0x9A, 0x4E, 0xBD, 0x4B, 0x2F, 0xAC, 0xAD, 0x30, 0xA2, 0x47, 0x3C, 0x5D, 0x44, 0x3D, 0xB8, 0x33, 0xF4, 0xD5, 0x11, 0xF0, 0x4F, 0x00, 0x00, 0x93, 0x58, 0xE9, 0x77, 0xC5, 0x67, 0x50, 0xAA, 0x1A, 0x92, 0xA6, 0x4D, 0x73, 0xC7, 0xC8, 0xBC, 0x50, 0x61, 0x69, 0xA7, 0x7F, 0xB5, 0x08, 0x97, 0xD9, 0xA3, 0xA0, 0x5D, 0x32, 0x0A, 0x59, 0x21, 0xE5, 0x0B, 0x71, 0x29, 0x00, 0x19, 0x99, 0xC3, 0xD2, 0x80, 0x1D, 0x0E, 0xA5, 0x5A, 0xCE, 0x0D, 0x21, 0x66, 0xA6, 0x4C, 0xD8, 0x84, 0x0A, 0xA0, 0x9E, 0x09, 0x36, 0xB9, 0xEE, 0xC1, 0x41, 0x86, 0x44, 0x97, 0x72, 0x1A, 0x3A, 0x02, 0x37, 0x2C, 0x93, 0x95, 0x14, 0x68, 0xFA, 0x1B, 0x93, 0xA5, 0xC7, 0x7B, 0xC7, 0xD7, 0x5C, 0x3B, 0xE0, 0x38, 0x6B, 0x5C, 0x7C, 0xE9, 0x69, 0x15, 0xE1, 0xF1, 0x3A, 0x43, 0xE2, 0x25, 0x3F, 0xEE, 0xE0, 0x4F, 0xA7, 0x92, 0x61, 0xA4, 0x65, 0xE3, 0xB6, 0x66, 0x51, 0x43, 0x10, 0xAA, 0xBE, 0x3B, 0x8C, 0x40, 0x95, 0xE3, 0x2C, 0x22, 0xD7, 0x53, 0xFB, 0x1E, 0x45, 0x98, 0xD5, 0x1F, 0xD2, 0x4B, 0x15, 0x5B, 0xA3, 0x5D, 0x57, 0x1B, 0x50, 0x57, 0x3A, 0xEE, 0x83, 0x65, 0xE7, 0xF0, 0x4F, 0x00, 0x00, 0x9B, 0x58, 0xE8, 0x6F, 0xC8, 0x3C, 0x24, 0x13, 0x63, 0x92, 0x08, 0x62, 0x88, 0xD9, 0x40, 0x58, 0x8B, 0xBA, 0xD5, 0xDE, 0x8E, 0x24, 0x67, 0x98, 0xB7, 0xBD, 0x85, 0xDF, 0x34, 0x0B, 0xE8, 0xFF, 0xC9, 0x6A, 0x70, 0x7D, 0xA1, 0xE6, 0x7F, 0x1C, 0x2A, 0xAA, 0xB2, 0x4A, 0x07, 0xC1, 0xF9, 0xD7, 0x12, 0x8E, 0x6B, 0x74, 0x2A, 0x6B, 0x9C, 0x2A, 0x76, 0xA1, 0x3C, 0x75, 0x00, 0x15, 0xF0, 0xD7, 0xA6, 0xE0, 0xE2, 0x58, 0x28, 0xA4, 0xCF, 0xBA, 0x49, 0x4B, 0x77, 0xF3, 0x33, 0x24, 0x2F, 0xE8, 0xA9, 0x22, 0xE3, 0xB2, 0x9F, 0xD3, 0x84, 0x10, 0x21, 0x1F, 0x9A, 0x54, 0x86, 0xB8, 0xBA, 0xFA, 0x0E, 0xFC, 0xD3, 0x96, 0xC3, 0xF4, 0x54, 0x86, 0xB8, 0x89, 0x48, 0x95, 0x07, 0x98, 0x56, 0xAB, 0x59, 0x19, 0xEA, 0x80, 0x90, 0xBD, 0x34, 0x8D, 0x07, 0xA3, 0xC8, 0xC8, 0xB8, 0xBE, 0x46, 0x43, 0xDE, 0x7F, 0x7A, 0xB3, 0x2F, 0xEF, 0x92, 0x4E, 0x3C, 0x59, 0x8C, 0x6F, 0x84, 0x14, 0x6B, 0xDD, 0xDD, 0xCB, 0xCC, 0x64, 0x92, 0xCE, 0x2F, 0x6B, 0x02, 0xAD, 0xA3, 0x9D, 0x4F, 0x00, 0x00, 0x91, 0x58, 0xE9, 0x7B, 0x3E, 0x73, 0xE0, 0xD8, 0xD2, 0x9E, 0xBB, 0x43, 0x48, 0x97, 0x7D, 0x97, 0xD1, 0x8D, 0x0C, 0xD8, 0x17, 0xCA, 0x9C, 0x4A, 0x2F, 0x16, 0x3F, 0x34, 0xA5, 0x11, 0xCB, 0x52, 0x5A, 0x9D, 0x9A, 0x67, 0x40, 0x22, 0x4F, 0x49, 0xF1, 0x50, 0x02, 0x71, 0x41, 0x32, 0x96, 0xD1, 0x2B, 0xDB, 0x25, 0xD6, 0x4E, 0x31, 0xAF, 0xCC, 0xD9, 0x2C, 0x2A, 0x33, 0x60, 0xDD, 0x54, 0xE7, 0x20, 0x18, 0xA2, 0x96, 0x9F, 0xA5, 0x2D, 0x98, 0x92, 0x70, 0x2C, 0x9B, 0xE3, 0x23, 0x63, 0x2A, 0xB7, 0x4F, 0x10, 0xE0, 0xF3, 0x62, 0x79, 0x1F, 0xAC, 0x5D, 0xB0, 0xD1, 0x58, 0x1B, 0x39, 0x91, 0xBC, 0xF2, 0x85, 0x67, 0xAB, 0xBD, 0x4D, 0x26, 0xBE, 0xEE, 0xF4, 0x54, 0x14, 0xB3, 0x4D, 0xB5, 0x16, 0x12, 0x63, 0x9D, 0xF7, 0xD8, 0xB4, 0x7A, 0x59, 0x05, 0x2A, 0xB8, 0xD5, 0xC6, 0xD1, 0xC5, 0xE2, 0xB6, 0x5A, 0x62, 0x12, 0x18, 0x9C, 0x9B, 0x92, 0x78, 0x6B, 0x98, 0xFF, 0x82, 0xE9, 0x5E, 0x0C, 0x4C, 0x4F, 0x00, 0x00, 0x94, 0x58, 0xE8, 0x6F, 0xF2, 0x50, 0x17, 0x8A, 0x4D, 0x6D, 0xE9, 0x4E, 0xD0, 0x06, 0xBB, 0x28, 0x30, 0xCB, 0x54, 0xB7, 0x4A, 0xCD, 0x99, 0xD1, 0x03, 0x77, 0x95, 0xAF, 0x14, 0x87, 0xB6, 0x49, 0xE9, 0xB9, 0x23, 0x3A, 0xB3, 0xAE, 0xE6, 0x04, 0x04, 0xF1, 0xF2, 0xB6, 0xCC, 0xC5, 0x66, 0x7F, 0x21, 0x56, 0x69, 0x3B, 0x5F, 0x74, 0x8C, 0x47, 0x82, 0xB0, 0x0F, 0x23, 0x31, 0x96, 0xB4, 0xFA, 0xA0, 0x30, 0x9D, 0xD0, 0x8E, 0xF8, 0xA5, 0x29, 0x71, 0x98, 0xA3, 0x91, 0xED, 0x30, 0xEA, 0x98, 0x29, 0x17, 0xB3, 0x45, 0x67, 0x45, 0x18, 0x43, 0x41, 0xF7, 0x91, 0x5D, 0x24, 0xF4, 0xCD, 0x4A, 0x6C, 0xDE, 0xF5, 0x40, 0x18, 0x0E, 0x86, 0x16, 0xD2, 0x5B, 0xAA, 0x05, 0xE5, 0xCC, 0xD9, 0xD5, 0x2F, 0x23, 0xC1, 0x52, 0xB0, 0x19, 0x01, 0xC2, 0x41, 0xAB, 0xC9, 0x73, 0x3B, 0x37, 0x02, 0xD1, 0x62, 0x2E, 0xE4, 0xB6, 0xA6, 0x19, 0x27, 0x83, 0xBD, 0x01, 0xCB, 0x7C, 0x58, 0xE5, 0xC3, 0x12, 0x62, 0x04, 0x52, 0x54, 0x20, 0x4F, 0x00, 0x00, 0x8A, 0x58, 0xE9, 0x7B, 0x4B, 0xE2, 0xEE, 0xED, 0x5A, 0x0F, 0x6C, 0xC1, 0x85, 0xBD, 0xDD, 0xE1, 0x67, 0x6C, 0x19, 0xCD, 0xE9, 0xE2, 0xB1, 0x21, 0xFC, 0xBB, 0xA8, 0x60, 0xB8, 0x5B, 0xB3, 0x73, 0xF7, 0x54, 0xD1, 0xDB, 0xC0, 0xC2, 0x38, 0x41, 0x47, 0x4D, 0xC8, 0xDA, 0x28, 0xBC, 0x4F, 0xDD, 0x95, 0x4C, 0x99, 0xD4, 0xF0, 0xB4, 0x9B, 0x04, 0xE3, 0x90, 0xD3, 0xFD, 0xFE, 0x7F, 0x64, 0x53, 0x68, 0xD3, 0x3A, 0x94, 0xC8, 0xF2, 0x77, 0xD5, 0xA1, 0x8C, 0x19, 0xA4, 0x02, 0x21, 0x3F, 0x72, 0x78, 0xEC, 0x44, 0x7C, 0xB6, 0xC8, 0x37, 0xAA, 0xAA, 0x60, 0x90, 0xC0, 0x42, 0x9E, 0xED, 0x4C, 0x88, 0xDF, 0x2E, 0x61, 0x17, 0x3E, 0xF9, 0x9B, 0x14, 0x1E, 0x7B, 0x6B, 0x6E, 0xB4, 0xD3, 0xD6, 0x6D, 0x3E, 0xC0, 0x84, 0xF3, 0x9B, 0x9C, 0xE0, 0x99, 0xC0, 0x5B, 0x1F, 0x62, 0xDC, 0xBE, 0xF6, 0x54, 0xEE, 0xB6, 0xA3, 0xCE, 0xBB, 0x13, 0x3F, 0x50, 0x25, 0x8C, 0x4F, 0x00, 0x00, 0x9C, 0x58, 0xE8, 0x71, 0x63, 0x2C, 0xB7, 0x80, 0x94, 0x6E, 0x63, 0x90, 0xC6, 0xC9, 0x23, 0x20, 0x5C, 0xEC, 0x57, 0xE6, 0xDA, 0x73, 0xB2, 0xC2, 0x31, 0xFD, 0x3B, 0x51, 0x05, 0x71, 0x7D, 0xBE, 0x15, 0x5A, 0xA2, 0x7C, 0x00, 0xBD, 0x0F, 0xAD, 0xD8, 0xF0, 0x2F, 0x0D, 0xD1, 0xB6, 0xD1, 0xD2, 0xC3, 0x83, 0xF9, 0x77, 0x66, 0x41, 0x8E, 0xFE, 0x4B, 0x84, 0x8E, 0x3E, 0x64, 0x19, 0xCC, 0xD9, 0x77, 0xC1, 0x75, 0xF7, 0x69, 0x38, 0xB6, 0x62, 0x7D, 0x62, 0x30, 0x36, 0x03, 0xAF, 0x21, 0x54, 0xFE, 0xA0, 0x14, 0x49, 0x7B, 0x6F, 0x30, 0x11, 0x48, 0x78, 0x6F, 0x33, 0x44, 0xDF, 0xB6, 0xC3, 0x11, 0x38, 0x97, 0xAA, 0xC4, 0x14, 0x28, 0xDD, 0x41, 0x29, 0xA2, 0x70, 0x78, 0x81, 0x89, 0xDF, 0xB8, 0x42, 0x6D, 0x14, 0xA8, 0xBC, 0xB6, 0x67, 0x9C, 0x50, 0x35, 0xFB, 0x52, 0x63, 0x87, 0x30, 0xD5, 0x98, 0x19, 0x1A, 0x7B, 0x57, 0xA7, 0xC1, 0xBA, 0xF0, 0xEE, 0x47, 0x90, 0x2A, 0x7C, 0x24, 0xB7, 0x25, 0xB1, 0x4A, 0x58, 0x7E, 0x92, 0xD5, 0xF1, 0xF2, 0xE8, 0x34, 0xB3, 0x4F, 0x00, 0x00, 0x80, 0x58, 0xE9, 0xCB, 0x0A, 0xDE, 0x3E, 0xBE, 0x24, 0x1D, 0x19, 0xD3, 0xBC, 0xAB, 0x3C, 0x26, 0x8B, 0xA6, 0xB5, 0x22, 0xBF, 0x54, 0x73, 0x4D, 0x5E, 0x67, 0xB6, 0xC8, 0xCA, 0xDB, 0x6F, 0x56, 0x25, 0xE2, 0xB1, 0x15, 0x8B, 0x14, 0x5A, 0xC4, 0x82, 0xEC, 0x64, 0xFD, 0xFF, 0x14, 0x16, 0xC4, 0x80, 0x6C, 0xE8, 0x24, 0x51, 0x3C, 0x58, 0x24, 0x82, 0x8B, 0x6C, 0xB7, 0x01, 0x1E, 0x29, 0xC1, 0x1F, 0xD4, 0xD4, 0xD5, 0xA3, 0x06, 0x7A, 0x7E, 0xAE, 0x33, 0xD9, 0x9E, 0x10, 0x09, 0x56, 0x16, 0x33, 0x03, 0xF2, 0x46, 0x6E, 0x46, 0x91, 0x5D, 0x74, 0xB4, 0xEC, 0x82, 0x85, 0x4E, 0x90, 0x5F, 0xA0, 0x07, 0x48, 0xB2, 0x47, 0xBD, 0x8E, 0x21, 0xED, 0x18, 0x6E, 0xE8, 0x2B, 0x88, 0xC1, 0xD6, 0x19, 0x96, 0x01, 0x7C, 0xE8, 0x6B, 0xA3, 0x8B, 0xE4, 0xDE, 0xB9, 0x5A, 0xD9, 0xB9, 0xB7, 0x2B, 0x98, 0x4F, 0x00, 0x00, 0x91, 0x58, 0xE9, 0x15, 0x93, 0x2E, 0xD1, 0xB7, 0x8A, 0x99, 0x72, 0x1A, 0xF9, 0xB3, 0xEF, 0x1B, 0xC2, 0xD9, 0x45, 0xAD, 0x1E, 0xF9, 0x51, 0x6C, 0xC9, 0x7E, 0x4F, 0x0C, 0xF0, 0xCA, 0x00, 0x9D, 0x71, 0x6F, 0xC1, 0x73, 0x38, 0x42, 0xE7, 0xDC, 0xB9, 0xDA, 0x6B, 0x85, 0x2B, 0x09, 0x8F, 0x24, 0x4F, 0xBD, 0x59, 0xE4, 0x7D, 0x21, 0x7E, 0x98, 0xB5, 0x46, 0x86, 0x5D, 0xDD, 0x51, 0x69, 0x51, 0xD3, 0x6C, 0xAE, 0x95, 0x2D, 0x5F, 0xEC, 0x72, 0xA8, 0x3B, 0xA4, 0x9A, 0xDF, 0x39, 0x7D, 0x7C, 0x20, 0x3A, 0x03, 0x6C, 0xEF, 0xD0, 0x08, 0xDD, 0x42, 0xB6, 0x7C, 0xC7, 0x60, 0x2C, 0x53, 0xCF, 0x47, 0xD5, 0x3F, 0x32, 0xDF, 0x61, 0x50, 0xDD, 0x3E, 0x80, 0x69, 0xBC, 0xA9, 0x02, 0x34, 0x1C, 0x21, 0xBD, 0x2D, 0xDE, 0x3D, 0x72, 0xCC, 0x79, 0x00, 0xC2, 0xC3, 0xAC, 0x20, 0xDC, 0xED, 0xE0, 0x82, 0x3B, 0xD6, 0x8F, 0x40, 0xE3, 0x4D, 0x5A, 0x42, 0xA6, 0xC5, 0xC3, 0x85, 0x71, 0xC2, 0x49, 0xD6, 0xC8, 0x4F, 0x00, 0x00, 0x8B, 0x58, 0xEA, 0x35, 0x8A, 0x89, 0x5E, 0x54, 0xA7, 0xCA, 0x9D, 0x9A, 0x64, 0x4F, 0xFE, 0x7C, 0x73, 0x25, 0x70, 0xC5, 0x83, 0x86, 0xE6, 0x7D, 0x6D, 0x36, 0x88, 0xC5, 0x38, 0x57, 0xA1, 0x65, 0xA8, 0x05, 0x91, 0xD1, 0xFF, 0x7C, 0xE4, 0x37, 0xB0, 0x83, 0x22, 0x0B, 0xDE, 0x24, 0x75, 0xC7, 0x47, 0xB4, 0x2E, 0x17, 0xA9, 0x5E, 0x5E, 0xD8, 0xE9, 0x71, 0xB6, 0x37, 0x1D, 0x1A, 0xEB, 0x3B, 0x9E, 0xCA, 0x1D, 0xDE, 0xDD, 0xA8, 0x19, 0xE6, 0xE6, 0xCE, 0x11, 0x6B, 0x97, 0xE1, 0x98, 0x47, 0x02, 0xBE, 0x9A, 0x94, 0xE3, 0x57, 0x99, 0xE8, 0xE2, 0xD8, 0xE6, 0x99, 0xC5, 0x6D, 0xA1, 0xC7, 0x77, 0xB9, 0x93, 0x03, 0x3D, 0x3B, 0xF4, 0x7D, 0xF0, 0xFB, 0x80, 0x95, 0x43, 0x14, 0x43, 0x20, 0xF6, 0x1D, 0x0E, 0xAA, 0x83, 0x16, 0xAB, 0x8B, 0x1B, 0x78, 0xA2, 0xC8, 0x97, 0x2F, 0xDA, 0xD9, 0x74, 0x77, 0x4C, 0x73, 0x06, 0x2E, 0x20, 0xD7, 0x6E, 0x3F, 0x8C, 0x89, 0x4F, 0x00, 0x00, 0x95, 0x58, 0xE9, 0x66, 0x15, 0x8D, 0xA2, 0x9E, 0xF1, 0x2D, 0xDB, 0x01, 0x10, 0xE4, 0x32, 0x27, 0xCA, 0x61, 0x2C, 0x9D, 0xC8, 0x8E, 0xA3, 0xD3, 0x90, 0xF3, 0x0D, 0x9D, 0x58, 0x51, 0xD7, 0x38, 0x15, 0x9A, 0x2C, 0x86, 0x05, 0xA4, 0x95, 0x9D, 0x46, 0xE5, 0xBC, 0x79, 0x5C, 0xB0, 0x1C, 0x95, 0xED, 0x06, 0x4C, 0x18, 0x99, 0xB1, 0x3A, 0x3D, 0x8F, 0x0A, 0x8B, 0x6E, 0x4F, 0x86, 0xBA, 0x12, 0xB4, 0x03, 0xED, 0x6A, 0xE5, 0x15, 0x06, 0x27, 0xD3, 0x07, 0xED, 0x04, 0x5F, 0x1B, 0x7F, 0xF9, 0x7B, 0xD5, 0xE6, 0xA8, 0x22, 0x28, 0xA7, 0xE8, 0xCF, 0xC4, 0x42, 0x42, 0x6B, 0xD9, 0x02, 0x87, 0x2A, 0x98, 0x6A, 0x48, 0xCF, 0xF0, 0x5F, 0x69, 0x5F, 0xAD, 0xB9, 0x61, 0xF3, 0x5C, 0x6F, 0x74, 0x33, 0x98, 0xEE, 0xBC, 0x4C, 0x1C, 0x5A, 0x88, 0xEF, 0xBF, 0x7D, 0x56, 0x37, 0x41, 0x3D, 0x93, 0xD7, 0x14, 0xC9, 0xCC, 0x39, 0x4E, 0xEF, 0xEC, 0x1B, 0x6A, 0x13, 0xF6, 0xF7, 0xE1, 0xB2, 0xC8, 0xC2, 0x8D, 0xA8, 0x0C, 0x54, 0x20, 0x4F, 0x00, 0x00, 0x7C, 0x58, 0xEA, 0x35, 0x9F, 0x74, 0x4F, 0x24, 0xEF, 0x6A, 0x75, 0x1E, 0xAE, 0xFB, 0x33, 0x6C, 0x73, 0x5F, 0xDB, 0xF2, 0xCA, 0xA8, 0x24, 0xD0, 0x48, 0x66, 0xF1, 0x26, 0xBB, 0x97, 0x36, 0xFC, 0x8D, 0x3D, 0xE4, 0xDD, 0x5F, 0xF7, 0xA4, 0xB7, 0x9C, 0xA2, 0x40, 0x04, 0xFA, 0xE7, 0xE6, 0x2F, 0xEF, 0xBF, 0x71, 0xAF, 0x68, 0xD5, 0xB4, 0x3D, 0x8D, 0x39, 0x4B, 0x9C, 0xB8, 0xE4, 0x72, 0x24, 0x4C, 0xFF, 0x67, 0xA1, 0x71, 0x59, 0x23, 0x3C, 0xA0, 0x3E, 0x98, 0x85, 0x67, 0x5E, 0x6D, 0x1D, 0xBE, 0x89, 0x84, 0x90, 0xDE, 0x66, 0xB9, 0x17, 0x78, 0x26, 0x1F, 0xB8, 0x4E, 0xAE, 0xD3, 0x30, 0x7C, 0xA4, 0xDD, 0x28, 0x40, 0x4D, 0x49, 0x96, 0x6C, 0xBE, 0x2A, 0xCD, 0x1A, 0xCB, 0xF1, 0x84, 0xE6, 0x89, 0xB3, 0x97, 0x86, 0x18, 0xD8, 0x69, 0x54, 0x13, 0x3D, 0x6A, 0x18, 0x4F, 0x00, 0x00, 0x01, 0x58, 0x4F, 0x00, 0x00, 0x01, 0x58, 0x4F, 0x00, 0x00, 0x01, 0x58, 0x4F, 0x00, 0x00, 0x01, 0x58, 0x4F, 0x00, 0x00, 0x01, 0x58, 0x4F, 0x00, 0x00, 0x01, 0x58, 0x4F, 0x00, 0x00, 0x01, 0x58, 0x4F, 0x00, 0x00, 0x01, 0x58, 0x4F, 0x00, 0x00, 0x01, 0x58, 
};

#endif // AUDIO_DATA_WAIT_H