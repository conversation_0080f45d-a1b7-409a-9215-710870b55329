#ifndef AUDIO_DATA_4_H
#define AUDIO_DATA_4_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfo4[] = {0, 24, 49, 104, 235, 366, 513, 661, 829, 969, 1053};
const uint8_t g_audioData4[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x33, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x0E, 0xF3, 0x38, 0x71, 0x0C, 0xF7, 0x75, 0xAD, 0x7C, 0x18, 0x7A, 0x08, 0x52, 0x2A, 0x0A, 0x33, 0x4F, 0x6B, 0x15, 0x54, 0xD3, 0x31, 0x48, 0x31, 0x64, 0xF0, 0x01, 0x24, 0x3D, 0xEA, 0x8A, 0xD3, 0xCC, 0x43, 0x6A, 0x3F, 0xBA, 0x8C, 0x8B, 0x91, 0xAC, 0xC6, 0xA0, 0x4F, 0x00, 0x00, 0x7F, 0x58, 0xE3, 0x8E, 0x79, 0x9C, 0x4E, 0x70, 0x52, 0x8F, 0x79, 0xD0, 0x3F, 0x32, 0xFA, 0xE9, 0x25, 0x3F, 0x04, 0xBE, 0x67, 0xEA, 0xDA, 0xA5, 0x69, 0x9F, 0xC9, 0xB1, 0x8C, 0xD3, 0x1C, 0x0D, 0xCF, 0xC5, 0x82, 0xDC, 0x7D, 0x0E, 0x91, 0xFB, 0x61, 0x32, 0x29, 0xA5, 0x05, 0xF6, 0xE3, 0xEB, 0xF4, 0x9C, 0xD4, 0xD7, 0x90, 0x05, 0xF5, 0x87, 0x48, 0x6E, 0x64, 0x8B, 0xB2, 0xA5, 0x7A, 0xDA, 0x63, 0xD8, 0x9B, 0xB4, 0xF9, 0x1D, 0x45, 0xC1, 0xE2, 0x78, 0x60, 0x1A, 0x68, 0x4B, 0xFE, 0xF7, 0xFA, 0xBD, 0xD6, 0x35, 0xDA, 0x31, 0x3E, 0xAA, 0x27, 0x3D, 0xED, 0x21, 0x5D, 0x71, 0xC4, 0xE0, 0xC7, 0x41, 0x57, 0x7B, 0xC7, 0xBF, 0x3B, 0x9C, 0xC3, 0x01, 0xB3, 0xF2, 0x0F, 0x03, 0x10, 0x56, 0xF4, 0x69, 0xAB, 0x06, 0xDF, 0x5F, 0xF2, 0x7A, 0x37, 0xB3, 0xDA, 0x37, 0x48, 0x74, 0xC9, 0x8A, 0x4F, 0x00, 0x00, 0x7F, 0x58, 0xE4, 0x3E, 0xA9, 0x01, 0xC9, 0x0A, 0x96, 0x0D, 0x8A, 0xBD, 0xC0, 0xE5, 0x80, 0xE5, 0x27, 0x65, 0x4A, 0x9D, 0x9F, 0xBD, 0x09, 0x76, 0xD0, 0x2D, 0x16, 0x82, 0xAF, 0xA6, 0xC2, 0x65, 0xAB, 0x07, 0x07, 0x2A, 0xCC, 0x08, 0x3F, 0x48, 0x61, 0x5B, 0x84, 0xB6, 0xC4, 0xE2, 0xBC, 0xFB, 0x56, 0x66, 0xBA, 0x45, 0xF6, 0x8C, 0x90, 0x73, 0x15, 0x7F, 0xB8, 0xE7, 0x9E, 0xC2, 0xEE, 0x73, 0x1D, 0xF2, 0x84, 0x2C, 0x3A, 0xF9, 0x9C, 0x56, 0xD3, 0x19, 0x0B, 0x83, 0x2A, 0xE9, 0x40, 0x90, 0x13, 0x3B, 0x4A, 0x60, 0xA5, 0x20, 0x3C, 0x5E, 0x3A, 0x21, 0xF6, 0x50, 0xF8, 0x65, 0xCD, 0x8C, 0x14, 0x23, 0x4E, 0x32, 0x6D, 0x2B, 0xAD, 0xF3, 0xF1, 0x73, 0x5B, 0xF7, 0xC1, 0xE7, 0xBE, 0x7F, 0xE4, 0x38, 0xD8, 0x81, 0x8D, 0x54, 0xD1, 0x57, 0xB3, 0xA7, 0x66, 0x96, 0x84, 0xA2, 0xC4, 0xF8, 0x4F, 0x00, 0x00, 0x8F, 0x58, 0xE4, 0xA5, 0x23, 0x71, 0xA7, 0x68, 0x31, 0x42, 0xB4, 0x24, 0x75, 0xF4, 0x9E, 0x6D, 0xA9, 0xCD, 0xC3, 0x97, 0xB2, 0xC7, 0x97, 0xB7, 0xDC, 0x94, 0x2B, 0x25, 0xBA, 0xE8, 0x75, 0x54, 0xB5, 0x75, 0xC4, 0x42, 0xA1, 0x40, 0x9B, 0x42, 0x89, 0x8C, 0xAE, 0x8D, 0xE2, 0x0C, 0xDD, 0x77, 0x46, 0x26, 0xB4, 0x75, 0xAC, 0x08, 0x81, 0x51, 0xCD, 0x2C, 0xCD, 0x91, 0xE5, 0xB4, 0x44, 0x36, 0x9D, 0xFB, 0xC0, 0xA5, 0x93, 0x60, 0x5F, 0x1E, 0x05, 0x77, 0xAA, 0xE3, 0xFB, 0x4D, 0x4C, 0x4F, 0x19, 0x75, 0x69, 0xA0, 0x45, 0x8D, 0x75, 0x14, 0x55, 0x1B, 0x02, 0x8A, 0xEB, 0xF5, 0x9A, 0x93, 0xEE, 0x79, 0x4F, 0x86, 0x5A, 0x17, 0xEC, 0x61, 0x7E, 0x28, 0x28, 0xCD, 0x0B, 0x8F, 0xBE, 0x12, 0x6B, 0x30, 0xA1, 0xFC, 0x92, 0x65, 0x87, 0xCE, 0xFC, 0x64, 0xC0, 0x2D, 0x0B, 0xA7, 0xA6, 0xB4, 0x07, 0x48, 0x18, 0x4F, 0x90, 0xF9, 0x4E, 0x17, 0xDD, 0x9E, 0xAD, 0x75, 0xD4, 0xCC, 0x90, 0x46, 0x4F, 0x00, 0x00, 0x90, 0x58, 0xED, 0x46, 0xDA, 0xC3, 0x22, 0x28, 0x0F, 0xC8, 0xC6, 0x14, 0xCD, 0x9C, 0xEC, 0xBD, 0x30, 0x2A, 0x6C, 0x5B, 0xF6, 0xFC, 0xA9, 0x42, 0xC2, 0x7D, 0xD9, 0x1A, 0xF0, 0x2F, 0xAE, 0xBD, 0x4B, 0x18, 0x15, 0xAE, 0xDF, 0xD8, 0x76, 0xF6, 0xAE, 0x0A, 0xE1, 0x76, 0x8F, 0xEC, 0x5F, 0xE7, 0x0A, 0xDC, 0x12, 0xA1, 0xD8, 0x4C, 0xB1, 0x2A, 0x5E, 0x26, 0xD4, 0x76, 0x7A, 0x4E, 0x00, 0xE5, 0x09, 0x61, 0xDF, 0x0B, 0x10, 0xA6, 0x1A, 0x83, 0x78, 0x62, 0xEA, 0xB5, 0xF8, 0xE1, 0x37, 0x91, 0x21, 0xA1, 0xBB, 0xA5, 0x24, 0x71, 0xBD, 0xB7, 0xB6, 0xA8, 0x67, 0xF2, 0x0F, 0xFA, 0xC6, 0x95, 0xBE, 0x0B, 0x4B, 0x1F, 0x68, 0xE5, 0x56, 0x52, 0x06, 0x24, 0x9D, 0xB0, 0x37, 0x71, 0x25, 0xA9, 0xF2, 0xA9, 0x46, 0x24, 0x13, 0x8C, 0xD4, 0xEB, 0x37, 0x27, 0x09, 0xA6, 0x2C, 0x55, 0x6B, 0xB6, 0x80, 0x2E, 0x22, 0x3A, 0x58, 0xA8, 0xB4, 0xF3, 0x8B, 0x4A, 0xCC, 0x5F, 0x9C, 0x6E, 0x16, 0x71, 0x80, 0x4F, 0x00, 0x00, 0xA4, 0x58, 0xED, 0x83, 0xB2, 0x50, 0x4B, 0x51, 0x3A, 0x5F, 0xB2, 0x8F, 0x4F, 0xEA, 0x2F, 0x60, 0x72, 0x9E, 0x20, 0x1D, 0x3A, 0x95, 0x76, 0x87, 0x04, 0x7E, 0x09, 0xC4, 0x5C, 0xF0, 0x10, 0x90, 0x2D, 0x96, 0xBA, 0x79, 0x3B, 0x84, 0x8C, 0x04, 0x91, 0x3A, 0xB7, 0xE0, 0x5C, 0xDF, 0x38, 0xE7, 0x2A, 0x0C, 0x22, 0x71, 0xEE, 0x61, 0x10, 0x0D, 0x74, 0xF5, 0x70, 0x97, 0xCA, 0x88, 0x95, 0x3E, 0xEE, 0x8B, 0x1E, 0x3C, 0x74, 0xF2, 0x4D, 0xFF, 0x12, 0x53, 0xC3, 0x22, 0xBB, 0xE0, 0x0A, 0xD0, 0x9F, 0x39, 0x04, 0x2F, 0xA2, 0xF7, 0x36, 0x40, 0xE3, 0x63, 0x05, 0x15, 0xC4, 0x3A, 0x57, 0xAA, 0x5B, 0x78, 0xC2, 0xDE, 0xDA, 0x5F, 0xF4, 0x91, 0xC2, 0x8B, 0x97, 0xF8, 0xBB, 0x05, 0xBD, 0x50, 0x40, 0xF9, 0x53, 0x15, 0x73, 0x18, 0x58, 0x70, 0x5A, 0xFC, 0x3D, 0x8B, 0x45, 0xB9, 0x46, 0x10, 0xA2, 0xEB, 0x0B, 0x8C, 0x1C, 0x1A, 0x62, 0x44, 0xD4, 0x67, 0x41, 0x13, 0xF7, 0xFC, 0x00, 0x79, 0x9C, 0xF2, 0x8D, 0x02, 0x9A, 0xCE, 0xAF, 0x7F, 0x90, 0x48, 0x93, 0xFA, 0x5A, 0x96, 0x45, 0x70, 0x67, 0x38, 0xC8, 0x96, 0xD0, 0x4F, 0x00, 0x00, 0x88, 0x58, 0xEE, 0x53, 0xE1, 0x1F, 0x84, 0x36, 0x6A, 0xC2, 0xC9, 0x1C, 0xA0, 0xAE, 0x10, 0x26, 0x4E, 0xB2, 0x77, 0x3E, 0x82, 0x7B, 0x50, 0x2A, 0xA9, 0x89, 0x42, 0x1F, 0x7B, 0x94, 0x29, 0x1F, 0x2A, 0xBC, 0xE1, 0x40, 0x37, 0xF0, 0x66, 0xB3, 0x05, 0xE8, 0x19, 0xAF, 0xA8, 0xF3, 0x95, 0x23, 0x1D, 0xE0, 0xC2, 0xB6, 0x5E, 0x3D, 0x71, 0xCD, 0x6D, 0x97, 0x7A, 0x2C, 0xEE, 0xE5, 0xD4, 0x56, 0x38, 0x79, 0x0F, 0xBE, 0xB9, 0x32, 0xB0, 0x9B, 0xD0, 0x76, 0xC6, 0x42, 0x0B, 0xE4, 0x09, 0x9B, 0x43, 0xC0, 0xEF, 0x22, 0x59, 0x26, 0x5D, 0x30, 0xA2, 0x64, 0x4C, 0x1D, 0xD7, 0x39, 0x5F, 0x50, 0xFA, 0x72, 0xE9, 0x71, 0xD0, 0xE3, 0x4D, 0xA3, 0x76, 0x95, 0x41, 0x5C, 0x8D, 0xD5, 0xBB, 0x71, 0xF8, 0xED, 0x36, 0x5C, 0xEC, 0xFD, 0x46, 0x98, 0x58, 0xF8, 0xC1, 0x7E, 0x37, 0xEF, 0x6A, 0x7F, 0x85, 0x14, 0xC4, 0x36, 0xB5, 0x0E, 0x76, 0xD8, 0xC0, 0x4F, 0x00, 0x00, 0x50, 0x58, 0x82, 0xAF, 0xFE, 0x4B, 0x6D, 0x66, 0xAD, 0x4F, 0x1B, 0x05, 0x3C, 0xDF, 0x43, 0x18, 0x98, 0x1E, 0x59, 0x1C, 0xF2, 0x36, 0xEA, 0x66, 0x2B, 0x59, 0x52, 0xBD, 0xE7, 0x1C, 0x18, 0x35, 0x77, 0x05, 0x78, 0xD1, 0x26, 0xCC, 0x4D, 0xFF, 0xD3, 0x2B, 0x2B, 0x30, 0xB8, 0xBD, 0xC3, 0x8A, 0xF7, 0x4A, 0xB4, 0xAA, 0x59, 0x91, 0xA3, 0xC7, 0x82, 0xE6, 0x1C, 0x76, 0x6B, 0xCB, 0x32, 0x56, 0xD2, 0x2C, 0xEC, 0x21, 0x8E, 0x37, 0xD4, 0x4B, 0x7A, 0x29, 0x21, 0x0E, 0x0E, 0x93, 0x72, 0xFE, 0x20, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20
};

#endif // AUDIO_DATA_4_H