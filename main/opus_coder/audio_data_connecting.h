#ifndef AUDIO_DATA_CONNECTING_H
#define AUDIO_DATA_CONNECTING_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfoConnecting[] = {0, 24, 81, 209, 330, 464, 589, 735, 875, 996, 1101, 1238, 1366, 1478, 1603, 1653, 1678, 1703};
const uint8_t g_audioDataConnecting[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x35, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x6A, 0xC4, 0x31, 0xFF, 0x62, 0xA4, 0x47, 0x05, 0x37, 0x08, 0xB5, 0x44, 0x58, 0x64, 0x98, 0xAC, 0x67, 0xE7, 0xBF, 0x12, 0x9C, 0x54, 0xE1, 0x0A, 0x4F, 0x8B, 0x02, 0xAE, 0x28, 0xC5, 0xD7, 0xEF, 0x8F, 0x4C, 0xDA, 0xD7, 0xD3, 0xD5, 0x06, 0x4F, 0x00, 0x00, 0x7C, 0x58, 0xE8, 0x5E, 0x3B, 0xBB, 0x55, 0xA6, 0xA1, 0xC9, 0x5D, 0x95, 0x63, 0x3E, 0x81, 0x05, 0xCF, 0x3C, 0x55, 0xCA, 0xC9, 0xBE, 0xE3, 0x5C, 0xD7, 0xED, 0xE7, 0x44, 0x20, 0x04, 0x17, 0xA8, 0xD1, 0x47, 0x2E, 0xFB, 0xBA, 0x82, 0xC5, 0xB1, 0x7B, 0x19, 0xBF, 0xA9, 0x80, 0xAE, 0x48, 0xD4, 0x73, 0x9C, 0x82, 0x2D, 0x56, 0x80, 0x9C, 0xB0, 0xEC, 0x8A, 0x0A, 0xEA, 0x13, 0xA3, 0xF4, 0x17, 0x20, 0x43, 0x35, 0xA9, 0xC2, 0xAE, 0x48, 0xF1, 0x0E, 0x23, 0x49, 0xF8, 0x64, 0xE2, 0xD6, 0xE5, 0x71, 0x76, 0xEF, 0xBF, 0xE2, 0x62, 0x25, 0xE7, 0xE8, 0xC8, 0xAF, 0xEF, 0x25, 0x4D, 0x8D, 0x61, 0x4D, 0xF0, 0x29, 0xEF, 0x38, 0x55, 0x71, 0xFD, 0xC2, 0x9A, 0x34, 0x40, 0xB7, 0x63, 0x34, 0xE8, 0x90, 0x47, 0x92, 0x96, 0xC9, 0x5A, 0xC8, 0x54, 0x76, 0x4C, 0x74, 0x80, 0xB0, 0x4F, 0x00, 0x00, 0x75, 0x58, 0xED, 0xCC, 0x0A, 0xA5, 0xE7, 0xF7, 0x33, 0x61, 0x9E, 0xBA, 0xEC, 0xE1, 0x89, 0xC9, 0x7F, 0xA2, 0xC0, 0x9F, 0x4E, 0x39, 0x2F, 0x84, 0x0F, 0xBD, 0xA9, 0xE9, 0x36, 0x39, 0xAA, 0x56, 0xE2, 0x23, 0xDE, 0xAB, 0x0B, 0x90, 0xC3, 0x13, 0x3B, 0x8D, 0xA3, 0x03, 0x43, 0x24, 0xD9, 0x23, 0x53, 0x68, 0x81, 0xB8, 0xF2, 0x77, 0x24, 0xE9, 0x0F, 0x39, 0x48, 0xC0, 0xE7, 0x6E, 0xEF, 0x92, 0xCC, 0x26, 0x79, 0x3D, 0xB2, 0x93, 0xEA, 0xAC, 0x9C, 0xAA, 0x1B, 0xCD, 0xD4, 0xD2, 0xAD, 0xC0, 0x67, 0x94, 0x9E, 0xE6, 0x1B, 0x92, 0xB6, 0x28, 0x39, 0xAB, 0xAE, 0x0A, 0x49, 0x79, 0x07, 0x82, 0x86, 0x89, 0x0D, 0x33, 0x0D, 0x52, 0x34, 0x7D, 0x06, 0x62, 0xC5, 0x87, 0x2B, 0x64, 0xD2, 0x1B, 0x7F, 0x4D, 0x5B, 0x82, 0x4A, 0x40, 0x4F, 0x00, 0x00, 0x82, 0x58, 0xEE, 0xD4, 0x0A, 0x62, 0x0B, 0x10, 0xC1, 0xEB, 0x1B, 0x8B, 0xFB, 0x2D, 0x5C, 0xE5, 0x7B, 0x44, 0x92, 0xBE, 0x42, 0x8A, 0xD8, 0x09, 0xDD, 0xEC, 0xA5, 0x07, 0x90, 0xEF, 0x43, 0x6D, 0x79, 0xC2, 0xC7, 0x69, 0xE8, 0x02, 0x9E, 0x84, 0x99, 0x62, 0xD8, 0xFD, 0xA1, 0x35, 0x9F, 0xE0, 0x87, 0xA8, 0xD6, 0x7E, 0x0E, 0x44, 0xF3, 0x84, 0xC1, 0xD2, 0x9E, 0xF4, 0xC1, 0xCD, 0xD3, 0x8F, 0x5B, 0xD8, 0xAE, 0x15, 0x17, 0x4A, 0x5D, 0x0C, 0xC6, 0xD7, 0x0E, 0xE6, 0xA9, 0x93, 0xAC, 0x9A, 0x5B, 0x5D, 0xC9, 0x25, 0x72, 0x61, 0x51, 0x3E, 0xF5, 0xE2, 0xF2, 0xB7, 0xE2, 0x4B, 0x62, 0xB4, 0x21, 0x8D, 0x14, 0xF0, 0xC7, 0x46, 0x0C, 0x29, 0xAF, 0x0E, 0xDD, 0xF6, 0xD0, 0x56, 0x45, 0xD4, 0xB6, 0xC6, 0x86, 0x52, 0x22, 0x10, 0x75, 0x84, 0xC2, 0xF8, 0xB1, 0x2B, 0x44, 0x36, 0x17, 0xDD, 0x0E, 0x31, 0xC8, 0x4F, 0x00, 0x00, 0x79, 0x58, 0xEB, 0x95, 0x84, 0x6B, 0xCC, 0xDD, 0x72, 0x5C, 0x61, 0x15, 0xF0, 0x5F, 0x3E, 0xCB, 0xAF, 0x01, 0x24, 0xBF, 0xBC, 0xFA, 0x8F, 0xA8, 0xB8, 0x80, 0xD8, 0x72, 0x68, 0xC4, 0x30, 0xD9, 0x5A, 0x81, 0x92, 0x5D, 0x4B, 0x5D, 0x0D, 0x09, 0x49, 0x6B, 0xBA, 0xCC, 0xB5, 0xF0, 0xDB, 0xBD, 0xE1, 0xD3, 0x6C, 0x69, 0x5A, 0xC3, 0x33, 0xE8, 0xA6, 0xFA, 0xAD, 0xDB, 0x01, 0x43, 0x45, 0x1B, 0x5E, 0x87, 0xF4, 0xFF, 0x27, 0x0C, 0x6A, 0x78, 0x27, 0xBE, 0x3D, 0xB8, 0x83, 0xF6, 0x96, 0x26, 0x5E, 0x0A, 0x3D, 0xC8, 0x76, 0x51, 0x98, 0x33, 0x1F, 0xDB, 0x12, 0xC9, 0x53, 0x86, 0xE6, 0x13, 0x75, 0x1E, 0x4C, 0xFB, 0x65, 0xAC, 0xCE, 0x00, 0xD9, 0x1D, 0x4A, 0x5F, 0xB7, 0x39, 0x38, 0xC4, 0x54, 0x75, 0x55, 0xD0, 0xE2, 0x2F, 0xAC, 0x28, 0xDB, 0x80, 0x4F, 0x00, 0x00, 0x8E, 0x58, 0xEA, 0xF5, 0x29, 0x1D, 0xE0, 0xDE, 0x91, 0xFC, 0x81, 0xC3, 0xAF, 0xBA, 0xAA, 0x27, 0x06, 0x65, 0x31, 0xD6, 0x6F, 0x1A, 0xB1, 0x34, 0xFB, 0xB7, 0x23, 0xF6, 0xE5, 0x74, 0x31, 0xF4, 0xB3, 0x00, 0x98, 0x94, 0x9A, 0x5D, 0x9F, 0x19, 0x0C, 0x24, 0xF9, 0xC9, 0xF8, 0x49, 0x5C, 0xBA, 0x65, 0xFD, 0x2A, 0xC9, 0xBD, 0x45, 0xF5, 0x98, 0x3B, 0xC7, 0x50, 0x25, 0x46, 0xF3, 0x5D, 0xE3, 0x27, 0xAC, 0xAE, 0x20, 0x6D, 0x98, 0x99, 0x0B, 0x69, 0x62, 0x4B, 0xC5, 0xEA, 0x25, 0x45, 0xA1, 0xDF, 0x94, 0x47, 0xA6, 0xB0, 0x02, 0x24, 0xE6, 0x11, 0x46, 0x07, 0xA0, 0x3D, 0x34, 0x42, 0x0E, 0x5F, 0x80, 0x0C, 0x79, 0xAC, 0x2E, 0x3F, 0xE0, 0x44, 0x2E, 0x32, 0xA4, 0x9D, 0xE0, 0xF3, 0x88, 0xC2, 0xB1, 0x3D, 0xCE, 0x8E, 0xB5, 0x85, 0x39, 0xB3, 0x8A, 0xAC, 0x03, 0x76, 0x58, 0xB0, 0x49, 0xCC, 0x8D, 0x05, 0xE1, 0x64, 0xAC, 0xA1, 0x8A, 0xCC, 0x03, 0x05, 0x0B, 0x6D, 0x25, 0x42, 0x4F, 0x00, 0x00, 0x88, 0x58, 0xED, 0xB3, 0xCA, 0xA3, 0x6F, 0x56, 0x83, 0xF0, 0x11, 0x35, 0x0E, 0x72, 0xFC, 0x5B, 0x0B, 0xF0, 0x60, 0x5A, 0xDB, 0xAE, 0x95, 0x13, 0x27, 0x7F, 0x61, 0x3F, 0xD7, 0xD7, 0x65, 0x6E, 0x61, 0x03, 0x93, 0xF1, 0x8B, 0xA5, 0x82, 0x3F, 0x50, 0xA2, 0x7F, 0x6F, 0x84, 0x9E, 0x66, 0x97, 0x06, 0xCF, 0x84, 0x91, 0x47, 0x3C, 0x3E, 0xF9, 0x57, 0xBE, 0x55, 0x8E, 0x0B, 0xFF, 0xA6, 0xD9, 0xA8, 0xDD, 0xB4, 0xEE, 0xF3, 0xAA, 0x01, 0x62, 0x5E, 0x8E, 0x32, 0x75, 0x93, 0x72, 0x2C, 0x7C, 0xAE, 0x06, 0x22, 0x0F, 0x1A, 0x57, 0x29, 0x81, 0xE8, 0x4A, 0x79, 0xE1, 0xFD, 0xA8, 0x45, 0xCA, 0xD0, 0x2D, 0x66, 0x92, 0xF4, 0x9A, 0x55, 0x1D, 0xA9, 0x05, 0x72, 0x4E, 0xA9, 0x02, 0x46, 0xF3, 0xF5, 0xF6, 0x8E, 0xB7, 0xF4, 0xEA, 0x45, 0xDB, 0x71, 0xE1, 0x37, 0x90, 0xD0, 0x7C, 0xFD, 0x58, 0x8C, 0xCA, 0xA8, 0xF5, 0x3C, 0x24, 0xA4, 0x82, 0x54, 0x4F, 0x00, 0x00, 0x75, 0x58, 0xE8, 0xD4, 0x7C, 0xF5, 0xAB, 0x04, 0x90, 0x8E, 0xB5, 0xE7, 0x86, 0x85, 0xB7, 0x1C, 0xDE, 0x4C, 0xCB, 0x6F, 0xF4, 0xDB, 0x48, 0xE2, 0x51, 0x58, 0xDB, 0xFD, 0xED, 0x7C, 0xCD, 0x59, 0x38, 0x28, 0x42, 0xC0, 0x33, 0xDD, 0x4E, 0xCB, 0xA2, 0x19, 0xE7, 0xBA, 0xD4, 0x2B, 0xF9, 0x0D, 0x96, 0xF1, 0xDB, 0xD2, 0x3B, 0x68, 0xD2, 0xC6, 0xC4, 0x69, 0xF1, 0xEF, 0xEA, 0x3E, 0xDB, 0xBB, 0x56, 0xF3, 0x30, 0x90, 0x8A, 0x4B, 0xFA, 0x71, 0xE0, 0xD5, 0x31, 0xC1, 0xE7, 0x15, 0xC4, 0x3E, 0x23, 0x7A, 0x7F, 0xE8, 0xCC, 0x49, 0xC3, 0x5F, 0xF1, 0x39, 0x84, 0x54, 0x69, 0x71, 0x5A, 0x8E, 0x62, 0x66, 0x79, 0x54, 0x2B, 0x10, 0x3A, 0xDA, 0xBE, 0x06, 0x80, 0x64, 0x4C, 0xAF, 0xF6, 0xAE, 0x52, 0xD2, 0x19, 0x76, 0x7A, 0x84, 0x4F, 0x00, 0x00, 0x65, 0x58, 0xA6, 0x8F, 0x22, 0xFD, 0x90, 0x42, 0xAD, 0xC5, 0x90, 0x68, 0x7E, 0xAD, 0xF1, 0x78, 0x1C, 0x94, 0x27, 0xEB, 0x8D, 0x37, 0xBB, 0x6D, 0x93, 0x6F, 0x91, 0x5C, 0x63, 0xB6, 0xBB, 0x01, 0x97, 0x9D, 0xD1, 0x16, 0xCB, 0x04, 0x39, 0x62, 0xD5, 0xB1, 0xCC, 0x91, 0xC6, 0xC3, 0x78, 0x1E, 0x51, 0x26, 0xFA, 0xD0, 0x33, 0x0C, 0xC9, 0x34, 0x5E, 0xE6, 0x48, 0xCE, 0x26, 0x2A, 0x08, 0x93, 0x73, 0xD0, 0x9E, 0x5E, 0x49, 0x2F, 0xB9, 0xB7, 0x84, 0x08, 0x79, 0x5A, 0x43, 0xC3, 0x91, 0x83, 0x17, 0x70, 0x90, 0xBA, 0x30, 0xC9, 0x8E, 0xF3, 0xD4, 0xC5, 0xBE, 0x8F, 0x0A, 0xFF, 0x17, 0x7B, 0xD9, 0xE5, 0xB3, 0x8A, 0x87, 0x74, 0x4F, 0x00, 0x00, 0x85, 0x58, 0xE1, 0x27, 0x3F, 0xF4, 0x95, 0x4B, 0xDA, 0x5F, 0xD8, 0x50, 0xD9, 0xF4, 0x86, 0xFC, 0xFD, 0xDE, 0xCD, 0xF9, 0x57, 0xE2, 0x01, 0x6C, 0xC7, 0x5A, 0x36, 0x0F, 0x7F, 0x4D, 0x09, 0xE1, 0x76, 0x40, 0x07, 0xDE, 0x4D, 0x1E, 0x92, 0x2C, 0x8A, 0xBB, 0xB9, 0x3A, 0x40, 0xB2, 0xA0, 0xA9, 0x07, 0xCB, 0x30, 0xA0, 0x0D, 0xFF, 0x47, 0xA0, 0xEB, 0x8F, 0xA6, 0x52, 0xB6, 0x25, 0x92, 0x3F, 0x6D, 0x70, 0x91, 0xE4, 0x82, 0xBC, 0x75, 0xC0, 0x84, 0xE3, 0xF7, 0x39, 0xDC, 0x77, 0x0D, 0x9C, 0xC9, 0xAA, 0x61, 0x1D, 0x30, 0x1C, 0xCB, 0x6F, 0xA9, 0xC6, 0xC6, 0xD8, 0x33, 0xA7, 0x6B, 0xE6, 0x0E, 0xDB, 0x02, 0x7A, 0x3C, 0x0A, 0x48, 0x17, 0x0B, 0x67, 0x42, 0xA2, 0x22, 0xAE, 0x71, 0xDD, 0x90, 0xA4, 0x48, 0x52, 0x8F, 0xE4, 0x95, 0x06, 0xE6, 0xBF, 0x65, 0x46, 0x9A, 0xCB, 0x35, 0x69, 0xF4, 0xAE, 0xB6, 0xE6, 0xE9, 0x80, 0x4F, 0x00, 0x00, 0x7C, 0x58, 0xED, 0x59, 0x1A, 0x57, 0xE1, 0x77, 0x35, 0xC0, 0x46, 0xDA, 0x6E, 0xA8, 0xBB, 0x17, 0xD0, 0x46, 0x1F, 0xD2, 0x74, 0xB6, 0xC0, 0x09, 0xCD, 0x41, 0xF5, 0xDC, 0xB5, 0xFA, 0x4F, 0x8A, 0x8B, 0x32, 0xBF, 0xEB, 0x16, 0x5D, 0x83, 0x8B, 0xF3, 0x71, 0xA1, 0x55, 0x5D, 0x1F, 0xA7, 0xAE, 0x6D, 0xDE, 0x13, 0x21, 0x40, 0x10, 0x77, 0xC9, 0x3D, 0xF9, 0x30, 0x97, 0x79, 0x7D, 0x51, 0xAD, 0x0A, 0x3E, 0x69, 0xC8, 0x95, 0x3C, 0xC8, 0x4A, 0xFE, 0xA9, 0xCF, 0x7A, 0xA8, 0xE8, 0xEF, 0x5A, 0x8E, 0x38, 0x35, 0x5F, 0x67, 0xC5, 0x1D, 0x3F, 0x80, 0x59, 0x1D, 0x68, 0x04, 0xEE, 0x5A, 0x1F, 0xE9, 0x92, 0x6B, 0xC1, 0x06, 0x30, 0x3A, 0x67, 0x3F, 0xBC, 0x5D, 0x83, 0xE0, 0x1A, 0x2D, 0x8C, 0xDC, 0x55, 0xF5, 0x86, 0x84, 0x4B, 0x67, 0x25, 0xFD, 0xDC, 0xCA, 0xEF, 0xA8, 0x4F, 0x00, 0x00, 0x6C, 0x58, 0xED, 0xA8, 0x35, 0x6D, 0xBD, 0x37, 0x6B, 0xA6, 0xDF, 0x75, 0xC4, 0x26, 0xF0, 0x8B, 0x7D, 0x44, 0xC3, 0xEC, 0x6C, 0xE9, 0xE8, 0x3F, 0x6C, 0x4E, 0x32, 0x30, 0x71, 0xDB, 0xFB, 0xB8, 0xF6, 0xF4, 0xAF, 0x2D, 0xB4, 0xC6, 0xA3, 0x16, 0x58, 0xC2, 0xBA, 0x10, 0x52, 0x84, 0x4E, 0xF0, 0x21, 0xBC, 0x5F, 0xD9, 0xE8, 0xC6, 0x28, 0xC1, 0xEE, 0xC6, 0xDF, 0x33, 0x1C, 0x6A, 0xDA, 0xAD, 0xD6, 0x1C, 0x18, 0x24, 0xCA, 0x07, 0x43, 0xD1, 0xF3, 0x18, 0x9F, 0xEB, 0x6E, 0x85, 0x8C, 0xD1, 0x39, 0x27, 0x4D, 0x7A, 0xD9, 0x49, 0x6F, 0x2C, 0xB3, 0xE5, 0xFB, 0x35, 0xF7, 0xFF, 0xA6, 0x47, 0x57, 0x85, 0x00, 0x14, 0x45, 0xC0, 0xBA, 0x33, 0x4E, 0x8A, 0x62, 0x4C, 0x80, 0x4F, 0x00, 0x00, 0x79, 0x58, 0xCA, 0x3D, 0x89, 0xD1, 0x21, 0x6C, 0xEC, 0x61, 0x67, 0x95, 0xF1, 0x55, 0x8D, 0xC1, 0xC3, 0x9C, 0xC6, 0x1C, 0x87, 0xB0, 0x96, 0x59, 0x5F, 0xEF, 0x65, 0xF4, 0x51, 0x0A, 0x0E, 0xCF, 0x4D, 0x30, 0x1E, 0x48, 0x71, 0xF3, 0xF6, 0x9B, 0x52, 0x2A, 0x9B, 0x8F, 0x7B, 0xDD, 0xF7, 0x59, 0x5A, 0x26, 0x16, 0xC8, 0x9E, 0x0A, 0xD5, 0x81, 0xFB, 0xB5, 0x9F, 0x3F, 0xCF, 0x4A, 0xFF, 0x40, 0x45, 0x0C, 0x40, 0xD5, 0x60, 0xD5, 0xFD, 0x51, 0xC6, 0xB4, 0x6B, 0xC8, 0x55, 0xB3, 0x6B, 0xD8, 0x69, 0x47, 0xD5, 0x5F, 0x35, 0x52, 0xF1, 0xBE, 0x6D, 0x87, 0x08, 0x7B, 0x4F, 0x54, 0xDD, 0x96, 0x00, 0x6F, 0xF7, 0xC1, 0xD4, 0x7D, 0xB8, 0x89, 0x53, 0xD0, 0xEC, 0x7D, 0x3D, 0x42, 0x8D, 0xF6, 0xF7, 0xB6, 0x0F, 0x9E, 0xC8, 0x98, 0x17, 0x35, 0xD9, 0x68, 0x4F, 0x00, 0x00, 0x2E, 0x58, 0x00, 0xBE, 0x93, 0xA6, 0x63, 0xA8, 0x55, 0x85, 0x83, 0x45, 0xA0, 0x6B, 0x4A, 0xF6, 0x73, 0xFC, 0x1F, 0x43, 0x73, 0xB7, 0xFC, 0xF1, 0x06, 0xB0, 0x62, 0x3A, 0x31, 0x29, 0x43, 0x49, 0xA7, 0x31, 0x8E, 0xCC, 0x7A, 0x69, 0x99, 0xCB, 0x48, 0x25, 0xD2, 0x6F, 0x2D, 0xF9, 0xC0, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20
};

#endif // AUDIO_DATA_CONNECTING_H