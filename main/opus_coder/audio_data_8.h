#ifndef AUDIO_DATA_8_H
#define AUDIO_DATA_8_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfo8[] = {0, 24, 49, 126, 255, 355, 439, 573, 640};
const uint8_t g_audioData8[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x49, 0x58, 0x61, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1E, 0xEF, 0x1D, 0xEC, 0x89, 0x2E, 0x6A, 0x70, 0xCC, 0x0C, 0xA5, 0x7B, 0x93, 0x0F, 0xD0, 0xD5, 0xA0, 0xDE, 0x13, 0xD1, 0x12, 0x34, 0xA5, 0x04, 0xAE, 0x7F, 0xB9, 0x81, 0x50, 0x09, 0x4B, 0x65, 0xA0, 0xF0, 0x33, 0x71, 0x2E, 0x41, 0x31, 0x7D, 0x71, 0xA9, 0xEB, 0x37, 0x93, 0x61, 0x8D, 0xAA, 0xF1, 0xB0, 0xB0, 0xDA, 0x8F, 0xB6, 0x8D, 0x89, 0x01, 0xED, 0xC9, 0x8F, 0x71, 0xFF, 0x05, 0xA2, 0x51, 0xEA, 0x4F, 0x00, 0x00, 0x7D, 0x58, 0xED, 0x92, 0x40, 0x98, 0x4B, 0x25, 0x56, 0x5C, 0xBE, 0x8A, 0xFA, 0xE5, 0x04, 0x13, 0xD2, 0xEB, 0xC9, 0xC3, 0x2A, 0x39, 0x98, 0xE1, 0xEF, 0x51, 0x74, 0x18, 0xC6, 0xE2, 0x92, 0xCF, 0x02, 0xD4, 0x9E, 0xF8, 0x29, 0x61, 0xF8, 0xA3, 0xC6, 0xBC, 0xBC, 0x60, 0xA4, 0xDB, 0x13, 0x39, 0xB6, 0xD1, 0x88, 0x2E, 0x94, 0x55, 0xAC, 0x8A, 0x47, 0x09, 0xD7, 0x05, 0x91, 0x3A, 0xC9, 0x38, 0xC5, 0x16, 0x41, 0x16, 0xE4, 0xB5, 0x5C, 0x82, 0xCF, 0xFA, 0xDB, 0x53, 0xBE, 0x4D, 0x7B, 0xEE, 0x5B, 0xD0, 0x91, 0x22, 0xF4, 0x77, 0xAC, 0xD9, 0xFC, 0x65, 0x56, 0x33, 0x1C, 0xA2, 0x73, 0xAA, 0xAE, 0x22, 0xC1, 0x63, 0x1F, 0x1A, 0x02, 0xD3, 0x44, 0x6E, 0x3F, 0xEB, 0xB4, 0x45, 0x80, 0x46, 0x8B, 0xD5, 0x5D, 0x44, 0xB8, 0x03, 0x27, 0x8B, 0x58, 0x3C, 0xBA, 0x6B, 0xCA, 0xD0, 0x4F, 0x00, 0x00, 0x60, 0x58, 0xED, 0xF2, 0x42, 0xA2, 0xD7, 0x73, 0x56, 0x38, 0x02, 0xF5, 0x12, 0x66, 0xE7, 0x9E, 0xFB, 0x17, 0xBD, 0x4F, 0x8C, 0xBE, 0xE9, 0xE2, 0x7E, 0xB4, 0x99, 0xB2, 0x80, 0x4E, 0x84, 0xF1, 0xAA, 0x5A, 0x6D, 0xAD, 0x1E, 0x11, 0xF6, 0x6C, 0x4B, 0x6B, 0x18, 0x4F, 0x46, 0x5C, 0x38, 0x35, 0x27, 0xBC, 0x20, 0xBF, 0xBF, 0x15, 0x14, 0x05, 0x54, 0xA1, 0x8A, 0x6A, 0x71, 0x14, 0x09, 0x71, 0xF3, 0x19, 0xF1, 0x95, 0x6B, 0x8A, 0x56, 0xF0, 0x76, 0x51, 0xFE, 0xB0, 0x8E, 0x4D, 0xEE, 0xCD, 0xC9, 0x32, 0xC2, 0x69, 0x2E, 0xFC, 0xDD, 0xC0, 0x08, 0x67, 0xD6, 0xBC, 0x4F, 0x85, 0xC0, 0xA6, 0x09, 0x4F, 0x00, 0x00, 0x50, 0x58, 0xEE, 0x27, 0x57, 0x96, 0x0C, 0x4C, 0x72, 0x3D, 0xBF, 0x89, 0xDC, 0x0C, 0x4D, 0xED, 0x0E, 0x7F, 0x21, 0x09, 0x22, 0x55, 0xF7, 0x70, 0x65, 0xC3, 0xC8, 0xAF, 0xC4, 0x4C, 0x2B, 0xA1, 0x2D, 0xFC, 0x26, 0x87, 0x48, 0x8A, 0x70, 0xF7, 0xD9, 0x7A, 0x65, 0x40, 0xB6, 0xBC, 0x1B, 0x55, 0x49, 0xDC, 0xF3, 0xA1, 0xA9, 0xE0, 0x8C, 0xC3, 0x4E, 0xF2, 0x8B, 0x95, 0x8D, 0xBB, 0x1A, 0x79, 0x70, 0xAA, 0x2E, 0x8A, 0xCA, 0x58, 0x46, 0x36, 0x53, 0x8C, 0x00, 0x04, 0x71, 0x03, 0x95, 0x4E, 0x80, 0x4F, 0x00, 0x00, 0x82, 0x58, 0xED, 0xDB, 0xC9, 0x93, 0x9A, 0xB9, 0x96, 0x91, 0x4D, 0x50, 0x55, 0x78, 0x5E, 0x9E, 0xC7, 0x13, 0x02, 0xA8, 0x33, 0xEE, 0x1D, 0xC0, 0xDE, 0xBA, 0xB3, 0x65, 0x27, 0xBA, 0xBF, 0xEB, 0x36, 0x2F, 0xA5, 0x6E, 0xE0, 0xFF, 0x18, 0xBF, 0xBF, 0x85, 0x76, 0xA0, 0x2B, 0x09, 0x5A, 0x61, 0x51, 0x01, 0x4A, 0xF1, 0x45, 0x2C, 0x7D, 0x07, 0x21, 0x8D, 0xE1, 0x7E, 0x56, 0x24, 0x74, 0x88, 0xFA, 0xEF, 0x1A, 0x13, 0x08, 0x65, 0x82, 0x0E, 0x70, 0x77, 0x6F, 0x20, 0x4A, 0x17, 0x84, 0xF0, 0xBE, 0xF1, 0x4C, 0x8B, 0x00, 0x2E, 0x57, 0x32, 0x2E, 0xD6, 0x24, 0x3B, 0x01, 0x4B, 0xEC, 0x98, 0x40, 0x3B, 0x9E, 0x4D, 0x0C, 0x7B, 0x21, 0xEA, 0x7B, 0x15, 0x4D, 0x8B, 0xFC, 0xEC, 0xF0, 0x76, 0xB4, 0x79, 0x4F, 0xE1, 0x91, 0x43, 0x6F, 0x70, 0x70, 0x43, 0x61, 0x42, 0xA8, 0xF1, 0xE6, 0xE0, 0xCB, 0x92, 0x40, 0x4F, 0x00, 0x00, 0x3F, 0x58, 0x0A, 0x3B, 0x66, 0x07, 0x8E, 0xB9, 0x8E, 0x84, 0x1C, 0x0D, 0x34, 0x70, 0x02, 0x49, 0x5F, 0x1D, 0x6B, 0x77, 0x94, 0x18, 0xD2, 0x02, 0xA2, 0x44, 0xB2, 0xF9, 0xBF, 0xBB, 0xA5, 0xD4, 0x0E, 0xD1, 0x9A, 0x92, 0xA6, 0x2B, 0x23, 0xA1, 0xD8, 0x22, 0x93, 0x10, 0x28, 0x44, 0x8A, 0xF0, 0x05, 0x11, 0xF4, 0x40, 0x4B, 0x76, 0x1D, 0x7B, 0x5B, 0x26, 0xAA, 0x04, 0x5C, 0x22, 0x92, 0x80, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20
};

#endif // AUDIO_DATA_8_H