/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_opus_inner.h
 * @description: OPUS子模块内部定义.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_OPUS_INNER_H
#define SK_OPUS_INNER_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

enum {
    SK_ENC_EVENT_PKT = 0x1,
    SK_ENC_EVENT_PCM = 0x2,
    SK_OPUS_EVENT_PLAY_LOCAL = 0x3,
    SK_OPUS_EVENT_REMOTE_LONG = 0x4,
    SK_OPUS_EVENT_REMOTE_SHORT = 0x5,
    SK_OPUS_EVENT_REMOTE_END = 0x6,
    SK_OPUS_EVENT_MUTE_REMOTE = 0x7,
    SK_OPUS_EVENT_UNMUTE_REMOTE = 0x8,
    SK_OPUS_EVENT_DEC_OUTPUT = 0x9,
    SK_OPUS_EVENT_ENC_OUTPUT = 0xa,
};

typedef struct {
    uint8_t event;      // 事件
    uint8_t param1;     // 参数
    uint8_t param2;     // 参数
    uint8_t param3;     // 参数
    uint32_t timestamp; // 时间戳
    void    *arg;
} SkOpusMsg;

typedef struct __attribute__((packed)) {
    uint8_t type;
    uint8_t reserved;
    uint16_t payloadSize;
    uint8_t payload[];
} SkOpusPktHdr;

typedef struct {
    char* name;
    const uint8_t* data;
    const uint16_t* pktOffsetList;
    int pktCnt;
    int length;
} SkCodedDataItem;

#ifdef __cplusplus
}
#endif

#endif