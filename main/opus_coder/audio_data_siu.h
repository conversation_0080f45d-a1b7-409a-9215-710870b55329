#ifndef AUDIO_DATA_SIU_H
#define AUDIO_DATA_SIU_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfoSiu[] = {0, 24, 49, 74, 99, 124, 182, 311, 443, 573, 702, 837, 978, 1089, 1197, 1319, 1458, 1602, 1740, 1861, 1974, 2093, 2229, 2359, 2505, 2653, 2788, 2892, 2969, 3007, 3032};
const uint8_t g_audioDataSiu[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x36, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x0E, 0xFC, 0xF6, 0x16, 0x2C, 0x18, 0x62, 0xC6, 0xBF, 0x4D, 0xB4, 0xC7, 0x43, 0x26, 0x14, 0x52, 0x7F, 0x7E, 0xC5, 0xF6, 0x5C, 0xC3, 0x90, 0xF1, 0x4D, 0xC9, 0x81, 0x4D, 0x01, 0xA7, 0x88, 0x58, 0x10, 0x01, 0x0D, 0x73, 0xDF, 0xC8, 0xAA, 0x96, 0x28, 0x0A, 0xFF, 0x1D, 0x94, 0xD2, 0x4F, 0x00, 0x00, 0x7D, 0x58, 0xE0, 0x60, 0x74, 0xDF, 0x20, 0xB7, 0x3A, 0x2F, 0x4D, 0xAA, 0xAE, 0xB1, 0xC8, 0x76, 0x9F, 0x47, 0x3B, 0x8B, 0xAA, 0xD7, 0xC8, 0x9F, 0xC5, 0xEE, 0x01, 0x29, 0x14, 0x6C, 0x8F, 0x0D, 0x6B, 0x66, 0x09, 0x96, 0x6B, 0xE9, 0xD1, 0x31, 0x1E, 0x51, 0xD0, 0x26, 0x9D, 0x18, 0xF1, 0xCA, 0x90, 0x9A, 0x40, 0x1C, 0xC1, 0x96, 0x5C, 0x18, 0x04, 0x6D, 0x75, 0x3D, 0xD4, 0x2B, 0x53, 0x25, 0x80, 0x9D, 0x9B, 0x40, 0xA5, 0xC8, 0xF6, 0x70, 0xFA, 0xC0, 0xC4, 0x24, 0x12, 0x06, 0x16, 0xF0, 0x88, 0xEC, 0x74, 0xEC, 0xC5, 0xC0, 0x7E, 0xF4, 0xC9, 0xF1, 0xE9, 0xB0, 0x39, 0x11, 0xE7, 0xC9, 0x36, 0xFA, 0xEB, 0xA5, 0x81, 0xB3, 0xD7, 0x66, 0x4B, 0x0B, 0xB0, 0x74, 0xE0, 0xED, 0xAF, 0x34, 0x75, 0xF1, 0xB6, 0x61, 0x2B, 0xDA, 0x9F, 0x95, 0x2B, 0x43, 0x75, 0xCF, 0x7F, 0xE0, 0x4F, 0x00, 0x00, 0x80, 0x58, 0xE3, 0xF0, 0xDA, 0x91, 0x83, 0xCA, 0xD5, 0xCC, 0xA5, 0x96, 0xF2, 0xEB, 0xE3, 0x61, 0xEB, 0x91, 0x91, 0x5C, 0x84, 0xBD, 0x34, 0x04, 0xA7, 0x15, 0xDE, 0x6F, 0xE7, 0x6E, 0xD9, 0xD7, 0xEF, 0x32, 0xB6, 0x11, 0x65, 0x4A, 0x90, 0xDF, 0xE2, 0x0A, 0xEE, 0xFF, 0x98, 0xD9, 0x3D, 0xCC, 0x0B, 0x94, 0x04, 0xCD, 0xD7, 0x25, 0x90, 0xF1, 0x3A, 0x9B, 0x40, 0x41, 0x91, 0x50, 0xD0, 0x74, 0x08, 0xD9, 0x69, 0x78, 0x08, 0x99, 0x35, 0x00, 0xA9, 0x8C, 0xD4, 0x78, 0x27, 0x34, 0xE3, 0xA9, 0x6F, 0x78, 0xE4, 0x1B, 0x98, 0x6F, 0xA0, 0xBA, 0x4C, 0x3A, 0x58, 0xF6, 0x82, 0x75, 0xC0, 0xFA, 0xF5, 0x71, 0x2E, 0x5B, 0x2C, 0x03, 0x80, 0x14, 0xD5, 0xE2, 0xF6, 0xEA, 0x7E, 0xCF, 0x96, 0x05, 0x1A, 0x06, 0x31, 0x18, 0x5A, 0x07, 0x28, 0xE8, 0x26, 0xCD, 0x18, 0xC6, 0xA9, 0x8B, 0xF9, 0xD0, 0x4C, 0x4F, 0x00, 0x00, 0x7E, 0x58, 0xE4, 0x16, 0x35, 0xE7, 0x3C, 0xE6, 0x31, 0xAF, 0x0A, 0xD7, 0x91, 0x08, 0xBD, 0x6D, 0x34, 0x04, 0xD3, 0x42, 0xD7, 0x6B, 0xF1, 0xFC, 0x2A, 0x42, 0x26, 0x55, 0x12, 0x22, 0xE6, 0x34, 0x52, 0xC7, 0xD7, 0x25, 0x9A, 0x0E, 0x12, 0x7D, 0x39, 0x83, 0x14, 0xF9, 0x18, 0xDE, 0x75, 0xFC, 0x9B, 0x0F, 0xFA, 0x25, 0x61, 0x3A, 0xF1, 0xFA, 0x3A, 0xD4, 0xE7, 0x62, 0x90, 0xAA, 0x1F, 0x79, 0x3A, 0xFE, 0x44, 0x38, 0xB2, 0x0B, 0xDC, 0x43, 0x26, 0x74, 0x89, 0x59, 0xE7, 0x7E, 0xAC, 0x3E, 0xFB, 0xBB, 0x16, 0xE1, 0x96, 0x45, 0x9C, 0xBB, 0xF1, 0x85, 0x51, 0x70, 0xCF, 0x65, 0x1D, 0x4F, 0x49, 0x7D, 0xCD, 0x74, 0xBE, 0xB5, 0xA1, 0x75, 0x7B, 0xA4, 0x8C, 0xC8, 0x3F, 0xAC, 0xA7, 0xE4, 0x88, 0xC6, 0xD7, 0x16, 0x6C, 0x00, 0x23, 0xAA, 0x7E, 0x87, 0x4F, 0x73, 0x16, 0x85, 0xE0, 0x4F, 0x00, 0x00, 0x7D, 0x58, 0xE4, 0x5F, 0x5F, 0x87, 0x76, 0xD3, 0xFB, 0x03, 0x36, 0x33, 0xD4, 0xDE, 0xE5, 0xD0, 0xCB, 0x64, 0xF7, 0x58, 0xEA, 0x70, 0xE7, 0x18, 0xAC, 0xC1, 0x45, 0xCA, 0x2E, 0x48, 0x9C, 0xFF, 0x59, 0x9B, 0xEA, 0x24, 0xE6, 0x82, 0x12, 0x8E, 0x96, 0xD7, 0x39, 0x61, 0x4C, 0xCC, 0x18, 0x9C, 0xBB, 0x50, 0x1E, 0x52, 0x75, 0x4F, 0x16, 0x11, 0x58, 0x26, 0x11, 0x54, 0x78, 0xF3, 0x18, 0xA7, 0xA9, 0x7D, 0xF7, 0x5F, 0x1F, 0xE4, 0x99, 0xAA, 0xB9, 0xCD, 0xD1, 0xF8, 0xBF, 0x5F, 0xC2, 0x7C, 0xF7, 0x39, 0xC4, 0xC0, 0x5A, 0xFB, 0x6F, 0x67, 0x60, 0xAA, 0x63, 0xDF, 0xB3, 0x43, 0x3D, 0x04, 0xED, 0xC4, 0xD6, 0x0D, 0x78, 0x61, 0x01, 0xF9, 0xB7, 0x19, 0x6F, 0x22, 0x6D, 0xEB, 0x91, 0x9A, 0x40, 0xF2, 0x6C, 0xA0, 0xF3, 0x11, 0xA7, 0xC0, 0xE6, 0x89, 0x06, 0xA5, 0x52, 0x40, 0x4F, 0x00, 0x00, 0x83, 0x58, 0xE4, 0x83, 0x72, 0xF8, 0xFD, 0x96, 0xDE, 0x02, 0x8A, 0x70, 0xA5, 0x41, 0xD5, 0xCB, 0x97, 0x56, 0x06, 0xEB, 0x8A, 0x9A, 0x98, 0xE7, 0x9B, 0xCE, 0xEB, 0x29, 0x4E, 0x2D, 0x82, 0x25, 0xD3, 0xE3, 0x15, 0x06, 0xB4, 0x3B, 0xDD, 0xC9, 0x1F, 0xB6, 0x5F, 0x8A, 0xF6, 0x9B, 0x95, 0xCC, 0xB8, 0xF3, 0x87, 0x6C, 0xD3, 0x7E, 0x7A, 0x68, 0xE3, 0x98, 0x29, 0x62, 0xD0, 0x0C, 0xBC, 0x43, 0xB5, 0x1B, 0xD9, 0xD4, 0xD3, 0x9D, 0x22, 0xDF, 0xB4, 0x2B, 0xAA, 0xF5, 0x31, 0x15, 0x6F, 0x19, 0x63, 0x34, 0x5D, 0xE8, 0xDE, 0x24, 0x0B, 0x86, 0x02, 0x36, 0x72, 0x57, 0xAB, 0xE6, 0x38, 0xD0, 0x45, 0x74, 0x8F, 0x6A, 0x3B, 0xEE, 0x8C, 0x19, 0x6A, 0x76, 0x49, 0x0E, 0x75, 0xDA, 0x7E, 0x3D, 0xB4, 0x2D, 0x81, 0xA3, 0xF2, 0x8E, 0x51, 0x35, 0xB1, 0xA1, 0x03, 0x99, 0xCA, 0x20, 0x5D, 0xFC, 0x0E, 0x8D, 0xE4, 0x6A, 0x4F, 0x00, 0x00, 0x89, 0x58, 0xE5, 0x29, 0x00, 0xA8, 0x36, 0x77, 0xBA, 0x9D, 0x49, 0x4A, 0xFC, 0x98, 0xC7, 0x96, 0x09, 0x46, 0x90, 0x2A, 0xC7, 0xA7, 0x37, 0x6E, 0xBC, 0x35, 0xA6, 0x6A, 0xA4, 0xB4, 0xD7, 0xE5, 0x38, 0x2B, 0x6E, 0xE1, 0xC8, 0xBB, 0xE3, 0xF3, 0x5B, 0x65, 0x56, 0xD4, 0xD1, 0x87, 0x13, 0xD2, 0xDA, 0x71, 0x12, 0x0E, 0x3D, 0x2C, 0x4D, 0x60, 0x4D, 0xB5, 0xDA, 0x45, 0x26, 0xA0, 0xFD, 0x83, 0x22, 0x67, 0xCC, 0xB2, 0x88, 0xFD, 0x16, 0x67, 0x96, 0x4A, 0xD9, 0x13, 0xD3, 0x82, 0xDF, 0x51, 0x46, 0x90, 0x87, 0x51, 0x9A, 0xB9, 0xEF, 0xEB, 0x28, 0x8D, 0x46, 0x6C, 0xCB, 0x85, 0xAD, 0xB8, 0x17, 0x88, 0x31, 0xF4, 0x62, 0x0E, 0x82, 0xBC, 0xD1, 0xD4, 0xBF, 0x61, 0x62, 0xD3, 0xE4, 0x94, 0x56, 0xEF, 0x33, 0xBA, 0xBF, 0x18, 0xEC, 0x54, 0x8A, 0xE1, 0xFC, 0x4F, 0x75, 0x69, 0xB7, 0x73, 0xC7, 0xCB, 0xD4, 0x6D, 0xF8, 0x04, 0x81, 0xE5, 0x1C, 0x2E, 0x4F, 0x00, 0x00, 0x6B, 0x58, 0xEE, 0x69, 0x77, 0x0F, 0x75, 0x89, 0xA7, 0x09, 0x5D, 0x94, 0x91, 0xF0, 0x0D, 0x48, 0x3D, 0x62, 0xB5, 0xBE, 0xAC, 0x0B, 0x71, 0xE7, 0xF0, 0xC7, 0x6E, 0x2F, 0x10, 0x03, 0x46, 0x88, 0xC3, 0xD6, 0x86, 0xA2, 0x61, 0xBE, 0x67, 0x27, 0x89, 0x68, 0xC5, 0xA9, 0xC6, 0x5B, 0x9C, 0xCD, 0xA1, 0x6A, 0xAB, 0x7F, 0x90, 0x7C, 0x35, 0x60, 0x46, 0x60, 0xF7, 0xED, 0x03, 0xC4, 0x05, 0x10, 0xF7, 0xC7, 0xFD, 0x87, 0x76, 0x30, 0x5E, 0xA5, 0xC4, 0x42, 0xDA, 0xAB, 0x89, 0x74, 0xC7, 0xCA, 0x59, 0xC0, 0x06, 0xD8, 0x1E, 0xBB, 0x91, 0x2E, 0x20, 0x7E, 0xE9, 0xE8, 0x6D, 0xF6, 0xDE, 0x82, 0x0C, 0xA1, 0xAE, 0x51, 0xD8, 0x21, 0xB4, 0x82, 0xCA, 0xF4, 0xB5, 0xE2, 0x4F, 0x00, 0x00, 0x68, 0x58, 0xED, 0xEF, 0xF0, 0xCB, 0xCD, 0x4D, 0x59, 0xB3, 0x9C, 0x57, 0x30, 0xC5, 0xD0, 0x4D, 0x3F, 0x16, 0xF7, 0x28, 0x12, 0x64, 0x01, 0xF4, 0xDE, 0x30, 0x3E, 0xF9, 0xE9, 0x65, 0x86, 0xC9, 0x4D, 0x1A, 0x18, 0x0F, 0x04, 0x1C, 0x84, 0xB9, 0x8B, 0x84, 0x98, 0x74, 0xE5, 0x40, 0x04, 0x41, 0xF5, 0xE0, 0x60, 0x15, 0xDC, 0x26, 0xA2, 0xDF, 0xD9, 0x47, 0xDA, 0x5A, 0x2B, 0x96, 0xD9, 0x5C, 0x1B, 0xBE, 0xBE, 0x82, 0x3D, 0x2F, 0x71, 0x2F, 0x29, 0x02, 0xB0, 0x6E, 0x0E, 0x1C, 0xC7, 0x21, 0x4E, 0x9B, 0x65, 0x34, 0xCA, 0x6B, 0xD6, 0x34, 0x4C, 0x9A, 0x1B, 0x3A, 0x73, 0x35, 0x20, 0x7B, 0xE7, 0x46, 0x06, 0x4C, 0x58, 0xF7, 0x3B, 0xB3, 0x80, 0x4F, 0x00, 0x00, 0x76, 0x58, 0xED, 0xBA, 0x7A, 0x95, 0x27, 0x10, 0xC2, 0xF1, 0x46, 0xDF, 0x17, 0x20, 0xAB, 0x72, 0x2E, 0xF8, 0x2C, 0xA1, 0xB6, 0xCF, 0x7F, 0x38, 0x4F, 0x71, 0x2B, 0x27, 0xE6, 0x8F, 0xA2, 0x05, 0x3A, 0x72, 0x46, 0x16, 0x82, 0xC2, 0x18, 0x01, 0xE3, 0xD2, 0xC6, 0x59, 0x1D, 0xD0, 0xE9, 0xFE, 0xD3, 0x00, 0x40, 0x78, 0x37, 0xAD, 0xFE, 0x16, 0xB9, 0x6A, 0x9D, 0xE5, 0xCB, 0x65, 0xE9, 0xFE, 0xD2, 0xEB, 0x6B, 0x78, 0x67, 0x59, 0xA4, 0x4A, 0x84, 0xD8, 0x85, 0x8C, 0xDB, 0x64, 0x4A, 0x6B, 0x84, 0xE0, 0x8E, 0x77, 0xC9, 0xFE, 0xF9, 0x1D, 0xB4, 0x8F, 0x2C, 0x0E, 0x34, 0xAF, 0x37, 0x3A, 0xEE, 0xE5, 0x04, 0x32, 0xF3, 0x68, 0xAA, 0x23, 0x02, 0x7F, 0x9E, 0x39, 0x83, 0x28, 0x30, 0xE1, 0x56, 0xCA, 0x30, 0x3B, 0x1F, 0x2B, 0xA0, 0x4F, 0x00, 0x00, 0x87, 0x58, 0xED, 0xF5, 0xE4, 0x59, 0x21, 0x6A, 0x13, 0x55, 0xD7, 0xFA, 0x2F, 0x92, 0x7C, 0xA3, 0xFE, 0x78, 0xD0, 0x83, 0x3C, 0xC8, 0x77, 0x64, 0x3F, 0xFE, 0x77, 0xDC, 0xAD, 0xA1, 0x17, 0x68, 0x41, 0x67, 0xF8, 0xB1, 0xDD, 0xBB, 0xEC, 0xD1, 0x6F, 0x3C, 0x5A, 0xD0, 0x5C, 0x0D, 0x47, 0x12, 0xED, 0x86, 0x86, 0x02, 0xC6, 0x1A, 0x26, 0xF7, 0x24, 0x48, 0xCF, 0xBC, 0x9B, 0xC0, 0x5E, 0x44, 0x3A, 0xEE, 0x43, 0xC2, 0xFE, 0xC7, 0x2D, 0xFD, 0xC4, 0x0A, 0x81, 0x3B, 0x1E, 0x2F, 0x62, 0x8C, 0x71, 0x3F, 0xAA, 0xAD, 0xB1, 0xF8, 0x71, 0x40, 0x40, 0x83, 0x88, 0xF4, 0xC1, 0x01, 0x06, 0xB7, 0xE5, 0x81, 0xE1, 0xFC, 0x73, 0x10, 0xF7, 0xC4, 0xA8, 0xF3, 0x44, 0xEC, 0xA9, 0x29, 0x9E, 0x28, 0x60, 0xBB, 0x7A, 0x0F, 0x36, 0x69, 0x6B, 0xBE, 0xB9, 0x33, 0xC4, 0x26, 0x41, 0x6E, 0xE1, 0x8B, 0x5B, 0x22, 0xAE, 0x18, 0x79, 0xEB, 0x87, 0x0E, 0x4F, 0x00, 0x00, 0x8C, 0x58, 0xED, 0xF4, 0xDB, 0xD2, 0xC6, 0x2A, 0xE0, 0xC9, 0x04, 0x2B, 0xA8, 0xC3, 0xA5, 0xEC, 0xAB, 0x46, 0x3A, 0xAB, 0x50, 0xC5, 0xCB, 0xEC, 0x14, 0xCC, 0x45, 0x18, 0xE0, 0xAF, 0xD4, 0x67, 0xEC, 0x73, 0x27, 0x22, 0x71, 0x50, 0xBA, 0x6E, 0xF3, 0x80, 0xF6, 0x60, 0xB8, 0xA2, 0x59, 0xB0, 0x17, 0x85, 0xE7, 0x81, 0xE3, 0x76, 0x87, 0x10, 0xF4, 0xDE, 0xE7, 0x3F, 0xFE, 0x27, 0x90, 0x4E, 0xA3, 0xAA, 0xE9, 0x3E, 0x7C, 0xDA, 0xBF, 0xAD, 0x8D, 0x73, 0x97, 0x1E, 0x91, 0xF1, 0xAB, 0x51, 0x83, 0x33, 0x29, 0x1D, 0x2F, 0xCF, 0x07, 0x42, 0x78, 0x4C, 0x27, 0xA6, 0xC1, 0x61, 0x6C, 0x0D, 0x5A, 0xA4, 0xD8, 0x5C, 0xA2, 0x85, 0xE4, 0xDE, 0xEE, 0xE1, 0xAE, 0xE2, 0xFA, 0x56, 0x3C, 0x57, 0xC8, 0x06, 0xE1, 0x26, 0x8F, 0x34, 0xAD, 0xAA, 0x20, 0x36, 0xC6, 0xA8, 0x06, 0x22, 0xB0, 0xEB, 0x4C, 0x78, 0x08, 0xD0, 0xC7, 0x6A, 0x77, 0xA7, 0x53, 0x37, 0xCC, 0xDF, 0xB1, 0x4F, 0x00, 0x00, 0x86, 0x58, 0xED, 0xF4, 0x4A, 0xC1, 0x4E, 0x9C, 0x7A, 0x0F, 0x3A, 0x3E, 0x1F, 0x95, 0xC5, 0x2F, 0x85, 0x29, 0xF8, 0xE9, 0x60, 0x18, 0x21, 0x94, 0xDF, 0xEF, 0x0E, 0x0A, 0xA8, 0xF5, 0xDB, 0x76, 0xBC, 0x2C, 0x69, 0x17, 0xB7, 0x39, 0x8D, 0x8E, 0x80, 0xE5, 0xA0, 0xB7, 0x6A, 0x6B, 0x10, 0x96, 0xEB, 0x3E, 0x20, 0xD4, 0x77, 0xB1, 0xDD, 0x34, 0x93, 0x92, 0x8F, 0x1F, 0x33, 0xDE, 0xC3, 0x1D, 0x34, 0x7A, 0x6D, 0x10, 0x44, 0x1F, 0x9F, 0x65, 0x7F, 0x0E, 0xE1, 0x42, 0x06, 0xD6, 0xBE, 0xBE, 0x0E, 0x22, 0x93, 0x09, 0x85, 0x89, 0x05, 0xE9, 0x91, 0x76, 0x97, 0x19, 0xB4, 0xE1, 0xB9, 0x4D, 0x50, 0x8E, 0x8C, 0x11, 0x74, 0x66, 0x95, 0x32, 0x3E, 0x62, 0x21, 0x35, 0x8B, 0x71, 0x33, 0xA5, 0x33, 0x20, 0x43, 0x91, 0xD6, 0xFA, 0x3B, 0x20, 0x65, 0xA4, 0x2B, 0x8E, 0xD7, 0x51, 0x57, 0x35, 0x28, 0xEC, 0x68, 0xFF, 0xAC, 0x57, 0x28, 0x4F, 0x00, 0x00, 0x75, 0x58, 0xED, 0xED, 0x0B, 0xEE, 0x99, 0xB2, 0x40, 0x33, 0x94, 0x89, 0x60, 0x18, 0x69, 0x67, 0x5D, 0xDB, 0x7E, 0xDA, 0x02, 0xFE, 0x9E, 0xBC, 0x7C, 0xC5, 0x92, 0x3F, 0xD8, 0x88, 0x3E, 0xF6, 0xD5, 0x01, 0x15, 0x60, 0x9A, 0x4A, 0xD7, 0x22, 0x81, 0x5C, 0x5E, 0x0F, 0x23, 0x33, 0x62, 0x6C, 0x81, 0x11, 0xDA, 0x68, 0xC4, 0x26, 0xB3, 0xF2, 0x43, 0x1C, 0x33, 0x49, 0xCF, 0x93, 0x86, 0xFA, 0x33, 0xAB, 0xBA, 0xEE, 0x4D, 0x2B, 0xB6, 0xE5, 0xC2, 0xB1, 0xCE, 0x58, 0x1D, 0x69, 0x7F, 0xC6, 0x5E, 0x14, 0xC4, 0xD9, 0xEB, 0x5B, 0xD8, 0x78, 0xF8, 0x17, 0xD2, 0x1D, 0xE4, 0x04, 0x8A, 0x39, 0xB3, 0x66, 0x21, 0x83, 0xA2, 0xEC, 0xCA, 0x55, 0xDA, 0xC6, 0x42, 0x1D, 0x3C, 0xCD, 0x3A, 0x06, 0x45, 0x27, 0x27, 0xD7, 0x55, 0x70, 0x4F, 0x00, 0x00, 0x6D, 0x58, 0xED, 0xB8, 0x45, 0xCE, 0x70, 0x0C, 0xF2, 0x38, 0xB2, 0xFB, 0xEC, 0x9D, 0x02, 0x31, 0x22, 0x00, 0xCE, 0x3B, 0x22, 0x04, 0x47, 0x78, 0x1A, 0xD7, 0x2F, 0xCE, 0xE5, 0xE6, 0xCF, 0x6C, 0xC9, 0xA3, 0x2A, 0x75, 0x95, 0xFA, 0xA0, 0xE9, 0xFD, 0x70, 0x24, 0xC3, 0x46, 0xB4, 0xF2, 0x81, 0x8C, 0x2C, 0x98, 0xD6, 0x28, 0x80, 0x59, 0xC0, 0x67, 0x1E, 0x15, 0x67, 0xD2, 0xEE, 0x0E, 0x69, 0x79, 0x49, 0xEB, 0xFE, 0xF4, 0xB4, 0xB9, 0xAE, 0x74, 0x32, 0x96, 0x33, 0x48, 0xD2, 0x96, 0xE9, 0x45, 0xF6, 0x79, 0xA6, 0xEE, 0x18, 0x77, 0xE7, 0x3C, 0x08, 0xF8, 0xC4, 0xFB, 0x82, 0x08, 0x16, 0x98, 0x1F, 0xBF, 0x8A, 0x99, 0x3A, 0x02, 0x2D, 0xA0, 0x44, 0x2F, 0x2F, 0x85, 0xDE, 0x4F, 0x00, 0x00, 0x73, 0x58, 0xED, 0x7C, 0xF1, 0xD4, 0xF3, 0x1F, 0x18, 0x26, 0x39, 0x62, 0x96, 0x9E, 0xFA, 0xFF, 0xFB, 0x37, 0xF0, 0x3A, 0x57, 0xBB, 0x34, 0x77, 0xE2, 0xB5, 0xC9, 0xEE, 0x38, 0xB3, 0x58, 0x10, 0x19, 0x41, 0x2A, 0xE7, 0x52, 0x5B, 0xF7, 0x13, 0x75, 0x8D, 0x97, 0xA8, 0xA8, 0xA6, 0x34, 0x62, 0xD6, 0x31, 0x74, 0xF7, 0x2E, 0xE2, 0x35, 0x09, 0xCE, 0x3B, 0x1C, 0x0C, 0xA1, 0x70, 0xC5, 0x34, 0xCA, 0xA6, 0x4C, 0x75, 0xFA, 0x1E, 0x77, 0x6B, 0x7B, 0x2F, 0xF1, 0xFD, 0x83, 0x58, 0x29, 0xFD, 0x5D, 0xD7, 0x36, 0x22, 0x22, 0x45, 0x44, 0x10, 0x17, 0x31, 0xB2, 0xE9, 0x11, 0xD2, 0x60, 0x21, 0x13, 0x9B, 0x62, 0x2C, 0xD4, 0x6D, 0x5C, 0x48, 0xEE, 0xA2, 0xA8, 0xAF, 0x30, 0x1C, 0xD8, 0x75, 0x2B, 0x0A, 0x21, 0x90, 0x4F, 0x00, 0x00, 0x84, 0x58, 0xED, 0x10, 0xAA, 0x06, 0x63, 0x43, 0xBC, 0x00, 0xC4, 0x3C, 0x62, 0x0D, 0x15, 0x08, 0xC3, 0x42, 0x4A, 0x88, 0xE6, 0x95, 0x9B, 0xBE, 0x5F, 0x8F, 0x35, 0x63, 0xA6, 0xD5, 0x9A, 0xC5, 0x52, 0x6C, 0xCA, 0xF5, 0x8E, 0xF4, 0x00, 0xD1, 0x66, 0x68, 0x40, 0xB2, 0x37, 0x21, 0x1D, 0xB0, 0x81, 0xB6, 0x7F, 0xD2, 0x34, 0x5B, 0x52, 0x56, 0x5B, 0x9A, 0x95, 0xDD, 0xDC, 0x9F, 0x7E, 0x97, 0x1F, 0x2B, 0xCB, 0x31, 0x4E, 0x2F, 0x3B, 0xE0, 0xF7, 0xB1, 0x21, 0x72, 0x2E, 0x52, 0xD0, 0x41, 0xE2, 0x8E, 0x8C, 0xCE, 0xAE, 0xBF, 0x3A, 0xEF, 0x13, 0x9B, 0x13, 0xB7, 0x26, 0x72, 0xF3, 0x35, 0xEB, 0x57, 0xDC, 0xAD, 0xFB, 0xB9, 0x0E, 0xD6, 0x88, 0x18, 0x09, 0x43, 0x8F, 0xF5, 0xCF, 0x28, 0xBA, 0x49, 0x26, 0xFD, 0x57, 0xB7, 0xF6, 0x93, 0x74, 0x05, 0x61, 0x39, 0xC2, 0x00, 0xA5, 0x41, 0x18, 0xDE, 0xD7, 0xCF, 0x7E, 0x4F, 0x00, 0x00, 0x7E, 0x58, 0xEB, 0xBA, 0x15, 0x3B, 0x66, 0x75, 0x7B, 0x4B, 0xB6, 0x5F, 0x20, 0xFC, 0x74, 0x18, 0xBA, 0x36, 0xC2, 0xCA, 0xE2, 0xFB, 0x69, 0xB8, 0xD4, 0x2A, 0xC3, 0x7F, 0x4B, 0x41, 0x54, 0xE1, 0xA4, 0x66, 0x7D, 0x93, 0x65, 0x5E, 0x98, 0xF7, 0xCA, 0x89, 0x6C, 0x7F, 0x08, 0xC1, 0x20, 0xB6, 0xF8, 0xD8, 0x34, 0xCD, 0x23, 0x65, 0x0C, 0x17, 0x4A, 0x15, 0xEC, 0xF8, 0x77, 0x75, 0x54, 0x05, 0x77, 0xEE, 0x45, 0xD7, 0x85, 0x46, 0xC2, 0x2C, 0xF5, 0xC5, 0xCC, 0xDB, 0xFB, 0xDC, 0x6E, 0xC9, 0xD6, 0x82, 0x8F, 0xD1, 0x5B, 0xF8, 0x66, 0x32, 0x49, 0xC1, 0x70, 0xB7, 0xE4, 0xAB, 0xEB, 0x62, 0x83, 0xFB, 0xAC, 0x0C, 0x7A, 0xF7, 0xB8, 0x78, 0x9C, 0x52, 0xF2, 0x80, 0xC4, 0x9D, 0xAB, 0xB5, 0x93, 0x48, 0x64, 0xAF, 0x74, 0xDE, 0x6E, 0xF5, 0x14, 0x5B, 0xD6, 0xC0, 0x3B, 0xDD, 0x80, 0x4F, 0x00, 0x00, 0x8E, 0x58, 0xEA, 0xAB, 0x4F, 0x7E, 0x8C, 0xE9, 0xC9, 0xDA, 0x45, 0x45, 0x86, 0x4C, 0xAE, 0xCB, 0x99, 0x71, 0xB4, 0x36, 0x87, 0xB0, 0x27, 0x08, 0x34, 0x0F, 0x7F, 0xA3, 0x34, 0x86, 0x32, 0xF4, 0x93, 0x6A, 0xF5, 0x00, 0x83, 0xF0, 0x71, 0xEB, 0xAF, 0x48, 0x77, 0x0A, 0xA3, 0x31, 0x72, 0x83, 0xAB, 0xBA, 0x0C, 0x89, 0x29, 0xD0, 0x4B, 0xB7, 0x14, 0x3F, 0xA9, 0xDB, 0x19, 0x5C, 0xB5, 0xCF, 0x44, 0x77, 0x79, 0xBF, 0xA0, 0x6A, 0xD0, 0x29, 0x73, 0x5F, 0x22, 0x58, 0x03, 0xC1, 0xC7, 0xC2, 0x9B, 0x0A, 0x69, 0x33, 0x47, 0x15, 0xF3, 0x1E, 0xE6, 0xEB, 0x81, 0xA4, 0x41, 0xBD, 0x82, 0x2C, 0xF1, 0xC2, 0x1B, 0xEA, 0xA9, 0x3A, 0x76, 0x03, 0x2B, 0xC2, 0x60, 0xE7, 0x94, 0x11, 0x15, 0x17, 0xEF, 0xD3, 0x33, 0xB5, 0xBF, 0xAE, 0x82, 0x05, 0xE5, 0xFF, 0xEB, 0x8C, 0xE5, 0x91, 0xD2, 0x80, 0x91, 0xBD, 0xE8, 0xDB, 0x75, 0xAC, 0xE6, 0xF4, 0x05, 0xD2, 0x4B, 0xD3, 0x4E, 0x38, 0x30, 0x4F, 0x00, 0x00, 0x90, 0x58, 0xE9, 0xE2, 0xC2, 0xA2, 0x93, 0x01, 0x05, 0x5E, 0x09, 0x66, 0x30, 0xB9, 0x11, 0xF2, 0x64, 0x48, 0xA0, 0x43, 0x73, 0x98, 0xD9, 0x23, 0xB1, 0xA3, 0x85, 0x57, 0x74, 0x3F, 0x0E, 0xAC, 0x30, 0x26, 0x3F, 0x55, 0xB5, 0x46, 0xEF, 0x0A, 0x11, 0xBA, 0x93, 0x43, 0x26, 0x30, 0x19, 0xCF, 0x2E, 0x7A, 0xD6, 0x62, 0xA3, 0x04, 0xD5, 0x38, 0x00, 0x39, 0x78, 0x7F, 0x46, 0x46, 0xD4, 0x52, 0xC1, 0x3E, 0x72, 0x0D, 0x0B, 0x71, 0xE3, 0xBC, 0x19, 0xF1, 0xFD, 0xB9, 0x51, 0xB5, 0x7B, 0x48, 0x0B, 0xE2, 0x2D, 0xF4, 0xB9, 0xA9, 0x77, 0x9C, 0x56, 0x3C, 0x82, 0x26, 0x3E, 0xE7, 0x49, 0xF3, 0xD9, 0xC5, 0x3F, 0xFA, 0x63, 0x45, 0x7B, 0x07, 0xA5, 0xEB, 0xEA, 0x5E, 0xAF, 0xA4, 0x79, 0x49, 0x96, 0x83, 0x3B, 0x50, 0x1B, 0xC7, 0x3C, 0x97, 0xCA, 0x5A, 0x27, 0x39, 0xD2, 0x4C, 0xEB, 0xBF, 0x3E, 0x3C, 0xE2, 0x21, 0xFD, 0x7F, 0x70, 0xE8, 0xDC, 0xBA, 0x78, 0xB7, 0x50, 0x55, 0x62, 0x64, 0xD6, 0x4F, 0x00, 0x00, 0x83, 0x58, 0xA9, 0x37, 0x98, 0x8B, 0xA2, 0x33, 0xF8, 0x91, 0x81, 0xFD, 0x37, 0x5A, 0xEF, 0xF3, 0x50, 0x75, 0x25, 0x07, 0xB9, 0xF8, 0x71, 0x62, 0x23, 0x05, 0xFF, 0x34, 0x98, 0xF0, 0x1A, 0xF0, 0xEE, 0x09, 0xC9, 0x1B, 0x60, 0x26, 0xE6, 0xE7, 0x41, 0x82, 0x0A, 0xD0, 0xB0, 0x47, 0xC5, 0x57, 0xDF, 0x94, 0xAF, 0x99, 0xAD, 0xB3, 0xE3, 0x91, 0x8D, 0x62, 0xFC, 0x10, 0x40, 0x43, 0xCD, 0xAF, 0xC3, 0x30, 0x1F, 0xF7, 0x3B, 0xE7, 0x76, 0xB7, 0x01, 0x76, 0x5A, 0x42, 0x29, 0xB1, 0x17, 0xB6, 0x3D, 0xC7, 0x63, 0xE8, 0xA5, 0x98, 0x6F, 0x3C, 0x20, 0xB1, 0x1B, 0xBC, 0xE5, 0x12, 0x9A, 0x45, 0x03, 0xAD, 0x05, 0x72, 0xFB, 0x5C, 0xE7, 0x40, 0xF4, 0x81, 0x85, 0xD0, 0xC4, 0x2E, 0xFD, 0x7C, 0x70, 0xC2, 0x20, 0x22, 0x17, 0xAE, 0x2A, 0xF2, 0x55, 0x69, 0x44, 0xDB, 0x3E, 0x53, 0xF1, 0x50, 0xFD, 0x9D, 0x1A, 0x80, 0x4F, 0x00, 0x00, 0x64, 0x58, 0x0D, 0xF4, 0x59, 0x2F, 0xA9, 0x2D, 0x80, 0x5B, 0x56, 0x49, 0xAC, 0xFA, 0xE1, 0xA0, 0xC5, 0x22, 0x25, 0x6C, 0x3B, 0x8E, 0xA4, 0x98, 0x3E, 0x94, 0x4A, 0xC2, 0x57, 0x77, 0xF8, 0xB0, 0x2F, 0x10, 0xC9, 0xB0, 0x76, 0x4C, 0x0C, 0x7B, 0xAA, 0x9C, 0x34, 0x8C, 0x7C, 0x3A, 0x5E, 0xA7, 0xE9, 0x6B, 0x25, 0x08, 0x40, 0x83, 0xBB, 0x5C, 0x61, 0xBC, 0x0B, 0x29, 0xE5, 0x53, 0xB9, 0x40, 0x97, 0x96, 0xC8, 0x25, 0x1B, 0x2D, 0x62, 0x7F, 0xB5, 0xF2, 0x2D, 0x84, 0xA0, 0xFA, 0x14, 0x81, 0x22, 0x17, 0xEE, 0xC8, 0x66, 0x15, 0xD4, 0x0A, 0x31, 0x41, 0x57, 0xEA, 0x09, 0x28, 0x6D, 0xFD, 0xD6, 0xB8, 0xE0, 0xB8, 0x58, 0x4F, 0x00, 0x00, 0x49, 0x58, 0x09, 0x00, 0x49, 0x24, 0xED, 0xCF, 0xDC, 0x19, 0x79, 0x14, 0x3C, 0x09, 0x90, 0x52, 0xC8, 0x9E, 0xC4, 0x00, 0x20, 0xCA, 0x57, 0x3C, 0x7E, 0x1C, 0x21, 0x9C, 0x14, 0x4B, 0xD0, 0xC1, 0x56, 0xC0, 0x62, 0xF9, 0x23, 0x13, 0xCD, 0xE1, 0xD1, 0xC2, 0xA0, 0x16, 0x45, 0xD2, 0xB3, 0x03, 0xB8, 0xF5, 0x36, 0x4C, 0xDB, 0xEA, 0x4B, 0x59, 0xB4, 0xA1, 0x8F, 0xD2, 0xA8, 0x3E, 0xF2, 0x3A, 0x73, 0xB9, 0x8E, 0x5F, 0x3D, 0x28, 0xA6, 0x53, 0xFB, 0x80, 0x4F, 0x00, 0x00, 0x22, 0x58, 0x02, 0x9E, 0x96, 0xB7, 0xFD, 0x7A, 0x07, 0x78, 0xE4, 0x2D, 0x44, 0xA4, 0x5D, 0x4A, 0x14, 0xB6, 0x2F, 0x67, 0x7E, 0x87, 0xE1, 0x83, 0xAE, 0xF8, 0xFA, 0xC1, 0x40, 0x89, 0xA6, 0xE6, 0xE6, 0x7D, 0x95, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20
};

#endif // AUDIO_DATA_SIU_H