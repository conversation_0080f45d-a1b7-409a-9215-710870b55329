/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_ring_buf.h
 * @description: 编码器使用的唤醒队列.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_RING_BUF_H
#define SK_RING_BUF_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    uint16_t head;      // 0~frameSize, 最高bit为翻转标志位，其余为偏移量
    uint16_t tail;      // 0~frameSize, 最高bit为翻转标志位，其余为偏移量
    uint16_t size;      // frameSize两倍大小
    uint16_t frameSize; // 帧大小
    uint64_t dataFlag;  // 标记数据是否为Silence, 每个bit一个block，每个block大小64个采样
    uint16_t *buffer;
} SkRingBuf;

inline uint32_t SkRBGetVadFlag(SkRingBuf *rb) {
    return rb->dataFlag & 0x7FFF;
}

inline uint16_t SkRBGetHead(SkRingBuf *rb) {
    return rb->head & 0x7FFF;
}

// 创建一个Ring Buffer
int32_t SkRBInit(SkRingBuf *rb, uint16_t samplePerFrame);

// sampleCnt不超过2倍frameSize
uint16_t SkRBDataIn(SkRingBuf *rb, uint16_t *samples, uint16_t sampleCnt, uint32_t vadFlag);

// 每次删除一个FrameSize，通过移动start，删除位置从0或者从偏移FrameSize开始的一个Frame数据。
uint16_t SkRBDataOut(SkRingBuf *rb);

void SkRBReset(SkRingBuf *rb);

// 获取数据个数，从tail-head数量
uint16_t SkRBSampleCnt(SkRingBuf *rb, uint32_t mask, uint32_t *vadValue);

#ifdef __cplusplus
}
#endif

#endif /* SK_RING_BUF_H */