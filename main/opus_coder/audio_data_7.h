#ifndef AUDIO_DATA_7_H
#define AUDIO_DATA_7_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfo7[] = {0, 24, 49, 121, 262, 398, 526, 643, 788, 889};
const uint8_t g_audioData7[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x44, 0x58, 0x61, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1E, 0xED, 0x08, 0x53, 0x62, 0x37, 0xB0, 0xC6, 0xAD, 0xF5, 0x84, 0x13, 0x3D, 0x73, 0xA0, 0xF2, 0x5C, 0x55, 0x92, 0x18, 0x27, 0xBF, 0x11, 0x5F, 0x15, 0x13, 0x8A, 0x8A, 0xBD, 0xE3, 0x6D, 0x4E, 0x1E, 0x7B, 0xFC, 0xA6, 0x76, 0x47, 0x70, 0x8E, 0xE3, 0x03, 0xDE, 0xE8, 0x67, 0xC3, 0xF9, 0x95, 0x3D, 0xEB, 0x80, 0x9A, 0xFD, 0x23, 0x6F, 0xDA, 0x62, 0x9F, 0x8D, 0x9F, 0xC6, 0x4F, 0x00, 0x00, 0x89, 0x58, 0xE4, 0xF3, 0x52, 0xF4, 0xD7, 0xAE, 0xBC, 0x25, 0xB2, 0x67, 0x76, 0xDE, 0x56, 0xA9, 0x94, 0xE0, 0xCE, 0x45, 0x5B, 0x2B, 0xF6, 0xD8, 0xC6, 0xC2, 0x62, 0x97, 0x78, 0x49, 0x8C, 0xCF, 0xBF, 0x4B, 0xB3, 0xA2, 0xEC, 0xAE, 0x2C, 0xBE, 0xBF, 0x84, 0xF9, 0x47, 0xAA, 0x4A, 0xBD, 0xF2, 0x1D, 0x2E, 0x1A, 0x02, 0xAD, 0x72, 0x39, 0x69, 0xBC, 0xAC, 0x60, 0xB7, 0xB5, 0x90, 0xB5, 0xEF, 0xB6, 0x81, 0x3C, 0x6C, 0x3C, 0xAE, 0x6F, 0x44, 0x41, 0xAE, 0x1A, 0xF6, 0x4D, 0x34, 0xF3, 0x53, 0xD7, 0xEB, 0x1E, 0x96, 0x1C, 0xDC, 0x37, 0xC2, 0x46, 0x54, 0x7E, 0xAA, 0x77, 0x8D, 0xEA, 0xCF, 0x12, 0x20, 0xE2, 0xBF, 0x05, 0xFB, 0xF0, 0x75, 0xE5, 0x5F, 0xE4, 0xF0, 0x6F, 0x48, 0xF2, 0x3A, 0x61, 0x4C, 0x08, 0x1B, 0x3C, 0x53, 0x39, 0x6F, 0x79, 0x91, 0xF6, 0xD2, 0xDA, 0x92, 0xBD, 0xB4, 0x3A, 0x31, 0x7E, 0x48, 0x57, 0xD4, 0x19, 0x9E, 0x58, 0x80, 0x4F, 0x00, 0x00, 0x84, 0x58, 0xE5, 0xD2, 0x35, 0xDF, 0x09, 0x7C, 0xE7, 0x37, 0x87, 0x7F, 0xE7, 0xB4, 0x73, 0xDB, 0x33, 0x6E, 0xF2, 0x4D, 0x65, 0x14, 0x91, 0xA8, 0x96, 0x58, 0x06, 0x37, 0x47, 0x78, 0x31, 0xF3, 0x3C, 0xAB, 0x84, 0xB5, 0xFF, 0x1A, 0xCF, 0x75, 0x71, 0x8C, 0x19, 0xC7, 0xAD, 0xDC, 0x1D, 0x8D, 0x64, 0x86, 0xF3, 0x09, 0x36, 0x10, 0x03, 0x4E, 0x0A, 0x74, 0x56, 0x5B, 0x2C, 0xF6, 0x37, 0x27, 0xAF, 0x7C, 0x5A, 0x77, 0xD5, 0x4E, 0x7B, 0x24, 0xE6, 0x29, 0xD5, 0x30, 0x2A, 0x22, 0xB9, 0x76, 0x46, 0x31, 0x34, 0x7C, 0xBA, 0xA9, 0xED, 0x52, 0x4F, 0x89, 0x0B, 0x7C, 0x26, 0x40, 0xF3, 0x4F, 0x44, 0xC7, 0xD3, 0x95, 0x4E, 0xDE, 0x4B, 0x46, 0xE4, 0xF3, 0x77, 0x32, 0x71, 0xA4, 0xFA, 0xF3, 0x3C, 0x4B, 0xE8, 0xB9, 0x50, 0x75, 0x1F, 0x81, 0x39, 0x07, 0xC0, 0xDA, 0xEC, 0x3C, 0x79, 0xB9, 0xB0, 0x33, 0x14, 0x1C, 0x80, 0x4F, 0x00, 0x00, 0x7C, 0x58, 0xEF, 0x10, 0xA9, 0xBF, 0x95, 0xB6, 0x12, 0xEF, 0x85, 0x2E, 0xFD, 0x12, 0xA3, 0x1B, 0x4A, 0x46, 0x61, 0x7B, 0xDB, 0xAF, 0x41, 0xF7, 0x2F, 0x11, 0x20, 0x6B, 0x35, 0xBF, 0x3F, 0xEC, 0x0A, 0xD9, 0x73, 0xED, 0x22, 0xF3, 0xA8, 0xEA, 0x4E, 0xC8, 0x07, 0xB4, 0x9E, 0x13, 0x4D, 0x9A, 0xF6, 0xC3, 0x99, 0x65, 0xF5, 0x7E, 0x53, 0xFD, 0x3B, 0x69, 0x16, 0x61, 0x4B, 0x5B, 0xFF, 0x35, 0x51, 0x84, 0xDD, 0x75, 0x33, 0x21, 0x5A, 0xFB, 0x42, 0x01, 0xC9, 0x10, 0x78, 0x59, 0x04, 0x9B, 0x6B, 0xB1, 0x09, 0x08, 0xD1, 0xDB, 0xDB, 0x80, 0x98, 0xF2, 0x1B, 0x48, 0x31, 0xDD, 0xE5, 0x2F, 0xC1, 0xBB, 0x26, 0x59, 0xE4, 0xB6, 0x7A, 0x5B, 0x93, 0xC6, 0xCE, 0x15, 0x15, 0xE9, 0x33, 0xC5, 0x5C, 0x90, 0x39, 0xB2, 0x9F, 0xDA, 0xF2, 0x23, 0x32, 0xA1, 0x91, 0xD1, 0xB0, 0x4F, 0x00, 0x00, 0x71, 0x58, 0xEE, 0xAA, 0x0C, 0xAC, 0xB0, 0x21, 0x53, 0x88, 0xBC, 0xD9, 0xEB, 0x91, 0x9A, 0x7E, 0x2B, 0x7D, 0xDA, 0x84, 0x99, 0x79, 0x54, 0x16, 0xC7, 0x0C, 0xD1, 0x8C, 0x0B, 0x69, 0xB4, 0x80, 0xD2, 0xEF, 0xC7, 0xDB, 0xC7, 0xDD, 0x52, 0x2B, 0xC8, 0x74, 0x9C, 0x12, 0xE0, 0x32, 0x0B, 0xBF, 0x16, 0xE2, 0x7C, 0xB6, 0xC8, 0xA6, 0x55, 0xC0, 0x43, 0x2E, 0xD5, 0x38, 0xAB, 0xD5, 0x23, 0x19, 0xC5, 0x82, 0x59, 0xCD, 0xE5, 0xEE, 0x96, 0x8D, 0x72, 0xE5, 0x99, 0x8A, 0x6D, 0xAB, 0xA9, 0x83, 0xB1, 0x79, 0x9F, 0xC6, 0x77, 0x8D, 0xEE, 0x32, 0xD3, 0x0D, 0x70, 0x1B, 0x0E, 0x8E, 0x7B, 0x44, 0x69, 0x81, 0x2E, 0x65, 0xB0, 0x09, 0xD1, 0x03, 0xEB, 0x7A, 0x49, 0x78, 0xFC, 0xAE, 0x7C, 0x2E, 0x40, 0xF8, 0x4F, 0x00, 0x00, 0x8D, 0x58, 0xEF, 0x13, 0xE3, 0x87, 0xC8, 0x59, 0x81, 0x52, 0x77, 0x1C, 0x5D, 0x28, 0x45, 0x93, 0x39, 0xF8, 0xD9, 0x63, 0x59, 0x5E, 0x7F, 0x48, 0x2A, 0xCE, 0x86, 0xC6, 0x3C, 0x0E, 0xDE, 0xC4, 0xF7, 0xCF, 0x24, 0x5F, 0x50, 0xDA, 0x08, 0xED, 0x2F, 0xD5, 0x53, 0x93, 0x3F, 0x76, 0x28, 0xE0, 0x8D, 0x2F, 0x90, 0x57, 0x1E, 0x56, 0x5E, 0x33, 0x41, 0x24, 0xB4, 0x03, 0xC5, 0x8D, 0x7E, 0xD0, 0xE5, 0xA0, 0x29, 0xC4, 0xB1, 0xEC, 0xE8, 0x43, 0x39, 0x18, 0xBC, 0xCF, 0x56, 0x04, 0x1E, 0x9C, 0x05, 0x4B, 0x4C, 0xEE, 0x2B, 0x72, 0x33, 0xC4, 0x50, 0x4A, 0x88, 0x56, 0x16, 0x4A, 0x8C, 0x37, 0xD2, 0x42, 0x57, 0x0A, 0x37, 0x14, 0x6D, 0x48, 0x52, 0xF7, 0x3B, 0x32, 0x6A, 0x25, 0x88, 0x2D, 0x04, 0x58, 0x32, 0xD5, 0x93, 0x2B, 0x7D, 0x1F, 0x28, 0xB0, 0xB9, 0x44, 0xCE, 0x76, 0x1D, 0x1D, 0x8E, 0xA7, 0x9E, 0xD3, 0x23, 0xB2, 0xAC, 0x92, 0x8E, 0x2A, 0x1E, 0x8F, 0xEB, 0x80, 0x4F, 0x00, 0x00, 0x61, 0x58, 0xC4, 0x72, 0x42, 0x8D, 0xD0, 0xBC, 0x27, 0x89, 0xB0, 0xB3, 0xDA, 0x02, 0x3A, 0x9A, 0x5D, 0x54, 0x08, 0x7A, 0x64, 0xEA, 0xD4, 0x6B, 0x0F, 0x98, 0x50, 0x11, 0xE3, 0x5E, 0x89, 0xA3, 0xAE, 0xCF, 0x77, 0x92, 0x3E, 0x3C, 0x83, 0xE2, 0x3E, 0x51, 0x97, 0x8D, 0x18, 0x46, 0xA4, 0xE4, 0x8E, 0x9C, 0x51, 0x10, 0x15, 0x5F, 0xA8, 0xA5, 0x7A, 0x5F, 0x6D, 0x15, 0x44, 0x13, 0xE1, 0x79, 0x4A, 0x22, 0x43, 0x76, 0x2A, 0xFD, 0x70, 0xD9, 0x29, 0xA1, 0x7B, 0xA3, 0x9D, 0x7D, 0x06, 0xDC, 0xE4, 0x06, 0x9E, 0x42, 0x81, 0xB0, 0x55, 0xE9, 0x2E, 0x20, 0x95, 0x0A, 0x93, 0xF1, 0x39, 0xD2, 0x1E, 0x7D, 0x4F, 0x00, 0x00, 0x25, 0x58, 0x05, 0x07, 0xF4, 0xD4, 0x8B, 0x44, 0x40, 0x25, 0xE8, 0x9A, 0x6B, 0xDE, 0x81, 0xB0, 0xAC, 0x02, 0x6B, 0x8A, 0xC8, 0x69, 0x44, 0xA0, 0x33, 0x81, 0x60, 0x07, 0x0B, 0xCD, 0x90, 0xC8, 0xF5, 0x99, 0x5E, 0x0F, 0xE7, 0x29
};

#endif // AUDIO_DATA_7_H