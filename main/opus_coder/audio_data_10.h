#ifndef AUDIO_DATA_10_H
#define AUDIO_DATA_10_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfo10[] = {0, 24, 49, 109, 245, 390, 517, 647, 790, 913, 1032};
const uint8_t g_audioData10[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x38, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x0A, 0xD8, 0x19, 0x96, 0x88, 0xDF, 0x65, 0x5E, 0xDD, 0x92, 0x80, 0xAF, 0x0D, 0xD1, 0xE2, 0x9E, 0x94, 0xFA, 0x2F, 0xFD, 0x31, 0xE2, 0xDA, 0x24, 0x4C, 0x15, 0x58, 0x99, 0x08, 0x64, 0xBD, 0xDF, 0x80, 0x21, 0x05, 0x4A, 0xF1, 0x6B, 0x5B, 0x6C, 0x06, 0x5F, 0xD6, 0x28, 0x47, 0x5B, 0x4E, 0x4A, 0x4F, 0x00, 0x00, 0x84, 0x58, 0xE0, 0x96, 0x8C, 0x8B, 0x92, 0x04, 0x7D, 0x53, 0x4A, 0x14, 0xA3, 0x03, 0xB7, 0xF9, 0xC6, 0xE7, 0x12, 0xEE, 0xAE, 0xE7, 0x5F, 0x49, 0x1F, 0xBC, 0xC8, 0x11, 0x0A, 0x4B, 0x0A, 0x5A, 0xD4, 0x85, 0x66, 0x65, 0x6D, 0x34, 0x0A, 0xC4, 0x1F, 0x75, 0x37, 0xC7, 0x23, 0xCA, 0x59, 0x0D, 0xA5, 0x2A, 0x9D, 0x7A, 0x50, 0x6B, 0x47, 0xCA, 0xF5, 0xF6, 0xF3, 0x70, 0xB7, 0x3E, 0x75, 0x14, 0xF4, 0x61, 0x2A, 0xF7, 0xEF, 0x99, 0x28, 0xAD, 0x2F, 0x4B, 0xB2, 0x4D, 0x81, 0x41, 0x2B, 0x93, 0x5A, 0x94, 0xB6, 0xB0, 0xD2, 0xAC, 0x88, 0xBC, 0x24, 0xA3, 0xD1, 0xDE, 0x6F, 0x48, 0x14, 0xAD, 0x1F, 0xB1, 0xE2, 0xEB, 0x4D, 0x98, 0x06, 0x99, 0xA2, 0xCB, 0x72, 0x1F, 0x91, 0x9F, 0x44, 0x0A, 0x8F, 0x39, 0xE3, 0x19, 0x72, 0x91, 0x0E, 0x21, 0xBE, 0xB9, 0xDE, 0x70, 0x86, 0x4C, 0x45, 0x9B, 0x8D, 0xCA, 0xB4, 0x89, 0xC0, 0x4F, 0x00, 0x00, 0x8D, 0x58, 0xE4, 0xAF, 0xED, 0x71, 0x14, 0xD6, 0xDB, 0x44, 0x8F, 0xC7, 0x03, 0x20, 0xFA, 0xAF, 0x16, 0x43, 0xFA, 0x1B, 0x3D, 0xED, 0xB3, 0x2F, 0xB7, 0xB9, 0x2D, 0xB6, 0x88, 0xDD, 0x6E, 0x56, 0xF9, 0xF0, 0x00, 0x3B, 0x62, 0x41, 0xF7, 0x5B, 0x8A, 0x2B, 0x4C, 0x6B, 0xA5, 0xDC, 0x6B, 0x0E, 0x1F, 0x31, 0x39, 0x6B, 0x2D, 0xC0, 0xB1, 0x92, 0xBA, 0x89, 0x2B, 0xC0, 0x47, 0x97, 0x11, 0xE9, 0x39, 0xA6, 0x7D, 0x81, 0xA8, 0x23, 0xBE, 0x6B, 0xDA, 0x9E, 0x22, 0x49, 0x91, 0x32, 0xC1, 0xDC, 0x5D, 0x99, 0x8E, 0x43, 0xEF, 0x1A, 0x1B, 0x7C, 0xC3, 0xA7, 0xF8, 0x84, 0xD2, 0x3E, 0xA2, 0xED, 0x4A, 0x87, 0x1B, 0x6B, 0x2E, 0xAF, 0xC1, 0x62, 0x91, 0x5F, 0x21, 0x0F, 0xC7, 0x8C, 0x02, 0xBD, 0xB0, 0x3D, 0xE5, 0xD8, 0x3D, 0x27, 0x54, 0xB7, 0xA3, 0x15, 0xFA, 0xD5, 0x9C, 0x8F, 0xCC, 0xAC, 0xB6, 0xDC, 0xC3, 0x73, 0x88, 0xC5, 0xA7, 0xAF, 0x6A, 0xEC, 0x09, 0x22, 0xAD, 0x32, 0x4F, 0x00, 0x00, 0x7B, 0x58, 0xE5, 0x27, 0x5F, 0x7E, 0x33, 0xB1, 0xD5, 0x7E, 0xB3, 0x20, 0x47, 0x1A, 0x88, 0x5A, 0x64, 0xA5, 0x77, 0xBB, 0x3E, 0x7D, 0x1C, 0x10, 0xCF, 0x63, 0xD9, 0x07, 0x32, 0xAA, 0xE6, 0xDD, 0xD7, 0xF8, 0x1F, 0x6B, 0xE9, 0xC6, 0x43, 0x16, 0xA4, 0x37, 0xC0, 0xED, 0xE5, 0x22, 0x58, 0x25, 0x87, 0x2A, 0x23, 0xDE, 0xEA, 0xFB, 0x36, 0x84, 0x98, 0x0E, 0x16, 0x42, 0xAF, 0x9C, 0xF0, 0xA9, 0xD2, 0xBE, 0x4D, 0x58, 0xEF, 0x79, 0x66, 0x8E, 0x6A, 0xAD, 0x3B, 0x3A, 0x02, 0x78, 0x1F, 0xA3, 0xBD, 0x37, 0x25, 0x13, 0x80, 0x6D, 0xEF, 0xCC, 0xF1, 0xF8, 0x26, 0x4B, 0x0A, 0x56, 0x91, 0x47, 0x27, 0xFD, 0xCB, 0xE3, 0x8D, 0x29, 0xA4, 0xF0, 0x72, 0x75, 0x79, 0x5E, 0x34, 0x57, 0x37, 0xC5, 0x8E, 0x29, 0xBF, 0xF2, 0x87, 0xA4, 0x1F, 0x47, 0x0A, 0xF5, 0x48, 0xD4, 0x4F, 0x00, 0x00, 0x7E, 0x58, 0xED, 0x6C, 0x60, 0xA9, 0xBA, 0xDA, 0x44, 0x6E, 0x61, 0x82, 0xF0, 0x7E, 0x28, 0xD4, 0x0F, 0x8A, 0x7B, 0x45, 0x4E, 0x60, 0x48, 0x99, 0x57, 0xCC, 0x96, 0x65, 0xEE, 0xA3, 0x07, 0xE3, 0xC0, 0x08, 0xEA, 0x33, 0x0A, 0xD0, 0xB2, 0xB5, 0x24, 0xB7, 0xCF, 0x0F, 0x99, 0xE4, 0x43, 0x4C, 0x8F, 0x54, 0xFC, 0xFC, 0x57, 0x88, 0x06, 0x20, 0x09, 0x56, 0xA2, 0x6F, 0x66, 0x39, 0x0C, 0xD4, 0x7B, 0x68, 0xB9, 0xF2, 0x10, 0x9F, 0x53, 0x60, 0xDE, 0xCE, 0xB1, 0x43, 0x9D, 0x7A, 0x3F, 0xA8, 0xCC, 0x72, 0x3C, 0xCF, 0x95, 0x82, 0xEA, 0x26, 0xCF, 0x12, 0x70, 0x7E, 0xB0, 0x36, 0x8C, 0x9A, 0x00, 0x85, 0x4D, 0x5E, 0xF0, 0x61, 0x1E, 0xE4, 0x89, 0xBA, 0x2E, 0x4B, 0xD6, 0x3E, 0x6D, 0x3B, 0xB2, 0xFA, 0x37, 0x3B, 0xEB, 0x1E, 0x87, 0xD8, 0x38, 0x07, 0xDD, 0x45, 0x39, 0x1D, 0x07, 0x4F, 0x00, 0x00, 0x8B, 0x58, 0xEB, 0xAC, 0x74, 0xDB, 0x1A, 0x36, 0xE5, 0xD6, 0x75, 0xB3, 0x58, 0x86, 0x6F, 0x26, 0x22, 0x73, 0x91, 0x22, 0x2E, 0x68, 0x1B, 0x2F, 0xA7, 0x36, 0x42, 0xFB, 0x32, 0xBD, 0x54, 0xA9, 0x76, 0x30, 0xDB, 0x5A, 0xA3, 0xF6, 0x7F, 0x61, 0xB8, 0x1C, 0x24, 0x29, 0xD3, 0x7B, 0xC9, 0x26, 0x11, 0x11, 0x59, 0x36, 0xCD, 0xDF, 0x87, 0x1A, 0x49, 0x65, 0xB9, 0x84, 0x4C, 0x5F, 0xD1, 0xB4, 0x49, 0xE4, 0x55, 0xD9, 0xD2, 0x3B, 0x74, 0xF5, 0x9F, 0xB4, 0xCB, 0x9F, 0x7F, 0x1A, 0xED, 0x13, 0x5D, 0xE9, 0x2A, 0xB7, 0x22, 0x24, 0xDB, 0x8F, 0x71, 0xE0, 0x15, 0x4D, 0x42, 0x8B, 0x8E, 0xB5, 0x1B, 0xE3, 0xC8, 0xBA, 0x01, 0x0F, 0x60, 0x27, 0xF2, 0x2E, 0x24, 0xBD, 0x34, 0x1C, 0xD3, 0xF6, 0x4A, 0xFF, 0x6A, 0x2E, 0x2D, 0xBF, 0x0B, 0xB7, 0xA9, 0xCA, 0xB9, 0x46, 0xDA, 0x5F, 0xB6, 0x48, 0xE1, 0x83, 0x2B, 0xC6, 0x78, 0x9E, 0xCD, 0x30, 0x39, 0xAD, 0x2D, 0xEE, 0x4F, 0x00, 0x00, 0x77, 0x58, 0xED, 0xBD, 0x43, 0x84, 0xAF, 0x52, 0x13, 0x2C, 0xAD, 0x48, 0x6F, 0x63, 0x99, 0xFD, 0xA9, 0xF4, 0xFE, 0xEC, 0x07, 0xD2, 0x4A, 0x69, 0xCA, 0x87, 0x3A, 0x8D, 0x46, 0x97, 0x69, 0x73, 0xC6, 0x21, 0x84, 0x14, 0x1E, 0xD7, 0xDC, 0x93, 0xF4, 0x98, 0x9C, 0x8A, 0xEF, 0xA4, 0xD3, 0xFE, 0xA7, 0x66, 0xD6, 0xDC, 0xBE, 0x0F, 0x94, 0xDC, 0x04, 0x76, 0x0D, 0x7D, 0xF9, 0xE2, 0x16, 0x61, 0x1E, 0xD7, 0xEB, 0x86, 0xE5, 0xE2, 0xF6, 0x63, 0x71, 0x7A, 0x3F, 0xC3, 0x2F, 0xEF, 0xA7, 0xCC, 0x42, 0x06, 0x01, 0x9D, 0x40, 0x0B, 0x3B, 0xA8, 0x50, 0x88, 0x48, 0x67, 0x03, 0xA1, 0x60, 0x4F, 0x18, 0xAB, 0xDE, 0xA8, 0xB7, 0x68, 0x89, 0x23, 0xB6, 0xF6, 0x08, 0x77, 0x24, 0xAF, 0xED, 0xDB, 0x3B, 0xE4, 0xA0, 0xD3, 0x60, 0x7F, 0x13, 0x21, 0x4F, 0x00, 0x00, 0x73, 0x58, 0xCB, 0x7B, 0xA5, 0x74, 0xBD, 0xA3, 0xC2, 0x52, 0x8D, 0xEF, 0x8E, 0x7D, 0xA6, 0x70, 0x1A, 0x3C, 0x50, 0x15, 0xE1, 0x2C, 0x69, 0x16, 0xB9, 0x54, 0x18, 0x4E, 0x53, 0x0D, 0xC4, 0x0A, 0x5D, 0x6A, 0xD6, 0x76, 0xB8, 0x55, 0x2A, 0x29, 0x98, 0x1A, 0x55, 0x68, 0x51, 0xC7, 0xFB, 0x2F, 0xB1, 0x24, 0xE0, 0x82, 0x33, 0x07, 0x40, 0xB0, 0x21, 0x59, 0x72, 0x85, 0x2C, 0xC3, 0x24, 0x7E, 0x7A, 0xDD, 0x40, 0x31, 0x11, 0x60, 0xC3, 0xDB, 0x25, 0xEA, 0x4A, 0xE5, 0x18, 0xCB, 0x23, 0x42, 0xF1, 0x98, 0xCC, 0x62, 0x90, 0xF6, 0x88, 0x5F, 0x22, 0x0F, 0xE7, 0x56, 0xCA, 0x74, 0x8A, 0x64, 0xE8, 0x2A, 0xAF, 0x0B, 0x68, 0x12, 0x56, 0x40, 0x1C, 0xAE, 0x8A, 0x76, 0xD5, 0x0C, 0x7F, 0xB5, 0x61, 0xB9, 0x2D, 0x80, 0x4F, 0x00, 0x00, 0x2B, 0x58, 0x04, 0x48, 0xF7, 0x52, 0x32, 0x2C, 0xBB, 0xED, 0x2E, 0xC0, 0xAA, 0xC2, 0x01, 0xF6, 0x3D, 0x7E, 0x6E, 0xE3, 0xF9, 0x88, 0xBE, 0xE6, 0x94, 0x97, 0x55, 0x30, 0xA5, 0xA5, 0xC5, 0x55, 0xC0, 0x4B, 0x8A, 0xD8, 0x5B, 0x5B, 0xF4, 0xD2, 0xED, 0x22, 0x36, 0x9B
};

#endif // AUDIO_DATA_10_H