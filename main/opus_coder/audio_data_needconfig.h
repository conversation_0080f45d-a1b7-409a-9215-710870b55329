#ifndef AUDIO_DATA_NEEDCONFIG_H
#define AUDIO_DATA_NEEDCONFIG_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfoNeedConfig[] = {0, 24, 76, 207, 321, 420, 554, 672, 775, 905, 1049, 1149, 1256, 1394, 1525, 1648, 1768, 1898, 2031, 2171, 2281, 2420, 2544, 2670, 2793, 2928, 3050, 3104, 3129, 3154};
const uint8_t g_audioDataNeedConfig[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x30, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x6A, 0xCC, 0x95, 0x66, 0xA9, 0x53, 0x38, 0x2E, 0x9D, 0x16, 0xDE, 0x46, 0x06, 0x3F, 0x0E, 0xF6, 0x7B, 0x91, 0xBA, 0x11, 0x44, 0x28, 0xE8, 0x6D, 0xD3, 0xB0, 0xBF, 0x83, 0x32, 0x7C, 0xF9, 0x82, 0xB9, 0x84, 0x4F, 0x00, 0x00, 0x7F, 0x58, 0xE4, 0x8C, 0xEE, 0xF3, 0x4F, 0x75, 0x47, 0xD1, 0xE9, 0xB7, 0x19, 0x27, 0x3B, 0x05, 0x4B, 0x2E, 0x81, 0x08, 0xF8, 0x2F, 0x59, 0xBD, 0xF0, 0x3E, 0x94, 0xDB, 0xD4, 0x35, 0xBF, 0x98, 0x5D, 0x5B, 0xEE, 0xF6, 0x60, 0xEF, 0x2F, 0xEE, 0xAF, 0x22, 0xC7, 0x48, 0xF6, 0x08, 0x0A, 0xFE, 0x65, 0x36, 0x4F, 0xCB, 0xD7, 0x6C, 0x0C, 0xDA, 0x89, 0x1B, 0xAF, 0xE5, 0x16, 0x5C, 0x59, 0xB1, 0xC8, 0x37, 0x51, 0x11, 0x2B, 0x02, 0x15, 0xE0, 0xC9, 0x96, 0x65, 0xC1, 0x82, 0x11, 0xE7, 0xEB, 0x95, 0xF5, 0xEF, 0xEA, 0xC6, 0x56, 0xCA, 0x16, 0x60, 0x3B, 0x87, 0xE3, 0x13, 0x22, 0xC1, 0x59, 0xC7, 0xCB, 0x54, 0x8C, 0x19, 0xCE, 0x74, 0x17, 0x2B, 0x58, 0x23, 0x80, 0x6E, 0x7C, 0xEE, 0xA8, 0x3E, 0xC1, 0x47, 0x0E, 0x63, 0xAD, 0x74, 0x75, 0x31, 0x51, 0x26, 0xF2, 0xA8, 0x63, 0x9F, 0xF6, 0x4F, 0x00, 0x00, 0x6E, 0x58, 0xE5, 0x1F, 0x85, 0x8D, 0x9C, 0xED, 0x6A, 0x39, 0x0A, 0x12, 0xA5, 0x28, 0x80, 0xCE, 0x87, 0xAB, 0xD8, 0x5E, 0x15, 0x14, 0x73, 0x9C, 0xF8, 0xE1, 0xF3, 0x70, 0x2B, 0x8C, 0x1A, 0x55, 0x6D, 0xDB, 0x70, 0xB2, 0xD3, 0xE6, 0x26, 0x27, 0x3E, 0x81, 0x88, 0x29, 0x23, 0x7E, 0xC3, 0xC9, 0x42, 0x22, 0x1D, 0xFD, 0x5C, 0x7D, 0xA1, 0x4D, 0xB7, 0x15, 0x5B, 0xCD, 0xDC, 0x82, 0x70, 0xD5, 0x32, 0x30, 0x34, 0x71, 0x87, 0x7E, 0x23, 0x95, 0x1A, 0xDB, 0xFD, 0x14, 0xFC, 0x56, 0x95, 0x86, 0x1F, 0xD9, 0x7A, 0x85, 0x33, 0xF4, 0xFE, 0x89, 0xA8, 0xE9, 0x60, 0x92, 0x41, 0x22, 0x56, 0x84, 0x65, 0x53, 0xFA, 0xC9, 0xBC, 0x62, 0x4E, 0x9F, 0x83, 0xC9, 0xD5, 0xAD, 0xBD, 0x09, 0x01, 0x4F, 0x00, 0x00, 0x5F, 0x58, 0xEC, 0xDB, 0xBD, 0x22, 0x66, 0x2E, 0x96, 0x02, 0x59, 0xE3, 0x01, 0x58, 0xA9, 0xA4, 0x48, 0x4B, 0x90, 0x60, 0x5B, 0xD4, 0x9D, 0xDD, 0x92, 0x0C, 0xBE, 0xA9, 0x53, 0xF8, 0x74, 0x98, 0x96, 0x20, 0x6F, 0xCB, 0x32, 0x5B, 0x88, 0x86, 0x86, 0x29, 0x1C, 0xF2, 0x88, 0x7E, 0x62, 0x17, 0x12, 0xD3, 0x6E, 0xAB, 0x39, 0xEE, 0xF2, 0x62, 0x6E, 0x60, 0x9C, 0xBE, 0x37, 0x04, 0xA8, 0x82, 0x4E, 0x86, 0x17, 0xF6, 0x37, 0xF7, 0xA8, 0x7E, 0x17, 0xC2, 0x28, 0xFA, 0xA9, 0xE8, 0xEC, 0xA1, 0x65, 0x6B, 0xC4, 0xAB, 0xCD, 0x3A, 0xDD, 0x27, 0x23, 0x21, 0xBC, 0xCA, 0x11, 0x2B, 0x0A, 0x5B, 0x4F, 0x00, 0x00, 0x82, 0x58, 0xE8, 0x87, 0xE3, 0x88, 0xA1, 0xF7, 0x2F, 0x9E, 0x9D, 0x18, 0x4E, 0x7F, 0x2D, 0xB1, 0xFD, 0x86, 0x7B, 0xEB, 0xA6, 0xE7, 0xD8, 0x40, 0xEF, 0x17, 0x6A, 0xD6, 0x95, 0x64, 0xA9, 0x54, 0xE6, 0x8D, 0xCA, 0xE1, 0xC0, 0xCB, 0x03, 0x89, 0x69, 0x69, 0x0C, 0x79, 0x46, 0x97, 0x0A, 0x01, 0x7C, 0x78, 0xDC, 0x0C, 0x8D, 0xA0, 0x2C, 0x14, 0x93, 0x97, 0x68, 0x9B, 0x85, 0x97, 0x76, 0xCF, 0x6B, 0x59, 0x03, 0x4C, 0xB4, 0xCE, 0x9D, 0xDC, 0xA3, 0xF9, 0x99, 0xE5, 0x27, 0xBE, 0x50, 0x15, 0xAB, 0x69, 0xC3, 0x90, 0x48, 0xF3, 0xB1, 0xA8, 0xA3, 0x8F, 0x5F, 0xFE, 0x08, 0x13, 0x58, 0x85, 0x51, 0x9F, 0xF2, 0xB0, 0x73, 0xE9, 0x39, 0x7F, 0x08, 0x87, 0x65, 0xCF, 0xBE, 0xBF, 0xD1, 0xC0, 0x46, 0x5B, 0xAA, 0xAE, 0x81, 0x17, 0xB3, 0x13, 0xBB, 0x25, 0x09, 0xAE, 0x5F, 0x34, 0x87, 0xB8, 0x48, 0xCA, 0x60, 0x4F, 0x00, 0x00, 0x72, 0x58, 0xED, 0x5B, 0x9F, 0x3B, 0xBA, 0x08, 0x37, 0x0D, 0xF6, 0x4D, 0x91, 0xD3, 0xAB, 0x29, 0x39, 0x4C, 0x8E, 0x6E, 0x50, 0xC7, 0x3A, 0x38, 0xD7, 0xD2, 0x88, 0xDA, 0x19, 0xE7, 0x7F, 0x9F, 0xD2, 0xC3, 0xFB, 0xAF, 0xC6, 0xF9, 0x0F, 0x14, 0xD3, 0x0E, 0xF1, 0x73, 0x29, 0x8D, 0xBD, 0x7E, 0xA1, 0x38, 0x49, 0xA4, 0x59, 0x88, 0xA9, 0x09, 0xF4, 0xE4, 0x4A, 0x1D, 0xBA, 0x63, 0x97, 0xCF, 0x62, 0x2A, 0xE4, 0xA6, 0x8A, 0x71, 0xC7, 0x74, 0xB1, 0xC7, 0x3E, 0xA2, 0xCD, 0x60, 0xF1, 0x0F, 0x08, 0xFA, 0x4B, 0x2A, 0x64, 0x34, 0x33, 0x95, 0xA1, 0x52, 0x4F, 0x49, 0xA0, 0xF6, 0x63, 0x14, 0xF1, 0xD4, 0x31, 0xCA, 0xDC, 0x48, 0x64, 0xFE, 0x8B, 0xFF, 0xD6, 0xFF, 0xB8, 0x3E, 0xEA, 0x14, 0x7C, 0x23, 0x1E, 0x4F, 0x00, 0x00, 0x63, 0x58, 0xEE, 0xD6, 0x29, 0x6E, 0x12, 0x97, 0xA6, 0x6B, 0x4C, 0xF9, 0xFF, 0x66, 0x82, 0x66, 0x59, 0xAA, 0x32, 0x26, 0xE0, 0xDF, 0x18, 0x54, 0x08, 0x5F, 0x57, 0xDF, 0xD6, 0xC2, 0xEF, 0xF6, 0x2B, 0x83, 0xE4, 0xCA, 0xF1, 0x97, 0x16, 0xE7, 0x0E, 0x5F, 0x0F, 0x4D, 0xED, 0x2B, 0x50, 0x22, 0xFD, 0xE3, 0x65, 0xD9, 0xE5, 0x23, 0x20, 0xAB, 0xAD, 0xA1, 0x95, 0x62, 0xAE, 0x26, 0x0D, 0xB2, 0xEB, 0xFE, 0xBF, 0x3A, 0x80, 0xC0, 0x95, 0x6D, 0x40, 0x7F, 0xB4, 0xF6, 0xF5, 0xA8, 0xC4, 0x9C, 0x55, 0x2B, 0xE8, 0xA1, 0x7A, 0x1A, 0x17, 0x41, 0x4B, 0x61, 0x00, 0x87, 0x85, 0xA7, 0x69, 0x4E, 0x82, 0x98, 0x1C, 0x48, 0x4F, 0x00, 0x00, 0x7E, 0x58, 0xEB, 0x7E, 0x1E, 0xA5, 0x0C, 0xF2, 0x80, 0x54, 0x3C, 0x01, 0x89, 0xD4, 0xEC, 0xD4, 0xA3, 0x0D, 0x1C, 0xB9, 0x3E, 0x2C, 0x4A, 0x21, 0x0D, 0x66, 0xEB, 0x98, 0xCD, 0x34, 0x32, 0x17, 0x01, 0xEC, 0x68, 0xA2, 0x1D, 0x99, 0xEF, 0x9C, 0x9C, 0xB5, 0xFE, 0xD8, 0x8F, 0x72, 0xE6, 0x1F, 0xD5, 0x59, 0xBF, 0x3A, 0xF5, 0x92, 0x37, 0x40, 0xC2, 0x09, 0xC3, 0xEF, 0x21, 0xAD, 0xE6, 0xE4, 0x65, 0xBA, 0x26, 0xDD, 0xE5, 0x68, 0x8E, 0xF0, 0xD5, 0x3A, 0x36, 0x66, 0x37, 0x01, 0x2D, 0x39, 0x80, 0xFB, 0xA6, 0x13, 0xA3, 0xC6, 0x4C, 0xF4, 0x1C, 0x02, 0xBB, 0x07, 0x3F, 0x4E, 0xAD, 0xBE, 0x9B, 0x74, 0xBC, 0xA7, 0x95, 0xA9, 0xDD, 0x60, 0xD9, 0x1A, 0xF8, 0x12, 0x38, 0xB0, 0xA3, 0x44, 0x71, 0x85, 0xD4, 0x6B, 0xFE, 0xB4, 0x70, 0xD4, 0x5A, 0x1D, 0xE5, 0xEC, 0x74, 0xC0, 0x35, 0x4F, 0x00, 0x00, 0x8C, 0x58, 0xEA, 0x8D, 0x12, 0xC9, 0xB3, 0xCA, 0x15, 0xF3, 0x1B, 0xE8, 0x12, 0x01, 0xE9, 0x11, 0x19, 0x55, 0x45, 0x93, 0x73, 0x81, 0x64, 0x00, 0x9E, 0x07, 0x32, 0x81, 0xAF, 0xC6, 0x79, 0x62, 0x0D, 0x9A, 0x33, 0xBA, 0x69, 0x1A, 0xA6, 0xC8, 0x98, 0x6B, 0xA0, 0x57, 0x2E, 0x81, 0x09, 0x10, 0x68, 0x06, 0x1F, 0x0F, 0x35, 0xB4, 0x5C, 0xBC, 0x5F, 0x4C, 0x28, 0x50, 0xC7, 0x5E, 0x75, 0x59, 0x56, 0x59, 0xF5, 0xE8, 0x2E, 0x08, 0x2F, 0x14, 0xD7, 0x11, 0x24, 0xC5, 0xB6, 0x4D, 0x1A, 0xA2, 0x17, 0x70, 0xD7, 0x13, 0x50, 0x26, 0x15, 0x6B, 0xEB, 0x6B, 0x1D, 0x54, 0x9D, 0x93, 0xD8, 0x77, 0x86, 0x13, 0x61, 0xC3, 0xAC, 0x38, 0x17, 0xB0, 0x8B, 0xF6, 0x33, 0xDE, 0xB3, 0x54, 0x1D, 0x99, 0xA6, 0x04, 0xD0, 0x87, 0x23, 0xCD, 0x51, 0x2C, 0x2A, 0xAD, 0xF0, 0xA3, 0xEC, 0xC4, 0x67, 0x5F, 0x7F, 0xE8, 0x07, 0xE8, 0x09, 0x3A, 0x35, 0xD9, 0xE3, 0x95, 0xA2, 0xD7, 0x80, 0x4F, 0x00, 0x00, 0x60, 0x58, 0xED, 0xB8, 0xC4, 0x3A, 0xB8, 0x77, 0x3F, 0xB0, 0x70, 0xDA, 0x91, 0xDA, 0xC7, 0xD3, 0xBE, 0x4C, 0x4B, 0xC3, 0x9C, 0x40, 0x49, 0xF9, 0x68, 0xA7, 0x1B, 0xF5, 0x12, 0xAB, 0x56, 0x55, 0xEE, 0xB7, 0xDF, 0x46, 0x44, 0x70, 0x68, 0xC0, 0x94, 0x53, 0xED, 0x1A, 0x5A, 0xA9, 0x2C, 0xC0, 0x4B, 0xD2, 0x02, 0x75, 0x94, 0x38, 0x07, 0x5E, 0x5B, 0xAE, 0x74, 0x1D, 0x86, 0x88, 0x96, 0x15, 0xFF, 0x90, 0xEF, 0xFB, 0x0F, 0xFF, 0x70, 0xD4, 0xC0, 0xA3, 0xBB, 0x03, 0x38, 0x2B, 0xFD, 0xC2, 0x23, 0x93, 0x10, 0xA7, 0x56, 0x21, 0xD0, 0x92, 0x7D, 0xAF, 0xC3, 0x04, 0xDD, 0xAE, 0x34, 0x1F, 0xC0, 0x4F, 0x00, 0x00, 0x67, 0x58, 0x8B, 0x0C, 0x50, 0x22, 0x51, 0x0C, 0xD4, 0x74, 0xC5, 0x05, 0x98, 0xC1, 0x2A, 0x9C, 0x3F, 0x17, 0x53, 0xA0, 0x1E, 0x4B, 0x44, 0xC6, 0xFD, 0x30, 0x6B, 0x38, 0x52, 0xC3, 0x47, 0xD8, 0x30, 0x15, 0x2F, 0xA8, 0x9C, 0x3C, 0x92, 0x0A, 0x48, 0xD9, 0x19, 0xC9, 0x7B, 0xEF, 0x25, 0x27, 0x80, 0xC5, 0xE8, 0xE0, 0xF4, 0x69, 0xD3, 0x9C, 0x15, 0x69, 0x7C, 0x83, 0xEB, 0xD0, 0xDC, 0x2B, 0x68, 0xC4, 0xE4, 0xBE, 0x66, 0x9D, 0x97, 0x59, 0x70, 0xB3, 0x45, 0xE9, 0x24, 0xDB, 0x07, 0x78, 0x9C, 0x23, 0x20, 0x5D, 0x06, 0x18, 0x36, 0x95, 0x7F, 0x1D, 0x66, 0x81, 0xA5, 0x41, 0x63, 0xB5, 0x6F, 0x9D, 0x9D, 0xDC, 0xC2, 0xE0, 0x6B, 0x40, 0x4F, 0x00, 0x00, 0x86, 0x58, 0xE0, 0x0F, 0x86, 0x42, 0x12, 0xE7, 0x45, 0x49, 0xBA, 0x85, 0xF8, 0xE0, 0x83, 0x87, 0xAF, 0xD8, 0x3B, 0x51, 0x45, 0x8D, 0xC1, 0xF0, 0x42, 0xFD, 0xE0, 0x9C, 0x1A, 0x9D, 0xD4, 0x0E, 0xAF, 0x77, 0x5A, 0x20, 0x38, 0xB5, 0xD4, 0xF9, 0x03, 0x9F, 0xD4, 0xCB, 0xB7, 0xF2, 0x3A, 0x14, 0x91, 0x87, 0x68, 0xB9, 0x54, 0x47, 0x87, 0x87, 0x8A, 0x39, 0x31, 0x25, 0x2A, 0xA6, 0x04, 0xC3, 0x9C, 0x28, 0x81, 0xBF, 0xE6, 0xF0, 0x0E, 0x3E, 0x6A, 0x75, 0xA2, 0xFB, 0xCA, 0xF6, 0x5D, 0xF3, 0xE0, 0x4F, 0xF4, 0x51, 0xBF, 0xFF, 0x0E, 0xC4, 0xF3, 0x5A, 0xDB, 0xD1, 0x60, 0x85, 0xAF, 0x0E, 0xB2, 0x82, 0x33, 0x7E, 0x98, 0xBD, 0x32, 0x1E, 0x5F, 0x33, 0x54, 0xC1, 0x02, 0xFB, 0x2C, 0x8C, 0x63, 0x0B, 0x75, 0xEB, 0x85, 0x19, 0xE9, 0xEB, 0xAF, 0x50, 0x51, 0xF5, 0xFA, 0x29, 0x58, 0x91, 0x05, 0xB3, 0x25, 0x22, 0xC5, 0xD1, 0x70, 0x4F, 0x00, 0x00, 0x7F, 0x58, 0xED, 0xB4, 0x1B, 0xE8, 0x1F, 0xA5, 0x21, 0x39, 0xBC, 0x7A, 0xA8, 0x43, 0xD2, 0xA6, 0x8C, 0x80, 0x1B, 0x8E, 0x3F, 0x1F, 0xDE, 0xD3, 0x98, 0x4D, 0x11, 0xAF, 0xEB, 0x2E, 0xDB, 0xCB, 0x03, 0xDD, 0x8F, 0xB1, 0x88, 0xBB, 0xF0, 0x78, 0xD5, 0x5D, 0x4A, 0xAB, 0x8F, 0x3A, 0xE4, 0x9F, 0x41, 0x54, 0x3C, 0xC2, 0x48, 0xC1, 0xA0, 0x97, 0x94, 0x15, 0xF6, 0x1D, 0xF6, 0x67, 0x1C, 0xF3, 0x81, 0x86, 0x94, 0x9C, 0xCC, 0xDD, 0x95, 0x53, 0x21, 0x43, 0xD5, 0x4B, 0xAD, 0x74, 0x47, 0x9B, 0x16, 0xDC, 0x01, 0xE5, 0xDB, 0x54, 0xA5, 0x9C, 0xE7, 0xCC, 0xBC, 0x0B, 0x83, 0x18, 0xBE, 0x7E, 0x48, 0xED, 0x1A, 0x1E, 0x51, 0x8F, 0x0D, 0xBE, 0xF4, 0xD0, 0x2A, 0x11, 0xCF, 0x5B, 0x62, 0x32, 0x4B, 0x56, 0x27, 0xE1, 0x4F, 0xB9, 0xAD, 0x34, 0xA2, 0x00, 0xF7, 0x37, 0x9D, 0x6D, 0x10, 0x80, 0x4F, 0x00, 0x00, 0x77, 0x58, 0xED, 0xAE, 0x83, 0x19, 0x70, 0x46, 0xAD, 0xCE, 0x77, 0x0D, 0xDF, 0x30, 0xFE, 0x90, 0xAE, 0x61, 0xA2, 0x8F, 0xF1, 0xE1, 0x93, 0xBD, 0x22, 0xFA, 0x6D, 0xC6, 0x47, 0xA4, 0x5D, 0x5F, 0xB6, 0x3C, 0x91, 0xF6, 0x4A, 0x78, 0xC0, 0xF8, 0x8A, 0x7C, 0x94, 0x13, 0xC8, 0xB4, 0x09, 0x13, 0xF9, 0x8D, 0x89, 0xFF, 0x17, 0x8F, 0xAB, 0x88, 0x13, 0xE6, 0x81, 0x03, 0x04, 0x1F, 0x14, 0x0E, 0x79, 0xA1, 0xA7, 0x5C, 0x85, 0x4B, 0xDB, 0x47, 0x19, 0x01, 0xE2, 0x01, 0x1F, 0xE4, 0x63, 0x52, 0x73, 0x36, 0xF8, 0x62, 0xB9, 0x52, 0xA6, 0xDD, 0x3C, 0x88, 0xCD, 0x6A, 0x01, 0x3E, 0x55, 0xAD, 0xB4, 0x47, 0x2A, 0xC1, 0xA7, 0x4E, 0x35, 0x4D, 0x59, 0xDA, 0xB0, 0xF6, 0x61, 0xAB, 0xC9, 0xBA, 0xF8, 0x7D, 0xD0, 0x09, 0xD3, 0xAE, 0x59, 0x5C, 0x4F, 0x00, 0x00, 0x74, 0x58, 0xE2, 0x59, 0xF8, 0xBA, 0x42, 0xD5, 0x37, 0xCB, 0x2C, 0xDF, 0x04, 0xBA, 0x9E, 0x4F, 0x7E, 0xD7, 0x70, 0xD4, 0xE8, 0x35, 0x3C, 0xA4, 0xB1, 0xBF, 0x11, 0xC6, 0x20, 0x89, 0x5F, 0xB9, 0xE4, 0x2C, 0x58, 0x5A, 0x03, 0x81, 0x2B, 0xA5, 0xCC, 0x5B, 0xF8, 0x30, 0xED, 0xDC, 0x14, 0x2F, 0x62, 0x86, 0xF0, 0x5E, 0xE9, 0x7D, 0xCC, 0xF5, 0x5D, 0xD7, 0x04, 0x4C, 0x41, 0x90, 0x46, 0x4A, 0x10, 0x8F, 0xC9, 0x05, 0x09, 0x29, 0xB4, 0x71, 0x79, 0xC0, 0x98, 0xFD, 0xCF, 0xBF, 0x23, 0xD6, 0x8B, 0xD1, 0xC1, 0x2A, 0x7F, 0x52, 0xFF, 0x81, 0x05, 0x2C, 0x3D, 0x89, 0xEE, 0x7D, 0x7B, 0x55, 0xCC, 0xF3, 0xE6, 0x10, 0xAA, 0x02, 0x20, 0x14, 0xCD, 0x87, 0xCE, 0xBE, 0x0F, 0xB9, 0xFA, 0xCF, 0x48, 0xC3, 0x26, 0x6F, 0x88, 0x4F, 0x00, 0x00, 0x7E, 0x58, 0xE5, 0x3A, 0x2C, 0x21, 0xC9, 0x60, 0x5E, 0x98, 0x97, 0xE5, 0xB9, 0xA0, 0x62, 0x23, 0x02, 0xAE, 0x8C, 0xEB, 0x13, 0x0A, 0x43, 0x09, 0xC3, 0xE7, 0x6D, 0x6B, 0x02, 0x40, 0x2D, 0x5B, 0x2B, 0x70, 0x10, 0x13, 0x63, 0x27, 0xFE, 0x5D, 0x56, 0x40, 0x7C, 0xD3, 0x86, 0x38, 0x28, 0x61, 0xF5, 0xA7, 0x94, 0xD2, 0x2B, 0xED, 0x3A, 0x60, 0xE0, 0x89, 0x3D, 0xE4, 0x64, 0x86, 0xAC, 0x45, 0x71, 0xFE, 0xC6, 0x02, 0xEF, 0xB4, 0x50, 0x7C, 0x0F, 0xCF, 0xBE, 0xBF, 0x22, 0x4F, 0x67, 0x5B, 0x63, 0xEF, 0xCE, 0xF3, 0x3A, 0xB5, 0x2E, 0xFC, 0x40, 0x74, 0x30, 0xE5, 0x92, 0x68, 0xA2, 0x71, 0x2D, 0xD4, 0x9E, 0xCF, 0xBF, 0xD2, 0xA3, 0x35, 0x4D, 0xB4, 0x9D, 0xB5, 0x41, 0xD7, 0x44, 0x5D, 0xAB, 0x0C, 0x15, 0x84, 0x2A, 0xF3, 0xF8, 0xC1, 0xC3, 0x2F, 0x17, 0x5D, 0x9F, 0x61, 0x40, 0x4F, 0x00, 0x00, 0x81, 0x58, 0xEC, 0xDB, 0xB1, 0x88, 0x5A, 0xB7, 0x09, 0x06, 0xB4, 0x5A, 0x63, 0x4C, 0x10, 0x68, 0xE8, 0x0B, 0x26, 0x0F, 0x3B, 0xDD, 0xF6, 0x48, 0xD6, 0x8A, 0xF1, 0xB4, 0x22, 0xA7, 0xD8, 0x8E, 0x58, 0xF4, 0xC4, 0x8A, 0x5D, 0x73, 0x6F, 0x1B, 0xF5, 0xB7, 0x2E, 0x4C, 0xBA, 0x4A, 0xB1, 0xCB, 0xED, 0x8D, 0x5F, 0xEC, 0xE2, 0x51, 0xEB, 0xC4, 0x58, 0x2F, 0x1A, 0xD1, 0x61, 0x00, 0x57, 0xBF, 0xD7, 0xC4, 0xDF, 0x7C, 0x7A, 0xD6, 0xF9, 0x1F, 0x20, 0xFA, 0x7C, 0x15, 0x02, 0x60, 0x04, 0x59, 0xB2, 0xCD, 0xAF, 0x3E, 0x0D, 0x5A, 0x64, 0x88, 0xB9, 0x30, 0xF9, 0x42, 0xFF, 0x70, 0x46, 0x88, 0x57, 0x5E, 0xB1, 0x4B, 0x00, 0xDB, 0x70, 0x05, 0xD5, 0x34, 0xEF, 0x05, 0xB5, 0x6A, 0x83, 0x97, 0x62, 0x31, 0x88, 0x9F, 0xB5, 0x69, 0x6D, 0xC3, 0xDA, 0x3B, 0x99, 0x5A, 0x46, 0x2E, 0xEF, 0x5F, 0xD4, 0x58, 0x4F, 0x00, 0x00, 0x88, 0x58, 0xE8, 0xA8, 0xDA, 0xBF, 0xA9, 0x12, 0xAA, 0x84, 0xBD, 0xBE, 0xB4, 0x07, 0x9C, 0xBE, 0xCA, 0x2A, 0xE7, 0x57, 0x81, 0x62, 0xB0, 0xC2, 0x9D, 0x78, 0xB0, 0x84, 0xE2, 0x49, 0x29, 0x60, 0xBB, 0xBF, 0xB6, 0x33, 0x1B, 0x3D, 0x89, 0x3F, 0xB3, 0x53, 0xF0, 0xDA, 0x5A, 0x3B, 0x64, 0x54, 0xB0, 0xD7, 0xBD, 0x8F, 0x12, 0x14, 0x55, 0xF3, 0x01, 0x9F, 0xA8, 0x33, 0x9A, 0xE2, 0x0D, 0x28, 0xFE, 0xF2, 0x65, 0xF2, 0x32, 0x0C, 0x2A, 0xF9, 0x7B, 0x99, 0xC4, 0xBB, 0x39, 0xD6, 0xDA, 0x8F, 0xFB, 0x44, 0x53, 0xF7, 0x73, 0x6C, 0xEC, 0xE1, 0xD9, 0xF4, 0x9D, 0xE9, 0x28, 0x18, 0x8E, 0x5B, 0x0C, 0x5E, 0x91, 0x80, 0xA6, 0x84, 0xFA, 0x66, 0x39, 0xFD, 0x3A, 0xD5, 0xEF, 0x47, 0xBD, 0xC4, 0xDC, 0xC3, 0xE1, 0xF7, 0x08, 0x08, 0xB5, 0x59, 0xBA, 0x44, 0x93, 0x2C, 0xF0, 0x0E, 0xC6, 0x04, 0x58, 0x73, 0xE8, 0xE2, 0x8A, 0x10, 0xE8, 0xCE, 0xF6, 0x4F, 0x00, 0x00, 0x6A, 0x58, 0xED, 0xB4, 0x47, 0xC3, 0x8C, 0x44, 0x73, 0xAE, 0xE5, 0xAA, 0xBD, 0xA6, 0x02, 0xA0, 0x17, 0xA3, 0x17, 0x8D, 0x29, 0xBA, 0x3C, 0x8E, 0xB5, 0x7C, 0x5B, 0x99, 0x81, 0xAE, 0x18, 0xAF, 0x5A, 0xF8, 0x44, 0x77, 0x6D, 0x07, 0x51, 0x02, 0xFB, 0x37, 0x6A, 0xA1, 0x81, 0x20, 0x61, 0x09, 0x35, 0x9B, 0x04, 0x40, 0xCC, 0xCE, 0x21, 0x45, 0x91, 0xB1, 0x17, 0x75, 0xED, 0xBE, 0xA1, 0xE0, 0xD4, 0x2C, 0x6C, 0x87, 0x47, 0x92, 0xE4, 0xCE, 0xE1, 0x31, 0x8A, 0x59, 0x9D, 0x4B, 0xF7, 0xDD, 0xF0, 0x59, 0x32, 0xD6, 0x48, 0x8F, 0x3A, 0x7A, 0x07, 0xE9, 0x92, 0xCA, 0x7F, 0x26, 0x09, 0x24, 0xFE, 0xA7, 0x1C, 0xBF, 0x31, 0xAB, 0xAB, 0x0B, 0x52, 0xE6, 0x80, 0x4F, 0x00, 0x00, 0x87, 0x58, 0xE9, 0x7B, 0xAA, 0x51, 0xA2, 0x57, 0xF2, 0x24, 0xD6, 0x6C, 0xCE, 0x1A, 0x20, 0xDF, 0xAA, 0xD5, 0xCA, 0xF1, 0x72, 0x64, 0x65, 0x3E, 0xE2, 0xD3, 0xB1, 0x11, 0xC9, 0x31, 0x8A, 0x93, 0x9B, 0xC5, 0x74, 0x5E, 0x1B, 0x04, 0xAC, 0x89, 0xB6, 0x7F, 0x7A, 0xC3, 0x49, 0x2A, 0xEA, 0xDC, 0x04, 0x29, 0x43, 0x52, 0x2A, 0xAE, 0x21, 0x9F, 0x94, 0x0B, 0xB2, 0x1C, 0x2B, 0x0B, 0x6D, 0xFB, 0x46, 0xF2, 0x4A, 0x8E, 0xF9, 0xBE, 0xD5, 0xF0, 0x32, 0x50, 0x9E, 0x05, 0x01, 0xAC, 0x84, 0xBD, 0xE2, 0xDD, 0x92, 0x66, 0xDB, 0x35, 0x85, 0x37, 0x98, 0x8F, 0x01, 0x05, 0xDA, 0xC9, 0x24, 0x38, 0x28, 0x8F, 0xF0, 0x2A, 0x6D, 0xA8, 0x7F, 0x20, 0x07, 0xED, 0x90, 0xD1, 0x55, 0xA4, 0x34, 0xA3, 0x4C, 0x36, 0x69, 0x66, 0x37, 0x8B, 0xB0, 0xB1, 0x96, 0x6E, 0xCD, 0x77, 0xCF, 0xB3, 0xFF, 0x72, 0xFF, 0xEF, 0x3A, 0x64, 0x7A, 0xC1, 0xB9, 0x1E, 0x4F, 0x00, 0x00, 0x78, 0x58, 0xE2, 0x16, 0x91, 0x10, 0xE0, 0x36, 0x7E, 0xAD, 0x06, 0x45, 0xB6, 0x68, 0x6C, 0x77, 0x0A, 0x1E, 0xA0, 0xC2, 0x87, 0xF0, 0xE9, 0xE1, 0xB6, 0x25, 0x45, 0x85, 0xD5, 0x36, 0xEE, 0xA6, 0xAF, 0x79, 0x64, 0xF0, 0xF5, 0x9E, 0xAF, 0x4E, 0xC0, 0x29, 0x8F, 0x96, 0xB4, 0x22, 0xEC, 0xAF, 0x3D, 0xFC, 0x33, 0xE7, 0xE6, 0xBD, 0x3D, 0x32, 0x19, 0x8C, 0x03, 0x70, 0x77, 0x85, 0xD7, 0xCF, 0x27, 0xDA, 0x8B, 0x08, 0x4E, 0x6E, 0x7B, 0x2F, 0x60, 0x7F, 0xA3, 0xB1, 0x56, 0x7B, 0xF1, 0xB6, 0x61, 0x58, 0x99, 0xA7, 0x53, 0x6A, 0x08, 0xD6, 0xC5, 0x9F, 0xE1, 0xB2, 0x35, 0x88, 0xF2, 0x0B, 0x58, 0x1B, 0xE1, 0x90, 0x4C, 0x93, 0x69, 0x57, 0x0C, 0x43, 0xFE, 0xA1, 0x49, 0x85, 0x08, 0xBC, 0x2C, 0x7D, 0xE7, 0xD8, 0x62, 0x26, 0x9F, 0xCA, 0x80, 0x4F, 0x00, 0x00, 0x7A, 0x58, 0xE6, 0xEB, 0x62, 0x9C, 0x83, 0x9D, 0x0B, 0x75, 0x57, 0xC2, 0xED, 0x54, 0xC3, 0x02, 0xD3, 0x76, 0x01, 0x3B, 0x46, 0x7F, 0x3C, 0x51, 0x28, 0x31, 0x39, 0xCA, 0xB6, 0xC2, 0x45, 0xD2, 0x4A, 0xD7, 0x62, 0x85, 0xA7, 0xFD, 0xE9, 0x35, 0x49, 0x39, 0xE5, 0xF9, 0xB4, 0xAB, 0xD5, 0x63, 0xE2, 0x16, 0xFA, 0xA4, 0x06, 0x2A, 0xEA, 0xF5, 0x17, 0x49, 0x44, 0xDB, 0xCD, 0x26, 0x61, 0x3F, 0x65, 0x1A, 0xA5, 0x9F, 0xA8, 0x5D, 0x23, 0xBA, 0xF6, 0x8D, 0x4B, 0xD0, 0xA2, 0x43, 0x1B, 0x1E, 0x8B, 0xB2, 0x6E, 0xE9, 0x78, 0x05, 0x0B, 0x10, 0xA7, 0xA2, 0x64, 0x1D, 0xA9, 0x8F, 0x35, 0xC4, 0xF0, 0xDA, 0x7E, 0x5F, 0x98, 0x42, 0xCB, 0x5A, 0xF0, 0xFC, 0x70, 0xC0, 0x57, 0x55, 0x89, 0xDB, 0x1F, 0x4F, 0xD4, 0xDB, 0x55, 0x1E, 0xFB, 0xC4, 0x6B, 0xD5, 0xB0, 0x4F, 0x00, 0x00, 0x77, 0x58, 0xE9, 0xB2, 0xF0, 0x0A, 0xF0, 0x3B, 0x9C, 0x40, 0xA1, 0x56, 0xF8, 0x96, 0xE8, 0x06, 0x85, 0x84, 0x6E, 0x53, 0x87, 0x0D, 0xD2, 0x8F, 0x39, 0xB1, 0x75, 0x3D, 0x05, 0xEF, 0xC9, 0xF1, 0x12, 0x64, 0xBB, 0xE2, 0x9A, 0x5E, 0x22, 0xB9, 0x41, 0xBA, 0x43, 0x3E, 0x99, 0x9A, 0xCF, 0x8E, 0x48, 0x4D, 0xD1, 0x9A, 0x3E, 0x76, 0x7E, 0xBC, 0x7A, 0xC1, 0xBA, 0x76, 0x57, 0x6A, 0x89, 0xD2, 0x19, 0x0A, 0x94, 0x72, 0x4D, 0xB5, 0xA4, 0x88, 0xE2, 0x39, 0x7A, 0xEE, 0x9F, 0x34, 0x8B, 0xB2, 0xFB, 0xCE, 0x2F, 0xDE, 0x6C, 0xFD, 0x64, 0x17, 0x64, 0xD0, 0xC4, 0x7F, 0xDD, 0x0A, 0xF9, 0x7E, 0xD7, 0xF7, 0x2B, 0xAB, 0x5A, 0x64, 0xEB, 0x64, 0xF4, 0xA3, 0xC9, 0x25, 0x7D, 0x71, 0x02, 0x5D, 0xB4, 0x95, 0xFF, 0xE8, 0x9C, 0x63, 0xEA, 0xF0, 0x4F, 0x00, 0x00, 0x83, 0x58, 0xEC, 0xA7, 0xB4, 0x75, 0x5F, 0x3E, 0xD2, 0xA6, 0x7B, 0x5C, 0x7E, 0xBD, 0x63, 0xB7, 0xFE, 0x75, 0x47, 0x8C, 0xE2, 0x9B, 0x19, 0xE8, 0xAE, 0xF9, 0xFD, 0x43, 0x9D, 0x31, 0xB3, 0x45, 0x44, 0xAE, 0xDE, 0xA2, 0x60, 0xEE, 0x3E, 0x51, 0x08, 0x19, 0xCA, 0x07, 0xC6, 0x8F, 0xE3, 0x16, 0xA3, 0xCD, 0xB9, 0xF7, 0x96, 0x70, 0x34, 0xE6, 0x43, 0xDF, 0x1B, 0xBE, 0xB4, 0x73, 0xDB, 0x98, 0x65, 0x1B, 0x23, 0x1C, 0xBB, 0xE1, 0x68, 0xEE, 0x26, 0x7C, 0xA5, 0xF4, 0x36, 0xFF, 0xD8, 0xAC, 0x2A, 0xAB, 0x7F, 0x02, 0xD0, 0xF3, 0x1A, 0x4A, 0xAD, 0x71, 0x4A, 0xEC, 0xE7, 0x0B, 0xBE, 0x7C, 0xE1, 0x90, 0x1D, 0x9A, 0xFE, 0x64, 0x6A, 0xED, 0xF3, 0x71, 0x7E, 0x88, 0x18, 0xFA, 0x4C, 0x77, 0xD2, 0xC0, 0x26, 0x94, 0x6B, 0x72, 0x19, 0x9F, 0x1D, 0xC8, 0x72, 0xD5, 0x03, 0x77, 0x0B, 0x72, 0xBE, 0x4C, 0xEC, 0x30, 0x4F, 0x00, 0x00, 0x76, 0x58, 0xED, 0x90, 0xAF, 0x60, 0x2D, 0x97, 0x25, 0xDD, 0xA9, 0x22, 0x29, 0x0E, 0x1F, 0x4D, 0x1D, 0x21, 0x00, 0xF0, 0xAF, 0x1F, 0xB2, 0xA9, 0x4C, 0x45, 0x5A, 0xBD, 0x96, 0x06, 0xE0, 0xEC, 0xD0, 0x7B, 0xE2, 0x53, 0x47, 0x96, 0x8C, 0x33, 0x33, 0x95, 0xD6, 0x8E, 0x75, 0x92, 0x85, 0x8D, 0x3C, 0x87, 0x0D, 0x84, 0xDA, 0xF3, 0xCF, 0x79, 0xFF, 0x7D, 0x0E, 0x29, 0x3D, 0x74, 0xB3, 0xC7, 0x15, 0x32, 0x32, 0x37, 0xAE, 0x07, 0x8D, 0x4E, 0xD8, 0xDE, 0xA0, 0xE8, 0x8C, 0x3D, 0xB0, 0x17, 0xE4, 0x66, 0xD3, 0xD4, 0x2E, 0x40, 0xF0, 0xED, 0xCE, 0xF3, 0xB7, 0x34, 0x7F, 0x05, 0x62, 0x8E, 0x80, 0xDB, 0xB0, 0xD3, 0xD9, 0x45, 0x4A, 0xE0, 0xB7, 0x38, 0x2F, 0x33, 0x44, 0xBA, 0xA0, 0x42, 0x15, 0x2D, 0xC7, 0x98, 0xD8, 0x2E, 0xE8, 0x4F, 0x00, 0x00, 0x32, 0x58, 0x08, 0x51, 0xDD, 0xD7, 0x99, 0x4F, 0x06, 0x3A, 0x5E, 0x5D, 0xB4, 0x0A, 0x56, 0xF5, 0x5B, 0xD0, 0x42, 0x65, 0x86, 0xE4, 0x21, 0x91, 0x9D, 0x00, 0x98, 0xBB, 0xA0, 0x0C, 0x10, 0x29, 0x89, 0x32, 0x59, 0x25, 0x0C, 0x75, 0xC0, 0xDF, 0xD7, 0x4A, 0x63, 0x55, 0x44, 0x2F, 0x35, 0x84, 0x37, 0xD2, 0xE0, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20
};

#endif // AUDIO_DATA_NEEDCONFIG_H