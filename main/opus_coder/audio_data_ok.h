#ifndef AUDIO_DATA_OK_H
#define AUDIO_DATA_OK_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfoOK[] = {0, 24, 49, 102, 240, 379, 510, 642, 735, 891, 1035, 1175, 1239};
const uint8_t g_audioDataOK[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x31, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x0A, 0x68, 0x18, 0xA4, 0x95, 0x5D, 0xD9, 0xED, 0xFA, 0x84, 0x23, 0xA7, 0x97, 0x31, 0x6E, 0x00, 0xBD, 0x80, 0x2A, 0xA6, 0x09, 0x32, 0xF2, 0xB6, 0x8C, 0xE0, 0x55, 0xEA, 0x7F, 0x99, 0x3B, 0x5D, 0xE1, 0x5F, 0x3A, 0x79, 0x89, 0x38, 0xE5, 0x33, 0x4D, 0x4F, 0x00, 0x00, 0x86, 0x58, 0xE3, 0x4F, 0x90, 0x1E, 0x5A, 0x00, 0xD5, 0xA1, 0xEC, 0x67, 0xB9, 0xBF, 0x11, 0xA1, 0x6A, 0xC8, 0xFE, 0x90, 0xBF, 0x31, 0x42, 0x5D, 0xFA, 0x91, 0x93, 0xC9, 0x51, 0x94, 0xD5, 0x9E, 0x16, 0x5E, 0x8D, 0x07, 0x7E, 0xB4, 0x2E, 0xAC, 0xFD, 0x94, 0xC9, 0x39, 0xF1, 0x11, 0xA6, 0x78, 0x12, 0x7A, 0xC8, 0x06, 0xFE, 0x9A, 0xC5, 0x9F, 0x79, 0x04, 0x01, 0x92, 0xBD, 0xEC, 0x7D, 0x95, 0xB8, 0x9A, 0xD4, 0xF6, 0x05, 0xB2, 0xC0, 0x8D, 0xB1, 0x86, 0x7F, 0xE6, 0x4A, 0x1D, 0x79, 0xB1, 0x8B, 0xA9, 0x2A, 0x1D, 0x3E, 0xD4, 0xED, 0x99, 0x9A, 0x82, 0x73, 0xF7, 0xEE, 0xB0, 0x38, 0x4D, 0xB6, 0xED, 0x9A, 0xF5, 0x82, 0x37, 0xEB, 0xCF, 0xDD, 0xBD, 0x20, 0xFE, 0x43, 0x6E, 0x58, 0xEF, 0xC8, 0x2A, 0x16, 0x50, 0x18, 0x9A, 0xD3, 0x99, 0x86, 0x14, 0x4F, 0x0E, 0x3E, 0x72, 0x47, 0x81, 0x11, 0x01, 0xD4, 0x50, 0xA7, 0x2E, 0x80, 0x4F, 0x00, 0x00, 0x87, 0x58, 0xEC, 0xEE, 0x0D, 0x3E, 0xF2, 0x36, 0xF3, 0xE2, 0x32, 0xF8, 0xB0, 0x0D, 0x85, 0x74, 0x76, 0x37, 0x8D, 0x6A, 0x08, 0xB2, 0xC0, 0x44, 0x63, 0x4A, 0x0B, 0x2B, 0x9E, 0xC9, 0xF8, 0x9F, 0xCE, 0xDA, 0xE0, 0x84, 0x8F, 0xF6, 0x67, 0x06, 0xC1, 0x1D, 0x15, 0xC2, 0x78, 0xD8, 0x64, 0xCC, 0x7A, 0x2F, 0x3A, 0x6B, 0x2C, 0x20, 0x62, 0xD8, 0x9D, 0x75, 0x8C, 0x91, 0xC4, 0x98, 0x7E, 0x40, 0x59, 0xAA, 0x05, 0x3E, 0xB8, 0x3E, 0x0F, 0xB9, 0x66, 0xEC, 0x86, 0x6F, 0xE4, 0xA8, 0x06, 0xD4, 0xDB, 0xC8, 0xE0, 0x77, 0x1F, 0xF5, 0x53, 0x4A, 0x5D, 0xAF, 0x9C, 0x21, 0x8A, 0xAA, 0xC1, 0x9C, 0xFB, 0x47, 0xEA, 0x40, 0x97, 0xB7, 0x5B, 0x44, 0xF4, 0xD0, 0xF3, 0xD2, 0xDE, 0x6D, 0x9D, 0xFF, 0x93, 0xF2, 0x2E, 0x83, 0x7C, 0xF6, 0x6A, 0x32, 0x93, 0x94, 0x1D, 0xCE, 0x69, 0xDA, 0x0E, 0x7F, 0x48, 0x24, 0xC0, 0x79, 0xFD, 0x03, 0xEF, 0xAB, 0x4F, 0x00, 0x00, 0x7F, 0x58, 0xEC, 0x5F, 0x6E, 0xCF, 0x37, 0x1A, 0xAD, 0xFC, 0x12, 0x17, 0xC8, 0x9F, 0x27, 0xEB, 0xE5, 0x64, 0xAA, 0xFB, 0xFE, 0xD4, 0x2F, 0xD9, 0x36, 0x36, 0x54, 0xBB, 0x0C, 0x98, 0xF2, 0xA9, 0x41, 0x4C, 0x64, 0xB6, 0xFB, 0x4E, 0x60, 0x45, 0x4A, 0xEF, 0x59, 0x87, 0x2C, 0x4A, 0x4D, 0xB3, 0xAF, 0xE3, 0x55, 0xA4, 0xFF, 0xE1, 0x73, 0xAC, 0x8C, 0x8C, 0xD9, 0xE3, 0x53, 0xA0, 0x91, 0xE1, 0x09, 0x6F, 0x33, 0x7C, 0x44, 0xE9, 0x4D, 0x69, 0xA7, 0x96, 0x90, 0x53, 0x34, 0x28, 0xE2, 0x05, 0x30, 0xA4, 0x70, 0xBF, 0x7D, 0x18, 0xE2, 0x17, 0xDC, 0x68, 0xC3, 0xAC, 0xEB, 0xE8, 0xA7, 0xD7, 0x87, 0x69, 0x5F, 0xF6, 0xA5, 0x61, 0xE6, 0xB8, 0x70, 0xA4, 0xAE, 0x87, 0x46, 0xAB, 0x3A, 0xEB, 0x14, 0xC9, 0x16, 0x9C, 0x7C, 0x75, 0x59, 0xC1, 0xA7, 0x0D, 0xA3, 0x42, 0xC5, 0xFD, 0x54, 0x40, 0x4F, 0x00, 0x00, 0x80, 0x58, 0xE8, 0xEC, 0xE2, 0xB3, 0x25, 0x00, 0x39, 0xF3, 0x69, 0x45, 0xCC, 0x31, 0x6F, 0x5D, 0xF3, 0x37, 0x9E, 0x63, 0xA4, 0xEA, 0x6B, 0xD0, 0x60, 0xB6, 0x6B, 0xFF, 0xD7, 0x97, 0x0F, 0xBC, 0xC2, 0xB1, 0xED, 0x7C, 0x70, 0x4E, 0x14, 0x93, 0xEE, 0x7F, 0xCA, 0xFF, 0x33, 0xFD, 0x1C, 0x9E, 0xD2, 0xEF, 0x1D, 0xCD, 0x3B, 0x9B, 0x55, 0x64, 0x1B, 0x54, 0x0D, 0x11, 0x80, 0xD2, 0xFA, 0xB5, 0x2A, 0x15, 0x35, 0xF2, 0x92, 0xD4, 0xD7, 0xC8, 0x58, 0x02, 0x0F, 0xBC, 0xB1, 0xB1, 0xD4, 0x16, 0x69, 0x7E, 0x57, 0x62, 0xD5, 0xFA, 0xC5, 0x51, 0x42, 0x06, 0x23, 0xD6, 0x76, 0x5F, 0x29, 0x8A, 0x10, 0x96, 0xC1, 0x57, 0x1E, 0x94, 0xB7, 0x83, 0x4B, 0x9A, 0xEE, 0x87, 0x4F, 0x67, 0x04, 0x40, 0xBF, 0x68, 0xD4, 0xDB, 0x6E, 0x30, 0xC4, 0x03, 0x62, 0xC5, 0xA9, 0xA2, 0x92, 0xA8, 0xC0, 0x5E, 0x78, 0x4F, 0x00, 0x00, 0x59, 0x58, 0x0A, 0x4F, 0xD6, 0xC1, 0xCD, 0x1C, 0x30, 0x7E, 0xE9, 0x96, 0x5B, 0x54, 0x9E, 0x18, 0xA0, 0x51, 0xC2, 0x46, 0x21, 0x0C, 0x37, 0x71, 0xED, 0x03, 0x47, 0x67, 0x21, 0x5A, 0x5E, 0xB7, 0xA9, 0x2C, 0x87, 0x63, 0x23, 0x7C, 0xAC, 0x52, 0xD8, 0x5F, 0x82, 0x4F, 0x49, 0x78, 0xDE, 0xD2, 0x8B, 0x9A, 0x54, 0x91, 0x7F, 0x78, 0x1D, 0xEA, 0x23, 0xFB, 0x4E, 0x02, 0x71, 0xA4, 0xE7, 0xF4, 0x1D, 0xCE, 0x33, 0xC8, 0x55, 0x73, 0x15, 0xF0, 0x72, 0xEB, 0xA4, 0xF0, 0x00, 0x88, 0x6E, 0xD8, 0xF2, 0xBB, 0x20, 0x93, 0xE6, 0x0D, 0xE9, 0xD4, 0x72, 0x30, 0x4F, 0x00, 0x00, 0x98, 0x58, 0xE0, 0x1C, 0x1C, 0x75, 0x47, 0x5F, 0xEC, 0xC4, 0xB5, 0x4E, 0x9E, 0xFB, 0xDE, 0xA0, 0xBF, 0x6E, 0x36, 0x72, 0xB3, 0x5E, 0x6D, 0xE1, 0x1F, 0xEC, 0xF7, 0x2C, 0x0D, 0xA6, 0xD6, 0x45, 0xE5, 0xF9, 0xB3, 0x98, 0x23, 0x52, 0x91, 0xF2, 0x72, 0xFA, 0xCD, 0xF0, 0xF7, 0x48, 0xEC, 0x02, 0x48, 0x46, 0xCA, 0xEE, 0x30, 0x16, 0x3C, 0x95, 0x95, 0x98, 0x84, 0x9A, 0x88, 0xFC, 0xBB, 0x5E, 0x2B, 0xDD, 0xDB, 0xA2, 0xD2, 0xC3, 0xE0, 0x40, 0xF8, 0xD5, 0x22, 0xAA, 0x57, 0x59, 0x2E, 0x42, 0xBC, 0xFD, 0x01, 0x07, 0x22, 0xAF, 0x95, 0xA4, 0xEC, 0xC9, 0xFD, 0x78, 0x94, 0x55, 0x1E, 0x4D, 0x47, 0xC2, 0x5F, 0xDB, 0x73, 0xE1, 0x60, 0x6E, 0xC9, 0x68, 0xD8, 0x4C, 0xA8, 0x96, 0x7F, 0x01, 0x92, 0x45, 0x4D, 0x35, 0x8F, 0xED, 0x15, 0x47, 0x3C, 0x1E, 0x31, 0xED, 0xDA, 0x9E, 0x25, 0xCF, 0x90, 0xA4, 0xFA, 0xAE, 0x94, 0xF4, 0x4A, 0x86, 0x5D, 0xA7, 0x1E, 0xB1, 0x2C, 0xCF, 0xB9, 0x0B, 0x83, 0xD1, 0x4D, 0xAA, 0x66, 0x36, 0xA0, 0x95, 0x8F, 0x4F, 0x00, 0x00, 0x8C, 0x58, 0xEE, 0x30, 0x45, 0xAE, 0x12, 0x7A, 0x54, 0xF7, 0xAE, 0xAC, 0x29, 0x8F, 0xE9, 0x67, 0xC8, 0x4C, 0x4D, 0x42, 0x2C, 0xB9, 0x8E, 0xD8, 0xC6, 0x85, 0xDC, 0x4D, 0x53, 0x4E, 0x1F, 0x7F, 0xF6, 0x69, 0x73, 0x8C, 0x68, 0xB8, 0x48, 0x06, 0x4A, 0xF2, 0xE3, 0xE6, 0x2B, 0x54, 0x28, 0xA8, 0x8F, 0x15, 0xC4, 0x27, 0x46, 0xAC, 0x56, 0x35, 0x71, 0x18, 0xDF, 0x3E, 0xAA, 0xC0, 0x0E, 0xF5, 0x53, 0x64, 0x3B, 0xEE, 0x73, 0xCB, 0x33, 0x22, 0x72, 0x3F, 0x10, 0x1D, 0x2F, 0x57, 0x97, 0xBC, 0x8E, 0x62, 0x45, 0x5B, 0x6A, 0x21, 0xAF, 0x50, 0x3F, 0x82, 0x35, 0x22, 0x45, 0xD7, 0xB3, 0xF6, 0xE7, 0x95, 0xCD, 0xA5, 0x25, 0x83, 0x1D, 0x61, 0x6A, 0xBF, 0xB9, 0x71, 0xC1, 0xC9, 0x37, 0xB9, 0x9C, 0x1C, 0xB0, 0x5D, 0xC8, 0x06, 0x27, 0xDE, 0xB2, 0x68, 0x80, 0x8F, 0x08, 0x2E, 0x9D, 0xB0, 0x38, 0x2E, 0x92, 0x92, 0x04, 0x7F, 0x44, 0xD5, 0xE4, 0x8C, 0x81, 0x12, 0x20, 0x4F, 0x00, 0x00, 0x88, 0x58, 0xED, 0x6A, 0xB4, 0x7B, 0x51, 0xD7, 0xE7, 0xEC, 0xF3, 0x15, 0x89, 0x36, 0x47, 0x1B, 0xB7, 0x7F, 0x58, 0x53, 0x2B, 0x02, 0x20, 0x19, 0x15, 0xA9, 0x7B, 0xE2, 0xD6, 0xAA, 0x77, 0x2C, 0x6A, 0xE1, 0xD6, 0xDB, 0x18, 0xAE, 0xD9, 0xF5, 0x1D, 0x8D, 0xE2, 0x52, 0x75, 0x03, 0xCE, 0xB9, 0xA8, 0x04, 0xBD, 0x63, 0xED, 0xC7, 0x5A, 0x12, 0xCA, 0xB2, 0x94, 0x2A, 0x1B, 0xB9, 0xB7, 0xC4, 0xC2, 0xBD, 0x7C, 0x69, 0x11, 0x09, 0x77, 0x78, 0x53, 0x35, 0x41, 0x03, 0x1C, 0xD3, 0xCE, 0xBF, 0x8B, 0x01, 0x62, 0x69, 0x7D, 0x1D, 0xE0, 0x55, 0xDC, 0xFD, 0xCE, 0x81, 0xAB, 0xC4, 0xB4, 0x7F, 0xE3, 0xB2, 0x28, 0xB4, 0x2E, 0xB0, 0xD0, 0xD6, 0x53, 0xCA, 0x74, 0xBA, 0x5C, 0xFA, 0x59, 0x44, 0xF2, 0x97, 0x3F, 0xD6, 0x93, 0xBC, 0x71, 0x57, 0x1B, 0xF6, 0x8C, 0x34, 0xA1, 0x61, 0xB5, 0x6D, 0x38, 0xB0, 0x12, 0x11, 0x66, 0x98, 0xB4, 0x94, 0xD0, 0x4F, 0x00, 0x00, 0x3C, 0x58, 0x01, 0x05, 0xF4, 0x94, 0x0A, 0xB6, 0x37, 0x63, 0x5E, 0xEA, 0x19, 0x41, 0x09, 0x2A, 0x7F, 0x77, 0x3B, 0xBD, 0xE7, 0x34, 0xAF, 0x21, 0x80, 0xA1, 0x0E, 0xB5, 0x6F, 0x97, 0xCE, 0xD3, 0x53, 0xC9, 0x59, 0x04, 0x04, 0x93, 0xF6, 0x0E, 0x22, 0x90, 0x71, 0xE1, 0xE1, 0x01, 0x57, 0xE8, 0x0F, 0xA6, 0x52, 0x4A, 0xD5, 0x67, 0x12, 0x76, 0x06, 0xAD, 0xEC, 0x28, 0x90, 0x4F, 0x00, 0x00, 0x16, 0x58, 0x01, 0xF2, 0x5E, 0x71, 0x51, 0x7B, 0xB9, 0xEA, 0xBA, 0xB9, 0x6A, 0x81, 0x8C, 0x63, 0x9E, 0xA6, 0xC3, 0x79, 0x97, 0x3F, 0x28
};

#endif // AUDIO_DATA_OK_H