/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_wifi_ap.c
 * @description: WIFI的AP模式
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <string.h>
#include <freertos/FreeRTOS.h>
#include <freertos/event_groups.h>
#include <esp_err.h>
#include <esp_event.h>
#include <esp_wifi.h>
#include <esp_log.h>
#include <esp_mac.h>
#include <esp_netif.h>
#include <esp_timer.h>
#include <lwip/ip_addr.h>
#include <nvs.h>
#include <nvs_flash.h>
#include <cJSON.h>
#include <esp_smartconfig.h>
#include "sk_wifi_ap.h"

#define TAG "SkWifiAp"

#define WIFI_CONNECTED_BIT BIT0
#define WIFI_FAIL_BIT      BIT1

typedef struct {
    char ssid[32];
    char ssidPrefix[8];
    EventGroupHandle_t eventGroup;
    esp_timer_handle_t scanTimer;
    esp_netif_t* apNetif;
    esp_event_handler_instance_t instanceAnyId;
} SkWifiAp;

SkWifiAp g_wifiAp;

SkApHandler SkApInit() {
    SkWifiAp *ctrl = &g_wifiAp;
    ctrl->eventGroup = xEventGroupCreate();
    strcpy(ctrl->ssidPrefix, "FTSK");
    return (SkApHandler)ctrl;
}

void SkApDeinit(SkApHandler handler) {
    SkWifiAp *ctrl = (SkWifiAp *)handler;

    if (ctrl->scanTimer) {
        esp_timer_stop(ctrl->scanTimer);
        esp_timer_delete(ctrl->scanTimer);
    }
    vEventGroupDelete(ctrl->eventGroup);
    // Unregister event handlers if they were registered
    if (ctrl->instanceAnyId) {
        esp_event_handler_instance_unregister(WIFI_EVENT, ESP_EVENT_ANY_ID, ctrl->instanceAnyId);
    }

    return;
}

void SkScanTimerCallback(void* arg) {
    esp_wifi_scan_start(NULL, false);
}

void SkApWifiEventHandler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data) {
    if (event_id == WIFI_EVENT_AP_STACONNECTED) {
        wifi_event_ap_staconnected_t* event = (wifi_event_ap_staconnected_t*) event_data;
        ESP_LOGI(TAG, "Station " MACSTR " joined, AID=%d", MAC2STR(event->mac), event->aid);
    } else if (event_id == WIFI_EVENT_AP_STADISCONNECTED) {
        wifi_event_ap_stadisconnected_t* event = (wifi_event_ap_stadisconnected_t*) event_data;
        ESP_LOGI(TAG, "Station " MACSTR " left, AID=%d", MAC2STR(event->mac), event->aid);
    }
}

char* SkApGetSsid(SkApHandler handler) {
    SkWifiAp *ctrl = (SkWifiAp *)handler;
    uint8_t mac[6];

    // Get MAC and use it to generate a unique SSID
    ESP_ERROR_CHECK(esp_read_mac(mac, ESP_MAC_WIFI_SOFTAP));
    snprintf(ctrl->ssid, sizeof(ctrl->ssid), "%s-%02x%02x", ctrl->ssidPrefix, mac[4], mac[5]);

    return ctrl->ssid;
}

void SkStartAccessPoint(SkApHandler handler) {
    SkWifiAp *ctrl = (SkWifiAp *)handler;
    char *ssid = SkApGetSsid(ctrl);

    // Initialize the TCP/IP stack
    ESP_ERROR_CHECK(esp_netif_init());

    // Create the default event loop
    ctrl->apNetif = esp_netif_create_default_wifi_ap();

    // Set the router IP address to ***********
    esp_netif_ip_info_t ip_info;
    IP4_ADDR(&ip_info.ip, 192, 168, 4, 1);
    IP4_ADDR(&ip_info.gw, 192, 168, 4, 1);
    IP4_ADDR(&ip_info.netmask, 255, 255, 255, 0);
    esp_netif_dhcps_stop(ctrl->apNetif);
    esp_netif_set_ip_info(ctrl->apNetif, &ip_info);
    esp_netif_dhcps_start(ctrl->apNetif);

    // Initialize the WiFi stack in Access Point mode
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));

    // Set the WiFi configuration
    wifi_config_t wifi_config = {};
    strcpy((char *)wifi_config.ap.ssid, ssid);
    strcpy((char *)wifi_config.ap.password, "66666666");
    wifi_config.ap.beacon_interval = 100;
    wifi_config.ap.ssid_len = strlen(ssid);
    wifi_config.ap.max_connection = 4;
    wifi_config.ap.authmode = WIFI_AUTH_WPA2_PSK;

    // Start the WiFi Access Point
    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_APSTA));
    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_AP, &wifi_config));
    ESP_ERROR_CHECK(esp_wifi_set_ps(WIFI_PS_NONE));
    ESP_ERROR_CHECK(esp_wifi_start());

    ESP_LOGI(TAG, "Access Point started with SSID %s", ssid);
}

void SkApStart(SkApHandler handler) {   
    SkWifiAp *ctrl = (SkWifiAp *)handler;
    esp_err_t ret;

    // Register event handlers
    ret = esp_event_handler_instance_register(WIFI_EVENT, WIFI_EVENT_AP_STACONNECTED, 
        &SkApWifiEventHandler, ctrl, &ctrl->instanceAnyId);
    ESP_ERROR_CHECK(ret);
    ret = esp_event_handler_instance_register(WIFI_EVENT, WIFI_EVENT_AP_STADISCONNECTED, 
        &SkApWifiEventHandler, ctrl, &ctrl->instanceAnyId);
    ESP_ERROR_CHECK(ret);
    SkStartAccessPoint(ctrl);

    // Start scan immediately
    esp_wifi_scan_start(NULL, false);
    // Setup periodic WiFi scan timer
    esp_timer_create_args_t timerArgs = {
        .callback = SkScanTimerCallback,
        .arg = ctrl,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "wifi_scan_timer",
        .skip_unhandled_events = true
    };
    ESP_ERROR_CHECK(esp_timer_create(&timerArgs, &ctrl->scanTimer));
    // Start scanning every 10 seconds
    ESP_ERROR_CHECK(esp_timer_start_periodic(ctrl->scanTimer, 10000000));
}

void SkApStop(SkApHandler handler) {
    SkWifiAp *ctrl = (SkWifiAp *)handler;

    // 停止定时器
    if (ctrl->scanTimer) {
        esp_timer_stop(ctrl->scanTimer);
        esp_timer_delete(ctrl->scanTimer);
        ctrl->scanTimer = NULL;
    }

    // 释放网络接口资源
    if (ctrl->apNetif) {
        esp_netif_destroy(ctrl->apNetif);
        ctrl->apNetif = NULL;
    }

    // 停止WiFi并重置模式
    esp_wifi_stop();
    esp_wifi_deinit();
    esp_wifi_set_mode(WIFI_MODE_NULL);

    // 注销事件处理器
    if (ctrl->instanceAnyId) {
        esp_event_handler_instance_unregister(WIFI_EVENT, ESP_EVENT_ANY_ID, ctrl->instanceAnyId);
        ctrl->instanceAnyId = NULL;
    }

    ESP_LOGI(TAG, "Wifi configuration AP stopped");
}
