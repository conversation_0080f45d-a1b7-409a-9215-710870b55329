/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_http_downloader.c
 * @description: HTTP文件下载到PSRAM模块实现
 * @author: <PERSON>
 * @date: 2025-01-20
 */
#include <string.h>
#include <stdlib.h>
#include "esp_log.h"
#include "esp_http_client.h"
#include "esp_heap_caps.h"
#include "freertos/FreeRTOS.h"
#include "freertos/ringbuf.h"
#include "sk_http_downloader.h"
#include "sk_opus_dec.h"
#include "sk_log.h"

static const char *TAG = "SkHttpDownloader";

#define HTTP_BUFFER_SIZE        4096
#define HTTP_TIMEOUT_MS         30000
#define RINGBUF_SIZE            (2 * 1024 * 1024)  

sk_err_t SkHttpDownloadFile(const char *url, SkHttpDownloadData *downloadData) {
    if (url == NULL || downloadData == NULL) {
        SK_LOGE(TAG, "Invalid parameters");
        return SK_RET_INVALID_PARAM;
    }

    memset(downloadData, 0, sizeof(SkHttpDownloadData));
    
    SK_LOGI(TAG, "Starting download: %s", url);
    
    // 配置HTTP客户端
    esp_http_client_config_t config = {
        .url = url,
        .timeout_ms = HTTP_TIMEOUT_MS,
    };
    
    esp_http_client_handle_t client = esp_http_client_init(&config);
    if (client == NULL) {
        SK_LOGE(TAG, "Failed to init HTTP client");
        return SK_RET_FAIL;
    }
    
    // 开始请求
    esp_err_t err = esp_http_client_open(client, 0);
    if (err != ESP_OK) {
        SK_LOGE(TAG, "Failed to open HTTP connection: %s", esp_err_to_name(err));
        esp_http_client_cleanup(client);
        return SK_RET_FAIL;
    }
    
    // 获取文件大小
    int content_length = esp_http_client_fetch_headers(client);
    if (content_length <= 0 || content_length > RINGBUF_SIZE) {
        SK_LOGE(TAG, "Invalid file size: %d", content_length);
        esp_http_client_close(client);
        esp_http_client_cleanup(client);
        return SK_RET_FAIL;
    }
    
    SK_LOGI(TAG, "File size: %d bytes", content_length);
    
    // 在PSRAM中分配环形缓冲区存储空间（包含数据空间和结构体空间）
    size_t totalSize = RINGBUF_SIZE + sizeof(StaticRingbuffer_t);
    downloadData->storage = heap_caps_malloc(totalSize, MALLOC_CAP_SPIRAM);
    if (downloadData->storage == NULL) {
        SK_LOGE(TAG, "Failed to allocate PSRAM: %zu bytes", totalSize);
        esp_http_client_close(client);
        esp_http_client_cleanup(client);
        return SK_RET_NO_MEMORY;
    }

    // 结构体放在存储空间的末尾
    StaticRingbuffer_t *structure = (StaticRingbuffer_t*)(downloadData->storage + RINGBUF_SIZE);

    // 创建静态环形缓冲区
    downloadData->ringbuf = xRingbufferCreateStatic(RINGBUF_SIZE, RINGBUF_TYPE_BYTEBUF,
                                                    downloadData->storage, structure);
    if (downloadData->ringbuf == NULL) {
        SK_LOGE(TAG, "Failed to create static ringbuffer");
        heap_caps_free(downloadData->storage);
        esp_http_client_close(client);
        esp_http_client_cleanup(client);
        return SK_RET_FAIL;
    }
    
    SK_LOGI(TAG, "Created ringbuffer in PSRAM: %d bytes", RINGBUF_SIZE);
    
    // 分配HTTP读取缓冲区
    uint8_t *buffer = malloc(HTTP_BUFFER_SIZE);
    if (buffer == NULL) {
        SK_LOGE(TAG, "Failed to allocate HTTP buffer");
        vRingbufferDelete(downloadData->ringbuf);
        heap_caps_free(downloadData->storage);
        esp_http_client_close(client);
        esp_http_client_cleanup(client);
        return SK_RET_NO_MEMORY;
    }
    
    // 下载数据到环形缓冲区
    size_t total_read = 0;
    while (total_read < content_length) {
        int data_read = esp_http_client_read(client, (char*)buffer, HTTP_BUFFER_SIZE);
        if (data_read < 0) {
            SK_LOGE(TAG, "HTTP read error: %d", data_read);
            free(buffer);
            vRingbufferDelete(downloadData->ringbuf);
            heap_caps_free(downloadData->storage);
            esp_http_client_close(client);
            esp_http_client_cleanup(client);
            return SK_RET_FAIL;
        }
        
        if (data_read == 0) {
            break;
        }
        
        if (total_read + data_read > content_length) {
            data_read = content_length - total_read;
        }
        
        // 写入环形缓冲区
        if (xRingbufferSend(downloadData->ringbuf, buffer, data_read, portMAX_DELAY) != pdTRUE) {
            SK_LOGE(TAG, "Failed to write to ringbuffer");
            free(buffer);
            vRingbufferDelete(downloadData->ringbuf);
            heap_caps_free(downloadData->storage);
            esp_http_client_close(client);
            esp_http_client_cleanup(client);
            return SK_RET_FAIL;
        }
        
        total_read += data_read;
        
        // 显示进度
        if (total_read % (content_length / 10) == 0 || total_read == content_length) {
            SK_LOGI(TAG, "Download progress: %zu/%d bytes (%d%%)", 
                   total_read, content_length, (int)(total_read * 100 / content_length));
        }
    }
    
    free(buffer);
    esp_http_client_close(client);
    esp_http_client_cleanup(client);
    
    if (total_read != content_length) {
        SK_LOGE(TAG, "Download incomplete: %zu/%d bytes", total_read, content_length);
        vRingbufferDelete(downloadData->ringbuf);
        heap_caps_free(downloadData->storage);
        return SK_RET_FAIL;
    }

    SK_LOGI(TAG, "Download completed: %zu bytes", total_read);
    downloadData->totalSize = total_read;
    
    return SK_RET_SUCCESS;
}

void SkHttpDownloadFree(SkHttpDownloadData *downloadData) {
    if (downloadData == NULL) {
        return;
    }

    if (downloadData->ringbuf != NULL) {
        vRingbufferDelete(downloadData->ringbuf);
        downloadData->ringbuf = NULL;
    }

    if (downloadData->storage != NULL) {
        heap_caps_free(downloadData->storage);
        downloadData->storage = NULL;
    }

    downloadData->totalSize = 0;
    SK_LOGI(TAG, "Download data freed from PSRAM");
}

// ==================== OGG解析功能 ====================

static uint8_t *g_audioData = NULL;
static size_t g_audioSize = 0;
static size_t g_currentPos = 0;
static bool g_initialized = false;

/**
 * @brief 初始化OGG解析器，从环形缓冲区获取所有数据
 */
static sk_err_t InitOggParser(RingbufHandle_t ringbuf) {
    if (g_initialized) {
        return SK_RET_SUCCESS;
    }

    // 从环形缓冲区获取所有数据
    size_t itemSize;
    uint8_t *item = xRingbufferReceive(ringbuf, &itemSize, pdMS_TO_TICKS(1000));
    if (item == NULL) {
        return SK_RET_TIMEOUT;
    }

    // 分配内存并复制数据
    g_audioData = malloc(itemSize);
    if (g_audioData == NULL) {
        vRingbufferReturnItem(ringbuf, item);
        return SK_RET_NO_MEMORY;
    }

    memcpy(g_audioData, item, itemSize);
    g_audioSize = itemSize;
    g_currentPos = 0;
    g_initialized = true;

    vRingbufferReturnItem(ringbuf, item);

    SK_LOGI(TAG, "OGG parser initialized with %zu bytes", g_audioSize);
    return SK_RET_SUCCESS;
}

/**
 * @brief 解析OPUS头部信息
 */
sk_err_t SkOggParseOpusHeader(RingbufHandle_t ringbuf, SkOpusHeader *header) {
    if (ringbuf == NULL || header == NULL) {
        return SK_RET_INVALID_PARAM;
    }

    // 初始化解析器
    sk_err_t ret = InitOggParser(ringbuf);
    if (ret != SK_RET_SUCCESS) {
        return ret;
    }

    // 验证OGG格式
    if (g_audioSize < 46 || memcmp(g_audioData, "OggS", 4) != 0) {
        return SK_RET_FAIL;
    }

    // 找到OPUS头部数据（跳过OGG页面头部和段表）
    uint8_t segments = g_audioData[26];
    uint8_t *pageData = g_audioData + 27 + segments;

    // 验证OPUS头部
    if (memcmp(pageData, "OpusHead", 8) != 0) {
        return SK_RET_FAIL;
    }

    // 解析OPUS参数
    header->version = pageData[8];
    header->channels = pageData[9];
    header->preSkip = pageData[10] | (pageData[11] << 8);
    header->sampleRate = pageData[12] | (pageData[13] << 8) | (pageData[14] << 16) | (pageData[15] << 24);
    header->outputGain = pageData[16] | (pageData[17] << 8);

    SK_LOGI(TAG, "OPUS: %dch, %luHz, preSkip:%d",
           header->channels, header->sampleRate, header->preSkip);

    return SK_RET_SUCCESS;
}

/**
 * @brief 从缓存数据获取下一个OPUS数据包
 */
sk_err_t SkOggGetNextOpusPacket(RingbufHandle_t ringbuf, SkOpusPacket *packet) {
    if (ringbuf == NULL || packet == NULL) {
        return SK_RET_INVALID_PARAM;
    }

    // 确保解析器已初始化
    if (!g_initialized) {
        sk_err_t ret = InitOggParser(ringbuf);
        if (ret != SK_RET_SUCCESS) {
            return ret;
        }
    }

    // 第一次调用时，跳过头部和注释页面
    static bool firstCall = true;
    if (firstCall) {
        g_currentPos = 0;

        // 跳过第一个页面（OPUS头部）
        if (memcmp(g_audioData, "OggS", 4) == 0) {
            uint8_t segments1 = g_audioData[26];
            size_t page1Size = 27 + segments1;
            for (int i = 0; i < segments1; i++) {
                page1Size += g_audioData[27 + i];
            }
            g_currentPos = page1Size;
        }

        // 跳过第二个页面（注释）
        if (g_currentPos + 27 < g_audioSize && memcmp(g_audioData + g_currentPos, "OggS", 4) == 0) {
            uint8_t segments2 = g_audioData[g_currentPos + 26];
            size_t page2Size = 27 + segments2;
            for (int i = 0; i < segments2; i++) {
                page2Size += g_audioData[g_currentPos + 27 + i];
            }
            g_currentPos += page2Size;
        }

        firstCall = false;
        SK_LOGI(TAG, "Audio data starts at: %zu", g_currentPos);
    }

    // 检查是否还有数据
    if (g_currentPos + 27 >= g_audioSize) {
        return SK_RET_NOT_FOUND;
    }

    // 验证当前页面
    if (memcmp(g_audioData + g_currentPos, "OggS", 4) != 0) {
        return SK_RET_FAIL;
    }

    // 提取OPUS数据
    uint8_t segments = g_audioData[g_currentPos + 26];
    size_t headerSize = 27 + segments;
    size_t pageDataSize = 0;

    for (int i = 0; i < segments; i++) {
        pageDataSize += g_audioData[g_currentPos + 27 + i];
    }

    // 检查数据完整性
    if (g_currentPos + headerSize + pageDataSize > g_audioSize) {
        return SK_RET_FAIL;
    }

    // 分配并复制数据
    packet->data = malloc(pageDataSize);
    if (packet->data == NULL) {
        return SK_RET_NO_MEMORY;
    }

    memcpy(packet->data, g_audioData + g_currentPos + headerSize, pageDataSize);
    packet->size = pageDataSize;

    // 更新位置
    g_currentPos += headerSize + pageDataSize;

    SK_LOGI(TAG, "OPUS packet: %zu bytes", packet->size);
    return SK_RET_SUCCESS;
}

/**
 * @brief 重置OGG解析状态（用于重新开始解析）
 */
void SkOggResetParser(void) {
    if (g_audioData) {
        free(g_audioData);
        g_audioData = NULL;
    }
    g_audioSize = 0;
    g_currentPos = 0;
    g_initialized = false;
    SK_LOGI(TAG, "OGG parser reset");
}

/**
 * @brief 释放OPUS包资源
 */
void SkOggFreeOpusPacket(SkOpusPacket *packet) {
    if (packet && packet->data) {
        free(packet->data);
        packet->data = NULL;
        packet->size = 0;
    }
}

// ==================== OPUS解码适配功能 ====================

/**
 * @brief 使用现有解码器解码OPUS包
 */
int32_t SkOpusAdapterDecode(SkOpusPacket *packet, int16_t *pcmBuffer, int32_t pcmSize) {
    if (packet == NULL || packet->data == NULL || pcmBuffer == NULL) {
        return 0;
    }

    // 获取现有的OPUS解码器句柄
    SkOpusDecHandler handler = SkOpusDecGetHandler();
    if (handler == NULL) {
        SK_LOGE(TAG, "OPUS decoder not initialized");
        return 0;
    }

    // 创建临时缓冲区，模拟现有系统的数据格式（前4字节为头部）
    uint8_t *tempBuffer = malloc(packet->size + 4);
    if (tempBuffer == NULL) {
        SK_LOGE(TAG, "Failed to allocate temp buffer for OPUS decode");
        return 0;
    }

    static uint16_t seqNum = 1;
    tempBuffer[0] = seqNum & 0xFF;                    
    tempBuffer[1] = (seqNum >> 8) & 0xFF;            
    tempBuffer[2] = packet->size & 0xFF;             
    tempBuffer[3] = (packet->size >> 8) & 0xFF;       
    seqNum++;

    // 复制OPUS数据到4字节头部之后
    memcpy(tempBuffer + 4, packet->data, packet->size);

    // 调试：显示构造的数据格式
    SK_LOGI(TAG, "Constructed header: %02X %02X %02X %02X, OPUS data: %02X %02X %02X %02X...",
           tempBuffer[0], tempBuffer[1], tempBuffer[2], tempBuffer[3],
           tempBuffer[4], tempBuffer[5], tempBuffer[6], tempBuffer[7]);

    // 按现有系统格式调用解码函数（跳过前4字节）
    int32_t samples = SkOpusDecDecode(handler, tempBuffer + 4, packet->size, pcmBuffer, pcmSize);

    // 释放临时缓冲区
    free(tempBuffer);

    if (samples > 0) {
        SK_LOGI(TAG, "Decoded OPUS: %zu bytes -> %ld samples", packet->size, samples);
    } else {
        SK_LOGE(TAG, "OPUS decode failed: %ld", samples);
        // 调试：显示解码失败的数据前8字节
        SK_LOGE(TAG, "Failed data: %02X %02X %02X %02X %02X %02X %02X %02X",
               packet->data[0], packet->data[1], packet->data[2], packet->data[3],
               packet->data[4], packet->data[5], packet->data[6], packet->data[7]);
    }

    return samples;
}

/**
 * @brief 测试OPUS解码功能
 */
sk_err_t SkOpusAdapterTest(SkHttpDownloadData *downloadData) {
    if (downloadData == NULL) {
        return SK_RET_INVALID_PARAM;
    }

    SK_LOGI(TAG, "=== Testing OPUS Decoding ===");

    // 确保解析器已初始化
    if (!g_initialized) {
        sk_err_t ret = InitOggParser(downloadData->ringbuf);
        if (ret != SK_RET_SUCCESS) {
            return ret;
        }
    }

    // 分配PCM缓冲区（16kHz, 1ch, 60ms = 960样本）
    const int32_t PCM_FRAME_SIZE = 960;
    int16_t *pcmBuffer = malloc(PCM_FRAME_SIZE * sizeof(int16_t));
    if (pcmBuffer == NULL) {
        SK_LOGE(TAG, "Failed to allocate PCM buffer");
        return SK_RET_NO_MEMORY;
    }

    int packetCount = 0;
    int successCount = 0;
    size_t totalSamples = 0;

    // 重置解析状态
    static bool firstCall = true;
    if (firstCall) {
        g_currentPos = 0;

        // 跳过头部和注释页面
        if (memcmp(g_audioData, "OggS", 4) == 0) {
            uint8_t segments1 = g_audioData[26];
            size_t page1Size = 27 + segments1;
            for (int i = 0; i < segments1; i++) {
                page1Size += g_audioData[27 + i];
            }
            g_currentPos = page1Size;
        }

        if (g_currentPos + 27 < g_audioSize && memcmp(g_audioData + g_currentPos, "OggS", 4) == 0) {
            uint8_t segments2 = g_audioData[g_currentPos + 26];
            size_t page2Size = 27 + segments2;
            for (int i = 0; i < segments2; i++) {
                page2Size += g_audioData[g_currentPos + 27 + i];
            }
            g_currentPos += page2Size;
        }

        firstCall = false;
    }

    // 测试解码前10个包
    for (int i = 0; i < 10; i++) {
        SkOpusPacket packet;

        // 提取OPUS包
        sk_err_t ret = SkOggGetNextOpusPacket(downloadData->ringbuf, &packet);
        if (ret != SK_RET_SUCCESS) {
            if (ret == SK_RET_NOT_FOUND) {
                SK_LOGI(TAG, "No more packets to decode");
            } else {
                SK_LOGE(TAG, "Failed to get packet %d: %d", i+1, ret);
            }
            break;
        }

        packetCount++;

        // 解码OPUS包
        int32_t samples = SkOpusAdapterDecode(&packet, pcmBuffer, PCM_FRAME_SIZE);
        if (samples > 0) {
            successCount++;
            totalSamples += samples;

            SK_LOGI(TAG, "Packet %d: %zu bytes -> %ld samples",
                   packetCount, packet.size, samples);

            // 显示PCM数据的前几个样本
            if (samples >= 4) {
                SK_LOGI(TAG, "   PCM: %d %d %d %d...",
                       pcmBuffer[0], pcmBuffer[1], pcmBuffer[2], pcmBuffer[3]);
            }
        }

        SkOggFreeOpusPacket(&packet);
    }

    // 统计结果
    SK_LOGI(TAG, "=== Decoding Results ===");
    SK_LOGI(TAG, "Packets processed: %d", packetCount);
    SK_LOGI(TAG, "Successfully decoded: %d", successCount);
    SK_LOGI(TAG, "Total PCM samples: %zu", totalSamples);

    if (successCount > 0) {
        float duration = (float)totalSamples / 16000.0f;  // 16kHz采样率
        SK_LOGI(TAG, "Decoded audio duration: %.2f seconds", duration);
        SK_LOGI(TAG, "OPUS decoding test successful!");
    } else {
        SK_LOGE(TAG, "No packets decoded successfully");
    }

    free(pcmBuffer);
    return (successCount > 0) ? SK_RET_SUCCESS : SK_RET_FAIL;
}
