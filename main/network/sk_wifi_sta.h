/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_wifi_sta.h
 * @description: WiFi的STA模式的内部接口.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_WIFI_STA_H
#define SK_WIFI_STA_H

#ifdef __cplusplus
extern "C" {
#endif

typedef void (*SkStaEventCB)(uint32_t event);

typedef void* SkStaHandler;
void SkStaStop(SkStaHandler handler);
void SkStaRegEventCallback(SkStaHand<PERSON> handler, SkStaEventCB cb);
void SkStaStart(SkStaHandler handler);
SkStaHandler SkStaInit();
void SkStaDeinit(SkStaHandler handler);

#ifdef __cplusplus
}
#endif

#endif