/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_wifi_ap.h
 * @description: WI-FI的AP模式的内部接口.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_WIFI_AP_H
#define SK_WIFI_AP_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef void* SkApHandler;

SkApHandler SkApInit();
void SkApDeinit(SkApHandler handler);
void SkApStart(SkApHandler handler);
void SkApStop(SkApHandler handler);

#ifdef __cplusplus
}
#endif

#endif // SK_WIFI_AP_H
