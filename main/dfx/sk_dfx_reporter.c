/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_dfx_reporter.c
 * @description: 上报数据的Reporter节点操作. 用于上报统计用数据, 例如音频时延, 电量和传感器数据.
 *               步骤为:
 *                  1)SkDfxInit, 初始化; 
 *                  2)SkDfxStart: 外部请求启动记录; 
 *                  3)SkDfxProcCall: 检测到启动标志, 分配空间;
 *                  4)SkDfxRecordData: 记录单条数据; 
 *                  5)SkDfxProcCall: 检测缓冲区达到上报大小时，上报数据; 清空缓存，便于循环记录;
 *                  6)SkDfxStop: 外部请求停止记录;
 *                  7)SkDfxProcCall: 检测到停止标志, 释放空间.
 *               注意点:
 *                  1)SkDfxRecordData和SkDfxProcCall在同一个任务中调用.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdlib.h>
#include <string.h>
#include "sk_log.h"
#include "sk_dfx.h"
#include "sk_os.h"
#include "sk_frame.h"

#define TAG "SkDfxCtrl"
#define SK_DFX_MAX_RECORDS 40
#define SK_EVENT_BIT_DFX_START (1 << 0)
#define SK_EVENT_BIT_DFX_STOP  (1 << 1)
#define SK_EVENT_BIT_DFX_FINISH  (1 << 2)

void DfxStartDbg(SkDfxCtrl_t *dfxCtrl);
void DfxStopDbg(SkDfxCtrl_t *dfxCtrl);

void SkDfxInit(SkDfxCtrl_t *dfxCtrl, uint8_t msgType, size_t size, size_t minUnitSize) {
    if (dfxCtrl == NULL) {
        return;
    }

    dfxCtrl->eventGroup = xEventGroupCreate();
    dfxCtrl->msgType = msgType;
    dfxCtrl->buffer = NULL;
    dfxCtrl->pos = 0;
    dfxCtrl->size = size;
    dfxCtrl->minUnitSize = minUnitSize;

    return;
}

void SkDfxStart(SkDfxCtrl_t *dfxCtrl) {
    xEventGroupSetBits(dfxCtrl->eventGroup, SK_EVENT_BIT_DFX_START);
    return;
}

void SkDfxStop(SkDfxCtrl_t *dfxCtrl) {
    xEventGroupSetBits(dfxCtrl->eventGroup, SK_EVENT_BIT_DFX_STOP);
    return;
}

void SkDfxRecordData(SkDfxCtrl_t *dfxCtrl, uint8_t *data, size_t len) {
    if ((dfxCtrl->buffer == NULL) || (dfxCtrl->pos >= dfxCtrl->size)) {
        return;
    }
    if ((dfxCtrl->pos + len) <= dfxCtrl->size) {
        memcpy(&dfxCtrl->buffer[dfxCtrl->pos], data, len);
        dfxCtrl->pos += len;
    }

    return;
}

void SkDfxProcCall(SkDfxCtrl_t *dfxCtrl) {
    if (xEventGroupGetBits(dfxCtrl->eventGroup) & SK_EVENT_BIT_DFX_START) {
        xEventGroupClearBits(dfxCtrl->eventGroup, SK_EVENT_BIT_DFX_START);
        SK_LOGI(TAG, "DFX start");
        DfxStartDbg(dfxCtrl);
        return;
    }

    if (xEventGroupGetBits(dfxCtrl->eventGroup) & SK_EVENT_BIT_DFX_STOP) {
        xEventGroupClearBits(dfxCtrl->eventGroup, SK_EVENT_BIT_DFX_STOP);
        SK_LOGI(TAG, "DFX stop");
        DfxStopDbg(dfxCtrl);
        return;
    }

    if (dfxCtrl->buffer == NULL) {
        return;
    }

    if ((dfxCtrl->minUnitSize + dfxCtrl->pos) >= dfxCtrl->size) {
        SkDfxLinkSendMsg(dfxCtrl->msgType, dfxCtrl->buffer, dfxCtrl->pos);
        dfxCtrl->pos = 0;
    }

    return;
}

void DfxStartDbg(SkDfxCtrl_t *dfxCtrl) {
    uint8_t *buffer = NULL;

    if (dfxCtrl->buffer == NULL) {
        buffer = (uint8_t *)malloc(dfxCtrl->size);
        if (buffer == NULL) {
            return;
        }
    } else {
        buffer = dfxCtrl->buffer;
    }
    memset(buffer, 0, dfxCtrl->size);
    dfxCtrl->pos = 0;
    dfxCtrl->buffer = buffer;

    return;
}

void DfxStopDbg(SkDfxCtrl_t *dfxCtrl) {
    if (dfxCtrl->buffer != NULL) {
        free(dfxCtrl->buffer);
    }
    dfxCtrl->buffer = NULL;
    dfxCtrl->pos = 0;
    return;
}
