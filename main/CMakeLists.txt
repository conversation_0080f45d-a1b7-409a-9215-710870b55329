# copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
cmake_minimum_required(VERSION 3.5)

set (USER_SRC_DIRS "board" "os" "network" "audio" "protocol" "app" "config" "opus_coder" "dfx" "ota")

if(TESTCASE_ENABLED)
set (USER_SRC_DIRS ${USER_SRC_DIRS} "test")
set (CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -DTESTCASE_ENABLED=${TESTCASE_ENABLED}")
endif()

idf_component_register(
	SRC_DIRS
		${USER_SRC_DIRS}
	INCLUDE_DIRS 
		"include"
		"ota"
	REQUIRES
		app_update
		driver
		efuse
		esp_adc
		esp_event
		esp_http_client
		esp_https_ota
		esp_netif
		esp_system
		esp_timer
		esp_wifi
		fatfs
		freertos
		mbedtls
		mqtt
		lwip
		nvs_flash
		soc
		)

if(COVERAGE_ENABLED)
	target_compile_options(${COMPONENT_LIB} PRIVATE --coverage)
	target_link_options(${COMPONENT_LIB} PRIVATE --coverage)
endif()
