/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_board_def.h
 * @description: 不支持AEC的单板接口定义.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#pragma once

#include "driver/gpio.h"
#include "esp_idf_version.h"

/**
 * @brief I2S GPIO defination
 * 
 */

#define FUNC_I2S_EN         (1)

#define BOARD_XZ 1
#define SIMPLEX_I2S 1

#ifdef BOARD_XZ 
#ifdef SIMPLEX_I2S
#define GPIO_I2S_LRCK       (GPIO_NUM_4)
#define GPIO_I2S_SCLK       (GPIO_NUM_5)
#define GPIO_I2S_SDIN       (GPIO_NUM_6)

#define GPIO_SPK_DOUT       (GPIO_NUM_7)
#define GPIO_SPK_BCLK       (GPIO_NUM_15)
#define GPIO_SPK_LRCK       (GPIO_NUM_16)
#else
#define GPIO_I2S_LRCK       (GPIO_NUM_4)
#define GPIO_I2S_SCLK       (GPIO_NUM_5)
#define GPIO_I2S_SDIN       (GPIO_NUM_6)
#define GPIO_SPK_DOUT       (GPIO_NUM_7)
#define GPIO_I2S_MCLK       (GPIO_NUM_NC)
#define GPIO_KEY_INPUT      (GPIO_NUM_39)
#endif
#else
#define GPIO_I2S_LRCK       (GPIO_NUM_3)
#define GPIO_I2S_MCLK       (GPIO_NUM_NC)
#define GPIO_I2S_SCLK       (GPIO_NUM_2)
#define GPIO_I2S_SDIN       (GPIO_NUM_5)
#define GPIO_SPK_DOUT       (GPIO_NUM_4)
#define GPIO_KEY_INPUT      (GPIO_NUM_12)
#endif

/**
 * @brief I2S GPIO defination
 * 
 */
#define FUNC_I2S0_EN         (0)
#define GPIO_I2S0_LRCK       (GPIO_NUM_NC)
#define GPIO_I2S0_MCLK       (GPIO_NUM_NC)
#define GPIO_I2S0_SCLK       (GPIO_NUM_NC)
#define GPIO_I2S0_SDIN       (GPIO_NUM_NC)
#define GPIO_I2S0_DOUT       (GPIO_NUM_NC)

/**
 * @brief power control IO
 * 
 * @note Some power control pins might not be listed yet
 * 
 */
#define FUNC_PWR_CTRL       (0)
#define GPIO_PWR_CTRL       (GPIO_NUM_NC)
#define GPIO_PWR_ON_LEVEL   (1)

#define I2S_CONFIG_DEFAULT(sample_rate, channel_fmt, bits_per_chan) { \
        .clk_cfg = I2S_STD_CLK_DEFAULT_CONFIG(sample_rate), \
        .slot_cfg = I2S_STD_PHILIPS_SLOT_DEFAULT_CONFIG(bits_per_chan, channel_fmt), \
        .gpio_cfg = { \
            .mclk = GPIO_I2S_MCLK, \
            .bclk = GPIO_I2S_SCLK, \
            .ws   = GPIO_I2S_LRCK, \
            .dout = GPIO_SPK_DOUT, \
            .din  = GPIO_I2S_SDIN, \
            .invert_flags = { \
                .mclk_inv = false, \
                .bclk_inv = false, \
                .ws_inv   = false, \
            }, \
        }, \
    }

#define I2S_CONFIG_MIC(sample_rate, channel_fmt, bits_per_chan) { \
        .clk_cfg = I2S_STD_CLK_DEFAULT_CONFIG(sample_rate), \
        .slot_cfg = I2S_STD_PHILIPS_SLOT_DEFAULT_CONFIG(bits_per_chan, channel_fmt), \
        .gpio_cfg = { \
            .mclk = GPIO_NUM_NC, \
            .bclk = GPIO_I2S_SCLK, \
            .ws   = GPIO_I2S_LRCK, \
            .dout = GPIO_NUM_NC, \
            .din  = GPIO_I2S_SDIN, \
            .invert_flags = { \
                .mclk_inv = false, \
                .bclk_inv = false, \
                .ws_inv   = false, \
            }, \
        }, \
    }

#define I2S_CONFIG_SPK(sample_rate, channel_fmt, bits_per_chan) { \
        .clk_cfg = I2S_STD_CLK_DEFAULT_CONFIG(sample_rate), \
        .slot_cfg = I2S_STD_PHILIPS_SLOT_DEFAULT_CONFIG(bits_per_chan, channel_fmt), \
        .gpio_cfg = { \
            .mclk = GPIO_NUM_NC, \
            .bclk = GPIO_SPK_BCLK, \
            .ws   = GPIO_SPK_LRCK, \
            .dout = GPIO_SPK_DOUT, \
            .din  = GPIO_NUM_NC, \
            .invert_flags = { \
                .mclk_inv = false, \
                .bclk_inv = false, \
                .ws_inv   = false, \
            }, \
        }, \
    }

    