/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: audio_noncodec.c
 * @description: 数字编解码器场景下的驱动, 直接使用I2S接口船速数据.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "driver/i2s_std.h"
#include <driver/i2s_tdm.h>
#include "soc/soc_caps.h"
#include "freertos/FreeRTOS.h"
#include "esp_err.h"
#include "esp_log.h"
#include "sk_board_def.h"
#include "sk_board.h"
#include "sk_common.h"
#include "sk_os.h"

#define TAG "SkBsp"
#define ADC_I2S_CHANNEL 2
#define MAX_PLAY_SAMPLE_PER_CALL 800*2

typedef struct {
    i2s_chan_handle_t txHandle;
    i2s_chan_handle_t rxHandle;
    int spkVol;
    int micVol;
} AudioNoncodec;

static AudioNoncodec g_audioNondev;

int32_t AudioDevInitNoncodec(uint32_t sampleRate, int bitsPerSample) {
    AudioNoncodec *audio = &g_audioNondev;
    int32_t ret = ESP_OK;
    i2s_chan_config_t chanCfg = I2S_CHANNEL_DEFAULT_CONFIG(0, I2S_ROLE_MASTER);
    i2s_std_config_t rxStdCfg = I2S_CONFIG_MIC(sampleRate, I2S_SLOT_MODE_MONO, bitsPerSample);
    i2s_std_config_t txStdCfg = I2S_CONFIG_SPK(sampleRate, I2S_SLOT_MODE_MONO, bitsPerSample);

    ret |= i2s_new_channel(&chanCfg, &audio->txHandle, NULL);
    ret |= i2s_channel_init_std_mode(audio->txHandle, &txStdCfg);

    chanCfg.id = (i2s_port_t)1;
    ret |= i2s_new_channel(&chanCfg, NULL, &audio->rxHandle);
    rxStdCfg.slot_cfg.slot_mask = I2S_STD_SLOT_LEFT;
    ret |= i2s_channel_init_std_mode(audio->rxHandle, &rxStdCfg);
    ret |= i2s_channel_enable(audio->rxHandle);

    return ret;
}

sk_err_t AudioDevDeinitNoncodec() {
    esp_err_t ret = ESP_OK;
    AudioNoncodec *audio = &g_audioNondev;

    ret |= i2s_channel_disable(audio->rxHandle);
    ret |= i2s_channel_disable(audio->txHandle);
    ret |= i2s_del_channel(audio->rxHandle);
    ret |= i2s_del_channel(audio->txHandle);
    audio->rxHandle = NULL;
    audio->txHandle = NULL;
    if (ret != ESP_OK) {
        ret = SK_RET_FAIL;
    } else {
        ret = SK_RET_SUCCESS;
    }

    return ret;
}

sk_err_t AudioDevReadNoncodec(int16_t *buffer, int bufferLen) {
    esp_err_t ret = ESP_OK;
    size_t rBytes;
    ret = i2s_channel_read(g_audioNondev.rxHandle, buffer, bufferLen, &rBytes, portMAX_DELAY);
    if (ret != ESP_OK) {
        ret = SK_RET_FAIL;
    } else {
        ret = SK_RET_SUCCESS;
    }
    return ret;
}

sk_err_t AudioDevEnableSpkNoncodec(bool enable) {
    esp_err_t ret;

    if (enable) {
        ret = i2s_channel_enable(g_audioNondev.txHandle);
    } else {
        ret = i2s_channel_disable(g_audioNondev.txHandle);
    }
    if (ret != ESP_OK) {
        ret = SK_RET_FAIL;
    } else {
        ret = SK_RET_SUCCESS;
    }

    return ret;
}

sk_err_t AudioDevWriteNoncodec(const int16_t* data, size_t length, uint32_t msToWait) {
    esp_err_t ret;
    size_t wBytes;
    AudioNoncodec *audio = &g_audioNondev;
    TickType_t ticksToWait;

    ticksToWait = pdMS_TO_TICKS(msToWait);
    ret = i2s_channel_write(audio->txHandle, data, length, &wBytes, ticksToWait);
    if (ret == ESP_OK) {
        return SK_RET_SUCCESS;
    } else {
        ESP_LOGI(TAG, "i2s write failed");
    }

    return SK_RET_FAIL;
}

sk_err_t AudioDevPreloadNoncodec(const int16_t* data, size_t length, size_t *wBytes) {
    if (i2s_channel_preload_data(g_audioNondev.txHandle, data, length, wBytes) != ESP_OK) {
        return SK_RET_FAIL;
    } else {
        return SK_RET_SUCCESS;
    }
}

sk_err_t AudioDevSetSpkVolNoncodec(int vol) {
    g_audioNondev.spkVol = vol;
    return SK_RET_SUCCESS;
}

int32_t AudioDevGetSpkVolNoncodec() {
    return g_audioNondev.spkVol;
}

sk_err_t AudioDevSetMicVolNoncodec(int vol) {
    g_audioNondev.micVol = vol;
    return SK_RET_SUCCESS;
}

int32_t AudioDevGetMicVolNoncodec() {
    return g_audioNondev.micVol;
}
