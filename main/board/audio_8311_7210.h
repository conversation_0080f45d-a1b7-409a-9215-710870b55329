/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: audio_8311_7210.h
 * @description: 8311+7210组合的音频处理的驱动接口.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef AUDIO_8311_7210_H
#define AUDIO_8311_7210_H

#include <stdint.h>
#include <driver/i2c_master.h>

#ifdef __cplusplus
extern "C" {
#endif

int32_t AudioDevInit(i2c_master_bus_handle_t i2cBus, uint32_t sampleRate, int bitsPerChan);
void AudioDevDeinit();
void AudioDevSetSpkVol(int vol);
void AudioDevSetMicVol(int vol);
int32_t AudioDevGetSpkVol();
int32_t AudioDevGetMicVol();
void AudioDevEnableMic(bool enable);
void AudioDevEnableSpk(bool enable);
int AudioDevRead(int16_t* dest, int bufSize);
int AudioDevWrite(const int16_t* data, int dataLen);

#ifdef __cplusplus
}
#endif


#endif // AUDIO_8311_7210_H