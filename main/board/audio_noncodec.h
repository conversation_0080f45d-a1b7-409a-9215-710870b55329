/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: audio_noncodec.h
 * @description: 数字编解码器场景下的驱动接口定义, 直接使用I2S接口船速数据.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef AUDIO_NONCODEC_H
#define AUDIO_NONCODEC_H

#include <stdint.h>
#include <driver/i2c_master.h>

#ifdef __cplusplus
extern "C" {
#endif

int32_t AudioDevInitNoncodec(uint32_t sampleRate, int bitsPerSample);
sk_err_t AudioDevDeinitNoncodec();
sk_err_t AudioDevReadNoncodec(int16_t *buffer, int bufferLen);
sk_err_t AudioDevEnableSpkNoncodec(bool enable);
sk_err_t AudioDevWriteNoncodec(const int16_t* data, size_t length, uint32_t msToWait);
sk_err_t AudioDevPreloadNoncodec(const int16_t* data, size_t length, size_t *wBytes);
sk_err_t AudioDevSetSpkVolNoncodec(int vol);
int32_t AudioDevGetSpkVolNoncodec();
sk_err_t AudioDevSetMicVolNoncodec(int vol);
int32_t AudioDevGetMicVolNoncodec();

#ifdef __cplusplus
}
#endif


#endif // AUDIO_NONCODEC_H