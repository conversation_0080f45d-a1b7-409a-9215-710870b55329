/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_gyro.c
 * @description: 陀螺仪驱动.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "soc/soc_caps.h"
#include "freertos/FreeRTOS.h"
#include "esp_err.h"
#include "esp_log.h"
#include "sk_board_def.h"
#include "sk_aec_board.h"
#include <driver/i2c_master.h>
#include "sk_board.h"
#include "sk_common.h"
#include "sk_os.h"

#define ONE_G 9.807f
#define SIGNAL_PATH_RESET 0x02
#define	TEMP_OUT_H		0x09
#define	TEMP_OUT_L		0x0A
#define	ACCEL_XOUT_H	0x0B
#define	ACCEL_XOUT_L	0x0C
#define	ACCEL_YOUT_H	0x0D
#define	ACCEL_YOUT_L	0x0E
#define	ACCEL_ZOUT_H	0x0F
#define	ACCEL_ZOUT_L	0x10
#define	GYRO_XOUT_H		0x11
#define	GYRO_XOUT_L		0x12	
#define	GYRO_YOUT_H		0x13
#define	GYRO_YOUT_L		0x14
#define	GYRO_ZOUT_H		0x15
#define	GYRO_ZOUT_L		0x16
#define	APEX_DATA4		0x1D    // FF_DUR[7:0]
#define	APEX_DATA5		0x1E    // FF_DUR[15:8]
#define PWR_MGMT0       0x1F
#define	GYRO_CONFIG		0x20
#define	ACCEL_CONFIG	0x21
#define APEX_CONFIG1    0x26
#define APEX_DATA0      0x31
#define APEX_DATA1      0x32
#define APEX_DATA2      0x33
#define APEX_DATA3      0x34
#define	WHO_AM_I	    0x75

#define TAG "SkBsp"

typedef struct {
    i2c_master_dev_handle_t i2cDev;
    uint32_t sensorReadFail[5];
} SkGyro;

int32_t SkGyroSetParams(SkGyro *gyro);

SkGyro g_gyroDev;

int32_t SkGyroInit(i2c_master_bus_handle_t i2cBus) {
    SkGyro *gyro = &g_gyroDev;

    if (i2cBus == NULL) {
        ESP_LOGE(TAG, "I2C bus handle is NULL");
        return SK_RET_FAIL;
    }

    i2c_device_config_t devCfg = {
        .dev_addr_length = I2C_ADDR_BIT_LEN_7,
        .device_address = ICM42607_ADDR,
        .scl_speed_hz = I2C_MASTER_FREQ_HZ,
        .scl_wait_us = 5000,
    };
    int ret = i2c_master_bus_add_device(i2cBus, &devCfg, &gyro->i2cDev);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to add ICM device");
        return SK_RET_FAIL;
    }
    ESP_LOGI(TAG, "ICM42607 device added");

    return SkGyroSetParams(gyro);
}

void SkGyroReset() {
    SkGyro *gyro = &g_gyroDev;

    if (gyro->i2cDev != NULL) {
        return;
    }
    uint8_t reset_cmd[] = {PWR_MGMT0, 0x00};
    ESP_ERROR_CHECK(i2c_master_transmit(gyro->i2cDev, reset_cmd, sizeof(reset_cmd), -1));
    vTaskDelay(pdMS_TO_TICKS(20));
    int32_t ret = SkGyroSetParams(gyro);
    if (ret != SK_RET_SUCCESS) {
        ESP_LOGE(TAG, "Failed to reset ICM42607");
    }

    return;
}

int32_t SkGyroSetParams(SkGyro *gyro) {
    // 唤醒传感器
    uint8_t wake_cmd[] = {PWR_MGMT0, 0x0F};
    ESP_ERROR_CHECK(i2c_master_transmit(gyro->i2cDev, wake_cmd, sizeof(wake_cmd), -1));

    // 配置加速度计（±2g，ODR 200Hz）
    uint8_t accel_cfg[] = {ACCEL_CONFIG, 0x64};
    if (i2c_master_transmit(gyro->i2cDev, accel_cfg, sizeof(accel_cfg), -1) != ESP_OK) {
        vTaskDelay(pdMS_TO_TICKS(20));
        ESP_ERROR_CHECK(i2c_master_transmit(gyro->i2cDev, accel_cfg, sizeof(accel_cfg), -1));
    }

    // 配置陀螺仪（±250dps，ODR 200Hz）
    uint8_t gyro_cfg[] = {GYRO_CONFIG, 0x64};
    if (i2c_master_transmit(gyro->i2cDev, gyro_cfg, sizeof(gyro_cfg), -1) != ESP_OK) {
        vTaskDelay(pdMS_TO_TICKS(20));
        ESP_ERROR_CHECK(i2c_master_transmit(gyro->i2cDev, gyro_cfg, sizeof(gyro_cfg), -1));
    }

    // 配置APEX(高级功能)（SMD: on, FF: on, TILE: on, PED: on, DMP_ODR: 50Hz）
    uint8_t apexCfg1[] = {APEX_CONFIG1, 0x7A};
    if (i2c_master_transmit(gyro->i2cDev, apexCfg1, sizeof(apexCfg1), -1) != ESP_OK) {
        vTaskDelay(pdMS_TO_TICKS(20));
        ESP_ERROR_CHECK(i2c_master_transmit(gyro->i2cDev, apexCfg1, sizeof(apexCfg1), -1));
    }

    uint8_t addrReg[1] = {WHO_AM_I};
    uint8_t whoami[1] = {0x00};
    i2c_master_transmit_receive(gyro->i2cDev, addrReg, sizeof(addrReg),
        whoami, sizeof(whoami), -1);
    ESP_LOGI(TAG, "ICM42607 whoami: 0x%02x", whoami[0]);

    return SK_RET_SUCCESS;
}

// 读取传感器数据
int32_t SkGyroReadData(SkSensorData_t *data) {
    esp_err_t ret;
    SkGyro *gyro = &g_gyroDev;
    const uint8_t accelReg[1] = {TEMP_OUT_H};
    uint8_t accelData[8];
    const uint8_t gyroReg[1] = {GYRO_XOUT_H};
    uint8_t gyroData[6];
    const uint8_t ffReg[1] = {APEX_DATA4};
    uint8_t ffData[2];
    const uint8_t pedometerReg[1] = {APEX_DATA0};
    uint8_t pedometerData[4];
    
    if (gyro->i2cDev == NULL) {
        ESP_LOGE(TAG, "Gyroscope device is NULL");
        return SK_RET_FAIL;
    }

    // 读取加速度计数据
    ret = i2c_master_transmit_receive(gyro->i2cDev, 
        accelReg, sizeof(accelReg), accelData, sizeof(accelData), -1);
    if (ret != ESP_OK) {
        gyro->sensorReadFail[0]++;
        //return SK_RET_FAIL;
    }
    // 读取陀螺仪数据
    ret = i2c_master_transmit_receive(gyro->i2cDev, 
        gyroReg, sizeof(gyroReg), gyroData, sizeof(gyroData), -1);
    if (ret != ESP_OK) {
        gyro->sensorReadFail[1]++;
        //return SK_RET_FAIL;
    }

    // 读取自由落体数据
    ret = i2c_master_transmit_receive(gyro->i2cDev, 
        ffReg, sizeof(ffReg), ffData, sizeof(ffData), -1);
    if (ret != ESP_OK) {
        gyro->sensorReadFail[2]++;
        //return SK_RET_FAIL;
    }
    
    // 读取计步数据
    ret = i2c_master_transmit_receive(gyro->i2cDev, 
        pedometerReg, sizeof(pedometerReg), pedometerData, sizeof(pedometerData), -1);
    if (ret != ESP_OK) {
        gyro->sensorReadFail[3]++;
        //return SK_RET_FAIL;
    }

    // 解析原始数据
    data->temp = ((uint16_t)accelData[0] << 8) | accelData[1];
    data->accel[0] = ((uint16_t)accelData[2] << 8) | accelData[3];
    data->accel[1] = ((uint16_t)accelData[4] << 8) | accelData[5];
    data->accel[2] = ((uint16_t)accelData[6] << 8) | accelData[7];
    
    data->gyro[0] = ((uint16_t)gyroData[0] << 8) | gyroData[1];
    data->gyro[1] = ((uint16_t)gyroData[2] << 8) | gyroData[3];
    data->gyro[2] = ((uint16_t)gyroData[4] << 8) | gyroData[5];

    data->ff = ((uint16_t)ffData[0] << 8) | ffData[1];

    data->step = ((uint16_t)pedometerData[0] << 8) | pedometerData[1];
    data->stepState = ((uint16_t)pedometerData[2] << 8) | pedometerData[3];
    data->timestamp = SkOsGetTickCnt();

    gyro->sensorReadFail[4]++;
    if (gyro->sensorReadFail[4] >= 100) {
        ESP_LOGI(TAG, "sensor read fail: %d %d %d %d",
            gyro->sensorReadFail[0], gyro->sensorReadFail[1],
            gyro->sensorReadFail[2], gyro->sensorReadFail[3]);
        gyro->sensorReadFail[4] = 0;
        gyro->sensorReadFail[0] = 0;
        gyro->sensorReadFail[1] = 0;
        gyro->sensorReadFail[2] = 0;
        gyro->sensorReadFail[3] = 0;
    }

    return SK_RET_SUCCESS;
}