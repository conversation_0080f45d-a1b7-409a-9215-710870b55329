/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_ths.c
 * @description: 温湿度传感器驱动.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "soc/soc_caps.h"
#include "freertos/FreeRTOS.h"
#include "esp_err.h"
#include "esp_log.h"
#include "sk_board_def.h"
#include "sk_aec_board.h"
#include <driver/i2c_master.h>
#include "sk_board.h"
#include "sk_common.h"
#include "sk_os.h"

#define TAG "SkBsp"

#define AHTX0_I2CADDR_DEFAULT 0x38   ///< AHT default i2c address
#define AHTX0_I2CADDR_ALTERNATE 0x39 ///< AHT alternate i2c address
#define AHTX0_CMD_CALIBRATE 0xE1     ///< Calibration command
#define AHTX0_CMD_TRIGGER 0xAC       ///< Trigger reading command
#define AHTX0_CMD_SOFTRESET 0xBA     ///< Soft reset command
#define AHTX0_STATUS_BUSY 0x80       ///< Status bit for busy
#define AHTX0_STATUS_CALIBRATED 0x08 ///< Status bit for calibrated



typedef struct {
    i2c_master_dev_handle_t i2cDev;
    uint32_t thsOperFail[3];
} SkTempHumiSensor;

SkTempHumiSensor g_ths;

int32_t SkTHSInit(i2c_master_bus_handle_t i2cBus) {
    int32_t ret;
    uint8_t state;
    SkTempHumiSensor *board = &g_ths;

    if (i2cBus == NULL) {
        return SK_RET_FAIL;
    }

    i2c_device_config_t devCfg = {
        .dev_addr_length = I2C_ADDR_BIT_LEN_7,
        .device_address = AHT30_ADDR,
        .scl_speed_hz = I2C_MASTER_FREQ_HZ,
        .scl_wait_us = 5000,
    };
    ret = i2c_master_bus_add_device(i2cBus, &devCfg, &board->i2cDev);
    if (ret != ESP_OK) {
        ESP_LOGI(TAG, "Failed to add ICM device");
        return SK_RET_FAIL;
    }

    // 复位
    uint8_t resetCmd[] = {AHTX0_CMD_SOFTRESET};
    ESP_ERROR_CHECK(i2c_master_transmit(board->i2cDev, resetCmd, sizeof(resetCmd), -1));

    vTaskDelay(pdMS_TO_TICKS(30));
    for (int32_t i = 0; i < 3; i++) {
        ret = i2c_master_receive(board->i2cDev, &state, 1, 10);
        if (ret == ESP_OK && ((state & AHTX0_STATUS_BUSY) == 0)) {
            break;
        }
        if (i == 2) {
            return SK_RET_FAIL;
        }
        vTaskDelay(pdMS_TO_TICKS(10));
    }

    uint8_t calibrateCfg[] = {AHTX0_CMD_CALIBRATE, 0x08, 0x00};
    if (i2c_master_transmit(board->i2cDev, calibrateCfg, sizeof(calibrateCfg), -1) != ESP_OK) {
        return SK_RET_FAIL;
    }

    vTaskDelay(pdMS_TO_TICKS(30));
    for (int32_t i = 0; i < 3; i++) {
        ret = i2c_master_receive(board->i2cDev, &state, 1, 10);
        if (ret == ESP_OK && ((state & AHTX0_STATUS_BUSY) == 0)) {
            break;
        }
        if (i == 2) {
            return SK_RET_FAIL;
        }
        vTaskDelay(pdMS_TO_TICKS(10));
    }

    ret = i2c_master_receive(board->i2cDev, &state, 1, 10);
    if (ret != ESP_OK || ((state & AHTX0_STATUS_CALIBRATED) == 0)) {
        return SK_RET_FAIL;
    }

    uint8_t detectCmd[] = {AHTX0_CMD_TRIGGER, 0x33, 0x00};
    if (i2c_master_transmit(board->i2cDev, detectCmd, sizeof(detectCmd), -1) != ESP_OK) {
        return SK_RET_FAIL;
    }

    return SK_RET_SUCCESS;
}

// 读取传感器数据
int32_t SkTHSReadData(SkTHSData_t *thsData) {
    esp_err_t ret;
    SkTempHumiSensor *ths = &g_ths;
    
    if (ths->i2cDev == NULL) {
        return SK_RET_FAIL;
    }

    ths->thsOperFail[0]++;

    uint8_t data[6];
    ret = i2c_master_receive(ths->i2cDev, data, 6, -1);
    if (ret == ESP_OK) {
        uint32_t h = data[1];
        h <<= 8;
        h |= data[2];
        h <<= 4;
        h |= data[3] >> 4;
        thsData->humi = (int32_t)(((float)h * 100) / 0x100000);

        uint32_t tdata = data[3] & 0x0F;
        tdata <<= 8;
        tdata |= data[4];
        tdata <<= 8;
        tdata |= data[5];
        thsData->temp = (int32_t)((float)tdata * 200 / 0x100000) - 50;
    } else {
        ths->thsOperFail[1]++;
    }

    uint8_t detectCmd[] = {AHTX0_CMD_TRIGGER, 0x33, 0x00};
    if (i2c_master_transmit(ths->i2cDev, detectCmd, sizeof(detectCmd), -1) != ESP_OK) {
        ths->thsOperFail[2]++;
    }

    if (ths->thsOperFail[0] >= 100) {
        ESP_LOGI(TAG, "th sensor read fail: %d %d",
            ths->thsOperFail[1], ths->thsOperFail[2]);
        ths->thsOperFail[0] = 0;
        ths->thsOperFail[1] = 0;
        ths->thsOperFail[2] = 0;
    }

    return SK_RET_SUCCESS;
}