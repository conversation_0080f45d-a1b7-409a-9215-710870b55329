/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_gpio_dev.c
 * @description: GPIO设备驱动, 包含功能按键和系统按键, 震动传感器.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "driver/i2s_std.h"
#include <driver/i2s_tdm.h>
#include "soc/soc_caps.h"
#include "freertos/FreeRTOS.h"
#include "driver/gpio.h"
#include "esp_err.h"
#include "esp_log.h"
#include "esp_rom_sys.h"
#include "esp_vfs_fat.h"
#include "sk_board_def.h"
#include "sk_aec_board.h"
#include <esp_codec_dev.h>
#include <esp_codec_dev_defaults.h>
#include <driver/i2c_master.h>
#include <driver/spi_common.h>
#include "esp_adc/adc_oneshot.h"
#include "sk_board.h"
#include "sk_common.h"
#include "sk_os.h"
#include "esp_sleep.h"
#include "driver/rtc_io.h"
#include <esp_pm.h>
#include <esp_wifi.h>

#define TAG "SkBsp"

typedef struct {
    int32_t gpio;
    int32_t keyState;
    int32_t cnt;
} FuncKey;

typedef struct {
    int32_t keyInput;
    uint32_t keyInputIsrCnt;
    FuncKey funcKey1;
    FuncKey funcKey2;
    FuncKey funcKey3;
} SkGpioDev;

SkGpioDev g_skGpioDev;

// 中断处理函数
static void IRAM_ATTR SkBspUserInputIsr(void* arg) {
    uint32_t gpioVal;

    SkGpioDev *board = (SkGpioDev *)arg;
    gpioVal = gpio_get_level(GPIO_KEY_INPUT);
    board->keyInput = (gpioVal == 0) ? 0 : 1;
    board->keyInputIsrCnt++;
    return;
}

#if (CONFIG_FUNC_KEY_MODE == CONFIG_SK_NORMAL_KEY)
static void IRAM_ATTR SkBspFuncKeyIsr(void* arg) {
    FuncKey *key = (FuncKey *)arg;
    int32_t val = gpio_get_level(key->gpio);
    key->keyState = (val == 0) ? 1 : 0;
    key->cnt++;
    return;
}
#endif

void SkBspKeyInit() {
    // 配置 GPIO 为输入模式，上升沿和下降沿都触发中断
    gpio_config_t ioConf = {};
    ioConf.intr_type = GPIO_INTR_ANYEDGE;
    ioConf.mode = GPIO_MODE_INPUT;
    ioConf.pin_bit_mask = (1ULL << GPIO_KEY_INPUT);
    ioConf.pull_up_en = 1;
    gpio_config(&ioConf);
    gpio_set_intr_type(GPIO_KEY_INPUT, GPIO_INTR_ANYEDGE);
    gpio_install_isr_service(0);
    gpio_isr_handler_add(GPIO_KEY_INPUT, SkBspUserInputIsr, &g_skGpioDev);
    gpio_intr_enable(GPIO_KEY_INPUT);
    g_skGpioDev.keyInput = SK_KEY_PRESS_UP;

#if (CONFIG_FUNC_KEY_MODE == CONFIG_SK_NORMAL_KEY)
    ioConf.pin_bit_mask = (1ULL << GPIO_FUNC_KEY1);
    gpio_config(&ioConf);
    gpio_set_intr_type(GPIO_FUNC_KEY1, GPIO_INTR_ANYEDGE);
    gpio_isr_handler_add(GPIO_FUNC_KEY1, SkBspFuncKeyIsr, &g_skGpioDev.funcKey1);
    gpio_intr_enable(GPIO_FUNC_KEY1);
    g_skGpioDev.funcKey1.gpio = GPIO_FUNC_KEY1;

    ioConf.pin_bit_mask = (1ULL << GPIO_FUNC_KEY2);
    gpio_config(&ioConf);
    gpio_set_intr_type(GPIO_FUNC_KEY2, GPIO_INTR_ANYEDGE);
    gpio_isr_handler_add(GPIO_FUNC_KEY2, SkBspFuncKeyIsr, &g_skGpioDev.funcKey2);
    gpio_intr_enable(GPIO_FUNC_KEY2);
    g_skGpioDev.funcKey2.gpio = GPIO_FUNC_KEY2;

    ioConf.pin_bit_mask = (1ULL << GPIO_FUNC_KEY3);
    gpio_config(&ioConf);
    gpio_set_intr_type(GPIO_FUNC_KEY3, GPIO_INTR_ANYEDGE);
    gpio_isr_handler_add(GPIO_FUNC_KEY3, SkBspFuncKeyIsr, &g_skGpioDev.funcKey3);
    gpio_intr_enable(GPIO_FUNC_KEY3);
    g_skGpioDev.funcKey3.gpio = GPIO_FUNC_KEY3;
#endif
    return;
}

int32_t SkBspReadUserInput() {
     // 返回 GPIO12 的电平状态，0 表示低电平，1 表示高电平
    return g_skGpioDev.keyInput;
}

void SkBspHeadPhoneGpioInit() {
#ifdef CONFIG_HEADPHONE_ENABLE
    gpio_config_t ioConf = {};

    ioConf.pin_bit_mask = (1ULL << GPIO_PHONE_INT);
    ioConf.mode = GPIO_MODE_INPUT;
    ioConf.pull_up_en = GPIO_PULLUP_DISABLE;
    ioConf.pull_down_en = GPIO_PULLDOWN_DISABLE;
    ioConf.intr_type = GPIO_INTR_DISABLE;
    gpio_config(&ioConf);
#endif
    return;
}

void SkBspVibrationGpioInit() {
#ifdef CONFIG_VIBRATION_ENABLE
    gpio_config_t ioConf = {};

    ioConf.pin_bit_mask = (1ULL << SHAKE_SENSOR_GPIO);
    ioConf.mode = GPIO_MODE_INPUT;
    ioConf.pull_up_en = GPIO_PULLUP_DISABLE;
    ioConf.pull_down_en = GPIO_PULLDOWN_DISABLE;
    ioConf.intr_type = GPIO_INTR_DISABLE;
    gpio_config(&ioConf);
#endif
    return;
}

int32_t SkBspGetHeadPhoneState() {
#ifdef CONFIG_HEADPHONE_ENABLE
    return gpio_get_level(GPIO_PHONE_INT);
#else
    return 0;
#endif
}

int32_t SkBspGetVibrationState() {
#ifdef CONFIG_VIBRATION_ENABLE
    return gpio_get_level(SHAKE_SENSOR_GPIO);
#else
    return 0;
#endif
}

int32_t SkBspGetFuncKeyState() {
#if (CONFIG_FUNC_KEY_MODE == CONFIG_SK_NORMAL_KEY)
    if (g_skGpioDev.funcKey1.keyState == 1) {
        return 1;
    } else if (g_skGpioDev.funcKey2.keyState == 1) {
        return 2;
    } else if (g_skGpioDev.funcKey3.keyState == 1) {
        return 3;
    }
#endif
    return 0;
}
