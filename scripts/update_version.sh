#!/bin/bash

# 版本更新脚本
# 使用方法: ./update_version.sh [major|minor|patch] 或 ./update_version.sh x.y.z

VERSION_FILE="version.txt"

# 检查版本文件是否存在
if [ ! -f "$VERSION_FILE" ]; then
    echo "v1.0.0" > "$VERSION_FILE"
    echo "创建初始版本文件: v1.0.0"
fi

# 读取当前版本
CURRENT_VERSION=$(cat "$VERSION_FILE" | tr -d '\n')
echo "当前版本: $CURRENT_VERSION"

# 解析版本号（去掉v前缀）
VERSION_NUM=${CURRENT_VERSION#v}
IFS='.' read -r MAJOR MINOR PATCH <<< "$VERSION_NUM"

# 如果参数是具体版本号
if [[ $1 =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    NEW_VERSION="v$1"
    echo "$NEW_VERSION" > "$VERSION_FILE"
    echo "版本已更新为: $NEW_VERSION"
    exit 0
fi

# 根据参数更新版本
case $1 in
    "major")
        MAJOR=$((MAJOR + 1))
        MINOR=0
        PATCH=0
        ;;
    "minor")
        MINOR=$((MINOR + 1))
        PATCH=0
        ;;
    "patch")
        PATCH=$((PATCH + 1))
        ;;
    *)
        echo "使用方法:"
        echo "  $0 major    # 主版本号+1 (x.0.0)"
        echo "  $0 minor    # 次版本号+1 (x.y.0)"
        echo "  $0 patch    # 补丁版本号+1 (x.y.z)"
        echo "  $0 x.y.z    # 设置为指定版本"
        echo ""
        echo "当前版本: $CURRENT_VERSION"
        exit 1
        ;;
esac

NEW_VERSION="v$MAJOR.$MINOR.$PATCH"
echo "$NEW_VERSION" > "$VERSION_FILE"
echo "版本已更新为: $NEW_VERSION"

# 显示版本变化
echo ""
echo "版本变化:"
echo "  $CURRENT_VERSION -> $NEW_VERSION"
echo ""
echo "下一步:"
echo "1. 编译固件: idf.py build"
echo "2. 上传到OTA服务器"
echo "3. 触发OTA更新"
