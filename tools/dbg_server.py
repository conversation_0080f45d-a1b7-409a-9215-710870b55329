import socket
import os
import signal

# 定义保存文件的路径
SAVE_DIR = "./received_data"
if not os.path.exists(SAVE_DIR):
    os.makedirs(SAVE_DIR)

# 初始化变量
file_index = 0
data_buffer = b""

# 创建 TCP 套接字
server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
server_socket.bind(("0.0.0.0", 9529))  # 绑定到所有可用的网络接口
server_socket.listen(5)
print("TCP Server is running on port 9529. Waiting for connections...")

def save_data():
    print("Save data")
    """将缓冲区中的数据保存为文件"""
    global file_index, data_buffer
    if data_buffer:
        file_path = os.path.join(SAVE_DIR, f"data_{file_index}.p3")
        with open(file_path, "wb") as f:
            f.write(data_buffer)
        print(f"Data saved to {file_path}")
        file_index += 1
        data_buffer = b""

def signal_handler(signal, frame):
    """处理 Ctrl+C 信号"""
    print("\nReceived Ctrl+C. Shutting down the server...")
    save_data()  # 保存当前缓冲区中的数据
    server_socket.close()
    print("Server closed.")
    exit(0)

# 注册信号处理函数
signal.signal(signal.SIGINT, signal_handler)

try:
    client_socket, client_address = server_socket.accept()
    print(f"Connection from {client_address}")
    while True:
        try:
            data = client_socket.recv(1024)
            if not data:
                break
            data_buffer += data
        except ConnectionResetError:
            print("Connection reset by peer.")
            break
    client_socket.close()
    save_data()  # 每次客户端断开连接后保存数据
except Exception as e:
    print(f"An error occurred: {e}")
finally:
    save_data()  # 确保所有数据都被保存
    server_socket.close()
    print("Server closed.")