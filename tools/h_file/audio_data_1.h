#ifndef AUDIO_DATA_1_H
#define AUDIO_DATA_1_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfo1[] = {0, 24, 84, 203, 315, 422, 552, 668, 703, 728};
const uint8_t g_audioData1[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x38, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x6A, 0xCC, 0xB5, 0x11, 0x50, 0xB5, 0x7E, 0xEB, 0x7F, 0x1F, 0xE2, 0xEC, 0x1E, 0x7B, 0x3D, 0xAF, 0xD0, 0x6A, 0x7C, 0xA8, 0xE2, 0xD8, 0x99, 0x30, 0x1F, 0xD0, 0xB6, 0x0B, 0x0A, 0x41, 0xA4, 0x82, 0xE0, 0x88, 0x7A, 0x6B, 0x16, 0xAC, 0xAF, 0x94, 0xF2, 0xA0, 0x4F, 0x00, 0x00, 0x73, 0x58, 0xE9, 0x99, 0xD5, 0x8B, 0xC4, 0x64, 0x74, 0xA2, 0xDC, 0xC6, 0x3A, 0xDE, 0x8B, 0x02, 0xA9, 0xC9, 0xBB, 0x0D, 0xC6, 0xFF, 0xB8, 0x76, 0x52, 0x28, 0xB2, 0x07, 0xB3, 0xB1, 0xAD, 0xDA, 0xBB, 0x19, 0xDC, 0xE1, 0x05, 0x5E, 0xE6, 0x15, 0x43, 0x51, 0x98, 0xD8, 0x4F, 0xF3, 0x74, 0x6C, 0x5E, 0xD1, 0x9E, 0xF5, 0xFE, 0x35, 0xDD, 0xA6, 0xFE, 0xD0, 0xBD, 0x8D, 0xBE, 0x82, 0xE9, 0xE0, 0x8B, 0x9C, 0x1C, 0x5A, 0xFD, 0x3A, 0x3A, 0x87, 0xC8, 0x48, 0x0A, 0x12, 0x82, 0x93, 0xA2, 0xEB, 0xFF, 0x79, 0xE6, 0x1B, 0x0C, 0xA1, 0xDA, 0xFB, 0x86, 0xAD, 0xDA, 0xA1, 0x64, 0x65, 0xE4, 0x26, 0x03, 0xF3, 0x2D, 0x36, 0x6A, 0x7B, 0xF0, 0x41, 0xD0, 0x29, 0xAF, 0xD7, 0x3D, 0xB9, 0x4A, 0x6B, 0x43, 0x36, 0xE8, 0x7A, 0x4F, 0x00, 0x00, 0x6C, 0x58, 0xEB, 0x5F, 0xFC, 0x9D, 0x47, 0x0A, 0x64, 0xB7, 0x09, 0xD0, 0x91, 0x54, 0xFE, 0x48, 0x0A, 0x58, 0xAE, 0xE9, 0x49, 0x7C, 0xFD, 0x83, 0x77, 0xCA, 0x2E, 0x7D, 0x6A, 0x7C, 0x6C, 0xB9, 0xA6, 0xDF, 0x52, 0x32, 0x17, 0x51, 0xDC, 0x23, 0x20, 0x33, 0xD8, 0x99, 0xC9, 0x39, 0xE2, 0x00, 0x98, 0x2E, 0xE6, 0x55, 0xF2, 0x2A, 0x7B, 0xB0, 0x5C, 0x3D, 0x39, 0x97, 0x39, 0xE4, 0xFC, 0xFC, 0x80, 0x99, 0x57, 0xBB, 0x03, 0xB4, 0xFE, 0x2B, 0x8A, 0x44, 0x9E, 0x0F, 0xF6, 0x9A, 0x1B, 0xF3, 0xF5, 0x4C, 0xBB, 0x9C, 0xE1, 0xA8, 0x8F, 0xCA, 0x34, 0x8E, 0x01, 0x10, 0xCA, 0x3E, 0xF1, 0xCC, 0x28, 0x61, 0x7C, 0x8F, 0x97, 0x99, 0x02, 0xBB, 0xC6, 0xDA, 0x8C, 0x67, 0xC0, 0x4F, 0x00, 0x00, 0x67, 0x58, 0xED, 0x4A, 0x3D, 0x9A, 0x6E, 0x38, 0xFF, 0x50, 0xAB, 0xCE, 0x0A, 0x5E, 0x9B, 0xB6, 0x6C, 0xBA, 0x95, 0x9A, 0xB5, 0xDD, 0xAA, 0x24, 0x8F, 0x3F, 0x56, 0xD9, 0x85, 0x65, 0xA1, 0xDB, 0x96, 0xD8, 0xCB, 0xB9, 0xEE, 0xE6, 0x25, 0xF3, 0xB0, 0x43, 0x20, 0xB0, 0x02, 0xCA, 0xAF, 0x31, 0xEA, 0xC3, 0xB0, 0x34, 0x9A, 0x4F, 0x58, 0x69, 0x8B, 0x79, 0x22, 0xCB, 0x6B, 0x6D, 0xBF, 0x43, 0xC8, 0x24, 0xED, 0x59, 0x1B, 0x45, 0xAB, 0xBC, 0xC8, 0x8C, 0xC6, 0x6C, 0x0F, 0x45, 0x7C, 0x8C, 0xD0, 0xC5, 0x30, 0x40, 0xDA, 0x8C, 0x53, 0xBD, 0xD7, 0xE8, 0xF0, 0x62, 0x97, 0x0C, 0x79, 0xCC, 0xCD, 0x5F, 0xF1, 0x20, 0x6D, 0xFD, 0x41, 0x01, 0x4F, 0x00, 0x00, 0x7E, 0x58, 0xEE, 0x30, 0x44, 0xF9, 0x20, 0x0D, 0x7C, 0xCA, 0x56, 0xE1, 0x45, 0xE7, 0x6A, 0x2D, 0x33, 0x33, 0x4E, 0x55, 0x69, 0x1F, 0x5E, 0xA4, 0x56, 0x03, 0x44, 0xD8, 0x08, 0x12, 0xC7, 0x33, 0xBF, 0x8E, 0x54, 0xBB, 0x63, 0xBD, 0xA8, 0x0B, 0x6F, 0xE1, 0x4C, 0x49, 0xF2, 0x3F, 0x47, 0x98, 0x00, 0xE4, 0x8E, 0x5D, 0x64, 0x01, 0x36, 0x86, 0x2F, 0x2C, 0xE6, 0xB0, 0x0E, 0x53, 0xCB, 0x06, 0x11, 0x98, 0x8F, 0x88, 0x7B, 0xE1, 0x12, 0x8A, 0x3C, 0x3D, 0x3F, 0x85, 0xA3, 0x36, 0x8D, 0xCC, 0x74, 0x92, 0xD1, 0xD6, 0x40, 0x7A, 0x6D, 0xED, 0xB4, 0x79, 0x62, 0xDF, 0x24, 0x53, 0x7F, 0x49, 0x2D, 0x57, 0x55, 0xD9, 0x76, 0x75, 0x8E, 0x22, 0x73, 0x30, 0x0E, 0x2E, 0x5D, 0xE3, 0x0B, 0xC2, 0xB1, 0xA5, 0xD6, 0x07, 0x69, 0x34, 0x19, 0x90, 0x29, 0x7C, 0xE9, 0x56, 0xB7, 0x89, 0x20, 0x4F, 0x00, 0x00, 0x70, 0x58, 0xEE, 0xFB, 0x47, 0xE8, 0x13, 0xC7, 0x5E, 0xEF, 0xE9, 0x7B, 0x39, 0x42, 0x87, 0xD3, 0x11, 0xD4, 0x85, 0xAE, 0x96, 0x7A, 0x78, 0xE3, 0x52, 0x40, 0x83, 0x0E, 0xBB, 0x6A, 0x0F, 0xD6, 0x7B, 0x00, 0x63, 0xF6, 0x12, 0xFC, 0xBE, 0x82, 0xEA, 0x77, 0xC8, 0xFD, 0x62, 0x22, 0x5B, 0xC4, 0xF4, 0x87, 0x43, 0xDE, 0xA2, 0xC8, 0xB8, 0xD9, 0x5C, 0xAE, 0xF8, 0x03, 0x3F, 0x39, 0xE6, 0xFE, 0x05, 0x34, 0xC0, 0x1D, 0x14, 0xBF, 0x1C, 0x3F, 0x64, 0xC4, 0x93, 0xD4, 0x9C, 0xDD, 0xB6, 0x6E, 0xEF, 0x63, 0xCE, 0x68, 0x92, 0xC2, 0x4B, 0x08, 0x86, 0x94, 0x6A, 0x26, 0xDC, 0x95, 0xDF, 0xEC, 0x64, 0x23, 0x05, 0xE3, 0xF6, 0x92, 0x93, 0x0E, 0xFD, 0x12, 0xF8, 0x46, 0xF0, 0xFB, 0x32, 0xA9, 0x50, 0x4F, 0x00, 0x00, 0x1F, 0x58, 0x0A, 0xAA, 0x60, 0x55, 0xE5, 0x11, 0xF1, 0xB9, 0xBB, 0xC9, 0xA6, 0x82, 0xC0, 0x75, 0x71, 0xEF, 0x77, 0x1B, 0x62, 0x5D, 0xD4, 0x0F, 0x53, 0x1A, 0x64, 0x21, 0xD9, 0xBE, 0xD3, 0x66, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20
};

#endif // AUDIO_DATA_1_H