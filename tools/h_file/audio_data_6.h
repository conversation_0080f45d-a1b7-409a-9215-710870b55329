#ifndef AUDIO_DATA_6_H
#define AUDIO_DATA_6_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfo6[] = {0, 24, 49, 112, 249, 382, 506, 641, 800, 909};
const uint8_t g_audioData6[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x3B, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x0F, 0xEE, 0xDF, 0x50, 0xB4, 0xB9, 0xD6, 0xAD, 0xEA, 0xE1, 0xAA, 0x16, 0x8B, 0x9E, 0x91, 0x1D, 0x8E, 0xF4, 0x91, 0xE2, 0x97, 0xD0, 0xF0, 0x10, 0xA2, 0x55, 0xE1, 0x09, 0xFE, 0x9C, 0x34, 0x77, 0x50, 0xE3, 0x8F, 0xA5, 0x6D, 0xB0, 0xB4, 0x7D, 0xD1, 0xA2, 0x75, 0x53, 0x1F, 0x8F, 0x32, 0x72, 0x17, 0x96, 0xB3, 0x4F, 0x00, 0x00, 0x85, 0x58, 0xE8, 0x6B, 0x3F, 0x8C, 0x9E, 0xBE, 0xAF, 0x45, 0x39, 0x5A, 0xA3, 0x32, 0xD8, 0xCA, 0x66, 0xB0, 0xB5, 0x38, 0x68, 0x5C, 0x2E, 0xF1, 0x93, 0xD7, 0xF6, 0xFD, 0xB7, 0x6C, 0xCA, 0x8F, 0xB3, 0x0C, 0x93, 0xC4, 0x6D, 0xA6, 0x21, 0xBC, 0x4A, 0x20, 0xC2, 0xA7, 0xBE, 0x96, 0x14, 0x71, 0x6F, 0xA1, 0xD0, 0x62, 0x45, 0x73, 0xDF, 0x81, 0xBD, 0xB5, 0x2D, 0xFF, 0xE3, 0xCB, 0xE4, 0x27, 0x72, 0x08, 0x27, 0xCD, 0xB2, 0xB0, 0x26, 0x5D, 0xB1, 0x96, 0xEC, 0xC8, 0x15, 0x31, 0xB7, 0xF9, 0x37, 0x33, 0xB5, 0x4E, 0xBC, 0x1B, 0xC1, 0xAD, 0xB5, 0xA6, 0x62, 0x4A, 0x75, 0x0A, 0x3C, 0x43, 0x8B, 0x98, 0xC1, 0x12, 0x58, 0x48, 0x44, 0x22, 0xC0, 0x16, 0x76, 0xF5, 0xCC, 0x6E, 0xFE, 0x2B, 0xA3, 0x6C, 0xDF, 0x99, 0x8D, 0x62, 0xD4, 0xEF, 0xBC, 0x72, 0x57, 0xC3, 0x95, 0xEF, 0x7E, 0x48, 0x88, 0x4F, 0x52, 0x22, 0xED, 0x80, 0x4F, 0x00, 0x00, 0x81, 0x58, 0xED, 0x5C, 0x82, 0x02, 0x81, 0x2C, 0x20, 0xDB, 0x2E, 0xDA, 0x12, 0x77, 0x29, 0x74, 0xEE, 0xD7, 0x01, 0xA6, 0xC4, 0x43, 0x9C, 0xEF, 0xEE, 0x95, 0x78, 0x87, 0x2A, 0xD0, 0x18, 0x05, 0xDA, 0xD3, 0x26, 0x37, 0x48, 0x24, 0x25, 0x26, 0xF5, 0x8B, 0x8A, 0x8D, 0xD0, 0x76, 0xA6, 0x27, 0x81, 0x5A, 0x63, 0x42, 0x54, 0xC7, 0x80, 0x95, 0x65, 0x5F, 0x4C, 0x31, 0x2B, 0xA1, 0x81, 0x3B, 0x43, 0x5E, 0x0D, 0xBC, 0xAE, 0x75, 0x51, 0x71, 0x46, 0xC7, 0x5A, 0x1D, 0x43, 0x94, 0x0D, 0xE3, 0x0E, 0x7D, 0x3D, 0x4F, 0x50, 0x08, 0x17, 0xE3, 0xF8, 0xF8, 0x97, 0xFD, 0x89, 0x51, 0x7D, 0x7E, 0x79, 0xC0, 0x74, 0xBC, 0x96, 0x3E, 0xA7, 0x28, 0xFD, 0xDD, 0x91, 0xCC, 0xC9, 0x5E, 0xFD, 0x0A, 0x77, 0x6E, 0x2D, 0xA5, 0x61, 0xC6, 0xF2, 0xF5, 0xEB, 0x93, 0xB2, 0x44, 0x8C, 0x13, 0xC1, 0xEB, 0x56, 0xA0, 0x4F, 0x00, 0x00, 0x78, 0x58, 0xEE, 0x3F, 0xA8, 0xA1, 0x8C, 0xB4, 0x22, 0x32, 0x77, 0x84, 0xEB, 0xAA, 0x92, 0x86, 0x4C, 0x3D, 0x6C, 0x65, 0xCB, 0xC3, 0x9C, 0x7B, 0x85, 0x41, 0xE4, 0x4B, 0x33, 0x91, 0xC4, 0xCE, 0x30, 0xDD, 0x95, 0xBF, 0x0F, 0xF6, 0x46, 0x33, 0x6E, 0x84, 0xCD, 0xC7, 0xD3, 0xDD, 0x94, 0xCB, 0x87, 0x67, 0x07, 0x11, 0xE4, 0x3C, 0xA6, 0x3E, 0x89, 0x51, 0xCC, 0xDC, 0xA3, 0x88, 0xF1, 0xE1, 0x04, 0x45, 0x85, 0xC3, 0x64, 0x64, 0x28, 0x78, 0x78, 0x73, 0xD7, 0x80, 0x72, 0xBA, 0xDD, 0x2B, 0xED, 0xD6, 0x99, 0x25, 0x44, 0x78, 0x19, 0x59, 0xFD, 0x60, 0xDB, 0xB5, 0x76, 0xA8, 0x80, 0xAF, 0xDA, 0xC6, 0x8E, 0x85, 0xCB, 0x4D, 0x30, 0x93, 0x63, 0xB5, 0x2F, 0x1C, 0x32, 0x05, 0xB1, 0xBC, 0xDC, 0x9F, 0x16, 0x0B, 0x6A, 0x24, 0xD9, 0x3B, 0xF6, 0x4F, 0x00, 0x00, 0x83, 0x58, 0xEE, 0x5D, 0x00, 0x58, 0xCF, 0x50, 0xF9, 0x8B, 0x3D, 0x24, 0xA5, 0x86, 0x2B, 0x24, 0x5A, 0xE3, 0xBC, 0xC3, 0x59, 0x6B, 0xBA, 0x16, 0x40, 0xC8, 0xC4, 0x56, 0xFC, 0xA7, 0x8F, 0xEC, 0xB5, 0xAA, 0xF7, 0x94, 0xA0, 0xC5, 0xEA, 0x9C, 0x2B, 0xAA, 0x2A, 0xE8, 0xB7, 0x4D, 0x67, 0x53, 0x22, 0x96, 0x26, 0x5A, 0xAD, 0x80, 0x6F, 0x81, 0x02, 0x73, 0xBC, 0x6F, 0x14, 0xCC, 0xDA, 0x6E, 0x38, 0xF1, 0xF5, 0xCC, 0x36, 0xEE, 0xEA, 0x11, 0xBB, 0xD5, 0xB1, 0xBF, 0x3F, 0x28, 0x90, 0x7F, 0xA8, 0x0E, 0x9F, 0x93, 0x81, 0x4D, 0xD2, 0xF7, 0x74, 0x02, 0xD4, 0xD2, 0xD0, 0xF7, 0x58, 0x99, 0x14, 0x4E, 0x23, 0x1F, 0x49, 0xB7, 0x8E, 0x6C, 0x91, 0x26, 0x1C, 0xFE, 0x07, 0xA2, 0x0C, 0xAE, 0x0A, 0xC3, 0x79, 0xD1, 0x95, 0x15, 0x73, 0xE8, 0x6F, 0xE3, 0xEF, 0x9B, 0xE8, 0x60, 0x45, 0x8C, 0xC3, 0x75, 0x9D, 0xAC, 0x4F, 0x00, 0x00, 0x9B, 0x58, 0xED, 0x6C, 0x83, 0x3E, 0x3F, 0x75, 0x3B, 0x28, 0xB8, 0xE6, 0xE8, 0x47, 0xC6, 0xD9, 0x41, 0xE9, 0x1D, 0xC1, 0x5A, 0xEE, 0xEB, 0xA1, 0x70, 0x70, 0xD4, 0x44, 0x36, 0xCE, 0xE8, 0xCC, 0xB3, 0x33, 0x1C, 0xA1, 0x98, 0x6D, 0x1B, 0x8C, 0x1A, 0x58, 0x92, 0xC5, 0x1D, 0x76, 0x33, 0x4D, 0x0A, 0xAD, 0xD1, 0xE4, 0x0E, 0xA7, 0xC5, 0x03, 0x44, 0xD1, 0x8F, 0x1E, 0x55, 0x5C, 0xEC, 0x64, 0x47, 0x35, 0xBC, 0x88, 0x23, 0x15, 0xE2, 0x0A, 0xF6, 0xA3, 0x8E, 0x59, 0x2A, 0x41, 0x5D, 0xB5, 0xC7, 0x74, 0x10, 0x1A, 0xA7, 0xC5, 0x9F, 0xD3, 0x80, 0xD7, 0xE1, 0x45, 0xB8, 0xAC, 0x88, 0xE8, 0x89, 0x83, 0x5A, 0xAE, 0x70, 0xE8, 0xC6, 0x37, 0xFB, 0x5F, 0xC9, 0x35, 0x28, 0x17, 0x4B, 0xCD, 0x30, 0x29, 0x04, 0x8E, 0xDE, 0x5A, 0x25, 0x01, 0xAF, 0x49, 0x98, 0x8D, 0x9C, 0xC8, 0x5C, 0xBA, 0x47, 0x1A, 0x35, 0x69, 0xC5, 0x54, 0x29, 0xEB, 0xFF, 0x4A, 0x8F, 0x9F, 0x39, 0xAA, 0xEE, 0x3D, 0x2D, 0x92, 0x7D, 0x8B, 0xD5, 0x35, 0x9A, 0xD6, 0x39, 0x0F, 0xCF, 0xCB, 0x4F, 0x00, 0x00, 0x69, 0x58, 0xC8, 0x3E, 0xA7, 0xF3, 0xAE, 0x17, 0x84, 0x9C, 0x10, 0x72, 0x5C, 0x26, 0x00, 0xC7, 0x87, 0x30, 0x8A, 0x30, 0xF4, 0xD0, 0x52, 0x43, 0x12, 0x32, 0xFE, 0x02, 0x70, 0x0D, 0x89, 0x14, 0xC9, 0x13, 0x39, 0xDF, 0xF3, 0xFA, 0x8F, 0x71, 0x24, 0x00, 0xC0, 0xDE, 0x39, 0x22, 0x47, 0x12, 0x78, 0xA2, 0x7D, 0x47, 0x07, 0x24, 0xA2, 0xA8, 0x72, 0x1A, 0x8D, 0x4D, 0x63, 0x33, 0xF6, 0x99, 0xDF, 0x4E, 0x98, 0x1C, 0xF6, 0xA4, 0x6C, 0x0B, 0x47, 0x5C, 0x69, 0x59, 0xE4, 0x8A, 0x78, 0x44, 0x94, 0xF8, 0xAA, 0x72, 0x4D, 0xC2, 0xC5, 0x04, 0xB1, 0x18, 0x1B, 0x03, 0xA8, 0x98, 0xA9, 0xAB, 0x30, 0x00, 0xDA, 0x92, 0x50, 0x40, 0xCC, 0xA7, 0x36, 0xD0, 0x4F, 0x00, 0x00, 0x21, 0x58, 0x03, 0x76, 0xE2, 0xC0, 0x8A, 0x0C, 0xC4, 0x41, 0xDC, 0x27, 0x6D, 0xCC, 0xA1, 0x30, 0x27, 0xDD, 0xFC, 0x89, 0x4E, 0xD9, 0xB6, 0x66, 0x40, 0x00, 0xA0, 0x95, 0x62, 0x01, 0x6F, 0x4E, 0xBE, 0x9A
};

#endif // AUDIO_DATA_6_H