#ifndef AUDIO_DATA_REBOOT_H
#define AUDIO_DATA_REBOOT_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfoReboot[] = {0, 24, 49, 114, 242, 355, 496, 641, 793, 924, 1056, 1177, 1312, 1439, 1571, 1646};
const uint8_t g_audioDataReboot[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x3D, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x10, 0x67, 0x27, 0x68, 0x6B, 0x8B, 0x54, 0x0D, 0x60, 0x3E, 0x65, 0x68, 0x23, 0x72, 0x24, 0xAC, 0x63, 0xAA, 0x98, 0xB5, 0x20, 0x0C, 0x02, 0x45, 0xEC, 0xB8, 0x88, 0x46, 0x2E, 0x7E, 0x3B, 0x0D, 0x35, 0xB7, 0xDC, 0x38, 0x8E, 0x27, 0xD8, 0x46, 0x24, 0x8D, 0x0E, 0x10, 0x05, 0xC5, 0x0A, 0x82, 0x16, 0x26, 0x77, 0xAF, 0xC0, 0x4F, 0x00, 0x00, 0x7C, 0x58, 0xE0, 0xFE, 0x22, 0xA4, 0x85, 0xB8, 0x34, 0x54, 0x05, 0x54, 0xFE, 0xA0, 0x47, 0x4F, 0x1F, 0xDF, 0xC8, 0x92, 0x61, 0xCB, 0xB0, 0xFC, 0x09, 0x3E, 0x1A, 0x58, 0xF2, 0xEF, 0xC5, 0x65, 0x70, 0xCE, 0xD6, 0xEA, 0x1C, 0x4F, 0x38, 0x5A, 0xE8, 0x94, 0xA0, 0x18, 0xE6, 0xCF, 0xC3, 0x47, 0xF0, 0xEB, 0xA5, 0xB7, 0x4F, 0x63, 0xBA, 0xC3, 0xD0, 0x07, 0x54, 0xB1, 0x6B, 0xA4, 0xBE, 0xB2, 0x05, 0x5F, 0x28, 0x78, 0x7D, 0x7A, 0xB7, 0x5E, 0x58, 0x46, 0x7D, 0xA9, 0x14, 0xCB, 0x97, 0x6B, 0x80, 0xD5, 0x88, 0xA2, 0x32, 0xD6, 0x37, 0x70, 0x79, 0xCC, 0x42, 0xDE, 0x6A, 0xEA, 0x6B, 0xA7, 0xEF, 0x61, 0xCB, 0x0B, 0xAA, 0x05, 0x9F, 0xB9, 0xCF, 0x62, 0xD2, 0xA5, 0xEC, 0xE5, 0x31, 0xAF, 0xBD, 0xE7, 0x17, 0x2C, 0x0C, 0x39, 0xDB, 0x24, 0xB3, 0x89, 0x81, 0xE9, 0xD8, 0x4F, 0x00, 0x00, 0x6D, 0x58, 0xEB, 0x4E, 0xF1, 0xCA, 0x44, 0x2C, 0xC6, 0x62, 0x6E, 0xA5, 0x9F, 0x44, 0x60, 0x42, 0x7C, 0xDC, 0x6B, 0x33, 0x75, 0xA3, 0x93, 0x08, 0x1B, 0xC0, 0x54, 0x7F, 0xE3, 0xAC, 0x53, 0xC0, 0x1C, 0x1F, 0x72, 0xD6, 0xFF, 0xD3, 0x14, 0xD5, 0x76, 0xA5, 0xA8, 0x52, 0x1C, 0xF2, 0x10, 0xDA, 0x86, 0xBE, 0x9E, 0x57, 0x69, 0xD6, 0xAF, 0xC7, 0x0D, 0x87, 0x6B, 0x9F, 0x31, 0xEB, 0x70, 0xDF, 0xCB, 0xDA, 0x7E, 0x31, 0xBD, 0xFC, 0x18, 0xD4, 0x90, 0x20, 0x39, 0x26, 0x01, 0x13, 0xFC, 0x3C, 0xCC, 0xC3, 0x86, 0x00, 0xC5, 0xB6, 0x55, 0x44, 0xF7, 0xC0, 0x64, 0x66, 0x48, 0xE9, 0xBB, 0x6F, 0xE8, 0x0D, 0xB5, 0x0E, 0xBB, 0xAA, 0xED, 0x02, 0xB3, 0xCF, 0x35, 0x1F, 0xAE, 0xEA, 0x4F, 0x00, 0x00, 0x89, 0x58, 0xEB, 0x17, 0x5E, 0x57, 0xED, 0xB7, 0x3C, 0x79, 0xBB, 0x63, 0xB0, 0xEB, 0x0D, 0xAE, 0x31, 0x55, 0xF9, 0x7A, 0xD0, 0x48, 0x44, 0xFD, 0x05, 0xED, 0x63, 0x07, 0x71, 0xCD, 0x0D, 0xA1, 0x97, 0x87, 0x1A, 0x5F, 0xF9, 0xFB, 0xDD, 0x9B, 0x87, 0x62, 0x49, 0x48, 0xD9, 0xB9, 0x88, 0x90, 0x5A, 0x84, 0x76, 0x02, 0x18, 0xF7, 0xF0, 0x10, 0xCF, 0x9D, 0xAA, 0xB6, 0xC6, 0x8F, 0xC7, 0x85, 0xCA, 0x04, 0xF4, 0xFF, 0x75, 0xBE, 0xA9, 0xF3, 0x86, 0x04, 0xFE, 0x42, 0x1B, 0x62, 0xBB, 0x21, 0x14, 0x0F, 0xA4, 0xF5, 0x7B, 0x72, 0xB4, 0x67, 0x13, 0x54, 0x3C, 0xCE, 0xB1, 0xB7, 0xC7, 0xC4, 0xEC, 0xC5, 0xE2, 0xDF, 0x14, 0xD3, 0x43, 0xA5, 0xBB, 0xFB, 0x6D, 0xCB, 0x51, 0x0D, 0x52, 0x55, 0x24, 0x65, 0x49, 0x7C, 0xCD, 0xE6, 0x5E, 0x09, 0x2E, 0xEB, 0xBE, 0x82, 0x6C, 0x7F, 0x0F, 0x65, 0xFA, 0x7D, 0xC2, 0xF0, 0x6E, 0x45, 0x88, 0xE9, 0x40, 0xC0, 0x4F, 0x00, 0x00, 0x8D, 0x58, 0xE9, 0xA4, 0x8A, 0xB6, 0x99, 0x99, 0x37, 0xF9, 0x61, 0xB5, 0xC0, 0xE1, 0x11, 0x4A, 0x5A, 0x6B, 0xF0, 0xC1, 0x6D, 0x44, 0x52, 0x73, 0x82, 0x17, 0x08, 0xB0, 0xC4, 0x3E, 0xA9, 0xE9, 0x26, 0x06, 0x87, 0x88, 0x63, 0x87, 0x7C, 0x55, 0x44, 0x8B, 0x45, 0x4A, 0x6A, 0x1D, 0x99, 0x09, 0xB2, 0x18, 0xC4, 0x16, 0xD5, 0x67, 0xFD, 0x62, 0x5E, 0x50, 0xC6, 0x22, 0xB0, 0x3A, 0x1B, 0x47, 0x52, 0xEE, 0x20, 0x2F, 0x96, 0x8A, 0xA4, 0xB3, 0x31, 0xBB, 0xF3, 0x9B, 0x25, 0x9F, 0x63, 0x56, 0x4D, 0xB6, 0x00, 0x32, 0x8B, 0x28, 0x16, 0x0F, 0x59, 0x9C, 0x04, 0xE6, 0xF9, 0xE9, 0x5D, 0xB0, 0x89, 0x26, 0x46, 0xD0, 0xBB, 0x0A, 0xAB, 0xE8, 0x32, 0x93, 0x50, 0xCE, 0xC1, 0x6D, 0x75, 0x22, 0xAD, 0xF3, 0x1D, 0x10, 0xD3, 0xBF, 0xEF, 0xA0, 0x50, 0x9E, 0x2F, 0xDF, 0xED, 0x80, 0x40, 0xFA, 0x69, 0xDE, 0xC5, 0x18, 0x5B, 0x41, 0x9B, 0x5A, 0x49, 0xB9, 0x61, 0xBC, 0x2B, 0xA4, 0x4F, 0x00, 0x00, 0x94, 0x58, 0xE8, 0x0C, 0x00, 0x7C, 0xEC, 0x73, 0x13, 0x04, 0x46, 0x2F, 0xA2, 0x30, 0xD7, 0x1B, 0x09, 0xB7, 0xCF, 0x2A, 0xE7, 0xB7, 0x85, 0xDD, 0x74, 0xAA, 0xCE, 0x90, 0x84, 0x5B, 0xF7, 0x71, 0x3C, 0x30, 0x23, 0xF1, 0xF5, 0xF2, 0x41, 0x94, 0x7A, 0x94, 0xFB, 0x5C, 0x05, 0x82, 0xE9, 0x10, 0x7B, 0x79, 0xE2, 0x39, 0xF0, 0xFC, 0x3E, 0x22, 0xFA, 0xCB, 0xCC, 0x60, 0xE9, 0xF7, 0xA1, 0xBC, 0x94, 0x5D, 0x5B, 0x71, 0xFE, 0x03, 0xA6, 0x43, 0x7F, 0x45, 0x75, 0xCB, 0x3E, 0x5E, 0x4A, 0x6E, 0x3D, 0x17, 0x23, 0x8D, 0xCB, 0x70, 0x04, 0x9D, 0xCD, 0x48, 0x0A, 0x84, 0xA6, 0xE8, 0x7B, 0xD3, 0x8F, 0xD9, 0xE0, 0xFB, 0xB1, 0x7D, 0xAB, 0xCC, 0xE7, 0x9D, 0xE4, 0xDB, 0xA6, 0xC1, 0xB7, 0x75, 0x4B, 0x1F, 0x6D, 0x61, 0xC5, 0x6F, 0x4C, 0x20, 0x37, 0xFB, 0x2B, 0xBF, 0x46, 0x16, 0xCA, 0x32, 0xEE, 0xFC, 0xD5, 0xEE, 0x4F, 0x4C, 0xDB, 0x4C, 0x0B, 0x86, 0xBE, 0x43, 0x66, 0x5C, 0x78, 0x80, 0x90, 0x85, 0x8B, 0x60, 0x80, 0x4F, 0x00, 0x00, 0x7F, 0x58, 0xE5, 0x1A, 0x57, 0xA4, 0x5E, 0x6A, 0x09, 0x73, 0xC3, 0xC8, 0x35, 0x70, 0x2F, 0xDE, 0xDD, 0x6F, 0xD6, 0xF1, 0x6E, 0xE6, 0x34, 0xD2, 0x87, 0x8A, 0x5D, 0x59, 0xF6, 0x6A, 0x68, 0xC6, 0xDB, 0x92, 0x7D, 0x02, 0xCC, 0xF5, 0x68, 0xE8, 0x9E, 0x6B, 0x0B, 0x69, 0xDB, 0x06, 0x6E, 0x30, 0x29, 0xA1, 0xB5, 0x52, 0xEF, 0x61, 0xD4, 0x48, 0xDE, 0x3C, 0x2C, 0xDE, 0x86, 0xED, 0x3C, 0x40, 0xB5, 0x4B, 0xDD, 0x3F, 0x0F, 0x3A, 0xD9, 0xDC, 0xE0, 0x66, 0x90, 0x62, 0x4C, 0xB1, 0x3A, 0x89, 0x51, 0x06, 0xBB, 0x44, 0xD8, 0x79, 0x2D, 0x63, 0x52, 0xD2, 0x87, 0x13, 0x7C, 0xAA, 0xBE, 0x88, 0x10, 0x11, 0xD4, 0xA2, 0x31, 0xE0, 0x6E, 0xBD, 0x4C, 0xA3, 0x91, 0x20, 0xED, 0xC1, 0x71, 0x9C, 0x00, 0xFD, 0xF1, 0x57, 0xBD, 0xE2, 0xAB, 0x6D, 0xE2, 0x50, 0x68, 0x0C, 0x25, 0x8C, 0xEF, 0xFC, 0x4F, 0x00, 0x00, 0x80, 0x58, 0xE5, 0x27, 0x4F, 0x41, 0x16, 0x5E, 0x7F, 0xE1, 0xB0, 0xD9, 0x7E, 0x3B, 0x2A, 0x54, 0x0C, 0x5C, 0x23, 0xC4, 0x64, 0x2F, 0x91, 0x6C, 0xD5, 0x28, 0x74, 0xDF, 0x6B, 0xBB, 0xAD, 0x22, 0x1A, 0x6D, 0x73, 0x83, 0x25, 0x3E, 0x4B, 0x4D, 0x98, 0x90, 0xE2, 0x84, 0x2B, 0x21, 0x88, 0x82, 0x34, 0x1B, 0xA9, 0x50, 0xAD, 0xA9, 0xE0, 0x1F, 0xA9, 0x8D, 0x3B, 0xB3, 0x41, 0xE5, 0x81, 0x2A, 0x4E, 0xB9, 0x08, 0xD5, 0x10, 0x19, 0xD8, 0xE3, 0xC0, 0xA4, 0xB2, 0x17, 0x6D, 0x14, 0xA4, 0xC5, 0xB4, 0x5D, 0xAB, 0x0C, 0x25, 0x57, 0x50, 0x62, 0x30, 0x02, 0x70, 0x34, 0xAB, 0x5C, 0x2F, 0x29, 0xBA, 0x55, 0x2E, 0xFC, 0x15, 0xD2, 0x4E, 0x73, 0x6E, 0xBE, 0xDB, 0xCD, 0x7D, 0x65, 0xF3, 0x46, 0xF7, 0x15, 0x6A, 0x29, 0xBF, 0xDD, 0x1D, 0x83, 0x35, 0x2A, 0x1C, 0x10, 0xFB, 0xEE, 0x63, 0xB2, 0x5A, 0x4F, 0x00, 0x00, 0x75, 0x58, 0xED, 0x6C, 0x5F, 0xDC, 0xC1, 0x3E, 0xDD, 0xD0, 0x77, 0xFE, 0x46, 0xEF, 0x78, 0x1F, 0xD3, 0xE9, 0x6E, 0xD3, 0xC6, 0x97, 0x91, 0x19, 0x1A, 0x68, 0x23, 0x95, 0xA4, 0x00, 0xCF, 0x7B, 0xC6, 0xD0, 0xA7, 0x99, 0x96, 0xBE, 0xFB, 0xD4, 0x3F, 0x7C, 0x82, 0x15, 0x76, 0x71, 0x6E, 0x50, 0x14, 0xAE, 0xBC, 0xD5, 0x15, 0x02, 0xE8, 0x79, 0xBA, 0xDB, 0x3E, 0xF2, 0x08, 0xB8, 0x1D, 0x5D, 0x83, 0xCB, 0x0B, 0x09, 0x1C, 0x28, 0x35, 0xD3, 0x0B, 0x89, 0x8B, 0x64, 0xA1, 0x8B, 0xD1, 0x05, 0xE0, 0x3B, 0xDF, 0x67, 0x14, 0x05, 0x32, 0x5A, 0x60, 0x12, 0x7A, 0xB8, 0x28, 0x2C, 0x27, 0xE0, 0x49, 0x65, 0x5C, 0x92, 0x61, 0xE5, 0x84, 0x01, 0x6B, 0x1F, 0x3A, 0xD8, 0x71, 0xCF, 0x19, 0x0C, 0xD0, 0xCC, 0x1E, 0xAB, 0x65, 0x86, 0x4F, 0x00, 0x00, 0x83, 0x58, 0xEC, 0x18, 0x0C, 0xD7, 0xBE, 0xD7, 0x89, 0x4A, 0xBD, 0xA8, 0x61, 0xA0, 0x92, 0xA2, 0x22, 0xDC, 0x34, 0xDB, 0x61, 0xD0, 0x6C, 0xB7, 0x3C, 0x27, 0xDC, 0x66, 0x04, 0x4B, 0xCE, 0x5C, 0x59, 0xE6, 0x2F, 0xEE, 0x55, 0xAD, 0xAF, 0x64, 0xC2, 0xA5, 0x78, 0x59, 0xDA, 0x42, 0x1A, 0xED, 0x32, 0x78, 0xFB, 0xC9, 0x7D, 0x99, 0x3D, 0x45, 0xF0, 0xD7, 0x29, 0x13, 0x87, 0x6C, 0xA2, 0x67, 0x5F, 0x16, 0xC6, 0x50, 0x1A, 0xCA, 0xD8, 0x7B, 0x42, 0x3B, 0xC4, 0xB4, 0xBC, 0xDF, 0x5D, 0x37, 0x28, 0xF7, 0xB1, 0x96, 0x83, 0x48, 0x7D, 0x57, 0xF1, 0xBE, 0xC1, 0x50, 0x58, 0x9C, 0xB6, 0x2D, 0x44, 0xBC, 0xFE, 0x7F, 0x1F, 0xDC, 0x5E, 0xB7, 0x60, 0x95, 0xCE, 0x30, 0x3E, 0x15, 0x5C, 0xB4, 0x19, 0xF8, 0x99, 0x9F, 0xCF, 0xCE, 0x61, 0xDC, 0x7A, 0x31, 0x2D, 0xBF, 0x0C, 0xA6, 0x28, 0x5A, 0x81, 0x6B, 0x8F, 0xCE, 0x4F, 0x00, 0x00, 0x7B, 0x58, 0xED, 0x93, 0x0D, 0xF4, 0x6E, 0xC5, 0x85, 0x20, 0xDA, 0x90, 0x9B, 0x6F, 0x13, 0x2B, 0xF1, 0x83, 0x9D, 0xF0, 0x72, 0xE3, 0x89, 0x1F, 0xDF, 0x7C, 0x97, 0xE7, 0xAD, 0x62, 0xF3, 0xF9, 0xED, 0xA6, 0xAD, 0x7A, 0x9C, 0x66, 0x63, 0xF1, 0xB2, 0x0F, 0xDF, 0x4F, 0x17, 0x14, 0xDE, 0xF4, 0xC1, 0x1B, 0x81, 0x3B, 0x90, 0x4E, 0x58, 0xB0, 0x6A, 0xB8, 0x20, 0xB7, 0x22, 0xF2, 0xC8, 0x25, 0x68, 0x7A, 0x84, 0x00, 0x90, 0xE1, 0x2D, 0xF5, 0x63, 0xF7, 0xDF, 0xD0, 0x56, 0xAF, 0x9E, 0xFB, 0x2F, 0xFA, 0xDA, 0xA7, 0x96, 0xF9, 0x48, 0x48, 0xE0, 0xE7, 0x5C, 0x54, 0xB8, 0x70, 0xA9, 0x6F, 0xF0, 0x5B, 0xD2, 0xC9, 0x63, 0x7E, 0x74, 0xF4, 0xEF, 0x65, 0x47, 0x59, 0x27, 0xA4, 0xB8, 0xC6, 0xB2, 0xF2, 0x9A, 0xEE, 0x2F, 0x39, 0xE6, 0x64, 0xDB, 0x95, 0xAB, 0x40, 0x4F, 0x00, 0x00, 0x80, 0x58, 0xEE, 0xFC, 0xF8, 0xEA, 0xDF, 0x83, 0x2F, 0xF7, 0x5E, 0x18, 0xF4, 0xD0, 0x6B, 0x60, 0xCC, 0x6E, 0xFF, 0xAF, 0xB5, 0xC3, 0x54, 0xD7, 0xB4, 0x14, 0x1A, 0x1A, 0xA2, 0x24, 0x10, 0xA2, 0x6E, 0x02, 0x3C, 0x98, 0xB1, 0xFD, 0x5C, 0xD5, 0x99, 0x67, 0x97, 0xD5, 0xD2, 0xE4, 0xC5, 0xE7, 0x29, 0x64, 0x51, 0x99, 0x9B, 0xA2, 0xA1, 0xC9, 0xA0, 0x5A, 0xCC, 0x61, 0x9B, 0xB5, 0xC9, 0xB6, 0xE8, 0x59, 0x12, 0x70, 0x28, 0x79, 0xBC, 0xD7, 0x50, 0xCD, 0x0C, 0x70, 0xCD, 0xFB, 0xD8, 0xCC, 0x5C, 0x0A, 0x65, 0x9C, 0x25, 0xCF, 0x76, 0xB2, 0x4F, 0x38, 0xAD, 0xD3, 0x3A, 0x1E, 0x4F, 0xB7, 0xDD, 0x5B, 0xFD, 0x8D, 0xAB, 0xD5, 0x96, 0x4B, 0xE1, 0xB6, 0xBA, 0x2C, 0xB8, 0x68, 0xB3, 0x63, 0xE2, 0x7C, 0x1A, 0x1D, 0xF7, 0x93, 0xA6, 0xF0, 0xAE, 0x09, 0xE5, 0x71, 0xE5, 0x53, 0xFE, 0x1A, 0xAA, 0x4F, 0x00, 0x00, 0x47, 0x58, 0x0E, 0x8E, 0x49, 0x59, 0x47, 0xCD, 0x49, 0x34, 0xB9, 0xD3, 0xEF, 0x55, 0x26, 0xF4, 0x9B, 0x3B, 0x42, 0xE1, 0x46, 0x0D, 0x5A, 0xF9, 0xC2, 0x98, 0x3F, 0xFC, 0x6D, 0x8F, 0x6D, 0xB8, 0xCF, 0xB7, 0xC3, 0x39, 0x0C, 0xF0, 0xFA, 0x0E, 0x7E, 0x50, 0xDD, 0xD8, 0x8E, 0x2E, 0x2F, 0xBD, 0x84, 0x4A, 0xA2, 0xD0, 0xF4, 0xC8, 0x91, 0xAB, 0x15, 0x3D, 0xC6, 0x15, 0xEB, 0xB4, 0x84, 0xAC, 0x23, 0x22, 0xDF, 0x11, 0xD2, 0xFB, 0x94, 0xD0, 0x4F, 0x00, 0x00, 0x14, 0x58, 0x01, 0xF2, 0x5C, 0x90, 0xC9, 0x5C, 0x2F, 0x4F, 0x6F, 0x2A, 0xB5, 0xC4, 0xCC, 0x23, 0x09, 0x71, 0xF0, 0x3B, 0x78
};

#endif // AUDIO_DATA_REBOOT_H