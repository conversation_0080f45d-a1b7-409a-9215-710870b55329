#ifndef AUDIO_DATA_YAO_H
#define AUDIO_DATA_YAO_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfoyao[] = {0, 24, 49, 107, 261, 392, 502, 633, 758, 801};
const uint8_t g_audioDatayao[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x36, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x0A, 0xEC, 0xF7, 0xF0, 0x90, 0x1D, 0x69, 0xBA, 0x48, 0xC6, 0x12, 0x26, 0x81, 0x82, 0xD9, 0xD4, 0xDE, 0xDE, 0x4C, 0xD8, 0xED, 0x3E, 0xB7, 0x01, 0x1D, 0xD6, 0xEC, 0xD5, 0xF9, 0x9D, 0xBC, 0xF1, 0xCD, 0xFF, 0xEA, 0x99, 0xA4, 0xE7, 0x82, 0xDF, 0xFF, 0x3D, 0xFD, 0xC5, 0x5F, 0xC0, 0x4F, 0x00, 0x00, 0x96, 0x58, 0xE3, 0x2B, 0x7D, 0xE4, 0xD2, 0xAD, 0x3A, 0xF6, 0xBD, 0xCD, 0x37, 0x3B, 0xFD, 0x61, 0x27, 0x2F, 0x33, 0xD6, 0x30, 0xD0, 0xA0, 0x10, 0xA8, 0x0A, 0x02, 0xBB, 0x00, 0xEE, 0xEF, 0x4A, 0x5D, 0x02, 0x38, 0xCF, 0x61, 0xE9, 0xD2, 0x96, 0x35, 0xEC, 0x25, 0xAE, 0x84, 0xCD, 0x48, 0x0B, 0x7F, 0x28, 0x86, 0x0E, 0x0A, 0xB2, 0x01, 0xAF, 0xD8, 0xB6, 0x31, 0x6B, 0x02, 0x5C, 0x54, 0x85, 0xB4, 0x41, 0xC0, 0xDA, 0x39, 0x5B, 0x83, 0xC0, 0x07, 0xB8, 0x15, 0xBD, 0xF3, 0xA9, 0x04, 0xAA, 0xFE, 0xE1, 0x37, 0xD6, 0xD0, 0x5A, 0xC2, 0x0E, 0xB0, 0x1B, 0xC8, 0x00, 0x3E, 0xE8, 0x13, 0x3D, 0x62, 0x30, 0x8E, 0xA6, 0x20, 0xF9, 0x3A, 0x2C, 0x79, 0x02, 0x4C, 0x13, 0xC2, 0xAC, 0x76, 0x3C, 0xC1, 0x13, 0x35, 0x5E, 0x56, 0x8F, 0x88, 0x69, 0x8D, 0x48, 0x7B, 0xFC, 0x43, 0x1D, 0xC2, 0xB3, 0xE5, 0x85, 0xFF, 0x5C, 0x53, 0x6C, 0x29, 0xAC, 0xE0, 0xC6, 0x74, 0xB2, 0xEE, 0x2F, 0xBB, 0x99, 0xBE, 0x83, 0x79, 0xEE, 0x49, 0x33, 0x60, 0x4F, 0x00, 0x00, 0x7F, 0x58, 0xED, 0xBD, 0x42, 0x64, 0xEF, 0x5D, 0x08, 0xD7, 0x94, 0x09, 0x08, 0x5E, 0x0E, 0xC8, 0x44, 0x45, 0x6E, 0x0D, 0xAA, 0xF1, 0xB7, 0x15, 0xC5, 0x78, 0x2E, 0x01, 0x9F, 0xD3, 0xEF, 0xF2, 0x0D, 0x66, 0xD0, 0x94, 0x3A, 0x2A, 0xB1, 0x81, 0xE1, 0x52, 0xD4, 0xFD, 0x60, 0xC5, 0xFA, 0xE5, 0x07, 0x3B, 0xDF, 0x6C, 0x7E, 0xFF, 0xF7, 0xBB, 0x3E, 0x58, 0xB6, 0x5D, 0x8F, 0xCE, 0xF8, 0xA8, 0x62, 0xB1, 0x2D, 0xE6, 0xF5, 0x23, 0xA3, 0xB9, 0xE9, 0x4C, 0x77, 0x40, 0x43, 0x9B, 0x77, 0x93, 0x4D, 0x09, 0x96, 0x8C, 0x7A, 0xA0, 0x23, 0xFF, 0x8F, 0x4A, 0xFF, 0xAB, 0xF8, 0xA0, 0xF7, 0x62, 0xC9, 0xEB, 0x48, 0xD1, 0x5D, 0x3C, 0x10, 0xAC, 0x7F, 0xB8, 0xE8, 0x58, 0x1D, 0x00, 0xF6, 0x32, 0x3E, 0x1F, 0xCA, 0x31, 0x9F, 0x32, 0xD2, 0xDC, 0xDF, 0xB7, 0x47, 0xE7, 0xBB, 0xD0, 0xD0, 0x90, 0x4F, 0x00, 0x00, 0x6A, 0x58, 0xED, 0xB7, 0x66, 0x74, 0xA3, 0x6C, 0x8E, 0x24, 0x2E, 0xDB, 0x71, 0x08, 0xB5, 0x34, 0xA6, 0xE9, 0x17, 0x26, 0xEA, 0x60, 0xC1, 0x1A, 0x3A, 0x4A, 0xD4, 0x4B, 0x13, 0xBD, 0xDC, 0xC3, 0xD7, 0x21, 0xB6, 0x80, 0x64, 0xD7, 0x2D, 0x62, 0xD7, 0xE1, 0xC0, 0x66, 0xFE, 0x02, 0x14, 0xC8, 0x34, 0x1B, 0x22, 0xE0, 0x50, 0xC6, 0x14, 0x53, 0xEF, 0xDF, 0x70, 0x06, 0x6D, 0x37, 0xC8, 0xD0, 0x1A, 0x47, 0xCA, 0xAA, 0x63, 0xBD, 0x21, 0x4F, 0xE6, 0x27, 0xF4, 0x83, 0x45, 0x3A, 0x08, 0x86, 0x16, 0x50, 0x1A, 0xD5, 0x04, 0x06, 0xAD, 0x63, 0xC6, 0xFF, 0x72, 0x67, 0x01, 0x91, 0xF5, 0x57, 0x95, 0x34, 0x95, 0x1D, 0xA7, 0x8B, 0x7F, 0xC1, 0x45, 0xC3, 0x6C, 0x4F, 0x00, 0x00, 0x7F, 0x58, 0xED, 0x47, 0x36, 0xDB, 0x9C, 0xED, 0xC7, 0x1A, 0x13, 0x08, 0x82, 0x27, 0xC9, 0xD6, 0x64, 0x38, 0x55, 0x32, 0xE7, 0xE5, 0x05, 0x12, 0x26, 0x48, 0x0E, 0x8B, 0x26, 0xA4, 0xE8, 0x16, 0xEE, 0xBF, 0x20, 0xC1, 0x4B, 0x65, 0x2C, 0x07, 0xEE, 0xA6, 0x0A, 0xEF, 0x4E, 0x7D, 0x5C, 0x6B, 0x57, 0x1C, 0x2E, 0x78, 0x76, 0x3D, 0x6E, 0xF9, 0xB8, 0x2A, 0xC8, 0xF7, 0xC6, 0xF0, 0x76, 0xC6, 0x8A, 0xB6, 0xE0, 0xA2, 0xF5, 0x95, 0x13, 0x60, 0xF0, 0xAD, 0x1F, 0x1B, 0xCE, 0x01, 0x8C, 0x6F, 0x3F, 0xD9, 0x12, 0xF3, 0x0A, 0xCB, 0x58, 0x66, 0x92, 0x49, 0x19, 0xF4, 0xD7, 0x5F, 0xEE, 0x82, 0xE5, 0x14, 0xCA, 0xFC, 0x69, 0x83, 0x61, 0x26, 0xAF, 0xF0, 0x0A, 0x7F, 0x05, 0x20, 0x77, 0x65, 0xD4, 0x30, 0xCF, 0x47, 0xC6, 0x0A, 0xE7, 0xEE, 0x6C, 0x9B, 0x73, 0x29, 0x35, 0xBC, 0xCD, 0x40, 0x4F, 0x00, 0x00, 0x79, 0x58, 0xCA, 0x3D, 0x74, 0xDC, 0x75, 0x38, 0xD4, 0x72, 0x60, 0x6B, 0xCF, 0xB3, 0x4B, 0xE0, 0x55, 0xA1, 0x66, 0x02, 0x3F, 0x96, 0x70, 0xDB, 0x32, 0xDF, 0x47, 0x62, 0xFA, 0x34, 0x66, 0x4D, 0xE4, 0x54, 0x08, 0x16, 0xEF, 0xE1, 0xCE, 0xC2, 0x8E, 0xD0, 0x76, 0x1F, 0x22, 0xAF, 0x01, 0x08, 0x6A, 0x10, 0x95, 0x96, 0xCC, 0x5E, 0x59, 0x1C, 0x33, 0xE3, 0x83, 0xD6, 0x02, 0xC0, 0xA8, 0x40, 0x36, 0x2D, 0x3E, 0x0C, 0xE4, 0x60, 0x1E, 0x27, 0xE4, 0xCD, 0xAE, 0x7F, 0x77, 0xCD, 0xF0, 0x6A, 0xAB, 0xD0, 0x1F, 0xEA, 0xDF, 0xDC, 0x54, 0x8B, 0xB8, 0x5D, 0x0F, 0x07, 0xBD, 0xD4, 0x77, 0xA2, 0x06, 0xB5, 0xE8, 0xE1, 0x2C, 0xB1, 0x45, 0x8E, 0xF0, 0x7D, 0xE6, 0x6A, 0xEC, 0x40, 0xDE, 0x14, 0x49, 0xE9, 0x4E, 0xCF, 0x1C, 0x3A, 0x62, 0xD6, 0x71, 0xB8, 0x4F, 0x00, 0x00, 0x27, 0x58, 0x04, 0x3E, 0xC0, 0xA5, 0xD1, 0x4B, 0x20, 0xF4, 0x06, 0x33, 0xDF, 0xF1, 0xA3, 0xB6, 0x53, 0x1E, 0xEA, 0x68, 0x85, 0xE1, 0x69, 0xD2, 0xAA, 0x3D, 0x81, 0xA5, 0x3C, 0xC4, 0x29, 0xC4, 0x28, 0xA5, 0x5F, 0x45, 0x99, 0x08, 0x74, 0x4C, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20
};

#endif // AUDIO_DATA_YAO_H