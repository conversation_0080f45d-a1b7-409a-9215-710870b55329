#ifndef AUDIO_DATA_CONFIGSTART_H
#define AUDIO_DATA_CONFIGSTART_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfoConfigStart[] = {0, 24, 49, 120, 277, 402, 516, 649, 771, 914, 1056, 1191, 1336, 1473, 1592, 1724, 1862, 2006, 2131, 2270, 2400, 2541, 2676, 2794, 2926, 3042, 3153, 3197};
const uint8_t g_audioDataConfigStart[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x43, 0x58, 0x61, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1E, 0xED, 0x08, 0x67, 0x3C, 0xCD, 0x9A, 0xE0, 0x37, 0x34, 0xDE, 0xF5, 0x30, 0x50, 0x32, 0x85, 0x7C, 0x5F, 0x32, 0x4C, 0x73, 0x1F, 0x8A, 0x46, 0x01, 0xD7, 0xD4, 0xF4, 0x21, 0xC0, 0x0C, 0x25, 0x41, 0xC5, 0x7C, 0xBB, 0x02, 0x28, 0x44, 0x16, 0xD6, 0x98, 0x96, 0xC0, 0x8C, 0xC6, 0x54, 0x15, 0x18, 0xF5, 0x45, 0xB5, 0xA0, 0xEF, 0x8E, 0xB7, 0x23, 0x9A, 0xF7, 0x4A, 0x4F, 0x00, 0x00, 0x99, 0x58, 0xE4, 0x8E, 0x71, 0x12, 0x25, 0xA3, 0xC3, 0xF7, 0x5E, 0x9F, 0xA3, 0x18, 0x1F, 0x5C, 0xCC, 0x93, 0x97, 0x56, 0xF6, 0xEC, 0x2E, 0xFE, 0xC8, 0xF6, 0xFA, 0xE8, 0x27, 0x30, 0xB8, 0xD9, 0x4E, 0x6E, 0x8E, 0xD3, 0x2A, 0xEE, 0x93, 0xD3, 0xFC, 0x8C, 0x4B, 0xDC, 0xE2, 0x8E, 0x4C, 0x08, 0x51, 0xED, 0x99, 0xE9, 0xEF, 0x79, 0x2A, 0xC3, 0x9B, 0x59, 0x7B, 0xE5, 0x6D, 0x66, 0x4F, 0x14, 0xCC, 0x16, 0x44, 0x6D, 0x76, 0xB3, 0xCC, 0x26, 0x55, 0x52, 0xE2, 0x31, 0xCC, 0x34, 0x5B, 0xC6, 0x4C, 0x76, 0x0C, 0x37, 0xC8, 0xBD, 0x39, 0x30, 0x40, 0xCC, 0x91, 0xD1, 0x1F, 0x90, 0x4E, 0x92, 0x63, 0x8E, 0xAE, 0x22, 0x15, 0xA0, 0xC2, 0x2C, 0x47, 0x31, 0xE7, 0xBC, 0x56, 0xFD, 0x23, 0x9D, 0x4B, 0xFD, 0x5E, 0xD7, 0x4C, 0xB5, 0xFB, 0xD2, 0x43, 0x8A, 0xC5, 0xEA, 0xF4, 0x86, 0x82, 0x41, 0x74, 0x11, 0xAE, 0xC2, 0xD2, 0xAA, 0x44, 0x1C, 0x34, 0x88, 0x20, 0x92, 0x96, 0x49, 0x76, 0x8B, 0xA1, 0xF9, 0x04, 0x77, 0xD4, 0xA7, 0xB5, 0x7D, 0x29, 0x80, 0x4F, 0x00, 0x00, 0x79, 0x58, 0xEE, 0x25, 0xEF, 0xE2, 0x8E, 0x85, 0xA8, 0xE0, 0x16, 0x88, 0x1B, 0x4A, 0x09, 0xC2, 0xDD, 0xB7, 0xD2, 0x56, 0x01, 0x01, 0x37, 0x14, 0xC9, 0xDE, 0x16, 0x1A, 0x80, 0xEA, 0x30, 0xA2, 0x7D, 0x34, 0xF0, 0x99, 0x26, 0x63, 0x48, 0x89, 0xBF, 0x89, 0x81, 0x1A, 0xBF, 0x26, 0x4E, 0xC7, 0xDD, 0xCF, 0xAC, 0x78, 0x86, 0x6A, 0x80, 0x82, 0x9A, 0xBE, 0xA5, 0x81, 0xA7, 0x62, 0x07, 0x47, 0x76, 0xCF, 0x58, 0xD8, 0xBD, 0x17, 0x65, 0xD8, 0x35, 0x3A, 0x4A, 0xF9, 0x0E, 0x45, 0xBB, 0x5F, 0x61, 0x8C, 0xCB, 0xFA, 0x31, 0x11, 0x73, 0xBB, 0x43, 0x4A, 0xDC, 0xAF, 0xED, 0xDC, 0x00, 0xEF, 0x90, 0x99, 0xD5, 0xD1, 0x60, 0x10, 0x29, 0x95, 0x08, 0x4B, 0x66, 0x38, 0xB7, 0x08, 0x6E, 0xE7, 0x92, 0x90, 0xCC, 0xB6, 0x64, 0x11, 0x16, 0x0D, 0x9D, 0x9E, 0x4F, 0x00, 0x00, 0x6E, 0x58, 0xEC, 0xF1, 0xFD, 0xE9, 0x86, 0xB9, 0x78, 0x5F, 0x5B, 0xEE, 0x13, 0x70, 0xB3, 0x7A, 0x14, 0x18, 0x6A, 0x44, 0x30, 0x42, 0x56, 0x5F, 0xD8, 0x62, 0x53, 0x74, 0xE8, 0xE9, 0xF8, 0xEB, 0xB1, 0x6A, 0x3B, 0x2E, 0x14, 0xCC, 0x4B, 0xA4, 0x54, 0x0E, 0xC8, 0x3F, 0x0C, 0x62, 0x4B, 0x87, 0x3F, 0x13, 0x9C, 0xE4, 0x22, 0x5F, 0x9E, 0xFA, 0xB3, 0x39, 0xB6, 0xBE, 0xDE, 0x11, 0xFC, 0xF1, 0x6D, 0xDA, 0x8B, 0x5E, 0xB4, 0xF0, 0x3C, 0xD6, 0x0F, 0xAD, 0xDC, 0x5E, 0x37, 0x55, 0x9D, 0x50, 0x0B, 0x09, 0x84, 0x2B, 0x74, 0xE9, 0x55, 0x54, 0x0C, 0x8F, 0x68, 0xED, 0x13, 0x49, 0x34, 0xE4, 0x7C, 0x7A, 0x36, 0xBF, 0x83, 0x2B, 0xF0, 0x1B, 0x70, 0xA8, 0xA3, 0xB3, 0x20, 0x0D, 0x50, 0x4F, 0x00, 0x00, 0x81, 0x58, 0xE3, 0xAC, 0xD8, 0xB7, 0x6A, 0xF5, 0x40, 0x83, 0x4E, 0x80, 0x6D, 0x97, 0x01, 0xCD, 0x6D, 0x50, 0xDB, 0x97, 0x60, 0x8C, 0x09, 0x3E, 0x5D, 0xCB, 0x3D, 0x30, 0x53, 0xFF, 0x51, 0x38, 0x5C, 0x66, 0x1D, 0x02, 0xF4, 0xD6, 0x1B, 0x78, 0x95, 0x44, 0x32, 0xE0, 0x3F, 0x24, 0x0B, 0xDE, 0x80, 0xA3, 0x9C, 0xC8, 0x73, 0x3B, 0xB9, 0xE0, 0xB9, 0x01, 0x56, 0xD7, 0xFB, 0x2F, 0x8F, 0xEC, 0xA5, 0xDF, 0x2E, 0xB9, 0x39, 0x07, 0xB8, 0x95, 0x81, 0x0F, 0x30, 0x88, 0xC0, 0xD8, 0x5B, 0xA2, 0x73, 0xD8, 0x0F, 0xE3, 0x01, 0x27, 0xCE, 0xE4, 0x5F, 0xC5, 0xE7, 0x7A, 0x9B, 0x5B, 0x07, 0xC5, 0x0E, 0xF7, 0x80, 0x15, 0x40, 0x5C, 0x81, 0x3A, 0x65, 0x34, 0x3F, 0xC5, 0x5B, 0xD4, 0xED, 0xCE, 0x5F, 0x5C, 0xFF, 0x1B, 0x91, 0xB1, 0x58, 0x4B, 0x85, 0x24, 0xBF, 0xE4, 0x8B, 0x51, 0x91, 0xB8, 0xEC, 0x70, 0x4F, 0x00, 0x00, 0x76, 0x58, 0xE1, 0x58, 0xFE, 0xCE, 0x99, 0xF8, 0x3F, 0xD8, 0xE1, 0x6F, 0x82, 0x4E, 0x06, 0x71, 0xB1, 0x65, 0xDC, 0x33, 0xE9, 0x6E, 0xAE, 0xC4, 0xE5, 0xA0, 0x1C, 0xA0, 0xD6, 0xD1, 0xD3, 0x8A, 0x79, 0x50, 0x26, 0x07, 0xE1, 0xA4, 0xAC, 0x77, 0x4F, 0xB8, 0xF2, 0xBC, 0xEF, 0x8E, 0xF2, 0x6A, 0x3A, 0xC6, 0xC4, 0x00, 0xA7, 0xA3, 0x78, 0x50, 0x12, 0xDD, 0xD0, 0x97, 0xD8, 0x28, 0x4E, 0x00, 0xE2, 0xF4, 0x1C, 0xA3, 0xDA, 0x3F, 0xE1, 0x47, 0xB4, 0x7D, 0x35, 0xA5, 0x93, 0xD9, 0x57, 0xF5, 0x0A, 0x17, 0x2B, 0x6F, 0x18, 0xA3, 0xE5, 0x36, 0x54, 0x25, 0x8C, 0x62, 0xAB, 0x96, 0x9F, 0xAC, 0x65, 0x40, 0xEA, 0x7F, 0x91, 0x9A, 0xF4, 0x4A, 0x69, 0xDD, 0x99, 0xB6, 0x34, 0x30, 0x52, 0xC3, 0xB5, 0xC0, 0x7A, 0x90, 0x3F, 0xDC, 0x50, 0x4F, 0x00, 0x00, 0x8B, 0x58, 0xED, 0x6B, 0x84, 0x90, 0x76, 0xD4, 0x03, 0xAF, 0x84, 0x3E, 0x10, 0xC8, 0xEE, 0x70, 0x65, 0xCE, 0xF7, 0x26, 0x54, 0x21, 0x29, 0x4F, 0x2B, 0xD3, 0x0A, 0x21, 0x04, 0x5D, 0xF4, 0x15, 0x4D, 0xAE, 0x25, 0x69, 0x75, 0x4D, 0x21, 0x4C, 0x69, 0xB5, 0x43, 0x70, 0xB6, 0x1C, 0x86, 0x02, 0x66, 0xB9, 0x8E, 0x01, 0x46, 0x5E, 0x0C, 0xCD, 0x05, 0xB0, 0xAC, 0xA2, 0x12, 0xD9, 0x07, 0x55, 0x28, 0xD0, 0x2E, 0xEF, 0xA4, 0xD7, 0x85, 0x84, 0xCA, 0xBF, 0xCA, 0x61, 0x25, 0x1E, 0x6F, 0x41, 0x00, 0x15, 0xF1, 0x3F, 0xFF, 0x1B, 0xB6, 0xA4, 0x3C, 0x1C, 0xB2, 0x8B, 0x78, 0xC5, 0x72, 0x39, 0x83, 0x26, 0xAC, 0xBD, 0x12, 0x74, 0x89, 0x19, 0xF6, 0x40, 0x18, 0xAC, 0x0D, 0x78, 0xFE, 0x7B, 0xB0, 0x85, 0x62, 0x3C, 0xD8, 0x4A, 0xF1, 0x40, 0x97, 0x8F, 0x45, 0x43, 0xC0, 0x71, 0xD4, 0x12, 0x98, 0x90, 0xAC, 0xF8, 0x39, 0x46, 0xAC, 0xE4, 0x82, 0x68, 0x09, 0x82, 0x4F, 0x00, 0x00, 0x8A, 0x58, 0xE0, 0xA3, 0xC3, 0x82, 0x4A, 0x7C, 0x1D, 0x27, 0xF9, 0x6F, 0xBC, 0x2F, 0xD0, 0x2F, 0x4B, 0x00, 0x51, 0x61, 0x6F, 0x7D, 0xEC, 0xE8, 0x5F, 0xD6, 0x3B, 0xF0, 0x34, 0x23, 0x0E, 0x52, 0x42, 0x1F, 0x79, 0x38, 0x3C, 0xAA, 0xB5, 0x94, 0xFA, 0x60, 0x16, 0xAC, 0x91, 0x08, 0x72, 0x79, 0x07, 0x36, 0x73, 0xC5, 0xBB, 0x1F, 0x7A, 0xF4, 0xFC, 0x81, 0x3F, 0x43, 0x62, 0xA8, 0x42, 0x89, 0x1C, 0x06, 0x8D, 0x7C, 0xB2, 0xA2, 0x04, 0x4F, 0xC9, 0xB7, 0x7B, 0xCB, 0xA2, 0xEC, 0x1B, 0x06, 0x54, 0x13, 0x4E, 0x73, 0x14, 0x45, 0x68, 0xA9, 0xE9, 0x21, 0xBA, 0xBF, 0xB9, 0x9E, 0xF5, 0x7C, 0xFA, 0x02, 0xCF, 0x84, 0xEA, 0x3A, 0x94, 0x7C, 0x6F, 0xEA, 0xBF, 0xBA, 0x95, 0xC7, 0x83, 0xB2, 0x25, 0xA5, 0x13, 0xE5, 0x93, 0x28, 0xAE, 0x58, 0xEC, 0x57, 0xA5, 0x4B, 0x23, 0x4A, 0x5C, 0x62, 0x64, 0x6D, 0x26, 0x8C, 0x19, 0xFC, 0x9C, 0x38, 0x31, 0xC3, 0x40, 0x4F, 0x00, 0x00, 0x83, 0x58, 0xEA, 0xE0, 0xA1, 0x8D, 0x68, 0xCE, 0x2B, 0x23, 0x82, 0xBB, 0x11, 0x3C, 0xD5, 0x0F, 0x31, 0x1A, 0x20, 0xE1, 0xA8, 0x47, 0xE7, 0xED, 0x53, 0xBB, 0x95, 0x85, 0x3C, 0x6F, 0x23, 0x3D, 0x41, 0x5D, 0xD3, 0xA2, 0x66, 0xEB, 0x99, 0x48, 0xEE, 0x4F, 0x98, 0x46, 0x73, 0xE7, 0xA3, 0xEF, 0x57, 0xA8, 0x44, 0x92, 0x74, 0xAD, 0x55, 0x53, 0x86, 0x6B, 0x00, 0x25, 0x0E, 0xDC, 0x52, 0xB2, 0x50, 0xDB, 0xF8, 0x1D, 0x31, 0xFF, 0xC5, 0x3A, 0xC5, 0x28, 0x85, 0x5A, 0x4F, 0x83, 0x40, 0xC7, 0x4B, 0x92, 0x95, 0x59, 0x07, 0x7F, 0xBB, 0x5F, 0x5E, 0x93, 0x2F, 0xCD, 0x17, 0xDE, 0xD8, 0x5D, 0xC4, 0x9A, 0xB3, 0x4A, 0xFB, 0xA5, 0xE0, 0x73, 0x44, 0x89, 0x36, 0x95, 0xC1, 0x6F, 0x82, 0xF5, 0xC9, 0x5E, 0x9E, 0x8A, 0x62, 0xAF, 0xA7, 0x9E, 0xF3, 0xC4, 0x9F, 0x67, 0x09, 0xC6, 0x70, 0xDF, 0xE8, 0xDA, 0x52, 0x08, 0x4F, 0x00, 0x00, 0x8D, 0x58, 0xEB, 0xA4, 0x40, 0x02, 0x45, 0xE2, 0x12, 0x08, 0xB2, 0xBC, 0x4D, 0xC2, 0xE7, 0x4B, 0x8B, 0x4B, 0xA2, 0x35, 0x77, 0x72, 0xAE, 0x98, 0x71, 0x01, 0xE6, 0xB1, 0xAF, 0x1D, 0xE3, 0x90, 0x8C, 0x4F, 0xE6, 0x9F, 0x03, 0x2F, 0xAF, 0xD8, 0x3A, 0x36, 0x6F, 0x41, 0x25, 0xD7, 0x62, 0x59, 0xCB, 0x14, 0xE8, 0x96, 0xC4, 0x04, 0x1E, 0xCE, 0xAC, 0x9C, 0x80, 0xE1, 0x10, 0xA4, 0xC9, 0x83, 0x48, 0x02, 0x11, 0xD0, 0x5F, 0xDF, 0x4C, 0x6F, 0xA4, 0x4B, 0x92, 0xFE, 0xF0, 0x9B, 0xD7, 0xBB, 0xB6, 0x95, 0x11, 0x91, 0x7C, 0xEB, 0xF1, 0xF6, 0x0E, 0x3B, 0x37, 0x19, 0x3D, 0xC9, 0x25, 0xCB, 0xDA, 0x43, 0x53, 0xF0, 0x0F, 0x3B, 0xE8, 0x91, 0x66, 0xF5, 0x1A, 0x70, 0x6F, 0x0B, 0x41, 0xC0, 0x35, 0xD4, 0x10, 0xD1, 0xEB, 0x7D, 0xD6, 0x1B, 0x7C, 0x23, 0xC4, 0xAE, 0x06, 0xE7, 0x39, 0x22, 0x80, 0xF1, 0x59, 0x5E, 0xE8, 0x65, 0x15, 0xF6, 0x10, 0xAF, 0x3C, 0x08, 0xC4, 0xA8, 0x4F, 0x00, 0x00, 0x85, 0x58, 0xEB, 0xAA, 0x10, 0xC9, 0x15, 0x7B, 0x71, 0x57, 0x72, 0x10, 0x90, 0x00, 0x31, 0x8D, 0x3D, 0x3F, 0xA3, 0x0A, 0x89, 0x60, 0x1C, 0xB2, 0x3E, 0xE5, 0x73, 0xFB, 0x4A, 0x70, 0x8B, 0x5C, 0xB2, 0x7C, 0x78, 0x87, 0x46, 0xD7, 0x70, 0xA8, 0x73, 0xFB, 0xED, 0x8B, 0x89, 0x04, 0xD9, 0x3F, 0xAC, 0xF3, 0xB7, 0xD7, 0x03, 0xDA, 0x75, 0xD2, 0xA2, 0x6F, 0x1B, 0x42, 0xA1, 0xA6, 0x7D, 0xB4, 0x2A, 0x38, 0x50, 0x36, 0x4C, 0x95, 0x11, 0x3F, 0x51, 0xF8, 0x09, 0x23, 0x7F, 0x0F, 0x6F, 0x5C, 0xF9, 0xA0, 0xDC, 0x46, 0xEF, 0xE0, 0xD9, 0x4A, 0x12, 0x29, 0xE1, 0x64, 0x8C, 0x7D, 0x57, 0xA9, 0x75, 0x6E, 0x44, 0xDE, 0x53, 0xCE, 0x1B, 0x02, 0xD5, 0x9F, 0xA3, 0x59, 0xA1, 0xDD, 0x3D, 0xBB, 0x0B, 0x67, 0xD2, 0xA5, 0x89, 0x55, 0xFB, 0xFF, 0xEB, 0x7E, 0xAB, 0x0A, 0x8E, 0x02, 0xBD, 0x3A, 0xCB, 0x4B, 0x68, 0xF9, 0x92, 0xA0, 0x4F, 0x00, 0x00, 0x73, 0x58, 0xED, 0x40, 0xA0, 0x25, 0x9E, 0x11, 0xF6, 0xAE, 0xCA, 0x1F, 0xBF, 0x96, 0x0E, 0x54, 0xAE, 0xB8, 0x9B, 0x96, 0x62, 0x29, 0x57, 0x0B, 0xAF, 0x17, 0xB8, 0x97, 0x82, 0x10, 0x8F, 0x24, 0xE6, 0x73, 0x54, 0x6A, 0xE6, 0xBE, 0x1B, 0x7B, 0x67, 0xED, 0x07, 0xB8, 0xFE, 0x20, 0xF9, 0x30, 0x8C, 0xE6, 0x10, 0x0F, 0x33, 0x84, 0x13, 0xFE, 0x3B, 0xC6, 0x39, 0x70, 0x99, 0xB4, 0x3C, 0x8B, 0xE0, 0xAE, 0x7E, 0x2B, 0x7C, 0x79, 0x49, 0x1C, 0xE4, 0x3C, 0xE5, 0x98, 0x9D, 0xD2, 0xF2, 0xCF, 0xA2, 0x9B, 0xF1, 0x3D, 0x63, 0xB3, 0x91, 0x98, 0x3C, 0x69, 0x87, 0x30, 0x96, 0x5E, 0xCE, 0x14, 0x05, 0x26, 0xAD, 0x2E, 0xDE, 0x4F, 0x1C, 0xDB, 0x95, 0x76, 0x7A, 0x77, 0x29, 0xC1, 0x61, 0x0A, 0xA3, 0x6C, 0x1D, 0x68, 0x4F, 0x00, 0x00, 0x80, 0x58, 0xEA, 0x39, 0x20, 0x33, 0xF3, 0x6F, 0x96, 0xD8, 0x2C, 0x71, 0x05, 0x27, 0x1B, 0x0F, 0x2D, 0x8A, 0xC0, 0x96, 0x2B, 0xD8, 0x1C, 0xAD, 0x21, 0x1F, 0xAC, 0xD0, 0xDF, 0x54, 0x88, 0xEF, 0xBD, 0x34, 0x11, 0x4D, 0x3D, 0x86, 0x76, 0x12, 0x53, 0x05, 0x5A, 0xC8, 0x54, 0x7A, 0x3A, 0xD6, 0x81, 0xB8, 0x54, 0xC5, 0xB9, 0xF9, 0x42, 0xE4, 0xE8, 0x55, 0x9C, 0x43, 0x07, 0xF8, 0xEE, 0x81, 0x2F, 0xCF, 0xB3, 0x30, 0x37, 0x3E, 0x46, 0x37, 0xE8, 0xB5, 0x4F, 0x9C, 0x70, 0xA5, 0xCE, 0xF6, 0x29, 0x7D, 0x8E, 0x0C, 0x0D, 0x98, 0xFB, 0x89, 0x45, 0xA5, 0xE2, 0x17, 0x01, 0xFC, 0x91, 0x86, 0x7F, 0xE1, 0x5C, 0xD9, 0xE4, 0x1A, 0xDB, 0x3D, 0xD0, 0x32, 0x8E, 0xF1, 0x92, 0xC1, 0x68, 0xD6, 0xB5, 0xB9, 0xEE, 0x26, 0xF2, 0x6F, 0x5B, 0xB6, 0x11, 0xB0, 0x1C, 0x23, 0x08, 0x73, 0xED, 0xAA, 0x30, 0x4F, 0x00, 0x00, 0x86, 0x58, 0xE5, 0x3B, 0x74, 0xC1, 0x9C, 0x10, 0x1E, 0x71, 0xDC, 0xE0, 0x69, 0xF4, 0x69, 0xFF, 0xF4, 0x44, 0x8B, 0x65, 0xDF, 0x23, 0xC0, 0x75, 0xB6, 0xDF, 0xB4, 0x0B, 0x2A, 0xCC, 0x17, 0x5C, 0x90, 0xD9, 0x73, 0x38, 0xED, 0xF0, 0xC1, 0x98, 0xAB, 0xDB, 0x0B, 0x44, 0x97, 0xDF, 0x99, 0x3A, 0xEE, 0x8F, 0xCC, 0xA3, 0xB0, 0x26, 0x99, 0x28, 0x5B, 0x73, 0xDB, 0x65, 0x0C, 0xE6, 0x4B, 0x57, 0x81, 0x8C, 0x48, 0xF8, 0xF8, 0x6A, 0xF7, 0xD9, 0x61, 0x01, 0x60, 0xAF, 0x3D, 0x28, 0xC2, 0x98, 0x61, 0xE8, 0x63, 0x4D, 0x38, 0x07, 0xD7, 0xDA, 0x0D, 0xF8, 0x04, 0x30, 0x10, 0x4D, 0x09, 0xDD, 0x3A, 0xDC, 0x7B, 0xD0, 0x4D, 0x76, 0xD9, 0x2C, 0x2C, 0x22, 0x30, 0x74, 0x9C, 0xE1, 0xE1, 0x4F, 0x73, 0x07, 0xE8, 0x53, 0x19, 0x29, 0xF5, 0xE2, 0x0B, 0x11, 0xDB, 0x1C, 0xAA, 0x4F, 0x30, 0xC3, 0x92, 0x6F, 0xC8, 0x49, 0xD6, 0x91, 0x18, 0x4F, 0x00, 0x00, 0x8C, 0x58, 0xE5, 0x8B, 0x5F, 0x4C, 0xEA, 0xBB, 0x3E, 0x5E, 0x78, 0x76, 0xC2, 0x47, 0x04, 0x1E, 0xBC, 0xA2, 0x89, 0xE7, 0xA5, 0xFC, 0xF7, 0x13, 0xDF, 0x51, 0x67, 0x04, 0x6D, 0x16, 0x29, 0x89, 0xFC, 0x75, 0x72, 0xFB, 0x15, 0x20, 0x99, 0xB5, 0x7D, 0x5C, 0x2F, 0xF0, 0x02, 0x3D, 0x61, 0xB0, 0x19, 0x1C, 0x20, 0x9B, 0x61, 0x0E, 0x24, 0x59, 0xB7, 0x8E, 0xFB, 0x59, 0x1B, 0x6E, 0x40, 0xDA, 0x5A, 0x4D, 0x27, 0x49, 0xC3, 0x5C, 0x64, 0x37, 0xC1, 0x07, 0x7D, 0xD4, 0x0F, 0x19, 0x95, 0xDB, 0x29, 0x83, 0x8F, 0x53, 0x09, 0x5F, 0x44, 0x94, 0xD8, 0xF0, 0xFF, 0x87, 0xF4, 0x84, 0x47, 0x1D, 0x45, 0x6D, 0xA8, 0xB7, 0xE8, 0x16, 0x9A, 0xA0, 0x22, 0x84, 0x0F, 0x85, 0x22, 0x3C, 0x66, 0x1A, 0x12, 0x47, 0xD0, 0x71, 0x09, 0xC7, 0x6F, 0xED, 0x02, 0xE1, 0x55, 0x87, 0x61, 0x08, 0xE5, 0x00, 0x79, 0x03, 0xD9, 0xFD, 0x1C, 0x09, 0xF7, 0xDF, 0x91, 0x92, 0x52, 0xC1, 0xBD, 0x4F, 0x00, 0x00, 0x79, 0x58, 0xEE, 0xFF, 0x00, 0x48, 0xEF, 0x68, 0x5D, 0xF3, 0x39, 0x5F, 0x8E, 0xA7, 0xCD, 0x83, 0x8E, 0xB5, 0xCB, 0x17, 0x88, 0x3B, 0x4E, 0x51, 0x9F, 0xAC, 0xA4, 0x59, 0x28, 0xA9, 0x4D, 0xAC, 0xC6, 0x93, 0x8A, 0x0E, 0x3C, 0x8D, 0x4E, 0x73, 0xA1, 0xF9, 0xB2, 0xA3, 0xE4, 0x1C, 0x19, 0xB0, 0x71, 0xD4, 0xB2, 0xFC, 0x28, 0xA3, 0x6D, 0xD7, 0xDA, 0x66, 0x36, 0x39, 0x36, 0xCB, 0xF0, 0x0F, 0x8F, 0xD0, 0xD4, 0x49, 0x9C, 0x5A, 0xF5, 0x7C, 0x44, 0xEB, 0x2B, 0xC3, 0x91, 0x4E, 0xAF, 0x60, 0xF6, 0xB7, 0x3B, 0xE2, 0x6C, 0x50, 0xF6, 0xA6, 0xC8, 0x89, 0x90, 0x57, 0x5D, 0x3A, 0x64, 0xD9, 0xF7, 0x73, 0x2F, 0x38, 0x8A, 0xD9, 0x74, 0xB7, 0x7B, 0x6A, 0x9F, 0x54, 0x44, 0x78, 0x32, 0xB5, 0xB8, 0xF2, 0xE7, 0xD5, 0xD9, 0x6F, 0x4B, 0x28, 0xBE, 0x90, 0x4F, 0x00, 0x00, 0x87, 0x58, 0xEE, 0x52, 0x79, 0xC8, 0x00, 0x30, 0xAE, 0xD5, 0xC3, 0x25, 0xAD, 0x0F, 0xDC, 0x9A, 0x77, 0xAC, 0x91, 0xA9, 0xC8, 0x89, 0x44, 0xFA, 0xC6, 0xC7, 0xA1, 0xB8, 0xA7, 0x2B, 0x0D, 0xE2, 0x16, 0x60, 0x1E, 0xAD, 0x30, 0xAB, 0xD1, 0x12, 0xAF, 0x49, 0x29, 0x63, 0x93, 0x55, 0x95, 0x87, 0xC4, 0xD4, 0x71, 0xD3, 0xC4, 0x0D, 0x23, 0x94, 0xF5, 0xCC, 0xAA, 0x53, 0x46, 0x28, 0x26, 0x38, 0xEA, 0x7F, 0x95, 0x99, 0xC9, 0x5E, 0x51, 0x43, 0x77, 0x28, 0xD9, 0xDD, 0x27, 0x5A, 0xDA, 0xB9, 0x32, 0xB4, 0xFB, 0x8A, 0xC8, 0x7A, 0x29, 0x6E, 0x33, 0x9E, 0x4C, 0x83, 0x71, 0x15, 0x72, 0x43, 0xD6, 0xE4, 0x30, 0x44, 0x75, 0x7E, 0x0E, 0x64, 0xAF, 0x02, 0xE9, 0x81, 0x31, 0xB9, 0x9C, 0x52, 0x85, 0xC6, 0x11, 0x77, 0x05, 0xAB, 0x42, 0xC8, 0xEA, 0xFB, 0xC0, 0x21, 0x6B, 0x6E, 0x9F, 0x63, 0xDF, 0x1A, 0x0D, 0xCA, 0xFA, 0x72, 0xAA, 0xD2, 0x4F, 0x00, 0x00, 0x7E, 0x58, 0x6F, 0x41, 0x56, 0xC3, 0x07, 0x2C, 0x7F, 0x51, 0xA7, 0xA3, 0x0B, 0x55, 0x5D, 0x88, 0x68, 0x8A, 0x11, 0x1D, 0xC0, 0x6A, 0x6A, 0x76, 0xBF, 0xBD, 0xF5, 0x11, 0xD4, 0xD7, 0xF2, 0x1A, 0xE2, 0x89, 0x08, 0x82, 0x5A, 0x42, 0xEB, 0x9A, 0x79, 0xE6, 0x19, 0x06, 0x89, 0x9E, 0x69, 0x20, 0x59, 0x6C, 0x4C, 0xBB, 0xE6, 0x7F, 0xE3, 0x35, 0x07, 0x8E, 0x0E, 0x85, 0xB9, 0x85, 0x26, 0xB6, 0x5C, 0x79, 0x6F, 0x82, 0x27, 0xB0, 0x48, 0x34, 0x06, 0xB4, 0x3B, 0x70, 0x89, 0x55, 0x10, 0x18, 0xE4, 0x96, 0xB2, 0xD7, 0xAA, 0x41, 0xB2, 0xD5, 0x91, 0xDB, 0x48, 0x9F, 0x53, 0x29, 0xFA, 0x78, 0x8C, 0xD7, 0x69, 0xD0, 0x57, 0x83, 0x77, 0xD7, 0xDB, 0xE7, 0xF4, 0x4D, 0xE5, 0x22, 0x04, 0x92, 0xC7, 0x80, 0xDC, 0x8C, 0x4E, 0x92, 0x1D, 0x7B, 0x7C, 0x53, 0x70, 0xA6, 0x05, 0x92, 0x0C, 0x4F, 0x00, 0x00, 0x89, 0x58, 0xE4, 0xE8, 0xA9, 0xDC, 0xC1, 0xB8, 0x53, 0xCE, 0xD3, 0x5C, 0xC6, 0x47, 0x6C, 0x7A, 0xE3, 0xC9, 0x94, 0x18, 0x01, 0x9F, 0x18, 0xE5, 0x3C, 0xE5, 0xB9, 0x04, 0xB5, 0x0A, 0x79, 0x6D, 0x97, 0xA4, 0x09, 0x89, 0xEB, 0xA3, 0x5D, 0xDA, 0x04, 0x05, 0xC2, 0xEC, 0xEE, 0x20, 0x3A, 0xF7, 0xC3, 0x34, 0x9C, 0xA1, 0xB4, 0x9C, 0x8E, 0xF9, 0x3F, 0x0D, 0xA1, 0x41, 0xD2, 0x1B, 0x30, 0xB1, 0xB8, 0xB7, 0xBC, 0x2E, 0x5A, 0x60, 0x8F, 0x0E, 0x6C, 0x06, 0x6E, 0x42, 0x45, 0x65, 0x7C, 0x98, 0x3C, 0xAB, 0x25, 0x72, 0xF2, 0x46, 0x0C, 0x7D, 0x14, 0x6F, 0xF2, 0x3C, 0xEC, 0x0C, 0x80, 0xE4, 0x32, 0x9E, 0x55, 0xE6, 0xC2, 0x81, 0xF2, 0x27, 0xD8, 0xFC, 0xD6, 0x3E, 0x38, 0x0F, 0x0D, 0x00, 0x63, 0xBD, 0xFD, 0x91, 0x0C, 0x79, 0xCA, 0x2F, 0x5C, 0x9B, 0x2C, 0x93, 0xE6, 0x77, 0x19, 0x1D, 0x3A, 0xA9, 0x46, 0x73, 0x6A, 0x89, 0xEA, 0x32, 0x31, 0x30, 0x4F, 0x00, 0x00, 0x83, 0x58, 0xE5, 0x21, 0x13, 0xDB, 0xD3, 0xEA, 0x33, 0x41, 0x15, 0x25, 0x1D, 0x02, 0x59, 0xA0, 0x7A, 0xA8, 0x83, 0xD2, 0x01, 0x65, 0x3D, 0x6F, 0xA8, 0xBE, 0xB1, 0x4F, 0xBD, 0x28, 0x97, 0xAA, 0x44, 0xBC, 0xCF, 0xF2, 0xE0, 0x09, 0xDC, 0x2B, 0xEB, 0xDD, 0xA1, 0xEA, 0x26, 0xFD, 0x30, 0x43, 0x8F, 0xC8, 0x73, 0x75, 0xED, 0xE8, 0x2B, 0x5B, 0x56, 0xAF, 0xFA, 0x6D, 0xAE, 0x0C, 0x0B, 0x1D, 0xD1, 0x51, 0xEE, 0x08, 0xFE, 0xE7, 0xC9, 0x0C, 0xE5, 0xE3, 0xF2, 0xCA, 0x3B, 0x81, 0xD1, 0x62, 0xB3, 0xD9, 0x53, 0x33, 0x86, 0x94, 0x6A, 0x88, 0x02, 0x4F, 0x54, 0xD2, 0x27, 0x49, 0xEA, 0x46, 0xBC, 0xC8, 0xF3, 0x76, 0xE6, 0x21, 0x64, 0x83, 0xA5, 0xB6, 0xF7, 0xEB, 0x09, 0xA3, 0x9A, 0x7D, 0x43, 0x44, 0x3E, 0x9C, 0x27, 0x53, 0xD2, 0x88, 0xBD, 0x80, 0xF1, 0xA0, 0x28, 0x3E, 0x13, 0xB5, 0x8A, 0x23, 0x08, 0x80, 0x4F, 0x00, 0x00, 0x72, 0x58, 0xED, 0x75, 0x30, 0x12, 0x2E, 0x50, 0x96, 0xC9, 0x32, 0x1C, 0x38, 0xE6, 0xAF, 0x5D, 0x1B, 0x5D, 0xF0, 0x2C, 0x95, 0x96, 0xA6, 0xF7, 0xD5, 0xF5, 0x5B, 0xA2, 0x7E, 0xB3, 0x6E, 0x49, 0x88, 0x67, 0x45, 0x8D, 0xDD, 0xB5, 0xD3, 0x0D, 0xAA, 0xB5, 0xA3, 0xDC, 0xE6, 0x90, 0x18, 0x31, 0x0E, 0xA4, 0xD0, 0x79, 0xB3, 0x2E, 0x73, 0xA8, 0xBF, 0xA9, 0x94, 0xD4, 0x22, 0x2B, 0x68, 0xDF, 0x71, 0xF4, 0x78, 0x77, 0x83, 0xE6, 0xA6, 0xFF, 0xF1, 0x20, 0x43, 0xE0, 0xCF, 0xAA, 0x16, 0xEB, 0x7F, 0x3D, 0xAD, 0xBE, 0x86, 0xED, 0x07, 0x3E, 0x09, 0xCB, 0x0F, 0x25, 0x91, 0xFA, 0x9E, 0x5C, 0xDC, 0x7B, 0xA0, 0xFF, 0x31, 0x5B, 0x67, 0xD2, 0x71, 0x65, 0x50, 0x33, 0x3C, 0x95, 0x31, 0x18, 0xEA, 0x16, 0x46, 0x4F, 0x00, 0x00, 0x80, 0x58, 0xEC, 0x18, 0x0C, 0x9D, 0xF6, 0x72, 0x59, 0xF1, 0xFB, 0x71, 0xD0, 0x40, 0xB5, 0x35, 0x71, 0xFA, 0xBE, 0x1E, 0x6E, 0x8F, 0xC7, 0x74, 0xC6, 0x32, 0x14, 0x35, 0xFD, 0x67, 0x7B, 0xD0, 0x54, 0xC5, 0xBD, 0x2E, 0x7D, 0x00, 0xD0, 0xCC, 0xDB, 0x83, 0x7F, 0x82, 0xE7, 0x76, 0xF2, 0xCA, 0x19, 0xD1, 0xF0, 0x07, 0x9C, 0xAD, 0xCD, 0x1D, 0x9F, 0x13, 0x17, 0x8C, 0x7A, 0x25, 0x0B, 0x13, 0xB2, 0x20, 0x2D, 0x45, 0xD8, 0x9E, 0x7F, 0x55, 0x66, 0x01, 0xD0, 0xA6, 0xA1, 0xF4, 0xB9, 0x3A, 0xAA, 0xD7, 0x57, 0xBF, 0x12, 0x29, 0x1D, 0xFC, 0xE6, 0x49, 0x15, 0xC8, 0x1D, 0x15, 0xE7, 0xBF, 0x7D, 0x62, 0xCF, 0x16, 0xE1, 0xBA, 0xE8, 0xCE, 0x8E, 0x8B, 0xD3, 0xC9, 0x0C, 0x01, 0x1E, 0x53, 0x89, 0x57, 0x84, 0xB4, 0x8A, 0x13, 0xFD, 0xBC, 0x55, 0x02, 0x23, 0xF6, 0xFA, 0x16, 0xBD, 0x0C, 0x78, 0x4F, 0x00, 0x00, 0x70, 0x58, 0xEE, 0x31, 0x39, 0x79, 0x27, 0x37, 0xAF, 0x90, 0xBF, 0x43, 0x92, 0x24, 0x05, 0x3D, 0x5C, 0xA5, 0x36, 0x2A, 0xEA, 0xF3, 0xEB, 0xCA, 0x18, 0x41, 0x01, 0x28, 0x9E, 0x92, 0x07, 0x82, 0x08, 0x4E, 0x37, 0xC8, 0x73, 0xC2, 0xAC, 0xBA, 0xD5, 0xE3, 0x53, 0xEA, 0xEA, 0x62, 0xA3, 0x1D, 0xD4, 0xC7, 0xE3, 0xDF, 0x96, 0x3B, 0x20, 0x9D, 0xE8, 0x2A, 0x32, 0x45, 0x33, 0x63, 0x42, 0x67, 0xD8, 0x70, 0xD9, 0x8F, 0x6A, 0xD6, 0xD2, 0xDA, 0x77, 0x98, 0x8F, 0xD6, 0x1B, 0x94, 0x69, 0x35, 0x4C, 0x5A, 0xC4, 0xA1, 0xC0, 0x1A, 0x70, 0x08, 0xB5, 0xE6, 0x0E, 0x09, 0x9D, 0xA7, 0x83, 0x1E, 0x7E, 0x91, 0xA5, 0xAE, 0x3F, 0xAC, 0xFE, 0x54, 0x95, 0xE0, 0x0F, 0x9F, 0xEF, 0x9E, 0xAB, 0x8A, 0x22, 0x4F, 0x00, 0x00, 0x6B, 0x58, 0xCD, 0xA1, 0xF8, 0xEA, 0xEC, 0xD6, 0xA2, 0x67, 0xFB, 0xD5, 0xF5, 0xD3, 0x61, 0x45, 0xEE, 0x62, 0xDA, 0x3F, 0xAC, 0x47, 0x36, 0x11, 0xBD, 0xFD, 0x68, 0xF9, 0xBC, 0x16, 0xEF, 0xED, 0x8A, 0xB5, 0x2F, 0x40, 0xDF, 0xD0, 0x81, 0xE3, 0xA8, 0x2B, 0x3F, 0x8A, 0x35, 0x75, 0xC8, 0xB0, 0x38, 0x82, 0xFF, 0xC4, 0x48, 0x7E, 0xED, 0x37, 0x31, 0x08, 0xC1, 0x28, 0x1D, 0xB9, 0x54, 0xCF, 0xA1, 0x56, 0xD0, 0x33, 0x1D, 0x4E, 0x9C, 0xD9, 0x4C, 0x37, 0x8F, 0x35, 0x4A, 0xE0, 0xC3, 0x2E, 0x12, 0xFB, 0x4F, 0xB6, 0x7C, 0x49, 0x88, 0x08, 0x20, 0x8D, 0x55, 0xCA, 0xA8, 0xE2, 0x29, 0x54, 0x9F, 0x92, 0x9A, 0xFC, 0xF1, 0x76, 0x5C, 0x5B, 0xAF, 0xF3, 0xBB, 0xB0, 0x4F, 0x00, 0x00, 0x28, 0x58, 0x00, 0xF8, 0x22, 0xE1, 0x1A, 0x9C, 0x6A, 0x18, 0x48, 0xE8, 0x08, 0x15, 0x2E, 0x09, 0x68, 0xB4, 0xD8, 0x70, 0xE5, 0xC1, 0x7C, 0xC8, 0xB2, 0x63, 0x59, 0x99, 0x2C, 0xFB, 0x1A, 0xC5, 0xA6, 0x7D, 0x41, 0x12, 0xF8, 0x63, 0xF5, 0xB7, 0x50, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20
};

#endif // AUDIO_DATA_CONFIGSTART_H