#ifndef AUDIO_DATA_3_H
#define AUDIO_DATA_3_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfo3[] = {0, 24, 49, 100, 232, 367, 499, 619, 739, 879};
const uint8_t g_audioData3[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x2F, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x0A, 0xE8, 0xAC, 0x71, 0xCC, 0x8E, 0x4F, 0xC3, 0xAE, 0x55, 0x7B, 0xD0, 0x9D, 0x18, 0x7E, 0x05, 0x82, 0x19, 0x21, 0x29, 0xED, 0x4E, 0x8D, 0x86, 0xBA, 0x76, 0x3D, 0xC4, 0xC7, 0x0A, 0xDC, 0x97, 0x87, 0xED, 0x40, 0x4C, 0x3C, 0xB1, 0x10, 0x4F, 0x00, 0x00, 0x80, 0x58, 0xE0, 0xA1, 0x1C, 0x5E, 0xD3, 0x9D, 0x78, 0x13, 0x3D, 0x29, 0xCD, 0xDE, 0x0B, 0xBC, 0x00, 0xD6, 0xFD, 0x7B, 0xC8, 0xF0, 0xF3, 0x82, 0xA9, 0xC8, 0xC8, 0x1A, 0x5B, 0xA6, 0xF1, 0xAC, 0xD0, 0x45, 0x62, 0x4A, 0x50, 0x78, 0x32, 0xFA, 0xBE, 0x02, 0x94, 0xD8, 0x9C, 0x5D, 0x15, 0x7D, 0x7E, 0x43, 0xB4, 0x38, 0xAA, 0xFC, 0x91, 0xF0, 0x5B, 0xCB, 0x67, 0x7F, 0xB3, 0x6F, 0x0D, 0x6C, 0x4E, 0x4E, 0x13, 0x22, 0x92, 0x92, 0xBD, 0x92, 0x7E, 0x42, 0xDD, 0x01, 0x56, 0x63, 0xA2, 0xEE, 0x9C, 0x83, 0x07, 0x2E, 0x27, 0xB3, 0xCC, 0x7B, 0x34, 0x3B, 0x1A, 0xE7, 0x27, 0x7B, 0xED, 0xF5, 0xEA, 0xC7, 0xCA, 0xE4, 0xD5, 0x87, 0xA9, 0x95, 0xC2, 0xA8, 0x65, 0x54, 0x9D, 0x28, 0xEC, 0xCF, 0xAA, 0xBA, 0xFD, 0xDF, 0x68, 0xCA, 0x96, 0x0B, 0x00, 0x84, 0x67, 0xBC, 0x3C, 0x0D, 0x94, 0x21, 0xBA, 0x4F, 0x00, 0x00, 0x83, 0x58, 0xE4, 0xCB, 0x11, 0x17, 0x1F, 0x5D, 0x81, 0x85, 0xE9, 0xEA, 0xA3, 0x04, 0xFB, 0x8D, 0xBB, 0xD8, 0xB3, 0x71, 0x60, 0x2C, 0xE1, 0x4E, 0x13, 0x98, 0x4B, 0x65, 0xF2, 0xA2, 0x61, 0x98, 0x4B, 0x83, 0x13, 0x39, 0x98, 0xDD, 0xA8, 0x2E, 0x18, 0xE4, 0xEA, 0x0F, 0x91, 0x4E, 0x6F, 0xB0, 0xBA, 0xE9, 0x54, 0xCA, 0xBC, 0xFF, 0xF4, 0x71, 0xFD, 0x35, 0x97, 0xAC, 0x76, 0x49, 0x8C, 0x92, 0xE2, 0xAE, 0x66, 0x13, 0xBB, 0x08, 0xD3, 0x33, 0x21, 0xB0, 0x19, 0x9A, 0x1F, 0x0E, 0x96, 0xC5, 0x9D, 0xEE, 0x77, 0xCC, 0x57, 0x7D, 0x52, 0x19, 0x2E, 0xEE, 0xFD, 0xE3, 0xB9, 0x45, 0xB3, 0xDB, 0x9F, 0x82, 0x2A, 0xAE, 0xF8, 0x69, 0x2A, 0xF7, 0xBB, 0x47, 0x51, 0xD1, 0xAE, 0x51, 0xC0, 0x3F, 0xBF, 0x86, 0x01, 0xBA, 0x9A, 0x90, 0x11, 0xD0, 0x07, 0x0E, 0x84, 0x6B, 0xFB, 0x30, 0x9C, 0xE4, 0x78, 0xB4, 0x8E, 0x60, 0x4F, 0x00, 0x00, 0x80, 0x58, 0xE5, 0x28, 0x11, 0x41, 0x86, 0x70, 0x9A, 0x68, 0xCD, 0x10, 0x7B, 0x81, 0x88, 0x72, 0xBE, 0x9A, 0x9E, 0xA4, 0xD7, 0xC7, 0x80, 0x7D, 0xDE, 0x76, 0xC3, 0x5A, 0xC4, 0xD1, 0xB7, 0xD6, 0xA5, 0x7C, 0xC7, 0x20, 0x41, 0xDE, 0x9F, 0x4C, 0xBC, 0x64, 0x9B, 0x41, 0xC4, 0xDF, 0xA2, 0x3A, 0x03, 0x06, 0x42, 0xF3, 0x73, 0xE7, 0x29, 0x9E, 0x2A, 0x9A, 0x17, 0xAC, 0x43, 0x65, 0x35, 0xF6, 0xB5, 0x69, 0x1F, 0xAF, 0xF8, 0xF7, 0x34, 0xF9, 0xC8, 0x65, 0xCA, 0x0C, 0x91, 0x72, 0x97, 0xE7, 0x0D, 0x5D, 0x25, 0x5B, 0xB7, 0x27, 0x39, 0x3F, 0x48, 0x81, 0x4D, 0x90, 0x7F, 0x14, 0x39, 0xFC, 0xB2, 0x89, 0x18, 0x1D, 0x4F, 0x65, 0x8D, 0xB2, 0x4E, 0x3E, 0x10, 0x8D, 0x7B, 0xE0, 0xB5, 0xA0, 0x7D, 0xD2, 0x8C, 0xDF, 0xF8, 0xA0, 0x7C, 0x13, 0x0D, 0x09, 0x34, 0x28, 0x93, 0x2D, 0x13, 0xA8, 0x10, 0x4F, 0x00, 0x00, 0x74, 0x58, 0xEE, 0xDB, 0xD3, 0xE6, 0x47, 0x5E, 0x0D, 0xF6, 0x5C, 0xB6, 0xDC, 0x9E, 0xD4, 0xAD, 0x04, 0x8E, 0xDD, 0xE9, 0xD6, 0x0D, 0xC8, 0x40, 0x3E, 0x79, 0xE5, 0xDA, 0x2F, 0xCD, 0x09, 0x8B, 0xF6, 0xFD, 0xED, 0x60, 0x84, 0xE9, 0x28, 0x4C, 0xFC, 0xBE, 0xAC, 0x47, 0x93, 0xF5, 0x12, 0xEF, 0x67, 0x3D, 0x54, 0x3E, 0x1E, 0x27, 0x13, 0xC8, 0x0B, 0x45, 0xF3, 0x14, 0x86, 0x60, 0xE6, 0x2C, 0x37, 0xA0, 0xFD, 0x4F, 0x4F, 0xA3, 0x6D, 0xFE, 0x90, 0xBA, 0xF7, 0x78, 0xAF, 0xCF, 0x92, 0x35, 0xAE, 0x45, 0x52, 0x80, 0x19, 0xAF, 0x76, 0xE2, 0x43, 0xDF, 0x29, 0x3B, 0x0F, 0x3E, 0xAD, 0xAB, 0x87, 0xE7, 0xCA, 0xC1, 0x2A, 0xFE, 0xCD, 0x5B, 0x9C, 0xA6, 0xD7, 0xBC, 0xE5, 0x44, 0x99, 0x7C, 0x74, 0xF1, 0x93, 0xE4, 0x26, 0x4F, 0x00, 0x00, 0x74, 0x58, 0xEE, 0xD4, 0x06, 0x4D, 0x76, 0x94, 0x93, 0x1C, 0xBE, 0xC1, 0x3C, 0x73, 0x5A, 0x4E, 0x8C, 0x16, 0xBC, 0xF9, 0x26, 0x4E, 0x73, 0x0A, 0x0E, 0x6E, 0xCA, 0x91, 0x21, 0x65, 0xE5, 0xA0, 0x53, 0xF0, 0x8E, 0x92, 0x04, 0xC9, 0x5A, 0x71, 0x45, 0x0E, 0x6C, 0xF3, 0xCB, 0xA4, 0x83, 0x73, 0xDC, 0x89, 0xD8, 0x64, 0xBE, 0xF5, 0xC5, 0x93, 0xF1, 0x4F, 0xB4, 0x39, 0x1C, 0x88, 0xD6, 0x84, 0x80, 0x6D, 0x65, 0x35, 0x5F, 0x9C, 0x3B, 0x46, 0x31, 0x13, 0xF4, 0xC6, 0x37, 0xED, 0xAD, 0x1F, 0x03, 0xA2, 0x5F, 0x37, 0x4E, 0xD3, 0xE8, 0x77, 0x84, 0x14, 0xD2, 0x7C, 0x5D, 0xF6, 0x12, 0x1C, 0x9F, 0x56, 0x0E, 0x2C, 0x8B, 0x5F, 0xB3, 0xD0, 0x75, 0xFD, 0x2F, 0x5A, 0xE1, 0x1A, 0x16, 0xA9, 0xD4, 0x55, 0xFA, 0x57, 0xD8, 0x4F, 0x00, 0x00, 0x88, 0x58, 0xED, 0x31, 0x52, 0x22, 0xFB, 0x94, 0x99, 0xA6, 0xEB, 0xF4, 0xF6, 0xD0, 0xAC, 0x83, 0x6A, 0x0E, 0x2D, 0xE9, 0x49, 0xD9, 0xBA, 0x80, 0x1E, 0x31, 0x09, 0xEE, 0x0D, 0x98, 0x1D, 0x01, 0xDD, 0xFA, 0xC0, 0xE6, 0x9D, 0xCC, 0xFF, 0x2E, 0xCC, 0x6B, 0xB7, 0x7D, 0xF1, 0xD3, 0x37, 0xE2, 0xF9, 0x46, 0xB7, 0x84, 0xC4, 0x1E, 0xA6, 0x68, 0x11, 0xBC, 0x5B, 0x3E, 0x61, 0xF6, 0x4E, 0x3E, 0xCF, 0x42, 0x3B, 0x89, 0x3C, 0x23, 0x3B, 0xD6, 0x37, 0x91, 0xFC, 0xFA, 0x0D, 0x17, 0x7E, 0xBD, 0x5D, 0xD5, 0x3E, 0x77, 0x95, 0xE5, 0x24, 0x3E, 0xFD, 0x95, 0x14, 0x0E, 0x47, 0xCC, 0xA9, 0xBB, 0x75, 0xA1, 0x2D, 0xAA, 0x77, 0x25, 0xDE, 0x0D, 0x06, 0x79, 0x3B, 0xA1, 0x38, 0x17, 0xBD, 0xAB, 0xCC, 0xB7, 0x2B, 0x32, 0x76, 0xEF, 0x37, 0x12, 0x5A, 0xFC, 0xFB, 0x3B, 0xBF, 0x1E, 0xC4, 0x48, 0x73, 0xEE, 0x92, 0x8F, 0xEF, 0xB4, 0x18, 0x32, 0x4C, 0x4F, 0x00, 0x00, 0x3F, 0x58, 0x00, 0xED, 0x2C, 0xB8, 0x81, 0x36, 0x7E, 0xFD, 0x2B, 0x4B, 0x6E, 0x86, 0x74, 0xA7, 0x76, 0xA6, 0x12, 0x8F, 0xD6, 0x04, 0x8B, 0x42, 0xE5, 0xA8, 0xFE, 0xAA, 0x94, 0x8B, 0x0F, 0xA0, 0x61, 0xE6, 0x93, 0x9C, 0xC4, 0x4E, 0x16, 0xE3, 0xDF, 0xD5, 0xEE, 0xEA, 0xA0, 0x04, 0x33, 0x70, 0x0F, 0x04, 0xEF, 0x9F, 0x8A, 0x7A, 0x02, 0x7C, 0xBB, 0x06, 0xCA, 0xF7, 0xEC, 0x5C, 0xAE, 0x6C
};

#endif // AUDIO_DATA_3_H