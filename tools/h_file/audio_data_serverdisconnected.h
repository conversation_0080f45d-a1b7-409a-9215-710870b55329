#ifndef AUDIO_DATA_SERVERDISCONNECTED_H
#define AUDIO_DATA_SERVERDISCONNECTED_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfoServerDisconnected[] = {0, 24, 49, 114, 242, 367, 510, 657, 768, 907, 1051, 1193, 1332, 1474, 1589, 1714, 1852, 1971, 2107, 2203, 2308, 2467, 2611, 2758, 2844};
const uint8_t g_audioDataServerDisconnected[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x3D, 0x58, 0x61, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1E, 0xEC, 0xE7, 0xA0, 0x3D, 0x4F, 0x5A, 0x06, 0x67, 0x23, 0xEA, 0x3D, 0x02, 0x15, 0xF3, 0x4A, 0xD5, 0xB0, 0x2F, 0x7E, 0xB6, 0x5F, 0x08, 0x78, 0x84, 0x62, 0x91, 0xF1, 0xDB, 0xA5, 0x30, 0x14, 0x96, 0xF3, 0x58, 0x25, 0x27, 0x13, 0xE0, 0x22, 0xBC, 0xC2, 0x64, 0x6C, 0x38, 0x0B, 0xB9, 0x69, 0x66, 0xA1, 0x5B, 0x72, 0x19, 0xE0, 0x4F, 0x00, 0x00, 0x7C, 0x58, 0xE5, 0x43, 0xE2, 0x52, 0x94, 0x4E, 0x2A, 0xBE, 0x2A, 0x5A, 0x76, 0xF2, 0x1E, 0xA0, 0xE8, 0x0D, 0x03, 0x40, 0x65, 0xF8, 0xF5, 0xBC, 0xCE, 0x27, 0x7F, 0x83, 0x8A, 0x9D, 0xCB, 0x48, 0xA1, 0x3D, 0x9A, 0xF7, 0xD0, 0xFC, 0x04, 0xBB, 0xE2, 0x83, 0x62, 0x27, 0x16, 0xB7, 0x6B, 0xA8, 0x21, 0xA3, 0x63, 0x53, 0x79, 0x18, 0x96, 0xDB, 0x12, 0x66, 0x6F, 0x5E, 0x06, 0xB7, 0xFB, 0x87, 0x2B, 0x00, 0xA0, 0x8C, 0x6C, 0x45, 0x57, 0x3E, 0xE4, 0x14, 0xEB, 0x99, 0xEB, 0x6D, 0x23, 0xA5, 0x90, 0x49, 0xA6, 0x5D, 0x1D, 0x7D, 0x3F, 0x4F, 0x48, 0xCC, 0xEC, 0x9D, 0x3B, 0x4B, 0x35, 0x6B, 0x5F, 0x1A, 0x52, 0xA7, 0x69, 0x77, 0x2A, 0x53, 0x7A, 0x4B, 0xDE, 0x00, 0x0A, 0xD1, 0x5B, 0xAC, 0xCB, 0x12, 0x29, 0x4F, 0xB0, 0x40, 0x53, 0xEA, 0x5C, 0x42, 0xDD, 0x33, 0x33, 0x4F, 0x00, 0x00, 0x79, 0x58, 0xED, 0x33, 0x4E, 0x28, 0x6D, 0x2D, 0x8D, 0x04, 0x61, 0xF0, 0xB5, 0x05, 0x85, 0xFD, 0xB9, 0xA9, 0x85, 0x6E, 0x88, 0xCA, 0xD9, 0x83, 0xB2, 0xBF, 0x46, 0x90, 0x20, 0xEB, 0x65, 0x38, 0xF6, 0xD3, 0xAE, 0x0A, 0x4D, 0x2C, 0x1E, 0x76, 0x69, 0x57, 0xBB, 0x01, 0x6D, 0xC7, 0x43, 0xCE, 0x81, 0x26, 0x83, 0xF0, 0x3B, 0x31, 0x8F, 0x7D, 0x13, 0x10, 0x14, 0x7C, 0x15, 0x0A, 0x45, 0x67, 0x11, 0x4C, 0x61, 0x8D, 0xC2, 0x96, 0x7B, 0x35, 0xFD, 0x65, 0x47, 0x51, 0xD1, 0xB0, 0x85, 0xA8, 0xF7, 0x5E, 0xA0, 0x94, 0x5B, 0xD3, 0x7C, 0x31, 0xCF, 0xEC, 0x34, 0xA9, 0x94, 0x66, 0x4F, 0x20, 0x7D, 0xD5, 0x34, 0x2B, 0x8A, 0xDC, 0xC4, 0x7F, 0xDB, 0x44, 0x02, 0xC8, 0x5E, 0x77, 0xD3, 0xD7, 0xAA, 0xCC, 0x5B, 0x34, 0xF2, 0x53, 0x91, 0x5A, 0xE3, 0xEF, 0x4F, 0x00, 0x00, 0x8B, 0x58, 0xEA, 0x4F, 0x00, 0x32, 0xBF, 0xC4, 0x9D, 0x5B, 0x0B, 0x58, 0xDB, 0xDE, 0x10, 0x10, 0xB0, 0x98, 0x80, 0xD4, 0x7D, 0xCE, 0x63, 0xB0, 0xFE, 0xC3, 0xA9, 0xD7, 0x42, 0x77, 0x6F, 0x1F, 0xF2, 0x9F, 0x1F, 0x1F, 0x0E, 0xEB, 0xFE, 0xCD, 0x4D, 0x20, 0xCC, 0x34, 0x9E, 0xCB, 0x66, 0x48, 0x69, 0xF0, 0x9F, 0x30, 0x77, 0x61, 0x90, 0x43, 0x10, 0xFB, 0x85, 0x56, 0x5F, 0x94, 0x7D, 0xA6, 0x7B, 0x7C, 0xAB, 0xC3, 0x1A, 0x2C, 0xB9, 0xA8, 0x1C, 0x1B, 0x79, 0xE5, 0x2E, 0xB3, 0x7F, 0x5A, 0x32, 0xE4, 0xC1, 0xB0, 0xD8, 0x5F, 0xE7, 0xF6, 0xB0, 0x71, 0x41, 0x0D, 0xF2, 0x33, 0x35, 0x84, 0xFF, 0xA5, 0xC6, 0xEA, 0x02, 0xF4, 0x3B, 0xA8, 0x4E, 0x1B, 0x4A, 0x69, 0xB3, 0x90, 0x06, 0xD2, 0xD7, 0xD9, 0x91, 0x3B, 0x16, 0x41, 0xA1, 0x06, 0x33, 0x02, 0xDD, 0x2B, 0xD2, 0x1C, 0xB8, 0xC6, 0x99, 0xB1, 0x8C, 0x55, 0xE3, 0x7B, 0x11, 0xF7, 0x3A, 0xCE, 0x9A, 0x44, 0x4F, 0x00, 0x00, 0x8F, 0x58, 0xEC, 0x3F, 0xD3, 0xE3, 0xFA, 0xD7, 0x3A, 0x99, 0x0A, 0xF9, 0x82, 0x69, 0x41, 0x18, 0xFE, 0xDC, 0x7F, 0xBD, 0x81, 0x82, 0x7F, 0xF1, 0x24, 0x77, 0x16, 0xD0, 0x31, 0x11, 0x72, 0x45, 0x6E, 0x14, 0x60, 0x4E, 0x5B, 0xF6, 0x18, 0x91, 0xA7, 0x38, 0xAC, 0x02, 0x94, 0x0D, 0xC6, 0x65, 0x16, 0x0D, 0x0B, 0xE4, 0x41, 0x99, 0x56, 0x6A, 0xCE, 0xA6, 0xED, 0xB0, 0x59, 0x75, 0xCA, 0x66, 0x0D, 0xD3, 0x6B, 0xAC, 0x25, 0x53, 0x7D, 0x81, 0x9C, 0x67, 0x90, 0x10, 0xE2, 0x7D, 0x37, 0x63, 0x15, 0xED, 0x90, 0xA6, 0xF7, 0x69, 0xA4, 0xD5, 0xA8, 0x08, 0x63, 0x9D, 0x50, 0x07, 0xA9, 0x6F, 0x63, 0x2E, 0x30, 0x95, 0x6C, 0x2F, 0x02, 0x95, 0x04, 0x33, 0x1A, 0x7C, 0x18, 0x20, 0x01, 0x32, 0x6F, 0x95, 0xD6, 0x7D, 0x10, 0xF2, 0xBB, 0x64, 0x40, 0xBB, 0x33, 0x2D, 0xE4, 0x42, 0x1F, 0x7A, 0x7E, 0x8E, 0xBC, 0x75, 0x13, 0xC3, 0xFC, 0xB0, 0xCC, 0x8E, 0x08, 0x56, 0x1B, 0x31, 0x78, 0x60, 0x4F, 0x00, 0x00, 0x6B, 0x58, 0xEE, 0x9C, 0xBE, 0x6D, 0xA3, 0x69, 0xD3, 0x03, 0x08, 0x7B, 0x78, 0x53, 0xF8, 0xF3, 0xEA, 0x4E, 0x7B, 0xB7, 0x0A, 0x57, 0x99, 0xE5, 0x52, 0x57, 0xBA, 0x85, 0x47, 0x70, 0x65, 0xBE, 0xCC, 0xA9, 0x83, 0x65, 0xCB, 0x39, 0x13, 0xA3, 0x4F, 0xA6, 0x29, 0x45, 0xC9, 0x70, 0x64, 0x22, 0x30, 0xC7, 0x2D, 0x32, 0x68, 0xA1, 0x50, 0xE9, 0x1A, 0x62, 0x43, 0xA3, 0x71, 0xF0, 0xDE, 0xAF, 0x37, 0x11, 0x3D, 0xA2, 0xEE, 0x42, 0xE8, 0x73, 0xEF, 0x5A, 0x12, 0x52, 0x28, 0x18, 0x33, 0x44, 0xF8, 0x15, 0x03, 0x6D, 0xBE, 0x33, 0xB1, 0x0C, 0x55, 0xFC, 0xA6, 0xE6, 0x1A, 0xDC, 0x27, 0xBD, 0x0F, 0x81, 0xFC, 0x53, 0xAB, 0xA3, 0x93, 0xB3, 0x07, 0x9B, 0xE9, 0xB8, 0x4F, 0x00, 0x00, 0x87, 0x58, 0xEE, 0x4F, 0x6B, 0x15, 0x7F, 0x69, 0x1E, 0x67, 0x65, 0x5F, 0x75, 0xAC, 0x6A, 0xB9, 0x74, 0x40, 0x28, 0x24, 0x35, 0x0F, 0x2B, 0x06, 0xA5, 0x5A, 0xD1, 0x0F, 0x4D, 0xEA, 0x95, 0x0D, 0xC9, 0x88, 0xF6, 0xD0, 0xF6, 0xE6, 0x9C, 0x08, 0x84, 0xB2, 0xCA, 0x6E, 0xC7, 0x33, 0x12, 0xD7, 0x6C, 0x5D, 0x9E, 0x84, 0x1C, 0x50, 0xD4, 0x3F, 0x83, 0x32, 0x89, 0xE9, 0x66, 0x1C, 0xF1, 0x55, 0x95, 0x48, 0xB6, 0xA7, 0x0E, 0xF5, 0x0A, 0xD6, 0x21, 0x9D, 0xC0, 0xBB, 0x8D, 0x07, 0x63, 0x76, 0x5E, 0xC9, 0x1D, 0xB4, 0xE9, 0xC1, 0x6D, 0x02, 0x6B, 0xF4, 0x51, 0xFA, 0xD5, 0x7B, 0xFB, 0xB8, 0xD5, 0x26, 0x36, 0x13, 0x57, 0x65, 0x52, 0xAE, 0xDF, 0xF0, 0x2E, 0xAC, 0x20, 0x88, 0xFB, 0xEC, 0xF4, 0xA8, 0xCF, 0xAD, 0x1F, 0x85, 0x52, 0x3E, 0x4A, 0xB4, 0x45, 0xFB, 0x15, 0x06, 0x48, 0xEC, 0x97, 0x0F, 0xA9, 0xE9, 0xDD, 0xFD, 0x10, 0x40, 0x4F, 0x00, 0x00, 0x8C, 0x58, 0xEB, 0x60, 0x21, 0x07, 0x4B, 0xAB, 0xE5, 0x38, 0x0D, 0x3F, 0x54, 0x63, 0x4F, 0x95, 0xFB, 0xE9, 0x3C, 0x81, 0x16, 0xF5, 0x4F, 0xF3, 0x89, 0x34, 0x01, 0x02, 0xA9, 0x13, 0xB7, 0x06, 0xE3, 0xEE, 0xF6, 0xD7, 0x6A, 0x98, 0xBB, 0x63, 0xB0, 0xC8, 0x97, 0x0E, 0x5C, 0xEE, 0xB0, 0x22, 0x46, 0xED, 0x8C, 0x27, 0x26, 0x27, 0x58, 0x0C, 0x14, 0x81, 0x70, 0xFF, 0x7A, 0x27, 0x16, 0xBD, 0xDC, 0x2E, 0x90, 0x38, 0xC9, 0xDA, 0x1C, 0xA2, 0x1C, 0xAB, 0x9D, 0xDA, 0x54, 0x50, 0x76, 0xAA, 0x1E, 0x54, 0xBA, 0xB9, 0xBD, 0x97, 0x64, 0x07, 0xE8, 0x07, 0xD1, 0xEC, 0xF8, 0x51, 0xF2, 0xCC, 0x5A, 0x1D, 0xFB, 0x04, 0x55, 0xD1, 0x94, 0x77, 0x10, 0x35, 0xE7, 0x6F, 0x81, 0x51, 0xA8, 0x9F, 0x5F, 0x4A, 0x85, 0x19, 0x6E, 0xFE, 0x71, 0xDD, 0xFF, 0x3E, 0x5A, 0x5C, 0x16, 0x11, 0xE1, 0x1B, 0x0B, 0xF0, 0xAC, 0x03, 0x94, 0xE5, 0x90, 0x98, 0x87, 0x55, 0x1C, 0x14, 0x08, 0x4F, 0x00, 0x00, 0x8A, 0x58, 0xED, 0x4E, 0x1F, 0xE7, 0x26, 0x93, 0xDA, 0xCA, 0xD5, 0xC1, 0x53, 0xEF, 0xE2, 0x2B, 0x6B, 0xA1, 0x0D, 0xDA, 0x72, 0x76, 0x6A, 0x12, 0xF8, 0x88, 0x21, 0x75, 0xAE, 0x8A, 0x4D, 0xC6, 0x8C, 0x9D, 0x94, 0x84, 0xF8, 0xF2, 0x65, 0xF8, 0x8B, 0xCE, 0xDD, 0x0C, 0xF4, 0x59, 0x62, 0x41, 0x57, 0x5C, 0xAE, 0xB2, 0xE2, 0x76, 0x78, 0x56, 0x13, 0xF7, 0x9B, 0x2C, 0x43, 0xF8, 0xEA, 0x1D, 0x1C, 0xF5, 0x38, 0x59, 0x4C, 0x8C, 0xE7, 0xC8, 0x72, 0x4E, 0xCB, 0x1D, 0xF8, 0x72, 0x31, 0x21, 0x67, 0xCA, 0xCB, 0xCE, 0xFF, 0xDD, 0x96, 0xE2, 0x62, 0x82, 0xFD, 0x6B, 0x21, 0xD7, 0x73, 0xAA, 0x48, 0x37, 0x1E, 0x65, 0xFF, 0x89, 0xB8, 0x09, 0x92, 0x0C, 0xB7, 0x8B, 0xD9, 0x22, 0xC9, 0x0C, 0x03, 0xBA, 0x8D, 0x80, 0x1A, 0xEE, 0x7C, 0x8F, 0x1F, 0x72, 0xF7, 0x6E, 0xEC, 0x41, 0x67, 0x2B, 0xB8, 0x1F, 0x81, 0x83, 0xE7, 0x29, 0x06, 0x42, 0xBF, 0xCA, 0x07, 0x4F, 0x00, 0x00, 0x87, 0x58, 0xED, 0x58, 0x50, 0xF9, 0x38, 0x6B, 0xCA, 0x87, 0xD8, 0x25, 0x8B, 0x8E, 0x7A, 0xB3, 0x34, 0xEB, 0xE4, 0x12, 0x4F, 0xFB, 0x5C, 0xE6, 0x10, 0x9B, 0xCF, 0xDA, 0x1F, 0x04, 0x43, 0x53, 0xE2, 0xA4, 0xED, 0x0E, 0x78, 0x56, 0xC6, 0x48, 0xA7, 0x3A, 0xF6, 0x3A, 0xBA, 0xEE, 0xF1, 0x07, 0xE8, 0x48, 0x71, 0x7F, 0x32, 0x91, 0xD9, 0x00, 0x40, 0x07, 0xC3, 0xE9, 0x75, 0x59, 0x76, 0x54, 0x6F, 0xD8, 0x8C, 0x1F, 0xA9, 0xE0, 0x79, 0x21, 0xDF, 0xCF, 0xB7, 0xE0, 0xC8, 0x5E, 0x42, 0x0E, 0x0C, 0x40, 0xDF, 0x33, 0x00, 0xF0, 0x2D, 0x98, 0xF2, 0x82, 0x6A, 0xA8, 0x66, 0x65, 0x7F, 0xD1, 0xE7, 0x38, 0x12, 0x03, 0x76, 0x0E, 0xB9, 0xFF, 0x9C, 0xFD, 0x70, 0xD6, 0xA9, 0xC6, 0x85, 0x22, 0x7D, 0xAD, 0x36, 0xF0, 0xAB, 0x9C, 0xB4, 0x01, 0xEF, 0xA9, 0x91, 0x83, 0xF4, 0x23, 0x91, 0xA7, 0x70, 0x02, 0x39, 0x91, 0xC8, 0xFD, 0xE3, 0x50, 0x4F, 0x00, 0x00, 0x8A, 0x58, 0xEF, 0xEA, 0x12, 0x53, 0xB5, 0x1A, 0x9D, 0x32, 0xBE, 0x43, 0x09, 0xE0, 0x24, 0x38, 0x32, 0x39, 0xE9, 0x1E, 0x2F, 0x38, 0x65, 0xD7, 0xD6, 0x95, 0x1F, 0xF2, 0xFA, 0xCB, 0x81, 0xBE, 0x5B, 0xA9, 0x10, 0x4B, 0x25, 0xB0, 0x5E, 0x1D, 0x24, 0xEC, 0xDA, 0x15, 0x6F, 0x95, 0xB9, 0x39, 0x34, 0x1E, 0x6B, 0xCF, 0x5F, 0xA9, 0x82, 0xDD, 0x77, 0x4F, 0x8D, 0xFF, 0xF5, 0xF3, 0x3A, 0x00, 0x8F, 0x60, 0x93, 0x04, 0xF5, 0x40, 0x3F, 0xA6, 0x5C, 0x70, 0xA9, 0xEC, 0x2F, 0x55, 0x07, 0xC3, 0x69, 0xA2, 0x5D, 0xEC, 0x28, 0x26, 0xBD, 0xFC, 0xCC, 0x60, 0x95, 0x1D, 0xAC, 0x30, 0x7D, 0xA1, 0x59, 0x1F, 0x06, 0x2D, 0xE9, 0x0F, 0x23, 0xE1, 0xC8, 0x52, 0xE8, 0x7C, 0x06, 0x0D, 0x40, 0xE7, 0xAB, 0xCE, 0x66, 0x1F, 0x65, 0x10, 0xDD, 0x74, 0x6D, 0xE3, 0x18, 0xD0, 0xE0, 0xDE, 0xEE, 0x0E, 0x90, 0x07, 0x67, 0x40, 0x52, 0x90, 0x24, 0x4C, 0x2E, 0x24, 0x29, 0x4F, 0x00, 0x00, 0x6F, 0x58, 0xEE, 0x35, 0x49, 0x67, 0x35, 0x4B, 0xEC, 0x71, 0x73, 0xAD, 0xE0, 0x99, 0x07, 0x04, 0x4C, 0x0C, 0xF5, 0x0D, 0x6A, 0x2B, 0x39, 0x30, 0x47, 0x2D, 0x81, 0xEC, 0x1E, 0x78, 0x4A, 0xF3, 0x6B, 0x4A, 0x51, 0xB0, 0xD8, 0x14, 0xE9, 0xE2, 0x13, 0xAE, 0x64, 0x05, 0x8F, 0x77, 0x40, 0x9D, 0x67, 0x40, 0x59, 0x5B, 0x79, 0x00, 0xE2, 0xDA, 0x90, 0xA3, 0x17, 0x81, 0x51, 0xC0, 0x08, 0x76, 0x0F, 0x40, 0x4D, 0x54, 0x6F, 0x85, 0x34, 0xE8, 0x99, 0x06, 0xDB, 0xFF, 0xAF, 0x59, 0xA9, 0xEA, 0xF6, 0xB4, 0x7B, 0x06, 0x22, 0x81, 0xD3, 0xDE, 0x16, 0x64, 0x27, 0x1D, 0x09, 0x30, 0x74, 0x97, 0x84, 0x00, 0x1F, 0x8B, 0xC0, 0x22, 0xC0, 0xFF, 0xE1, 0x2D, 0x6B, 0x3C, 0xB9, 0x31, 0x0C, 0xE8, 0x4F, 0x00, 0x00, 0x79, 0x58, 0xCD, 0xA1, 0xF9, 0x88, 0xD7, 0x98, 0x16, 0x24, 0x99, 0x70, 0xCA, 0x24, 0x97, 0xD5, 0x23, 0x83, 0x6F, 0x07, 0x48, 0xA3, 0xEA, 0xE5, 0x61, 0x3D, 0xAF, 0xB4, 0x1C, 0x52, 0xF5, 0xB4, 0x6C, 0xE3, 0x0B, 0xA4, 0x56, 0xDE, 0xEA, 0x93, 0xC8, 0x04, 0x42, 0x9C, 0xE1, 0xE2, 0x70, 0xA6, 0x82, 0x29, 0x57, 0x4F, 0x27, 0xAB, 0xEE, 0x59, 0x45, 0x09, 0x88, 0x84, 0x43, 0x26, 0xF7, 0x49, 0x1C, 0x13, 0xB3, 0x9F, 0xE4, 0x31, 0x2E, 0xE5, 0xB9, 0x0E, 0x90, 0x9E, 0x07, 0xDA, 0xD3, 0x8B, 0x4A, 0x91, 0xED, 0x98, 0x7C, 0xA1, 0x2E, 0xF9, 0x16, 0x8B, 0x68, 0xE0, 0x4D, 0x5D, 0x37, 0x9D, 0xEE, 0x3A, 0xD2, 0xD3, 0x15, 0xF2, 0x73, 0x30, 0xB3, 0xEB, 0x12, 0xFA, 0x0B, 0x90, 0xD5, 0xE6, 0xCD, 0x50, 0xDC, 0x4F, 0xEC, 0xFE, 0x15, 0xCB, 0xB8, 0xF0, 0x4F, 0x00, 0x00, 0x86, 0x58, 0xE4, 0x23, 0x9C, 0x5A, 0xB5, 0x56, 0x72, 0xFE, 0xD2, 0x86, 0x4F, 0xB1, 0x97, 0x9A, 0xD6, 0xBC, 0x69, 0x81, 0xDA, 0xCB, 0xC0, 0x72, 0x8D, 0x00, 0xDB, 0x1B, 0x1F, 0xCF, 0x2F, 0xB3, 0x41, 0x8E, 0xEC, 0xB0, 0xB6, 0x14, 0x07, 0x09, 0x96, 0x42, 0xA1, 0x16, 0x32, 0xF1, 0x09, 0x78, 0x80, 0x3B, 0xE5, 0x7A, 0x03, 0xF6, 0xD3, 0x0A, 0x5C, 0x66, 0x16, 0x61, 0x61, 0xA0, 0x2A, 0x51, 0x77, 0x8E, 0xB3, 0xBD, 0x0F, 0x18, 0xB8, 0xC8, 0x13, 0x16, 0x07, 0x60, 0x33, 0x82, 0x08, 0x28, 0x4F, 0x90, 0xB8, 0x10, 0x05, 0x9D, 0xFD, 0xC3, 0x70, 0x28, 0x56, 0xD0, 0x47, 0x97, 0xE0, 0x03, 0xBD, 0xCD, 0xB0, 0x19, 0xB6, 0xAE, 0x20, 0x02, 0xFF, 0x72, 0x20, 0xDB, 0xEC, 0xE8, 0xD3, 0xE4, 0x22, 0x87, 0xBA, 0x2A, 0x17, 0xC5, 0x7B, 0x65, 0xE4, 0x0F, 0x89, 0x57, 0xEE, 0x3A, 0xA3, 0x44, 0x17, 0x59, 0xD1, 0x2D, 0xEB, 0x89, 0x2C, 0x4F, 0x00, 0x00, 0x73, 0x58, 0xED, 0x6C, 0xA5, 0xD1, 0xDA, 0xF0, 0x78, 0x87, 0x73, 0x85, 0xB3, 0xBB, 0x2A, 0xEA, 0x53, 0x4E, 0x76, 0xFA, 0x99, 0xBB, 0x71, 0x48, 0x66, 0xD5, 0x4B, 0xEE, 0xED, 0x60, 0xA0, 0x9D, 0xAA, 0x5C, 0x0A, 0x93, 0xFB, 0x52, 0xD6, 0x6C, 0xA9, 0x99, 0xF2, 0x05, 0x0F, 0x98, 0x49, 0x0C, 0x80, 0x5A, 0x70, 0x82, 0x5B, 0x45, 0x2D, 0x9F, 0xBA, 0x1D, 0x3E, 0xB9, 0xE0, 0x61, 0x1F, 0x7C, 0x41, 0x8D, 0x79, 0x65, 0x38, 0x89, 0x07, 0xE7, 0xD8, 0x19, 0x96, 0xFE, 0x76, 0xE8, 0x41, 0xA4, 0x07, 0x1F, 0x5A, 0x07, 0x1E, 0x16, 0x99, 0x39, 0x92, 0xF8, 0x8E, 0x99, 0x51, 0xAE, 0x5E, 0xD3, 0x14, 0xF4, 0xC6, 0x11, 0xB8, 0x0B, 0x07, 0x81, 0x75, 0x3B, 0x21, 0xE7, 0xCB, 0xDB, 0x00, 0x9C, 0xCF, 0x9F, 0x00, 0xC5, 0x4F, 0x00, 0x00, 0x84, 0x58, 0xE9, 0x2B, 0xE9, 0xAE, 0xF9, 0x5C, 0x34, 0xFE, 0x1E, 0x86, 0x35, 0x35, 0x7D, 0x6C, 0x5C, 0x11, 0xFB, 0x61, 0x78, 0x05, 0x99, 0x36, 0xE7, 0xA2, 0xA2, 0x6F, 0x7C, 0xFC, 0x71, 0x0A, 0x71, 0xD1, 0x91, 0x02, 0xAC, 0x35, 0x2D, 0xFB, 0xE9, 0xEB, 0x16, 0x04, 0x35, 0x65, 0xEC, 0x67, 0x64, 0x57, 0x8C, 0x8A, 0x04, 0x30, 0x7D, 0x97, 0x23, 0xDE, 0xE6, 0xCE, 0xB8, 0x1F, 0xCB, 0xCD, 0x41, 0x8A, 0x65, 0xAA, 0x34, 0x6E, 0x94, 0xDB, 0x41, 0xA9, 0x10, 0x7B, 0x5B, 0x1E, 0xF1, 0xC5, 0xE1, 0x8E, 0x66, 0xB7, 0x8F, 0xD5, 0x84, 0xCF, 0x1A, 0x2D, 0x8D, 0xE5, 0x54, 0x86, 0x9D, 0xD4, 0x9E, 0x0D, 0x09, 0xAF, 0xB1, 0xE6, 0x11, 0x34, 0x23, 0x78, 0x3F, 0x40, 0xAC, 0x18, 0xD1, 0x97, 0x30, 0x55, 0x06, 0x14, 0x9D, 0x10, 0x7E, 0x92, 0xC8, 0xBB, 0x2B, 0x63, 0x95, 0x0D, 0x7B, 0xE2, 0x90, 0xE9, 0x8C, 0xD8, 0x7C, 0x4F, 0x00, 0x00, 0x5C, 0x58, 0x86, 0xFD, 0x0D, 0xC9, 0x94, 0xF0, 0x87, 0x72, 0xAA, 0x23, 0x21, 0xB6, 0xE8, 0xD7, 0x5A, 0x02, 0x83, 0x7D, 0x26, 0x1F, 0x8D, 0x5C, 0xA7, 0xF7, 0x45, 0xBA, 0x09, 0xA5, 0xBF, 0x78, 0xCD, 0x73, 0x21, 0x25, 0x0A, 0x7E, 0xBD, 0x0F, 0x33, 0x0C, 0x94, 0xBA, 0xC6, 0x9F, 0xFF, 0x1E, 0xDE, 0xF6, 0xB0, 0x79, 0xFF, 0xFF, 0x06, 0xA1, 0x94, 0x6F, 0x5C, 0x42, 0x04, 0xFA, 0x86, 0x58, 0x7D, 0x13, 0xC9, 0xAF, 0x8F, 0xF4, 0x8D, 0x6D, 0x66, 0xDF, 0x9B, 0x8F, 0xEE, 0x4E, 0x01, 0x31, 0x48, 0x64, 0x62, 0x2A, 0xAA, 0xC7, 0x79, 0xA2, 0xCD, 0xDD, 0xCA, 0x42, 0x80, 0x4F, 0x00, 0x00, 0x65, 0x58, 0x60, 0xCA, 0x84, 0x21, 0x51, 0xF6, 0x6D, 0x90, 0xA5, 0x65, 0x41, 0xC9, 0xCF, 0xC3, 0x7D, 0x4E, 0xEE, 0x94, 0x22, 0x91, 0x8A, 0x5E, 0xF8, 0x96, 0xEC, 0x6A, 0x08, 0x29, 0xA1, 0xE5, 0x69, 0x66, 0x64, 0xE6, 0xB3, 0xEE, 0x5D, 0x25, 0xD1, 0xA9, 0xF2, 0xD2, 0xC6, 0x03, 0xBF, 0xDC, 0x13, 0x45, 0x47, 0x67, 0x3C, 0x0F, 0x26, 0x60, 0xBC, 0x8E, 0x4C, 0x93, 0xB9, 0x5B, 0x67, 0xCB, 0xDF, 0xF7, 0x86, 0xAB, 0xB2, 0x24, 0x06, 0x29, 0x0B, 0x95, 0xAC, 0x41, 0xF6, 0x59, 0x09, 0x11, 0x7C, 0xA3, 0xF1, 0xBE, 0x5B, 0x60, 0x69, 0xC7, 0xEC, 0x4F, 0x80, 0xD9, 0x51, 0xFC, 0x41, 0x02, 0x97, 0xB3, 0xBC, 0x66, 0xEA, 0x18, 0x4F, 0x00, 0x00, 0x9B, 0x58, 0xE9, 0x10, 0x97, 0x49, 0x56, 0x55, 0xDE, 0x80, 0xDB, 0xF0, 0x31, 0x2C, 0x19, 0x8C, 0xD9, 0x32, 0x21, 0x85, 0x7A, 0x46, 0x4D, 0xA1, 0x28, 0x7F, 0xBF, 0xC1, 0x9C, 0x8E, 0x3E, 0xF8, 0x65, 0x5A, 0xBE, 0x28, 0x9F, 0x07, 0xA3, 0x22, 0x0C, 0xB4, 0xDF, 0x3F, 0x4E, 0x2B, 0xDC, 0x32, 0xE7, 0x36, 0x20, 0xF4, 0x27, 0x79, 0x65, 0x8E, 0xDC, 0x2D, 0x0E, 0xEB, 0xB6, 0x69, 0x96, 0x70, 0xBD, 0x1E, 0xAE, 0x9D, 0x71, 0xCD, 0x50, 0xA1, 0xB8, 0xF6, 0xA9, 0x6E, 0xAA, 0x22, 0xD5, 0xC0, 0xB2, 0xF8, 0xA1, 0x83, 0xA2, 0x73, 0x09, 0x43, 0xF4, 0x3E, 0xD3, 0xB3, 0x30, 0x57, 0xD7, 0x35, 0x43, 0xC1, 0x03, 0x70, 0xE2, 0x6F, 0xF8, 0xEA, 0x43, 0x5D, 0x3C, 0x20, 0x0F, 0x73, 0x5D, 0x08, 0x14, 0xF8, 0xE0, 0x64, 0x53, 0xF1, 0x29, 0xB5, 0x35, 0xD7, 0x84, 0x47, 0xF1, 0x25, 0xF6, 0xC3, 0xD4, 0xE4, 0x62, 0x4A, 0x7C, 0x9A, 0x8B, 0x9B, 0x8A, 0xEF, 0x66, 0xC3, 0xAB, 0x76, 0x35, 0x17, 0x7F, 0x93, 0x93, 0x81, 0xA4, 0x88, 0x81, 0xDC, 0x94, 0x3A, 0xA2, 0xD0, 0x4F, 0x00, 0x00, 0x8C, 0x58, 0xEA, 0xF3, 0x7C, 0x56, 0x1B, 0x47, 0x1C, 0xEC, 0xFA, 0x57, 0x2D, 0x32, 0xB7, 0xDE, 0x4C, 0x6C, 0xC8, 0x55, 0x13, 0xBC, 0x8B, 0x26, 0x56, 0xCF, 0xBF, 0xDA, 0xDB, 0xEE, 0x17, 0x0A, 0x4C, 0x25, 0xDD, 0x66, 0xDA, 0xD7, 0x0F, 0x22, 0x75, 0xD8, 0x1D, 0xEC, 0x38, 0xE2, 0xB7, 0x20, 0x8B, 0x58, 0x3C, 0xE6, 0x20, 0x9E, 0xA5, 0xD9, 0x96, 0x4B, 0x2C, 0x1C, 0xAC, 0xFA, 0xDE, 0x44, 0x36, 0x93, 0x69, 0x3A, 0xD4, 0xC4, 0x5D, 0x13, 0xCD, 0x96, 0xBB, 0x52, 0xA8, 0x18, 0x30, 0xF7, 0x94, 0xDE, 0xE1, 0x46, 0x32, 0x99, 0xF0, 0xA3, 0xE3, 0xF7, 0x61, 0xC1, 0x04, 0x2E, 0x45, 0xFF, 0xD4, 0xA0, 0x42, 0x5C, 0xC2, 0x95, 0x45, 0x97, 0x02, 0xDA, 0xC8, 0x7E, 0x4B, 0x31, 0x7B, 0xD1, 0x15, 0xAA, 0x8F, 0x43, 0x28, 0x58, 0xD6, 0x1A, 0x77, 0x4B, 0x41, 0x2D, 0xF5, 0x22, 0x86, 0x8B, 0x5A, 0x24, 0xCB, 0xBF, 0x1B, 0x3A, 0x77, 0xE3, 0x43, 0x6E, 0xC0, 0x95, 0x89, 0x4F, 0x00, 0x00, 0x8F, 0x58, 0xEB, 0x98, 0x2E, 0x7D, 0x15, 0xA0, 0xC6, 0x6C, 0x84, 0xD6, 0xB5, 0x70, 0x42, 0xA9, 0x9F, 0x1F, 0xA4, 0x63, 0x4B, 0xD2, 0x80, 0x72, 0xD6, 0xDA, 0xCC, 0x6E, 0xA5, 0xDA, 0x11, 0xF6, 0xC8, 0x81, 0x95, 0x87, 0xC5, 0xE5, 0xA9, 0xEE, 0xB2, 0xF4, 0xFA, 0x92, 0xCA, 0x94, 0x9D, 0x47, 0x30, 0xFD, 0xB1, 0x6E, 0x9C, 0xA0, 0x5C, 0x19, 0x86, 0x1D, 0xFA, 0x09, 0x44, 0x32, 0xC2, 0xF8, 0x4C, 0x1E, 0x4C, 0x0F, 0xC4, 0x01, 0x85, 0x27, 0xCB, 0x18, 0x5B, 0x87, 0x7C, 0xA0, 0x4D, 0xCC, 0x77, 0x4F, 0x14, 0x33, 0xD9, 0xEA, 0x99, 0xA2, 0x61, 0x14, 0x9A, 0xC8, 0x50, 0x7A, 0x36, 0xD5, 0x5E, 0x1F, 0x2B, 0x30, 0x51, 0xF7, 0x5F, 0xED, 0x28, 0x11, 0xEA, 0x94, 0xE9, 0xFF, 0x8B, 0x61, 0x9F, 0xA7, 0x6A, 0x1F, 0x6B, 0x06, 0x44, 0x32, 0xA0, 0x70, 0x8B, 0x52, 0x61, 0x7D, 0x55, 0x60, 0x0F, 0x23, 0xBE, 0x48, 0xB1, 0x64, 0x9D, 0x56, 0x74, 0xAD, 0x91, 0x01, 0xA3, 0x4A, 0x9B, 0x68, 0x4F, 0x00, 0x00, 0x52, 0x58, 0x82, 0x8D, 0xFC, 0x42, 0x90, 0x14, 0x2A, 0x07, 0xFD, 0x74, 0x1B, 0xE0, 0xD6, 0x0B, 0x1F, 0x50, 0xA0, 0x36, 0x38, 0xBC, 0x8E, 0x45, 0xE1, 0x99, 0xD9, 0xD8, 0xEB, 0x81, 0x19, 0x19, 0x92, 0x3A, 0xE2, 0x27, 0x67, 0xD7, 0x01, 0x9A, 0xB6, 0x1E, 0x86, 0x3C, 0x65, 0x15, 0xFD, 0x25, 0xED, 0xCA, 0x78, 0x16, 0x5A, 0x96, 0x5B, 0x8A, 0xF7, 0xB6, 0xE1, 0x48, 0x32, 0x43, 0xF7, 0x7B, 0x78, 0xD6, 0x94, 0xFC, 0xDC, 0x0D, 0xDB, 0x68, 0xFE, 0x74, 0xD9, 0x25, 0x9C, 0x82, 0xBA, 0xFF, 0x6C, 0x9F, 0xC0, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20
};

#endif // AUDIO_DATA_SERVERDISCONNECTED_H