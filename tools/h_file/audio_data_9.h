#ifndef AUDIO_DATA_9_H
#define AUDIO_DATA_9_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfo9[] = {0, 24, 49, 103, 244, 381, 489, 611, 758, 900, 1039, 1097};
const uint8_t g_audioData9[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x32, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x0F, 0xF2, 0xB4, 0x69, 0x8B, 0xF3, 0x84, 0x3C, 0x69, 0x77, 0xA7, 0xE2, 0x35, 0xAE, 0x2C, 0x25, 0x74, 0x8A, 0xE6, 0xBA, 0xA6, 0xF5, 0x41, 0x64, 0x6E, 0x4F, 0xE5, 0x6C, 0xA8, 0x52, 0x5C, 0x42, 0x81, 0x5B, 0xA0, 0x5B, 0x8E, 0xFD, 0xD2, 0xC7, 0xC6, 0xE0, 0x4F, 0x00, 0x00, 0x89, 0x58, 0xE0, 0xFD, 0x55, 0xA7, 0xA5, 0xF4, 0x4A, 0xB5, 0x75, 0x70, 0x73, 0xA3, 0x51, 0xAC, 0x11, 0x9A, 0x9E, 0xA0, 0xE0, 0x4A, 0x50, 0xE8, 0xDF, 0xAA, 0xE0, 0x2C, 0x41, 0xF0, 0xB6, 0x99, 0x06, 0xB6, 0x0F, 0x73, 0x4E, 0x38, 0x88, 0xC7, 0x2F, 0xF7, 0x75, 0x3D, 0xED, 0xB4, 0xF3, 0x71, 0x9B, 0x35, 0xD0, 0xEE, 0x5E, 0x3D, 0x75, 0x70, 0xFD, 0x50, 0xB3, 0x4D, 0x83, 0x1F, 0xA6, 0x28, 0x52, 0xB7, 0xC4, 0x7D, 0x44, 0x51, 0x4A, 0xCC, 0xAE, 0x54, 0x01, 0x15, 0x14, 0x88, 0x0A, 0xDC, 0xD7, 0x5F, 0x41, 0x41, 0xE3, 0x55, 0xD5, 0x16, 0xB8, 0x36, 0x40, 0x39, 0xDE, 0x5A, 0xF3, 0x3F, 0xD8, 0xE5, 0x55, 0x0D, 0x36, 0xF1, 0x20, 0x65, 0xBA, 0x62, 0x5A, 0x06, 0x56, 0xB0, 0x49, 0x76, 0xF2, 0xE9, 0xB7, 0xA9, 0x74, 0x6E, 0x57, 0x4B, 0x16, 0x78, 0xB8, 0x9A, 0x14, 0xE9, 0x84, 0x2A, 0x02, 0x88, 0xAB, 0x0F, 0xE2, 0x07, 0x7F, 0x04, 0x4C, 0x80, 0x4F, 0x00, 0x00, 0x85, 0x58, 0xE5, 0x74, 0x78, 0xEC, 0xEC, 0x8E, 0x54, 0x70, 0xF0, 0x69, 0xAA, 0xC3, 0x03, 0xBE, 0xB3, 0x54, 0xFB, 0xF7, 0x0E, 0x0B, 0x55, 0xE2, 0x7A, 0x9F, 0xDC, 0x18, 0xB9, 0x44, 0xA2, 0x7B, 0x66, 0x58, 0x75, 0xAF, 0x7E, 0x0D, 0x72, 0x4D, 0x94, 0x5A, 0x3A, 0x4E, 0x15, 0x01, 0xDD, 0x09, 0x1B, 0x3B, 0xCB, 0xF4, 0xB5, 0xA7, 0x46, 0x8C, 0x21, 0x86, 0x05, 0x5E, 0xFE, 0x08, 0x54, 0x3B, 0x00, 0xDA, 0x84, 0xCC, 0x36, 0x2E, 0x16, 0xFB, 0xA5, 0xE1, 0xCD, 0x71, 0xE5, 0x34, 0xB5, 0xDF, 0x9F, 0x5F, 0x63, 0x54, 0xB0, 0x90, 0x50, 0x02, 0xE4, 0x99, 0x2E, 0x8D, 0x59, 0x36, 0x44, 0x53, 0xE8, 0x46, 0x1E, 0x97, 0x2C, 0xA7, 0x84, 0x2B, 0x73, 0xCF, 0xAE, 0x2B, 0x54, 0xAB, 0x43, 0xC8, 0xF3, 0x91, 0x4F, 0xD0, 0x9E, 0x5F, 0xA3, 0x98, 0x7D, 0x67, 0xB6, 0xC9, 0x22, 0x31, 0x82, 0xA4, 0x7B, 0x94, 0xD9, 0xCB, 0xF6, 0x12, 0x4F, 0x00, 0x00, 0x68, 0x58, 0xEE, 0x1A, 0x86, 0x5D, 0x4C, 0xD3, 0x0A, 0x8E, 0x44, 0xB2, 0xD2, 0x97, 0xBB, 0x59, 0xF8, 0x9B, 0x8D, 0xA0, 0x8F, 0xEA, 0x91, 0x6A, 0x1F, 0xE0, 0x4D, 0x73, 0x82, 0xDD, 0xC4, 0xF2, 0x13, 0x9A, 0x2A, 0x83, 0x1B, 0x3C, 0xE0, 0x15, 0xA8, 0x28, 0x5E, 0xFE, 0xBB, 0x8B, 0x4D, 0xD0, 0x6A, 0x33, 0xCE, 0x19, 0xE3, 0x55, 0xD9, 0x4E, 0xFF, 0x6D, 0x12, 0xA0, 0xB6, 0xA4, 0xBA, 0xB5, 0x83, 0x9C, 0x1D, 0x4D, 0xB4, 0x15, 0x8A, 0x5F, 0xBB, 0xC0, 0x81, 0x62, 0xC3, 0xDF, 0xA2, 0xD2, 0xA2, 0xF5, 0x52, 0xD2, 0x77, 0x9A, 0xC7, 0xE5, 0x64, 0x51, 0xAE, 0x8E, 0x58, 0x34, 0xA6, 0xEA, 0x0E, 0x7B, 0xF4, 0x7F, 0xC1, 0x64, 0x77, 0x91, 0x04, 0x4F, 0x00, 0x00, 0x76, 0x58, 0xED, 0xA6, 0xAD, 0x48, 0x6B, 0x51, 0xA6, 0xB0, 0x1E, 0xF9, 0x99, 0x18, 0xCA, 0x0A, 0xC0, 0x59, 0xE4, 0x51, 0x87, 0x75, 0xBA, 0x86, 0x8F, 0x78, 0x38, 0xBC, 0xBB, 0x7B, 0x03, 0x8E, 0xAB, 0x49, 0x2A, 0x22, 0x8D, 0x56, 0x1A, 0xD6, 0x70, 0x9B, 0x17, 0x9D, 0x7F, 0x2A, 0x70, 0x75, 0x69, 0xBD, 0x98, 0xE3, 0xE6, 0x98, 0x39, 0x60, 0x18, 0x3A, 0xF8, 0x3E, 0x92, 0x78, 0x14, 0xDC, 0xC3, 0x5E, 0x42, 0x3E, 0x6C, 0x6B, 0xAA, 0x83, 0x10, 0x45, 0x9C, 0xF0, 0xCB, 0x03, 0x96, 0xA9, 0x2C, 0xF8, 0x33, 0xF0, 0x4F, 0x37, 0x4E, 0x3F, 0x08, 0xA5, 0x75, 0x04, 0x14, 0x62, 0x83, 0xE3, 0x3F, 0xB7, 0xD4, 0x4C, 0xFA, 0x51, 0xF2, 0x5E, 0xB6, 0x47, 0x3C, 0xE8, 0xD9, 0x9E, 0x03, 0xC7, 0xF0, 0xAD, 0x0B, 0x88, 0xCF, 0x6A, 0x04, 0x4F, 0x00, 0x00, 0x8F, 0x58, 0xEC, 0xF4, 0x9C, 0xA2, 0x46, 0xB9, 0x0F, 0xC3, 0x6D, 0xEF, 0xB3, 0x6A, 0x35, 0xA1, 0x89, 0x62, 0xC3, 0xE4, 0x01, 0x08, 0xA6, 0xD2, 0x99, 0x9C, 0x6D, 0xD1, 0x7D, 0x03, 0xB3, 0x6B, 0xD9, 0x69, 0xFF, 0xAF, 0x66, 0xB9, 0x25, 0xEE, 0x65, 0xC9, 0xC0, 0xF7, 0xA8, 0x51, 0x5F, 0x63, 0xB5, 0xDC, 0x6F, 0x24, 0x10, 0x47, 0x40, 0x15, 0x54, 0xB9, 0x51, 0xAA, 0xEA, 0x88, 0xEE, 0xC7, 0x6E, 0x5D, 0x80, 0xEC, 0x15, 0x14, 0x98, 0xEB, 0x3B, 0xFC, 0x56, 0x49, 0x38, 0x0D, 0x3B, 0x44, 0x79, 0xF2, 0xE6, 0x29, 0x97, 0x9B, 0x17, 0x26, 0x07, 0xB0, 0x0E, 0x95, 0xD7, 0x9E, 0x51, 0x20, 0x72, 0xC7, 0x66, 0x2A, 0x0C, 0x32, 0xFF, 0xCB, 0x12, 0xEF, 0x50, 0x79, 0x36, 0x4C, 0x7C, 0xAE, 0x2E, 0xCE, 0x25, 0x42, 0x32, 0x1B, 0xAD, 0xE1, 0x82, 0xB3, 0x7E, 0xFD, 0x4C, 0xE4, 0x45, 0x1A, 0xB3, 0x23, 0xB7, 0x66, 0x85, 0x84, 0x79, 0x90, 0x1C, 0x60, 0x55, 0xE1, 0x82, 0xD5, 0x17, 0x60, 0x4F, 0x00, 0x00, 0x8A, 0x58, 0xED, 0x48, 0x23, 0x8E, 0x63, 0x0B, 0xC4, 0xC9, 0x66, 0x38, 0xC1, 0x5A, 0x76, 0xF6, 0xE1, 0x5C, 0xB2, 0x50, 0x30, 0x6C, 0xA2, 0xF3, 0x6D, 0xAD, 0xE5, 0x4A, 0xF6, 0x2F, 0x46, 0xBE, 0xEA, 0xEC, 0x17, 0x4B, 0xFD, 0xF0, 0x4E, 0xD1, 0x8B, 0xAD, 0x3A, 0xAD, 0x60, 0x1A, 0x73, 0x3F, 0x0C, 0x65, 0x2B, 0xA0, 0x99, 0xA6, 0x73, 0x1E, 0x3C, 0xD5, 0x9B, 0x07, 0xD8, 0xEC, 0x5C, 0x3E, 0x5D, 0xA2, 0xC2, 0xAC, 0x75, 0x6E, 0x29, 0x94, 0xAF, 0x93, 0x19, 0xAD, 0x49, 0x1B, 0x3D, 0x35, 0xFC, 0x64, 0xDD, 0x5C, 0x10, 0x2C, 0x5F, 0x11, 0x4A, 0x7F, 0xAF, 0x35, 0xC1, 0x53, 0x20, 0x54, 0xAD, 0x8E, 0x6B, 0xC5, 0x58, 0x96, 0x91, 0x0E, 0xAD, 0xB2, 0x8A, 0x15, 0xCA, 0x95, 0xE6, 0x5F, 0x2E, 0xD3, 0xEA, 0xFB, 0x25, 0x82, 0x26, 0xA7, 0xD1, 0x9F, 0x48, 0xF0, 0x7E, 0x15, 0x95, 0xB8, 0x45, 0xA4, 0xCF, 0xC2, 0xCA, 0x6B, 0x7A, 0x50, 0xB7, 0xF3, 0x50, 0x4F, 0x00, 0x00, 0x87, 0x58, 0xCC, 0x5C, 0x4D, 0xD0, 0x4F, 0x06, 0x89, 0x0A, 0x30, 0xB6, 0x78, 0x1B, 0xDF, 0x6E, 0xB7, 0x2A, 0xE7, 0x5C, 0x05, 0x98, 0x97, 0xA8, 0x27, 0xAB, 0x13, 0xBF, 0x2D, 0x54, 0x6B, 0xA1, 0xD7, 0xFB, 0x36, 0x46, 0xDE, 0x67, 0x09, 0xEC, 0x18, 0x77, 0x36, 0x97, 0x1B, 0x9B, 0x46, 0xC3, 0x3B, 0xC4, 0xFC, 0x30, 0x82, 0x80, 0x37, 0x18, 0xEE, 0x21, 0xBC, 0x1E, 0xEF, 0xB7, 0x8A, 0x93, 0xA4, 0x15, 0xB1, 0xDA, 0x3C, 0x7B, 0xE8, 0x6A, 0xE9, 0xC3, 0x3A, 0x4E, 0xA9, 0x33, 0x5D, 0xB6, 0xC0, 0x01, 0x4C, 0xBC, 0x19, 0xE2, 0x63, 0xBE, 0x82, 0x6A, 0x46, 0x97, 0x7A, 0x51, 0x99, 0xC6, 0x8F, 0x4D, 0x83, 0xE7, 0x45, 0xE0, 0x61, 0x13, 0xAD, 0x40, 0xE3, 0x44, 0x7D, 0xF1, 0xC0, 0xA6, 0x2E, 0x87, 0x32, 0xC4, 0x83, 0xBC, 0x8C, 0x52, 0xC0, 0x44, 0x3C, 0x60, 0xB2, 0x3D, 0x1E, 0x12, 0xC5, 0xE3, 0x03, 0xDE, 0x06, 0x18, 0x4B, 0xC4, 0x4F, 0x00, 0x00, 0x36, 0x58, 0x08, 0x29, 0x43, 0x6B, 0xE7, 0x07, 0x93, 0x77, 0x00, 0x30, 0x8F, 0x22, 0x24, 0xB2, 0xF7, 0x2A, 0x98, 0xF4, 0x46, 0x65, 0x6F, 0x9F, 0x40, 0x5C, 0x30, 0x9C, 0x77, 0xCB, 0x76, 0x90, 0x14, 0x1D, 0x74, 0xB2, 0xE9, 0x9C, 0x9A, 0x8D, 0xB3, 0x5F, 0x85, 0x34, 0x12, 0x6B, 0x15, 0x4B, 0x0E, 0x35, 0xD1, 0xB6, 0xCF, 0xE4, 0xC0, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20
};

#endif // AUDIO_DATA_9_H