#ifndef AUDIO_DATA_HERE_H
#define AUDIO_DATA_HERE_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfoHere[] = {0, 24, 79, 217, 320, 432, 553, 677, 760, 851, 959, 1010, 1035, 1060};
const uint8_t g_audioDataHere[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x33, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x6A, 0xBB, 0xD3, 0xBE, 0x22, 0xDE, 0x6F, 0x39, 0xF9, 0x29, 0xB9, 0xA7, 0xE1, 0x37, 0x29, 0x63, 0x83, 0x4F, 0x6A, 0x49, 0x97, 0xBC, 0x73, 0xE7, 0x72, 0x76, 0xDB, 0xBE, 0x33, 0x69, 0xD7, 0x08, 0x85, 0x5F, 0xF8, 0xA5, 0x40, 0x4F, 0x00, 0x00, 0x86, 0x58, 0xE4, 0x45, 0x5C, 0xA3, 0x0E, 0xBC, 0x82, 0x94, 0x49, 0x25, 0x90, 0x17, 0xB0, 0x73, 0xD8, 0x78, 0x72, 0xFC, 0xBB, 0xD5, 0x0D, 0x17, 0x4D, 0xB8, 0x23, 0x19, 0x57, 0x11, 0x7D, 0xAB, 0x5E, 0xEE, 0x27, 0xB9, 0x99, 0x35, 0x47, 0x18, 0x6D, 0xD7, 0xA6, 0x93, 0x2B, 0x41, 0x59, 0xF2, 0x90, 0x45, 0x25, 0x13, 0x5C, 0x44, 0x93, 0xBA, 0x92, 0x8F, 0xB6, 0x45, 0x97, 0xBB, 0x6D, 0x11, 0xEC, 0x34, 0xB6, 0xCE, 0xD0, 0x94, 0x14, 0xF0, 0x92, 0x8D, 0x3E, 0xF5, 0x32, 0x3C, 0xD7, 0x56, 0xE8, 0x92, 0x16, 0x11, 0x2B, 0x63, 0x64, 0xC0, 0x6E, 0x46, 0xDF, 0x25, 0x0C, 0x3C, 0x59, 0x26, 0xB4, 0x75, 0x8A, 0x26, 0x60, 0x4B, 0xC5, 0x67, 0xA5, 0xDA, 0x37, 0xB9, 0xBA, 0x0B, 0x0E, 0x9D, 0xA6, 0xAC, 0x80, 0xD0, 0xA0, 0x37, 0x5D, 0x2E, 0x9F, 0x03, 0x57, 0x05, 0x6C, 0xC6, 0x40, 0x77, 0x4A, 0xA7, 0x6C, 0x75, 0x40, 0xED, 0x9C, 0x4F, 0x00, 0x00, 0x63, 0x58, 0xEE, 0x69, 0x3E, 0x7F, 0xBC, 0x7B, 0x27, 0x76, 0x41, 0x02, 0xC5, 0xFF, 0x6D, 0x20, 0x0C, 0xB8, 0xC2, 0x76, 0xAD, 0x58, 0x83, 0xE1, 0x13, 0x25, 0xF1, 0x77, 0xC3, 0x2C, 0xD1, 0xEC, 0x0C, 0x0B, 0x67, 0x3E, 0x98, 0xAE, 0x85, 0x53, 0x3C, 0x23, 0x74, 0x59, 0x79, 0xD8, 0x08, 0x6B, 0x5E, 0xBB, 0x0C, 0xAA, 0xEF, 0x55, 0x86, 0x72, 0xED, 0x6D, 0x03, 0x38, 0x80, 0x32, 0x5B, 0xA5, 0xD4, 0x13, 0x86, 0x9E, 0x64, 0xB9, 0x99, 0x11, 0xCA, 0x39, 0x89, 0xD0, 0x2B, 0xED, 0xCF, 0x29, 0xD6, 0x3F, 0x96, 0x50, 0xA3, 0x2A, 0x6E, 0x1D, 0x65, 0x7F, 0x93, 0x62, 0xF8, 0x08, 0x94, 0xA5, 0x3B, 0x64, 0x6F, 0xE3, 0x4F, 0x00, 0x00, 0x6C, 0x58, 0xEE, 0x63, 0xD2, 0x09, 0x08, 0xAB, 0xB4, 0x46, 0x0D, 0xB3, 0x4C, 0xB8, 0x59, 0x43, 0xC7, 0xD7, 0x49, 0xCC, 0x5D, 0x5B, 0xAD, 0x8B, 0xDB, 0x33, 0x94, 0xF4, 0x2D, 0xE8, 0x1B, 0xBB, 0x25, 0xD1, 0x20, 0x3F, 0x0F, 0x3C, 0x4B, 0x5E, 0x30, 0xF2, 0x5A, 0x90, 0xC2, 0x44, 0xFF, 0x49, 0x26, 0x76, 0x4E, 0x5F, 0xCE, 0x43, 0xFD, 0xF9, 0xBC, 0xE3, 0x22, 0x4B, 0x89, 0x7C, 0x88, 0xE4, 0x6F, 0x8C, 0x7A, 0x28, 0x57, 0x37, 0xB6, 0x81, 0x1B, 0x4C, 0x66, 0xDD, 0x1B, 0xC7, 0xC8, 0x04, 0x65, 0xF8, 0xD3, 0x9F, 0xB0, 0xD1, 0xEF, 0x0D, 0x74, 0xA6, 0x50, 0x66, 0x6F, 0x8F, 0xF8, 0x53, 0xAC, 0x5E, 0x5B, 0x96, 0x38, 0xEA, 0x90, 0x7B, 0x64, 0x07, 0x10, 0x3E, 0xA0, 0x4F, 0x00, 0x00, 0x75, 0x58, 0xED, 0xE8, 0x0D, 0x92, 0xA1, 0x3E, 0xEF, 0xF7, 0xB0, 0x31, 0xE4, 0xA8, 0xC6, 0x22, 0x9D, 0x6F, 0xB3, 0x19, 0x5B, 0xDD, 0x18, 0x2E, 0x5B, 0xF5, 0x0C, 0x3D, 0xC8, 0x4E, 0xD4, 0xC4, 0x8E, 0xA4, 0x60, 0x36, 0xF5, 0xCF, 0x7C, 0x8B, 0xAF, 0x66, 0x53, 0xEE, 0x12, 0xA1, 0xD5, 0x8A, 0x23, 0x11, 0x35, 0x70, 0x5E, 0xB1, 0x7B, 0xE8, 0xF7, 0xFE, 0x3C, 0x12, 0x9D, 0x78, 0x46, 0x31, 0xEE, 0x0B, 0xB9, 0xCE, 0x18, 0xD9, 0x9E, 0x66, 0xB6, 0x88, 0xD6, 0xDC, 0xCD, 0xC5, 0x3C, 0xAC, 0xAB, 0xD0, 0xBF, 0x0E, 0x14, 0xE8, 0xA7, 0xA1, 0x98, 0xCB, 0xF9, 0x50, 0xFB, 0x63, 0xE2, 0x00, 0x10, 0xEF, 0x82, 0x0A, 0x8A, 0xD6, 0x69, 0xC9, 0x6D, 0xDA, 0x92, 0xE4, 0xE1, 0x12, 0x44, 0x83, 0x96, 0x08, 0x76, 0xBF, 0x0D, 0x40, 0x4F, 0x00, 0x00, 0x78, 0x58, 0xE9, 0x02, 0x23, 0x52, 0x1F, 0xFB, 0x7E, 0xDD, 0xB8, 0xC8, 0xBD, 0x43, 0x5E, 0x73, 0xCD, 0x59, 0x33, 0x11, 0x08, 0x0A, 0x65, 0x99, 0x7D, 0x97, 0x4C, 0x92, 0x01, 0xDB, 0x6C, 0x79, 0xB9, 0xEC, 0xB4, 0xC9, 0x07, 0xC5, 0xD7, 0xE0, 0xF5, 0xB7, 0x1F, 0x01, 0xBF, 0x0F, 0xA5, 0x0D, 0xE5, 0x14, 0x34, 0x2A, 0x46, 0x5D, 0xAB, 0xB2, 0x4D, 0xE6, 0x40, 0xC3, 0xD4, 0xA0, 0xD7, 0xF6, 0x42, 0x4E, 0x82, 0xF8, 0x86, 0x30, 0x76, 0xB3, 0x9A, 0x7E, 0xCC, 0xCC, 0xAC, 0xE8, 0x22, 0xF0, 0xCE, 0x38, 0x40, 0xE0, 0x84, 0xE5, 0xCB, 0xFD, 0xEA, 0x75, 0x14, 0x48, 0xD6, 0x6D, 0x94, 0xF0, 0x11, 0x14, 0x5C, 0x64, 0xF8, 0xBD, 0x9D, 0xA5, 0xAF, 0xB4, 0x4D, 0x4E, 0x1E, 0x87, 0xA7, 0x96, 0x0B, 0xF5, 0xEB, 0x5D, 0x66, 0xAA, 0xD9, 0x3D, 0xC0, 0x4F, 0x00, 0x00, 0x4F, 0x58, 0xED, 0xBA, 0xC6, 0x49, 0x94, 0x52, 0x8B, 0xC9, 0xFA, 0xCF, 0x11, 0x88, 0x89, 0x95, 0x90, 0x3D, 0x8F, 0x98, 0x67, 0xC2, 0xE9, 0xB6, 0x68, 0x2F, 0x75, 0xB6, 0x94, 0xDF, 0x64, 0x0B, 0x36, 0x99, 0xF7, 0xCF, 0xB7, 0xCC, 0x22, 0x91, 0x91, 0x0F, 0x2C, 0x80, 0xE7, 0xDB, 0x3F, 0x78, 0xFE, 0x83, 0xC0, 0x93, 0xEC, 0x8D, 0xD6, 0x7E, 0x8D, 0x8B, 0xB7, 0x8F, 0xF9, 0xC3, 0x30, 0xC2, 0xF1, 0x75, 0x99, 0x01, 0xFC, 0x7D, 0x4B, 0x58, 0xC4, 0x0C, 0x56, 0x34, 0xF3, 0xF3, 0xB5, 0x48, 0x4F, 0x00, 0x00, 0x57, 0x58, 0xED, 0x46, 0xDC, 0x1C, 0xFB, 0xA3, 0x34, 0xF0, 0x7A, 0x14, 0xCA, 0x56, 0xC6, 0x56, 0xF3, 0x4A, 0xC0, 0x7F, 0xA1, 0xC4, 0x02, 0xCE, 0x52, 0x52, 0x1F, 0x0C, 0x5F, 0xA3, 0x99, 0xB2, 0x80, 0xC4, 0x49, 0x8B, 0x74, 0xD1, 0x6A, 0x0F, 0xFF, 0xD6, 0x9C, 0x84, 0xEC, 0xB7, 0x17, 0xEE, 0x3E, 0xD7, 0xC6, 0xD5, 0xF1, 0x04, 0x12, 0x56, 0xBB, 0x59, 0xA4, 0x34, 0xA7, 0x43, 0xDD, 0xF5, 0xEA, 0xB4, 0x42, 0xB6, 0x5B, 0x05, 0x9A, 0xB2, 0x0C, 0x0A, 0x1D, 0x60, 0x8B, 0x96, 0x08, 0x0F, 0x07, 0x5C, 0xAE, 0x64, 0x7C, 0x9A, 0x61, 0x64, 0x4F, 0x00, 0x00, 0x68, 0x58, 0xCB, 0x91, 0xF3, 0xE0, 0x72, 0x52, 0xD5, 0xAF, 0x8B, 0x8E, 0x25, 0xFA, 0x2E, 0x2D, 0x12, 0x4F, 0xFB, 0xC6, 0x23, 0x95, 0xD1, 0x11, 0x96, 0x45, 0xB3, 0x1A, 0x51, 0x5C, 0x31, 0x32, 0x1B, 0x77, 0x34, 0xF4, 0x3B, 0x93, 0x66, 0x34, 0x06, 0x98, 0x7E, 0x96, 0xBA, 0x12, 0x3A, 0x12, 0x92, 0xD3, 0x55, 0xD9, 0x54, 0xDE, 0x77, 0xB0, 0xEA, 0xB4, 0xF6, 0x88, 0xBF, 0x9D, 0x6F, 0x4C, 0xC5, 0xC1, 0x6E, 0x96, 0x50, 0x0C, 0x74, 0x82, 0x3E, 0x5D, 0x6E, 0x40, 0xC3, 0x32, 0x94, 0x40, 0xC7, 0x33, 0x1B, 0x70, 0xBA, 0x0F, 0xAF, 0xF0, 0x66, 0x04, 0x63, 0x95, 0x1B, 0x4B, 0x88, 0x63, 0xC3, 0x3C, 0xDA, 0x89, 0x4F, 0x26, 0xF4, 0xA9, 0x7E, 0x4F, 0x00, 0x00, 0x2F, 0x58, 0x00, 0xAA, 0x75, 0xC7, 0x11, 0xF6, 0x1E, 0x1F, 0x99, 0xE5, 0x01, 0x47, 0xA6, 0xBD, 0x8F, 0xB0, 0xDC, 0xB5, 0x61, 0x47, 0x02, 0x1A, 0x00, 0x04, 0x88, 0xD7, 0xF1, 0x6C, 0xD4, 0xAB, 0xEB, 0x7F, 0xE6, 0x3C, 0xE1, 0xF2, 0xBC, 0x09, 0x92, 0x52, 0xE6, 0x65, 0xE7, 0x60, 0xC8, 0xEA, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20
};

#endif // AUDIO_DATA_HERE_H