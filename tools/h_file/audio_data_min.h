#ifndef AUDIO_DATA_MIN_H
#define AUDIO_DATA_MIN_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfoMin[] = {0, 24, 49, 113, 244, 391, 533, 687, 823, 965, 1096, 1212, 1333, 1427, 1559, 1629};
const uint8_t g_audioDataMin[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x3C, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x0F, 0xF3, 0xB1, 0x4B, 0x6F, 0x84, 0x5A, 0x28, 0x8B, 0x2B, 0xEF, 0x20, 0x09, 0x8E, 0x15, 0x60, 0x3F, 0x28, 0x81, 0x34, 0x2F, 0xA7, 0x71, 0x97, 0xCF, 0x11, 0xC3, 0x1D, 0x69, 0x68, 0x84, 0x86, 0x60, 0x71, 0xB3, 0x68, 0x36, 0xBA, 0x34, 0x19, 0x16, 0x79, 0x2E, 0x0B, 0x53, 0xE2, 0x43, 0x0B, 0x6B, 0xBC, 0x8C, 0xA0, 0x4F, 0x00, 0x00, 0x7F, 0x58, 0xE3, 0xB8, 0x42, 0x2E, 0x30, 0x46, 0xD7, 0x54, 0x53, 0x79, 0xDB, 0x8C, 0xFF, 0x1D, 0xF0, 0xAB, 0xF7, 0x2F, 0xDC, 0x10, 0xDC, 0x29, 0x66, 0x3D, 0x4D, 0xDE, 0x21, 0x82, 0x0E, 0x56, 0x87, 0x4E, 0xFF, 0x62, 0xF2, 0x9A, 0x77, 0x3E, 0x1E, 0xAE, 0x0D, 0x7C, 0x87, 0x1B, 0xE7, 0x16, 0x97, 0xE9, 0xA8, 0xF1, 0x94, 0x43, 0xC1, 0x56, 0xCC, 0xFD, 0x44, 0xC0, 0x78, 0x00, 0xB6, 0xF3, 0xB8, 0x8C, 0xF8, 0xB1, 0x52, 0xEF, 0xC0, 0xA3, 0xE1, 0x83, 0x91, 0xA7, 0x66, 0xF9, 0xB5, 0xF5, 0x07, 0x68, 0xE7, 0xAE, 0xBC, 0x01, 0xFB, 0x01, 0x04, 0x60, 0x5E, 0x67, 0xC3, 0xAA, 0x2B, 0xDB, 0x23, 0xDC, 0x75, 0xA4, 0xEB, 0x78, 0x4A, 0x2C, 0x49, 0xBB, 0x02, 0x2E, 0xB9, 0x84, 0x3E, 0x84, 0xDF, 0x56, 0x0D, 0x8E, 0x8A, 0x04, 0x3F, 0xD8, 0x00, 0xC5, 0x1F, 0xEE, 0xDA, 0x42, 0xEA, 0x10, 0x4F, 0x00, 0x00, 0x8F, 0x58, 0xED, 0x33, 0xCE, 0x82, 0xC9, 0x09, 0xFA, 0xBE, 0x3D, 0xBF, 0x49, 0x55, 0xF2, 0x11, 0xAB, 0x45, 0x5C, 0x2C, 0x87, 0x3A, 0xE3, 0xE4, 0x4A, 0xF8, 0x5C, 0x8F, 0xCE, 0xBB, 0x6C, 0xDC, 0x8E, 0xA8, 0xF9, 0x2B, 0x43, 0xF6, 0x28, 0xD9, 0xA9, 0xED, 0x8C, 0x46, 0x31, 0x5E, 0x3B, 0x1E, 0x07, 0x46, 0x42, 0xCE, 0x72, 0x9C, 0x02, 0xCD, 0x83, 0xD2, 0xEB, 0xC2, 0x95, 0x33, 0x57, 0x69, 0xD3, 0x0B, 0x64, 0x32, 0x1C, 0x81, 0xBB, 0x64, 0xE2, 0x0C, 0x70, 0x6F, 0xB7, 0x55, 0x38, 0x1F, 0x2B, 0x47, 0x56, 0x3F, 0xCA, 0xF0, 0xDF, 0xA6, 0x4F, 0x4D, 0xD7, 0x15, 0xA1, 0x2C, 0xC4, 0xD9, 0xB1, 0xE2, 0xBB, 0x59, 0x7C, 0xDE, 0x25, 0xBC, 0x79, 0x91, 0xEC, 0xDC, 0xC7, 0x24, 0x96, 0x72, 0x83, 0x08, 0xF6, 0xAF, 0xDA, 0x80, 0x60, 0x39, 0xC9, 0xAE, 0x7E, 0xDA, 0xA0, 0x74, 0xF2, 0xE2, 0x66, 0xE4, 0xD7, 0x1B, 0x0F, 0x49, 0xFB, 0xEA, 0x11, 0x1A, 0xD7, 0x6D, 0xC7, 0xE2, 0x5F, 0x80, 0x4F, 0x00, 0x00, 0x8A, 0x58, 0xED, 0xC2, 0x3F, 0x9B, 0xAE, 0xC1, 0x05, 0x41, 0xD6, 0x34, 0x87, 0x73, 0x44, 0x28, 0x2C, 0xC2, 0xC9, 0x26, 0xBC, 0xD3, 0x7A, 0xDB, 0x61, 0x2D, 0x84, 0x84, 0x08, 0x90, 0x39, 0x13, 0x92, 0x66, 0xA8, 0xA4, 0x78, 0x78, 0xE9, 0x09, 0xEF, 0x99, 0x91, 0x6D, 0x77, 0xFE, 0xD7, 0x6B, 0xD5, 0xB5, 0x95, 0xF6, 0xF4, 0xE0, 0xB3, 0x6E, 0xD6, 0x36, 0xCD, 0x34, 0xE5, 0x75, 0xBB, 0x53, 0x4F, 0x3C, 0x24, 0xC5, 0xF6, 0xDD, 0xD9, 0xD5, 0x4B, 0xCD, 0xDD, 0x05, 0x3B, 0x19, 0x9B, 0xE9, 0xCF, 0xDE, 0x4C, 0x9B, 0xEA, 0xD8, 0x5F, 0xEB, 0x1F, 0xE2, 0x91, 0x22, 0xDA, 0x89, 0x91, 0xFA, 0x6C, 0x4B, 0xBC, 0x31, 0x4C, 0x24, 0x5D, 0x06, 0xE7, 0xF4, 0x7E, 0xEA, 0x71, 0x9F, 0x28, 0xAC, 0xAB, 0x5A, 0x50, 0xB7, 0x1E, 0xAD, 0x1B, 0xB1, 0xAC, 0xDE, 0xFC, 0xF8, 0xDE, 0x5D, 0x17, 0x40, 0x39, 0xA9, 0x46, 0xA1, 0x6B, 0x77, 0x98, 0x4A, 0x44, 0x2A, 0x40, 0x4F, 0x00, 0x00, 0x96, 0x58, 0xED, 0xF3, 0xC2, 0x69, 0x79, 0x81, 0x33, 0xD9, 0x40, 0x89, 0xA5, 0x06, 0xE7, 0x7A, 0x77, 0x80, 0xE8, 0x8B, 0x66, 0x65, 0xCA, 0x74, 0x1A, 0xAB, 0xEC, 0x90, 0x4B, 0xA0, 0x0F, 0x13, 0x99, 0x6F, 0x70, 0x12, 0x7D, 0xF4, 0x01, 0xB4, 0x5B, 0xD1, 0xB4, 0xF4, 0x87, 0x66, 0x67, 0xBA, 0x4B, 0xAB, 0x9D, 0xE8, 0xEA, 0x86, 0x5D, 0x60, 0xF7, 0xBE, 0x7D, 0x5E, 0x96, 0xBE, 0xE6, 0x90, 0x67, 0xDD, 0x04, 0x6E, 0xE1, 0x93, 0xB5, 0x27, 0x5A, 0x77, 0xB1, 0xFB, 0xB9, 0xC8, 0x47, 0x6F, 0xF2, 0xA9, 0x39, 0x08, 0x10, 0x81, 0xF1, 0x50, 0x62, 0x6D, 0xAA, 0xF7, 0xE9, 0xDF, 0x37, 0xAC, 0xBF, 0xB2, 0x9E, 0x46, 0x34, 0x6B, 0x65, 0x31, 0x6E, 0x1C, 0x40, 0x89, 0xA9, 0x66, 0xCD, 0x26, 0xDF, 0x66, 0xF7, 0xAC, 0x1B, 0x76, 0xD1, 0xCD, 0xAF, 0x1A, 0xEA, 0xF4, 0x35, 0x1A, 0x05, 0xF5, 0x26, 0x2D, 0x97, 0xD0, 0x0A, 0xFE, 0xCB, 0x87, 0xEC, 0x5C, 0x1A, 0x68, 0x40, 0xED, 0xB3, 0xA9, 0xFD, 0x30, 0x28, 0x4B, 0x4A, 0xD2, 0x58, 0x4F, 0x00, 0x00, 0x84, 0x58, 0xE5, 0x42, 0x80, 0xA4, 0xC1, 0x8F, 0x3F, 0xD2, 0x14, 0xA5, 0x77, 0x9E, 0x9A, 0x16, 0x71, 0x50, 0xE8, 0x48, 0x6D, 0xC2, 0xCF, 0x8A, 0xAC, 0x24, 0x38, 0x8A, 0x21, 0xD0, 0xCE, 0x2E, 0x13, 0x53, 0xBB, 0x47, 0x40, 0x72, 0x3B, 0xF7, 0x51, 0xE7, 0xFB, 0xC4, 0x77, 0xB8, 0x1B, 0xF4, 0x8D, 0xAB, 0x65, 0xCB, 0x99, 0x7A, 0x51, 0x7C, 0x89, 0xC4, 0x67, 0xEF, 0x94, 0xFA, 0x3F, 0xE4, 0xD0, 0xC2, 0x60, 0x33, 0x8F, 0x31, 0x24, 0x68, 0x51, 0x15, 0x9A, 0xCB, 0x2B, 0xC1, 0x00, 0x82, 0xB4, 0xD7, 0xDB, 0x40, 0x82, 0xC0, 0xAC, 0x2D, 0x65, 0xCF, 0x55, 0x87, 0xD0, 0x91, 0xF5, 0xCE, 0x88, 0x83, 0x71, 0xDA, 0x63, 0x05, 0xAA, 0x93, 0xA6, 0x07, 0x2E, 0xEB, 0xAF, 0x8C, 0x94, 0xDC, 0x24, 0x86, 0x15, 0xAD, 0xE7, 0xF6, 0xF7, 0x65, 0xB0, 0x5C, 0xED, 0x5F, 0xCF, 0x5A, 0x4C, 0x00, 0x67, 0xC2, 0xD2, 0x1E, 0xE0, 0x4F, 0x00, 0x00, 0x8A, 0x58, 0xE4, 0xE0, 0x2D, 0x85, 0xF1, 0x83, 0x64, 0x9E, 0xBE, 0x3A, 0x88, 0xC8, 0x0F, 0x09, 0x1F, 0xAC, 0x8C, 0xA4, 0x25, 0x3E, 0x1C, 0x33, 0xA8, 0xD4, 0xD4, 0x78, 0x35, 0xB7, 0x36, 0x5D, 0x16, 0xBE, 0x89, 0xB9, 0x8E, 0x87, 0xFC, 0xC6, 0xE9, 0x0C, 0xAC, 0x1C, 0x00, 0x7A, 0x01, 0xD9, 0x8A, 0x5D, 0xED, 0x89, 0x18, 0x21, 0x98, 0x20, 0x7F, 0x9B, 0x65, 0x65, 0xF6, 0xC8, 0x1C, 0x90, 0x1B, 0x69, 0x2B, 0xA3, 0xAE, 0xD0, 0x3C, 0x59, 0xA7, 0x42, 0x06, 0x86, 0x64, 0x95, 0x82, 0x2E, 0xE7, 0xD1, 0x05, 0x07, 0x78, 0x39, 0x17, 0x80, 0x3A, 0xFC, 0x5E, 0xAE, 0x1A, 0x4E, 0x49, 0xED, 0x2C, 0xD7, 0xE1, 0x61, 0x26, 0x8A, 0x42, 0x29, 0xC4, 0xC7, 0xB6, 0xBA, 0x4F, 0x89, 0x5E, 0xB0, 0xD5, 0x66, 0x29, 0xBF, 0xF9, 0x74, 0x2F, 0xCB, 0x41, 0xA6, 0xE3, 0x3A, 0x35, 0xA6, 0x97, 0x33, 0xE0, 0xB0, 0xDD, 0xF2, 0x73, 0x98, 0x47, 0x09, 0x07, 0xCC, 0x60, 0x4F, 0x00, 0x00, 0x7F, 0x58, 0xED, 0xF5, 0xC8, 0x6E, 0xDD, 0x59, 0xC6, 0x58, 0x0D, 0x75, 0x6D, 0x3B, 0xB9, 0x0A, 0xF4, 0x29, 0xD6, 0x44, 0x52, 0x28, 0xEC, 0x40, 0xC7, 0x94, 0xF1, 0x18, 0x1B, 0x62, 0x2B, 0xEC, 0x1F, 0xDE, 0xDC, 0xC6, 0x91, 0x75, 0x1D, 0x57, 0xE3, 0xF1, 0x22, 0xD9, 0xB8, 0x46, 0xE4, 0xAE, 0x38, 0x71, 0x0F, 0xE4, 0xBA, 0xF6, 0xF3, 0x04, 0x20, 0x1D, 0xC9, 0x0F, 0x93, 0xBA, 0x28, 0x76, 0x7E, 0x84, 0x10, 0x0D, 0x5C, 0x8D, 0x33, 0xD0, 0xFD, 0xBD, 0x7B, 0x0A, 0x2B, 0xD9, 0xEC, 0x43, 0xED, 0x51, 0xDA, 0x55, 0x1E, 0xA0, 0xFD, 0x50, 0x79, 0x65, 0xBF, 0x74, 0xA8, 0x12, 0xEB, 0x5E, 0x15, 0x85, 0x7F, 0x10, 0x68, 0x2A, 0x1F, 0xEC, 0xDF, 0x43, 0x80, 0x3C, 0x7E, 0xA5, 0x60, 0x61, 0x29, 0x99, 0xDE, 0xD2, 0x4E, 0x63, 0xB1, 0x5D, 0x92, 0x70, 0x92, 0x28, 0x9A, 0xE8, 0xC4, 0x64, 0x4F, 0x00, 0x00, 0x70, 0x58, 0xED, 0x76, 0xA7, 0xFC, 0x32, 0x39, 0xF8, 0xB8, 0xFA, 0x82, 0x20, 0xF0, 0x36, 0x45, 0xF3, 0xE0, 0x1B, 0x95, 0x6C, 0x40, 0x3F, 0x6E, 0x32, 0xC3, 0xE7, 0xB4, 0x2F, 0x20, 0x2D, 0xC4, 0x47, 0x24, 0xE9, 0x57, 0x3D, 0x16, 0xD7, 0x42, 0x72, 0x71, 0x91, 0x39, 0x40, 0x83, 0x1A, 0x93, 0xF9, 0x96, 0x73, 0x41, 0x5D, 0x4A, 0xBB, 0xFD, 0xDE, 0xA1, 0x2F, 0x2D, 0xCF, 0x93, 0x8C, 0x3D, 0x21, 0xE1, 0x18, 0x7C, 0x14, 0xE1, 0x13, 0xD5, 0x4C, 0xE4, 0x4D, 0xC3, 0xF7, 0x43, 0xC1, 0x98, 0xA7, 0x26, 0x89, 0x66, 0xE4, 0xF1, 0x51, 0x8F, 0x75, 0x1B, 0x46, 0x39, 0x8E, 0xB0, 0xC7, 0x00, 0xCD, 0xEE, 0x5C, 0xAF, 0x31, 0xB5, 0xAE, 0x40, 0x61, 0x95, 0xBA, 0x71, 0x06, 0xFE, 0x76, 0xCD, 0xC0, 0x4F, 0x00, 0x00, 0x75, 0x58, 0xEC, 0x1C, 0xA2, 0x4B, 0xA2, 0x68, 0x04, 0xE7, 0x63, 0x31, 0x7D, 0x41, 0x2B, 0xF2, 0xE0, 0xC0, 0xA3, 0x0B, 0x2D, 0x8F, 0x6D, 0xB9, 0x92, 0xC2, 0xD3, 0xA5, 0xF2, 0x5F, 0xED, 0xFB, 0xE4, 0x8A, 0x3C, 0xF3, 0xEF, 0x55, 0xDF, 0x32, 0x62, 0xAE, 0xD5, 0xC9, 0x7D, 0x42, 0x39, 0x87, 0x03, 0xAB, 0x3A, 0x15, 0xA9, 0x2D, 0xFE, 0xF7, 0x02, 0xEB, 0x68, 0x66, 0x9A, 0x84, 0x61, 0x64, 0xDF, 0xF1, 0x30, 0xA6, 0xF8, 0x3E, 0x14, 0x09, 0xEC, 0x89, 0x4D, 0x23, 0x74, 0xDA, 0x52, 0xDE, 0x0C, 0x70, 0xD2, 0x13, 0xAE, 0xDB, 0x44, 0xB2, 0x0B, 0xBD, 0x09, 0x6F, 0xD3, 0x2E, 0x91, 0x67, 0xAD, 0x57, 0x0F, 0x60, 0x68, 0x0A, 0x1F, 0xF0, 0xA9, 0x07, 0xC4, 0xB1, 0x5D, 0xC7, 0xF6, 0x39, 0xE5, 0x76, 0xB0, 0xA7, 0xCE, 0xD0, 0x4F, 0x00, 0x00, 0x5A, 0x58, 0xED, 0x49, 0x83, 0xA6, 0x6A, 0x68, 0x3A, 0x8A, 0x4B, 0xDF, 0xF6, 0xF8, 0x99, 0xAD, 0x17, 0x64, 0x07, 0x98, 0x73, 0xB0, 0x12, 0x6A, 0x21, 0xDB, 0x07, 0x5B, 0x6A, 0x04, 0x66, 0x1F, 0x9E, 0x4E, 0x19, 0x69, 0xC6, 0x74, 0x2F, 0xAF, 0xA8, 0x91, 0x25, 0x79, 0xC3, 0x93, 0x6D, 0x32, 0x90, 0xDE, 0x80, 0x10, 0x41, 0xAB, 0xD0, 0x08, 0xFA, 0x7E, 0x74, 0xF7, 0x6A, 0xEA, 0x1C, 0xB5, 0x53, 0xDF, 0x27, 0x1F, 0xB1, 0x48, 0x88, 0xFF, 0xEC, 0xF8, 0x35, 0x86, 0xBE, 0x9F, 0x10, 0x62, 0xF2, 0xC4, 0x93, 0xA8, 0x2F, 0x53, 0x5B, 0x88, 0xF6, 0xF2, 0x4D, 0x4F, 0x00, 0x00, 0x80, 0x58, 0xED, 0xB2, 0x07, 0xA6, 0xCC, 0x71, 0x39, 0x1D, 0xC4, 0xC4, 0x20, 0x7F, 0x31, 0xE8, 0xAF, 0xCF, 0x10, 0x4C, 0xE2, 0x29, 0x0C, 0x9D, 0x10, 0x18, 0x27, 0xED, 0x0A, 0x3E, 0x5E, 0xD8, 0x10, 0x5D, 0x54, 0x3D, 0xEE, 0x99, 0x55, 0xCA, 0x3A, 0xBD, 0xC2, 0xBD, 0x9E, 0xE0, 0x60, 0xD4, 0xA9, 0x75, 0xC5, 0x86, 0xEE, 0x53, 0x4B, 0x14, 0x6E, 0x8F, 0x0E, 0x08, 0xB0, 0xE8, 0xDB, 0x04, 0xB9, 0xF4, 0x92, 0x44, 0xD0, 0x4A, 0x08, 0xFB, 0xA1, 0xD9, 0xBA, 0x2C, 0xC3, 0x46, 0x8C, 0xBE, 0x6F, 0xAD, 0x77, 0xBE, 0xF5, 0x14, 0x99, 0xB0, 0x3F, 0x8D, 0xD4, 0xB0, 0xF0, 0x90, 0xBD, 0x12, 0x84, 0x9E, 0x95, 0xB8, 0x46, 0xCC, 0xE7, 0x6A, 0xED, 0x00, 0x15, 0xF1, 0xBA, 0xDE, 0xF5, 0x60, 0xE4, 0x58, 0x36, 0x95, 0x0E, 0x35, 0x96, 0x4F, 0x15, 0x18, 0xD5, 0x7C, 0x88, 0xE9, 0xA7, 0x8F, 0x5E, 0x4F, 0x00, 0x00, 0x42, 0x58, 0x01, 0x3D, 0x32, 0xE1, 0x14, 0xC1, 0x1C, 0x9B, 0x23, 0x71, 0x7F, 0x6C, 0xAD, 0x76, 0x04, 0x6E, 0xC6, 0xFE, 0x0C, 0x52, 0x78, 0x95, 0x13, 0xC2, 0xAE, 0x3E, 0x67, 0x8F, 0xD9, 0x74, 0x41, 0xF6, 0x52, 0x8B, 0x14, 0x04, 0xD5, 0x77, 0xC5, 0x3B, 0xB8, 0x86, 0xE9, 0x65, 0xDB, 0x55, 0xF4, 0x2A, 0x4B, 0x23, 0x48, 0x68, 0x25, 0xC0, 0xCB, 0x7A, 0x89, 0x13, 0xFA, 0xCA, 0x92, 0xFB, 0x78, 0xA7, 0xF0, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20
};

#endif // AUDIO_DATA_MIN_H