#ifndef AUDIO_DATA_MAX_H
#define AUDIO_DATA_MAX_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfoMax[] = {0, 24, 49, 101, 238, 370, 510, 644, 792, 894, 1021, 1145, 1197};
const uint8_t g_audioDataMax[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x30, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x0E, 0xC6, 0x64, 0x8C, 0x1F, 0xF8, 0x80, 0x67, 0x4D, 0x95, 0xE5, 0x64, 0x46, 0xED, 0xE9, 0xA1, 0x1F, 0xB6, 0xA2, 0x18, 0x1C, 0xB4, 0xFF, 0x7D, 0x52, 0x8A, 0x95, 0x16, 0x4A, 0x9C, 0x2C, 0x3F, 0x86, 0xEA, 0x40, 0xFA, 0x9E, 0x4D, 0x31, 0x67, 0x4F, 0x00, 0x00, 0x85, 0x58, 0xE3, 0x70, 0xEB, 0xBF, 0xF3, 0xF6, 0xC6, 0x5C, 0xD6, 0x98, 0xAD, 0xBB, 0x64, 0x69, 0x8E, 0x52, 0xBB, 0x84, 0xE4, 0x6E, 0x74, 0x92, 0xDF, 0x60, 0xD1, 0xE0, 0xFD, 0x32, 0x99, 0x0A, 0x27, 0x26, 0x75, 0xF0, 0x5D, 0x2E, 0xED, 0x08, 0xFB, 0x11, 0x48, 0xD5, 0xBA, 0xD2, 0xAF, 0xBD, 0xC4, 0x21, 0x9B, 0x70, 0x0B, 0x07, 0xE4, 0x41, 0x2A, 0xA4, 0x9B, 0x09, 0x41, 0x98, 0x65, 0x10, 0x00, 0xDF, 0x73, 0xB4, 0xF0, 0x22, 0x6F, 0x6B, 0x1A, 0x5C, 0xE1, 0x15, 0x77, 0x0C, 0x2C, 0xC3, 0xF1, 0xAC, 0x57, 0x96, 0x0E, 0x7A, 0x94, 0x2D, 0x40, 0xCF, 0x5D, 0xB5, 0x39, 0x8B, 0x86, 0x57, 0xE7, 0xDD, 0xA8, 0xCB, 0x68, 0x1E, 0xA9, 0x8A, 0x11, 0x1E, 0xA2, 0x2B, 0xB8, 0x11, 0x60, 0x48, 0xC4, 0x3C, 0xB3, 0xE1, 0x1D, 0x1E, 0x2E, 0x40, 0xFA, 0x2D, 0x71, 0x77, 0xEE, 0x0E, 0x7F, 0x35, 0xA4, 0x86, 0xBB, 0xF1, 0x89, 0x10, 0x4F, 0x00, 0x00, 0x80, 0x58, 0xED, 0x6E, 0x15, 0xD3, 0x40, 0xF4, 0x0E, 0x67, 0x22, 0x19, 0xB6, 0x3F, 0x18, 0x0F, 0xE6, 0x1C, 0xC8, 0xB6, 0xFA, 0xE9, 0xD5, 0x56, 0xC7, 0xE7, 0x33, 0x0C, 0x25, 0x34, 0xEB, 0xDB, 0x17, 0xBD, 0x2D, 0xF5, 0x49, 0xAE, 0x93, 0xEF, 0xB7, 0xE3, 0xE3, 0x62, 0x2F, 0x15, 0xC4, 0x76, 0x77, 0x1D, 0x8A, 0xEC, 0xDA, 0x09, 0x64, 0x5C, 0x81, 0x36, 0x0E, 0x9A, 0x42, 0x05, 0xF8, 0x70, 0x25, 0x2B, 0xC4, 0x61, 0xE0, 0x1D, 0x07, 0x57, 0xA9, 0xD3, 0x78, 0x97, 0x61, 0x4F, 0x94, 0xA6, 0xC2, 0x41, 0x8F, 0x56, 0xCB, 0x51, 0x78, 0x05, 0x16, 0x17, 0xB0, 0xA8, 0x3A, 0xEB, 0x2D, 0x6A, 0xB2, 0xE9, 0x84, 0xF9, 0x23, 0xF9, 0xBD, 0x3B, 0x2F, 0x44, 0x4B, 0x70, 0x1B, 0xFB, 0xD0, 0x13, 0x8D, 0x67, 0x81, 0xF3, 0x0E, 0x11, 0x37, 0xA0, 0x8F, 0xB4, 0x73, 0x8F, 0x28, 0x53, 0xB2, 0x1E, 0xA0, 0x4F, 0x00, 0x00, 0x88, 0x58, 0xED, 0xF6, 0x25, 0xFF, 0xB9, 0xAF, 0xB5, 0x87, 0x75, 0x48, 0xC9, 0x96, 0xF1, 0x97, 0x7F, 0x0C, 0x8B, 0xCB, 0x2C, 0x3F, 0x77, 0x32, 0x07, 0xA2, 0xE8, 0x79, 0xA7, 0xA8, 0xAA, 0x8B, 0x47, 0x5E, 0xE1, 0xA6, 0x1A, 0x31, 0x93, 0x8A, 0x29, 0x8D, 0xC0, 0x09, 0x25, 0x71, 0x25, 0x30, 0x92, 0xC3, 0xFD, 0x05, 0xAE, 0x55, 0x58, 0x8C, 0x7E, 0x97, 0x7C, 0x9D, 0x35, 0x30, 0xFB, 0x95, 0x77, 0x24, 0xFA, 0x58, 0xE8, 0x71, 0xB3, 0xBB, 0x4F, 0xE2, 0x52, 0x9A, 0x1F, 0x7F, 0xCD, 0xF2, 0x77, 0x52, 0x80, 0x4E, 0x20, 0x11, 0xA0, 0x0D, 0x2D, 0x09, 0x1E, 0x6F, 0x55, 0xB3, 0x02, 0xA9, 0xD8, 0xE7, 0xFF, 0xC2, 0x0D, 0x6B, 0x8F, 0xE5, 0x98, 0x07, 0xBA, 0xA0, 0x44, 0x52, 0xF5, 0xE9, 0x68, 0xCC, 0x17, 0xDC, 0x7F, 0x2E, 0xB6, 0x39, 0x8A, 0x31, 0xCE, 0x79, 0x37, 0x29, 0xE6, 0x30, 0xA4, 0xCA, 0x7F, 0xDB, 0xB0, 0xEF, 0xBF, 0xC2, 0x52, 0x4F, 0x00, 0x00, 0x82, 0x58, 0xED, 0x40, 0x71, 0x6E, 0x71, 0xE1, 0x45, 0x93, 0x0C, 0x95, 0x85, 0x8C, 0x5D, 0xD9, 0xBD, 0x49, 0xA1, 0xA4, 0x4F, 0x9B, 0xD4, 0x0E, 0xA0, 0x7D, 0x10, 0xD1, 0x93, 0x7A, 0xE4, 0xB3, 0xBD, 0xBB, 0xA8, 0xD0, 0x53, 0x64, 0x5F, 0x7A, 0xF5, 0x8E, 0x8C, 0x52, 0x16, 0x98, 0xAE, 0x98, 0xB2, 0x81, 0x1E, 0xD7, 0x4F, 0x98, 0x14, 0xB1, 0x9B, 0xD9, 0xFE, 0x85, 0x22, 0x70, 0xF9, 0xC4, 0x51, 0x8F, 0x13, 0x6E, 0xDC, 0x8A, 0x2C, 0xB8, 0x7B, 0xCC, 0xBD, 0xD5, 0x8A, 0xE7, 0xE0, 0xA8, 0xC2, 0xCC, 0x01, 0x30, 0xC7, 0x32, 0x6A, 0x0E, 0x49, 0x42, 0xF9, 0xEA, 0xCD, 0x7B, 0xE4, 0x3F, 0x82, 0x47, 0xCE, 0x2B, 0x3E, 0x86, 0x37, 0xAD, 0xC1, 0x6E, 0x14, 0x0D, 0x64, 0xF6, 0x76, 0xB0, 0x6B, 0xA6, 0xF2, 0xB7, 0xF8, 0x35, 0x8C, 0x58, 0x50, 0x2E, 0x86, 0xB5, 0x2E, 0x77, 0x2C, 0xC5, 0x11, 0xB8, 0x80, 0x4F, 0x00, 0x00, 0x90, 0x58, 0xEF, 0x6A, 0x51, 0xE2, 0x4E, 0x97, 0x0F, 0x49, 0x9D, 0xF7, 0xB5, 0x16, 0x93, 0xE1, 0x84, 0x9A, 0x8F, 0xD3, 0x67, 0xA0, 0xC3, 0x54, 0x77, 0xB8, 0x3A, 0x7A, 0x99, 0xC3, 0x06, 0x93, 0xF4, 0x59, 0x7C, 0x41, 0xC7, 0x9C, 0x81, 0x30, 0x57, 0x60, 0x01, 0xB4, 0xC5, 0x44, 0xE3, 0x1C, 0x43, 0x67, 0xE2, 0x0F, 0x8C, 0x44, 0x3E, 0x76, 0x26, 0xF2, 0x79, 0x8B, 0x7A, 0xA4, 0x73, 0xA8, 0xED, 0x09, 0xA2, 0x4E, 0x50, 0xE7, 0x4A, 0x2F, 0xF1, 0x32, 0x7C, 0xBA, 0x8B, 0xD9, 0xFA, 0xDA, 0xDD, 0x7C, 0xD9, 0xC8, 0x42, 0x78, 0x27, 0x64, 0xB6, 0x6E, 0xD4, 0x8E, 0xE0, 0xA0, 0xFF, 0x86, 0x02, 0x67, 0x6A, 0xAC, 0xFA, 0x48, 0x9D, 0xAE, 0xDA, 0x20, 0xAC, 0x43, 0xF3, 0x7C, 0x74, 0x90, 0x0C, 0x99, 0xD4, 0xBD, 0x43, 0x73, 0x6E, 0xFB, 0xE2, 0x44, 0x23, 0x05, 0x1D, 0xB5, 0xE2, 0x31, 0x89, 0x25, 0x15, 0xD1, 0x07, 0xF1, 0x88, 0x6B, 0xA0, 0xC4, 0xF6, 0x7B, 0x2B, 0x1D, 0xED, 0x22, 0x90, 0x4F, 0x00, 0x00, 0x62, 0x58, 0xEE, 0x62, 0x30, 0x4E, 0xD9, 0x96, 0x46, 0xBA, 0x3C, 0xB7, 0xB3, 0x88, 0x76, 0xA7, 0xAA, 0xC5, 0x80, 0x4D, 0xD4, 0x62, 0x72, 0x9B, 0xA8, 0xB1, 0xE5, 0x04, 0x4B, 0x8D, 0xCD, 0x00, 0xA0, 0xEA, 0xAD, 0xCA, 0xF7, 0x38, 0x10, 0x0C, 0xC6, 0xEC, 0x01, 0x83, 0x18, 0x16, 0xC6, 0x4C, 0x38, 0x19, 0x9F, 0xEF, 0xBC, 0x78, 0x12, 0x07, 0x20, 0x64, 0x90, 0x89, 0xD1, 0xEB, 0xA0, 0x99, 0xF2, 0xF4, 0xD8, 0x07, 0x7B, 0x84, 0xCF, 0x05, 0x22, 0x30, 0x17, 0x88, 0x97, 0x36, 0xF0, 0x41, 0xF6, 0x4F, 0xD1, 0x22, 0x50, 0x71, 0x36, 0x14, 0x2F, 0x18, 0x8D, 0xD1, 0x17, 0x23, 0x01, 0xD3, 0x7F, 0x62, 0xC0, 0x4F, 0x00, 0x00, 0x7B, 0x58, 0xEE, 0x62, 0xAC, 0x2D, 0x26, 0xF8, 0xD4, 0x79, 0x89, 0xC3, 0x08, 0xC9, 0xFF, 0xA7, 0xF3, 0x22, 0x08, 0xE6, 0x7B, 0x65, 0xB7, 0x03, 0x47, 0xF0, 0xCE, 0xCF, 0x53, 0x15, 0xAA, 0xDE, 0xC1, 0x0B, 0x7A, 0xB4, 0x68, 0x5E, 0x52, 0x0E, 0xF2, 0x5B, 0x0D, 0x56, 0xB1, 0x07, 0xBA, 0xD0, 0x21, 0x21, 0x7A, 0x33, 0x61, 0xF4, 0x8D, 0xD2, 0xA3, 0xA8, 0xBB, 0x6E, 0xE2, 0xC1, 0x05, 0x2B, 0xF0, 0x79, 0xE4, 0x18, 0x49, 0x56, 0xF0, 0x2F, 0x24, 0x31, 0xA4, 0xCA, 0x8B, 0xEB, 0x59, 0xF9, 0x31, 0xBC, 0x2D, 0x19, 0x7B, 0xF6, 0x01, 0x00, 0x06, 0xC3, 0xA5, 0xAB, 0x58, 0x5E, 0xD1, 0xA7, 0xB6, 0x4F, 0x9D, 0xF0, 0x39, 0xEB, 0x8F, 0x15, 0x22, 0x8A, 0xE0, 0x4B, 0xEA, 0x30, 0x2A, 0x50, 0x91, 0x4E, 0x93, 0xA9, 0x38, 0xA1, 0xBF, 0x9A, 0x29, 0x2A, 0xD6, 0x60, 0x4F, 0x00, 0x00, 0x78, 0x58, 0xCB, 0xE8, 0x6C, 0xF6, 0xF8, 0x11, 0x79, 0x2F, 0xBB, 0xF2, 0xE4, 0x32, 0x67, 0x18, 0x77, 0x61, 0x9B, 0x49, 0xBD, 0x68, 0x58, 0xEB, 0x28, 0xA9, 0x63, 0xCB, 0xEB, 0x0B, 0x99, 0x6F, 0xFF, 0x4F, 0x32, 0x5E, 0xBE, 0x26, 0xFA, 0xE1, 0xF0, 0x00, 0xB4, 0x4B, 0x51, 0xA5, 0xF5, 0x45, 0x9B, 0x05, 0x31, 0xF3, 0x8B, 0xB6, 0xD5, 0x39, 0x4F, 0x9A, 0x92, 0xDF, 0x0E, 0x24, 0x53, 0x97, 0x64, 0xA7, 0x63, 0x5B, 0xCC, 0xA6, 0xA5, 0x63, 0xC6, 0xC0, 0x64, 0x95, 0x33, 0xB3, 0xA2, 0x96, 0xB1, 0x1E, 0xC3, 0x3C, 0x6D, 0xBD, 0x2C, 0xD3, 0x54, 0x41, 0x34, 0x98, 0xC1, 0x57, 0xA0, 0x09, 0x62, 0x14, 0xD5, 0xD0, 0x99, 0x55, 0x81, 0x83, 0x8C, 0x96, 0x74, 0x7E, 0x7E, 0xB6, 0x9B, 0x61, 0x1E, 0x4B, 0x94, 0x2A, 0xEA, 0x95, 0xDA, 0x2C, 0xC8, 0x4F, 0x00, 0x00, 0x30, 0x58, 0x07, 0x5A, 0x5D, 0x98, 0x22, 0xB6, 0xD6, 0xB0, 0xC0, 0x1D, 0x75, 0xFC, 0x0F, 0xC7, 0x04, 0x0F, 0x21, 0x71, 0xF4, 0x3C, 0xA9, 0x12, 0xE3, 0x49, 0x07, 0x9D, 0xB6, 0xEF, 0x6D, 0x4B, 0x2B, 0x92, 0xBE, 0xBD, 0xA5, 0x29, 0xF0, 0x67, 0x89, 0xFC, 0x6A, 0x95, 0x8D, 0xF3, 0xA4, 0xBF, 0x80, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20
};

#endif // AUDIO_DATA_MAX_H