#ifndef AUDIO_DATA_CTRLCONNECTED_H
#define AUDIO_DATA_CTRLCONNECTED_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfoCtrlConnected[] = {0, 24, 85, 247, 381, 511, 633, 757, 898, 1021, 1148, 1275, 1409, 1532, 1656, 1800, 1900, 2018, 2153, 2273, 2385, 2527, 2642, 2744, 2769, 2794};
const uint8_t g_audioDataCtrlConnected[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x39, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x6A, 0xAA, 0x9F, 0x65, 0xB7, 0xC2, 0xFF, 0x5A, 0xF4, 0x21, 0xD8, 0x1A, 0x0B, 0x38, 0x3D, 0xF5, 0xD2, 0x03, 0xAA, 0x3F, 0x6B, 0xEE, 0xBA, 0x2F, 0xC9, 0xF3, 0x7E, 0xF1, 0xFE, 0x81, 0x79, 0xB6, 0xBC, 0x9B, 0x63, 0x32, 0x8C, 0xFA, 0x20, 0x3E, 0x37, 0xD2, 0xA0, 0x4F, 0x00, 0x00, 0x9E, 0x58, 0xE6, 0xD4, 0xEB, 0xEF, 0x78, 0xFA, 0x7B, 0x62, 0x50, 0xA1, 0x63, 0xD5, 0x5E, 0x76, 0x5B, 0xD4, 0xDB, 0xCA, 0xE6, 0x05, 0x91, 0x7C, 0x81, 0x5E, 0x69, 0xAE, 0x2C, 0x90, 0x25, 0x75, 0x9F, 0xB2, 0xCF, 0x0E, 0xA7, 0x3E, 0xBA, 0xEA, 0xF2, 0xAC, 0x77, 0xCA, 0xF3, 0x58, 0x87, 0x30, 0x8F, 0x1B, 0x1D, 0x6B, 0x00, 0xAB, 0x9F, 0x6E, 0x90, 0x76, 0xE5, 0x02, 0xE1, 0xDA, 0xD1, 0x21, 0x87, 0x0F, 0xE9, 0x25, 0x50, 0x67, 0x7E, 0x46, 0xA0, 0x29, 0xD6, 0xEC, 0x79, 0xAD, 0x6C, 0x35, 0xF1, 0xEC, 0x62, 0x9D, 0x3C, 0x0E, 0x85, 0x5F, 0x8A, 0x8B, 0x99, 0xBD, 0x43, 0xC1, 0xFE, 0x1E, 0xA1, 0xDB, 0x0A, 0xA6, 0x4A, 0x8C, 0x8C, 0x54, 0x10, 0x4E, 0xFF, 0x67, 0x3F, 0x9A, 0x21, 0xA4, 0xB6, 0xC8, 0xAE, 0x04, 0xAD, 0x22, 0x18, 0x12, 0xA6, 0xC8, 0xFA, 0x23, 0x8F, 0x2F, 0xC7, 0xDC, 0x7B, 0xDC, 0x70, 0xA8, 0x77, 0xC0, 0xAB, 0x6E, 0x88, 0xE9, 0x1C, 0x59, 0x27, 0x8B, 0x02, 0x73, 0x89, 0xE4, 0x84, 0x72, 0x8C, 0xDA, 0xF0, 0xC7, 0xC4, 0xB6, 0xC4, 0x07, 0xB7, 0x54, 0xCE, 0x4F, 0x00, 0x00, 0x82, 0x58, 0xEB, 0xB9, 0xBB, 0x7F, 0x85, 0x9E, 0x64, 0x5B, 0x43, 0x1C, 0x74, 0xBE, 0x67, 0xA2, 0x47, 0x96, 0x05, 0x07, 0xF3, 0x37, 0x99, 0xD8, 0x88, 0x40, 0x3D, 0xB3, 0x3D, 0x71, 0x3E, 0x9A, 0x09, 0x45, 0x29, 0x56, 0xA4, 0x46, 0xEF, 0x8F, 0x1D, 0x7B, 0x3E, 0x23, 0x9F, 0x4C, 0x28, 0x6A, 0xFC, 0xF9, 0x3E, 0x8A, 0xEC, 0xE3, 0x18, 0xC6, 0x78, 0xB6, 0x9F, 0x1C, 0x04, 0x1D, 0x25, 0x13, 0xEE, 0x4E, 0xBE, 0xA8, 0x5E, 0xE8, 0x79, 0xDF, 0xE9, 0xB1, 0xA8, 0x51, 0x40, 0xB1, 0xFB, 0x00, 0x57, 0x45, 0x9B, 0x96, 0xD1, 0x08, 0xAF, 0x96, 0x27, 0xFC, 0xEE, 0xEE, 0xB2, 0x6D, 0x58, 0xE9, 0x26, 0xB5, 0xBC, 0x5B, 0x06, 0x58, 0x8A, 0xB8, 0x1B, 0x71, 0x43, 0x7D, 0x93, 0xA1, 0x00, 0xD6, 0xD7, 0x39, 0x35, 0x13, 0x08, 0xB7, 0xF8, 0x04, 0xFA, 0xB1, 0xCD, 0x7F, 0xAC, 0xC2, 0xF9, 0xB9, 0x0A, 0x46, 0x30, 0x4F, 0x00, 0x00, 0x7E, 0x58, 0xE8, 0x01, 0xD0, 0x42, 0x2D, 0x31, 0x63, 0xDE, 0x40, 0x76, 0x3D, 0x58, 0xAB, 0xFB, 0x44, 0x1B, 0xA8, 0xD8, 0x48, 0x0D, 0x6E, 0xC2, 0x31, 0xD5, 0x9D, 0xEA, 0x6D, 0x77, 0x9B, 0x2B, 0x75, 0x47, 0xDD, 0xBD, 0xF4, 0x2C, 0xD8, 0x02, 0x7D, 0xCA, 0x1C, 0xAD, 0x30, 0x1E, 0x26, 0xF9, 0xF4, 0x29, 0x73, 0xE0, 0x11, 0xCE, 0x65, 0xBA, 0x0A, 0xF9, 0x3D, 0x52, 0x7E, 0x5E, 0xDB, 0x8C, 0xBB, 0x20, 0x5E, 0xB0, 0x36, 0x03, 0xFF, 0x40, 0x60, 0x1C, 0x69, 0x60, 0x51, 0x1A, 0x43, 0xA4, 0xFC, 0xBF, 0x8C, 0x87, 0x06, 0xF8, 0x44, 0x78, 0xED, 0x4D, 0x65, 0xC6, 0xD5, 0xE0, 0x9D, 0x49, 0xAC, 0x09, 0x37, 0xCA, 0x95, 0xBF, 0xBA, 0x7F, 0x0A, 0xBB, 0x98, 0x90, 0xE4, 0x31, 0x79, 0x41, 0xCA, 0xDE, 0xF0, 0xD2, 0x8B, 0x9C, 0x78, 0x0A, 0x08, 0xE3, 0x38, 0x65, 0x41, 0x1F, 0xB8, 0x4F, 0x00, 0x00, 0x76, 0x58, 0xEC, 0x87, 0xA9, 0x0D, 0x88, 0xFE, 0x22, 0x6D, 0xB2, 0xD1, 0xFC, 0x77, 0xF5, 0xD0, 0xD8, 0xB2, 0x22, 0x30, 0xD1, 0x59, 0x17, 0x18, 0x6A, 0x16, 0x0B, 0xC6, 0xB3, 0xF7, 0x8F, 0x26, 0xD3, 0x5F, 0x35, 0xE7, 0xBA, 0xA5, 0xB5, 0xEC, 0x1D, 0x13, 0x7D, 0x54, 0xDE, 0xA8, 0x34, 0xFA, 0x49, 0xA5, 0xBE, 0x89, 0x51, 0xFA, 0x31, 0xD8, 0x5C, 0xCE, 0x37, 0x33, 0x4F, 0x3A, 0x49, 0x1B, 0x48, 0x3C, 0x37, 0x7D, 0x03, 0x53, 0x84, 0xF0, 0xA0, 0xA7, 0x47, 0x3B, 0x96, 0x59, 0xF9, 0xA7, 0x41, 0x90, 0xA7, 0x24, 0x4E, 0x6D, 0x62, 0xBA, 0x6D, 0x36, 0x4E, 0x05, 0x67, 0x14, 0xB6, 0xAF, 0x7E, 0x23, 0x9F, 0xCA, 0xFA, 0x08, 0x31, 0x8B, 0x5E, 0x3D, 0xB6, 0x5B, 0x12, 0x3B, 0xAC, 0x08, 0xBD, 0x48, 0x7D, 0xDB, 0x3C, 0x9F, 0xA0, 0x4F, 0x00, 0x00, 0x78, 0x58, 0xE7, 0xDD, 0x4C, 0xFA, 0xC3, 0xEF, 0x3B, 0x9A, 0x00, 0xBD, 0x0C, 0x22, 0xEF, 0x24, 0x3C, 0x2C, 0x5F, 0x20, 0xEB, 0x17, 0xE5, 0x76, 0x03, 0x3D, 0x02, 0xE9, 0xAD, 0x38, 0x4E, 0x28, 0x36, 0x93, 0xF6, 0xBE, 0x85, 0x8C, 0x91, 0xFA, 0x95, 0xEB, 0x63, 0xD7, 0x90, 0x54, 0x5C, 0xC8, 0x03, 0x9A, 0xBF, 0x2A, 0x2D, 0x8F, 0x19, 0xBE, 0x90, 0xD6, 0x5C, 0xEF, 0xE6, 0x19, 0xF8, 0x83, 0x8D, 0x5F, 0xAF, 0x72, 0xF3, 0x5F, 0x9A, 0x72, 0x47, 0x18, 0xB3, 0xCA, 0x61, 0xCF, 0x1A, 0xCA, 0xC5, 0x86, 0xF4, 0xA2, 0x05, 0x6A, 0xA7, 0x9A, 0xBD, 0x54, 0x84, 0xCB, 0xDB, 0x7B, 0xB9, 0xF8, 0x45, 0x77, 0xF2, 0xA0, 0xAF, 0x02, 0xAA, 0xB4, 0x28, 0xF3, 0x2C, 0x78, 0x6B, 0xFE, 0x35, 0x21, 0x87, 0x6A, 0x25, 0x47, 0xF6, 0x9D, 0xFE, 0x01, 0x70, 0x4F, 0x00, 0x00, 0x89, 0x58, 0xE1, 0x1D, 0xD2, 0x3C, 0xC4, 0x51, 0xBF, 0xF9, 0x86, 0x0C, 0xA8, 0xFC, 0x86, 0x98, 0x2E, 0xF7, 0xC0, 0x72, 0x5A, 0xBB, 0xC5, 0x22, 0x28, 0xFA, 0xAC, 0x47, 0x2F, 0xCC, 0xE6, 0x88, 0x79, 0x74, 0x89, 0x45, 0xDC, 0xD0, 0x24, 0x84, 0xB0, 0x07, 0x08, 0xF3, 0xF5, 0xBA, 0x46, 0xC9, 0x5E, 0x93, 0x2A, 0x7B, 0x33, 0xB2, 0x9C, 0xEC, 0xCF, 0xBC, 0x56, 0xED, 0xA4, 0x79, 0x14, 0x8E, 0x5F, 0x39, 0xBA, 0x04, 0x22, 0x69, 0x79, 0x9B, 0x47, 0xE2, 0xE5, 0xC6, 0x44, 0x06, 0xB8, 0x35, 0x4C, 0x85, 0x4B, 0xE4, 0x31, 0x8A, 0x4A, 0xFF, 0x46, 0xA7, 0xFC, 0xC3, 0x80, 0x6D, 0xD3, 0x40, 0xF4, 0xED, 0xAE, 0xAB, 0x98, 0x5C, 0x3B, 0x63, 0xE6, 0x1A, 0xFB, 0xC7, 0x5A, 0xAE, 0x7C, 0x95, 0xB3, 0x72, 0x40, 0x3A, 0x52, 0x34, 0x09, 0x0A, 0x41, 0x06, 0x6A, 0xAC, 0x9E, 0xE8, 0x40, 0x3F, 0xD6, 0xB3, 0xBF, 0x62, 0x93, 0x3C, 0x9A, 0x9E, 0x4F, 0xF4, 0x4F, 0x00, 0x00, 0x77, 0x58, 0xED, 0x93, 0x12, 0xF9, 0x2A, 0xA7, 0x42, 0x4A, 0x27, 0x3C, 0x31, 0x76, 0xAA, 0x2B, 0xCB, 0x35, 0x73, 0xD2, 0x66, 0x65, 0xC8, 0x4A, 0x88, 0xD2, 0x5C, 0x82, 0x52, 0xD1, 0xB0, 0xBD, 0x84, 0xFD, 0xFF, 0xA3, 0xD9, 0xE8, 0x47, 0xA9, 0x25, 0xD2, 0x6C, 0xF1, 0x05, 0xB7, 0x2F, 0xC7, 0x12, 0x50, 0x45, 0xCC, 0x67, 0x2F, 0x1E, 0x26, 0x2C, 0x3C, 0xE0, 0xA6, 0x8A, 0x78, 0xBB, 0xD8, 0xFC, 0x52, 0x21, 0x1D, 0x32, 0x3A, 0xA4, 0x6A, 0xBF, 0x29, 0xBC, 0x1B, 0xA0, 0x3D, 0x12, 0x24, 0xA8, 0x68, 0xC4, 0x34, 0xDE, 0x14, 0xF7, 0x26, 0xB5, 0xA4, 0x07, 0x35, 0x85, 0x47, 0x84, 0x36, 0xA6, 0xD1, 0x49, 0x0A, 0xC7, 0x95, 0xAD, 0x63, 0xBB, 0xA9, 0x95, 0xE7, 0x56, 0x20, 0xF0, 0x55, 0xF9, 0xF3, 0xE7, 0xA8, 0xAD, 0x13, 0xF1, 0x02, 0x4F, 0x00, 0x00, 0x7B, 0x58, 0xED, 0x2E, 0xF9, 0x86, 0x15, 0xED, 0x11, 0x62, 0x7C, 0x92, 0x5E, 0xCB, 0x75, 0x65, 0x8C, 0x49, 0x97, 0x05, 0x8F, 0xAE, 0x4E, 0x1D, 0x46, 0xB9, 0xC8, 0x13, 0x80, 0xDC, 0x1B, 0x51, 0xB7, 0xD4, 0xCA, 0x75, 0x8A, 0x0C, 0xD2, 0xE7, 0x26, 0x18, 0xDB, 0xF4, 0x27, 0x88, 0x68, 0xC6, 0x3C, 0xD0, 0x4A, 0xD8, 0x21, 0x71, 0xD0, 0x11, 0x51, 0x1F, 0x5A, 0x6E, 0xBA, 0x96, 0x2E, 0xC4, 0x20, 0x63, 0xDC, 0x45, 0x16, 0x6A, 0xE2, 0x21, 0x04, 0x1B, 0xA0, 0xCD, 0x36, 0xBA, 0x4F, 0xA6, 0x54, 0x05, 0x0B, 0x22, 0xC2, 0xC8, 0x20, 0x83, 0x17, 0x9A, 0x51, 0x01, 0x45, 0x23, 0x05, 0xEE, 0x0D, 0x37, 0x3E, 0xCC, 0x92, 0x76, 0x32, 0xBB, 0x67, 0xC0, 0x0B, 0x3B, 0x86, 0x88, 0x06, 0xBA, 0x9B, 0xB6, 0x07, 0x76, 0x00, 0x7E, 0x5E, 0x3F, 0xB3, 0xB2, 0x08, 0x50, 0x4F, 0x00, 0x00, 0x7B, 0x58, 0xE5, 0x27, 0x32, 0x32, 0xD5, 0xC3, 0x61, 0xBF, 0x43, 0x61, 0xF5, 0x0C, 0xC2, 0xCA, 0x9F, 0x5C, 0x07, 0x30, 0x10, 0xF9, 0x13, 0x5D, 0xF1, 0x3D, 0xCD, 0x46, 0x32, 0x95, 0x20, 0x65, 0x61, 0xE6, 0x3B, 0x9B, 0x61, 0x29, 0x1D, 0x46, 0x77, 0x3C, 0x03, 0x06, 0x7C, 0x7A, 0x4E, 0x93, 0xE7, 0x09, 0x0B, 0x02, 0xB9, 0x09, 0xA8, 0x40, 0x79, 0xC5, 0xE8, 0x08, 0x43, 0x6D, 0x65, 0xF9, 0x4F, 0x5F, 0xB0, 0x3F, 0xC6, 0x5B, 0x85, 0x80, 0xBC, 0x24, 0xD4, 0xF7, 0x56, 0xAB, 0xC3, 0xDC, 0x34, 0x1C, 0xFD, 0xA3, 0xD8, 0x73, 0x20, 0x1E, 0x33, 0x23, 0x05, 0xF1, 0x16, 0x1A, 0x98, 0x51, 0x9C, 0x4D, 0x8D, 0x3E, 0xC9, 0xFD, 0x4D, 0x33, 0x02, 0xBF, 0x2B, 0x1F, 0xEF, 0xCD, 0x36, 0x83, 0xE6, 0xD4, 0x7D, 0xBF, 0x15, 0x8B, 0x46, 0x21, 0x37, 0x15, 0xC9, 0xA2, 0x4F, 0x00, 0x00, 0x82, 0x58, 0xEC, 0x85, 0xB3, 0x33, 0x55, 0x97, 0xF4, 0xCA, 0x21, 0x89, 0x1E, 0x8D, 0xBA, 0xB3, 0xFA, 0x34, 0x72, 0x91, 0x8E, 0x3C, 0x39, 0x88, 0x73, 0xF1, 0x3E, 0x2D, 0x89, 0x46, 0x07, 0x7C, 0xA0, 0x3F, 0x0B, 0x7A, 0x76, 0x2E, 0xA0, 0x56, 0x3F, 0x5F, 0x2D, 0x7F, 0x45, 0x0C, 0xB6, 0x46, 0xD5, 0xD1, 0xB6, 0x9F, 0x7C, 0xE3, 0x5B, 0xA2, 0x91, 0x17, 0x63, 0x4E, 0xF0, 0xD6, 0xF1, 0x71, 0xB4, 0x3F, 0xC1, 0xA2, 0x3B, 0xB5, 0xD2, 0x93, 0xE3, 0x2E, 0x51, 0x10, 0xD1, 0x0A, 0x9E, 0x7B, 0x6A, 0xED, 0xD1, 0x9A, 0x16, 0xBB, 0xBF, 0x2C, 0xFF, 0x9B, 0x08, 0x22, 0x5A, 0xA6, 0xDE, 0xD0, 0xF0, 0xB8, 0x9D, 0x2B, 0xB4, 0x5E, 0xA2, 0xC3, 0x36, 0x27, 0x5A, 0x4E, 0x66, 0x13, 0x6B, 0xBD, 0x72, 0xC9, 0xAC, 0x1C, 0xFC, 0xF2, 0x46, 0xA7, 0x62, 0x98, 0xDF, 0xA5, 0x77, 0x3F, 0xA5, 0xB7, 0x6C, 0x29, 0x98, 0x4F, 0x00, 0x00, 0x77, 0x58, 0xEB, 0x62, 0x42, 0xBB, 0x6C, 0xD1, 0x5B, 0xFA, 0x5D, 0x55, 0x0F, 0xCF, 0xC0, 0x53, 0x1F, 0xC9, 0x2D, 0x30, 0x8F, 0x10, 0xB0, 0xE1, 0x5A, 0xCC, 0x26, 0x7F, 0x9B, 0xD0, 0x29, 0x33, 0x18, 0xB0, 0x42, 0x64, 0x7F, 0xB6, 0xD1, 0xE6, 0x03, 0x2C, 0xA4, 0xAE, 0xA0, 0x99, 0xCE, 0xD2, 0x68, 0xBB, 0xB6, 0xC8, 0x9A, 0x56, 0x79, 0x1C, 0xAC, 0x3C, 0xAB, 0xC1, 0x2A, 0x24, 0x23, 0xFC, 0xFE, 0xA0, 0xBF, 0x39, 0x3D, 0xAE, 0xB3, 0xD5, 0x0C, 0x4A, 0xAB, 0x90, 0x46, 0xE0, 0x06, 0x0E, 0x42, 0xC5, 0xB8, 0x97, 0x1B, 0x27, 0x44, 0x73, 0x9E, 0x68, 0x07, 0x03, 0x1F, 0xBB, 0x4C, 0xA5, 0x49, 0x20, 0x38, 0x54, 0xCF, 0x22, 0x84, 0xE9, 0xD3, 0x97, 0x13, 0x88, 0xA7, 0xC2, 0x44, 0x4A, 0xEA, 0x3C, 0xF6, 0x0C, 0x26, 0x29, 0x0E, 0x18, 0x4F, 0x00, 0x00, 0x78, 0x58, 0xEE, 0x62, 0x49, 0xEC, 0x27, 0xDC, 0xF2, 0x9C, 0x73, 0x1F, 0xB7, 0x80, 0x37, 0xA3, 0x0F, 0x4D, 0xD1, 0x57, 0x60, 0x96, 0xEF, 0xB4, 0x3D, 0xA0, 0xC7, 0x54, 0x11, 0x58, 0x12, 0xEF, 0x35, 0xDE, 0xE9, 0x59, 0x7E, 0xE4, 0xBC, 0x97, 0x9D, 0xAB, 0x3D, 0x11, 0x27, 0xA9, 0x8E, 0xFF, 0x78, 0x17, 0x6B, 0x81, 0x9C, 0xEA, 0x35, 0x58, 0x49, 0xAC, 0x01, 0x8C, 0x47, 0x01, 0x06, 0x5A, 0xA3, 0xAE, 0x47, 0x72, 0x22, 0x8E, 0xF1, 0x19, 0xEE, 0xB2, 0x7B, 0x87, 0xAD, 0x3B, 0x3E, 0xA9, 0x01, 0xB3, 0x30, 0xA7, 0x16, 0x2C, 0x1F, 0xD8, 0xE0, 0xB4, 0xF9, 0xEC, 0x3B, 0xE3, 0x10, 0xFB, 0x46, 0x45, 0x78, 0x3B, 0x00, 0xD3, 0xB9, 0xE8, 0xE9, 0x29, 0x45, 0x5F, 0x50, 0xD0, 0x3B, 0xDB, 0xAB, 0x8F, 0x6F, 0x9E, 0x6F, 0x29, 0xFF, 0x55, 0x80, 0x4F, 0x00, 0x00, 0x8C, 0x58, 0xED, 0x82, 0x09, 0x56, 0xF1, 0x5F, 0x5E, 0x81, 0xFC, 0xAB, 0xAF, 0x59, 0x4A, 0xCD, 0x8F, 0xBF, 0x84, 0x86, 0x3C, 0xC8, 0x6A, 0x5F, 0x04, 0xC2, 0x16, 0xFD, 0x03, 0xDD, 0x99, 0xA7, 0x56, 0xC4, 0x73, 0xD3, 0x49, 0x41, 0xCD, 0x63, 0x04, 0xA4, 0x32, 0xF1, 0x09, 0x94, 0xD9, 0x30, 0x3C, 0xD4, 0x1E, 0x38, 0x34, 0xFA, 0xB6, 0x5C, 0x73, 0x9B, 0x06, 0x4F, 0xDE, 0xAA, 0x94, 0xA8, 0x55, 0x02, 0x43, 0x21, 0xB2, 0x5D, 0x0F, 0x12, 0x78, 0x05, 0x7C, 0x5F, 0x5F, 0x28, 0xA6, 0x8D, 0x3E, 0xA5, 0xEC, 0x61, 0xFB, 0x04, 0x6F, 0x40, 0x5C, 0x15, 0xFD, 0x0A, 0x79, 0xBD, 0xF2, 0x87, 0x08, 0x7F, 0xB3, 0x9D, 0x4F, 0x17, 0x31, 0x89, 0x41, 0xC0, 0x78, 0x19, 0xCE, 0x49, 0x18, 0x41, 0x46, 0xFB, 0xE2, 0x27, 0x81, 0x72, 0x8B, 0x68, 0xC1, 0x7A, 0x11, 0xF8, 0xA3, 0x66, 0x09, 0xFA, 0x54, 0xFE, 0x98, 0xD6, 0xD8, 0x11, 0x22, 0xF5, 0x0E, 0xBB, 0x43, 0x3A, 0x78, 0x4F, 0x00, 0x00, 0x60, 0x58, 0xE0, 0xBA, 0x00, 0x05, 0x60, 0x1D, 0x9E, 0x49, 0xFB, 0x43, 0x26, 0x15, 0x73, 0xD8, 0x91, 0x9E, 0xA7, 0x68, 0x21, 0x90, 0xF5, 0x41, 0x9D, 0x3D, 0x7F, 0x0A, 0xBD, 0x2A, 0xDB, 0x51, 0xD8, 0x75, 0x9C, 0xB7, 0x93, 0x62, 0x00, 0xD7, 0xAB, 0x83, 0x69, 0x0F, 0xF0, 0xE3, 0xAF, 0x3B, 0xC9, 0x94, 0xAD, 0xE6, 0xB6, 0x84, 0x10, 0x09, 0x2F, 0x9D, 0x63, 0x99, 0x6D, 0x99, 0xEB, 0xF6, 0xC7, 0x76, 0x33, 0x4B, 0xA6, 0xED, 0xB2, 0xD1, 0x14, 0x23, 0xFD, 0x35, 0x0F, 0xAF, 0x6E, 0x4C, 0xDB, 0x8E, 0x21, 0x1A, 0x25, 0x36, 0xF8, 0x2F, 0x67, 0x06, 0x51, 0xFA, 0x77, 0x85, 0x5F, 0xB0, 0xDA, 0x4F, 0x00, 0x00, 0x72, 0x58, 0xE4, 0x83, 0x73, 0x48, 0x73, 0xB7, 0x63, 0xDF, 0xDB, 0x7A, 0xDA, 0x00, 0xC5, 0xD5, 0xB1, 0x7C, 0x1D, 0x55, 0x40, 0xBD, 0x8E, 0x5B, 0x3D, 0x9C, 0xAE, 0x73, 0x23, 0x86, 0x9B, 0x17, 0x24, 0xC7, 0xA9, 0x01, 0x65, 0x18, 0x1A, 0xAC, 0xA5, 0x60, 0xE7, 0x3E, 0xB9, 0xB6, 0xA7, 0x81, 0x46, 0x04, 0x6D, 0x41, 0x24, 0x91, 0x58, 0x85, 0x8D, 0x8F, 0x04, 0x74, 0x74, 0xD9, 0x16, 0xB9, 0x00, 0xF6, 0x98, 0x95, 0xC4, 0x5F, 0xD2, 0x11, 0x9E, 0x68, 0xE6, 0x8A, 0x2D, 0x09, 0x37, 0xF7, 0xDD, 0xD9, 0x9A, 0x50, 0x67, 0xFD, 0xE6, 0xEE, 0xF2, 0x7E, 0x57, 0xC6, 0x89, 0xD7, 0x55, 0x02, 0x75, 0x29, 0x31, 0x56, 0x1C, 0xBC, 0x48, 0x79, 0x42, 0xBE, 0x51, 0x0D, 0xA6, 0x1C, 0xA4, 0x79, 0x14, 0xDC, 0x80, 0x4F, 0x00, 0x00, 0x83, 0x58, 0xED, 0x18, 0x9B, 0xBC, 0xCF, 0xD6, 0x87, 0x59, 0x1B, 0x1F, 0xF0, 0x98, 0x66, 0x1E, 0xE6, 0x14, 0x0A, 0x88, 0xE8, 0x11, 0x1C, 0xE4, 0x08, 0x80, 0x87, 0x62, 0xBF, 0x73, 0x2B, 0x33, 0x03, 0x72, 0xBB, 0xF6, 0xC9, 0xAA, 0xF0, 0x7E, 0x26, 0x11, 0x12, 0x4A, 0x27, 0xCB, 0x11, 0x32, 0xA9, 0x23, 0x2D, 0xC8, 0xB2, 0x03, 0x24, 0xEC, 0x77, 0xFE, 0x0A, 0x8D, 0xEF, 0x72, 0x1C, 0xDF, 0x16, 0xCB, 0x5D, 0x9C, 0x63, 0x24, 0x31, 0x0F, 0x51, 0x83, 0xB7, 0x63, 0xB0, 0xCF, 0x98, 0xD0, 0xD6, 0xC7, 0xA4, 0x48, 0xA3, 0xA0, 0xAD, 0x64, 0x22, 0xA1, 0x90, 0xBC, 0x8E, 0xFB, 0xC9, 0xF2, 0x1C, 0x14, 0x8F, 0xDF, 0x10, 0x89, 0x7D, 0x8A, 0x2F, 0x27, 0x48, 0x74, 0x37, 0x78, 0x62, 0x69, 0x69, 0x39, 0x85, 0x49, 0x84, 0xB0, 0xE2, 0x3F, 0xF5, 0xD8, 0xBB, 0x88, 0x38, 0x38, 0xFC, 0x40, 0x59, 0x65, 0x74, 0x90, 0x4F, 0x00, 0x00, 0x74, 0x58, 0xED, 0xED, 0xF4, 0x17, 0x39, 0x08, 0xC4, 0x39, 0xED, 0xC7, 0xCF, 0x49, 0x93, 0x3C, 0xB6, 0x32, 0x6C, 0x4F, 0x3A, 0x6E, 0xD9, 0xA9, 0xB3, 0x6E, 0xDC, 0xAB, 0x1A, 0x05, 0x1D, 0xBD, 0xBC, 0x3C, 0x27, 0x1F, 0x91, 0xD0, 0xE0, 0x77, 0x36, 0x6A, 0xBD, 0x26, 0x22, 0x06, 0x36, 0x4B, 0x24, 0x09, 0xD7, 0xB4, 0x46, 0x3F, 0xED, 0x3E, 0x09, 0x47, 0x81, 0x89, 0x8B, 0x22, 0xBD, 0x8A, 0x77, 0xD4, 0x19, 0xE8, 0xE7, 0x38, 0x3C, 0xC0, 0x17, 0x84, 0x08, 0xB2, 0x0A, 0xF6, 0x53, 0x40, 0x3B, 0x6E, 0x3D, 0x4C, 0xEC, 0x47, 0xCE, 0xE9, 0x5D, 0xD2, 0x03, 0xCE, 0x3B, 0x90, 0x4C, 0x75, 0xBD, 0xDB, 0xA2, 0x4E, 0xFF, 0x69, 0x3D, 0xD0, 0x5E, 0x77, 0xC7, 0x69, 0x3A, 0x13, 0x1C, 0x5F, 0x9E, 0xFE, 0xF0, 0xAA, 0x98, 0x4F, 0x00, 0x00, 0x6C, 0x58, 0xE8, 0x41, 0xCB, 0x88, 0x24, 0xC3, 0x1B, 0xAB, 0xBA, 0xA7, 0xD7, 0x28, 0x3C, 0xC3, 0xB6, 0x23, 0x54, 0x61, 0xB7, 0x6F, 0xC9, 0xE5, 0x80, 0x07, 0x55, 0xEB, 0xF0, 0x8D, 0xBF, 0x49, 0x1F, 0x1C, 0x52, 0xF0, 0xB4, 0xA4, 0x3A, 0x4C, 0xAE, 0xFD, 0x14, 0x0C, 0x77, 0x0A, 0xDE, 0x4E, 0xBD, 0xD8, 0x74, 0x31, 0x74, 0xBE, 0x86, 0x95, 0x7B, 0x7B, 0x0A, 0x04, 0x2D, 0x39, 0x8A, 0xA5, 0xB0, 0x4A, 0x57, 0xF6, 0x44, 0xE3, 0xDE, 0xC0, 0xDA, 0xCE, 0x5A, 0x65, 0x41, 0x43, 0x6E, 0xE1, 0xC2, 0x61, 0x0B, 0x0F, 0xAA, 0x7A, 0xBF, 0xFE, 0x72, 0x41, 0x5B, 0x7E, 0x32, 0x63, 0x03, 0xAD, 0x31, 0x03, 0x83, 0x81, 0x8E, 0xC6, 0xF9, 0xEE, 0x30, 0x62, 0x23, 0xF0, 0xA6, 0x4F, 0x00, 0x00, 0x8A, 0x58, 0xE6, 0x3E, 0xBD, 0x4C, 0xBF, 0x76, 0x5F, 0x0B, 0x97, 0x01, 0x91, 0x3F, 0xDA, 0x3B, 0x5E, 0xD5, 0x34, 0xC0, 0x22, 0x7E, 0x5B, 0x9A, 0x1D, 0x35, 0xAB, 0x47, 0xE7, 0x8B, 0xC6, 0xDA, 0x00, 0xD3, 0xF8, 0x63, 0x08, 0xAD, 0xAB, 0x9C, 0xCA, 0x43, 0xD4, 0x24, 0x85, 0x84, 0x91, 0xFC, 0x9A, 0x5B, 0x0A, 0x99, 0x27, 0xE5, 0x17, 0xB9, 0x63, 0xC4, 0xA8, 0x2B, 0x47, 0xFE, 0xB5, 0x89, 0x4E, 0xD5, 0x6F, 0x8E, 0x91, 0x54, 0x55, 0xA0, 0x53, 0xD8, 0xCD, 0xAF, 0x74, 0x52, 0x0D, 0x03, 0x8D, 0x31, 0x89, 0x61, 0xCD, 0x60, 0x01, 0x7E, 0x8F, 0xF0, 0x3C, 0xB0, 0xCB, 0x76, 0x51, 0x4E, 0x51, 0xF2, 0x48, 0xC1, 0x97, 0x0B, 0xC0, 0x6F, 0x8E, 0x18, 0x58, 0x70, 0xC6, 0x58, 0x25, 0xD4, 0xB2, 0xDC, 0x8E, 0x52, 0x71, 0x39, 0x0E, 0xAC, 0x45, 0x06, 0x0F, 0xE4, 0x93, 0x88, 0x16, 0xED, 0x2B, 0xE9, 0x28, 0xB6, 0xE1, 0x2F, 0x46, 0x46, 0xE2, 0x8A, 0xE0, 0x4F, 0x00, 0x00, 0x6F, 0x58, 0xEE, 0x1B, 0x18, 0xB1, 0x98, 0x36, 0x1F, 0xE9, 0x31, 0xAA, 0x37, 0x54, 0xA8, 0xB7, 0x6E, 0x2F, 0xD3, 0x0C, 0xC5, 0x37, 0x3E, 0x12, 0xF2, 0x8D, 0x77, 0x51, 0x27, 0xF4, 0xEB, 0x23, 0x2E, 0x92, 0x2F, 0xC4, 0xCD, 0xFE, 0xF0, 0x24, 0xA1, 0xAB, 0x5B, 0x21, 0xE1, 0x13, 0x22, 0x90, 0xAC, 0x0A, 0x25, 0x34, 0x9E, 0xC6, 0x05, 0xCD, 0xC3, 0x30, 0x8D, 0xE5, 0x17, 0x48, 0x52, 0x68, 0x06, 0x3B, 0x63, 0xC3, 0xC8, 0x86, 0x40, 0x40, 0x6C, 0xB2, 0x39, 0xA9, 0xA5, 0x1C, 0xC9, 0xF2, 0xE3, 0x84, 0x45, 0x7E, 0xB9, 0x4C, 0xD5, 0xA7, 0x1D, 0x1F, 0xEA, 0x54, 0x59, 0x77, 0xFA, 0xAD, 0x3A, 0x69, 0x33, 0xD4, 0x55, 0x47, 0x5A, 0x48, 0x3E, 0x83, 0x05, 0x83, 0x05, 0x1B, 0x72, 0x2E, 0x4F, 0x00, 0x00, 0x62, 0x58, 0xCA, 0x35, 0x95, 0x38, 0x33, 0x96, 0x64, 0xAC, 0x87, 0x1C, 0x97, 0x35, 0xAB, 0xFA, 0xF3, 0x78, 0x91, 0x6C, 0x23, 0x3B, 0xAE, 0x79, 0xCA, 0x24, 0xB4, 0x2B, 0xFA, 0x3D, 0x81, 0x66, 0x11, 0xC8, 0xCE, 0x40, 0x7D, 0xCA, 0xFA, 0xEA, 0xF9, 0xA7, 0x33, 0xC7, 0xD9, 0xA2, 0x66, 0xA0, 0x3D, 0xA3, 0xB1, 0x9F, 0xB0, 0xE6, 0xDF, 0xE1, 0xAE, 0xFF, 0x0B, 0xDF, 0x8D, 0x3B, 0x73, 0xC2, 0xC5, 0x1F, 0xF6, 0xDF, 0xDD, 0xEC, 0x73, 0x50, 0xEA, 0x72, 0x62, 0x85, 0x64, 0x17, 0x94, 0x7A, 0x1D, 0x7E, 0xD4, 0x42, 0xC9, 0xF9, 0x51, 0x50, 0x2E, 0xD2, 0x3D, 0x62, 0xCF, 0x6D, 0xB1, 0x53, 0x5F, 0x2B, 0xC0, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x02, 0xC0, 0x16, 0xD1, 0x4E, 0xC3, 0xC8, 0x38, 0x93, 0xBD, 0x67, 0x66, 0xE9, 0x8B, 0x0D, 0x29, 0x66, 0x88, 0x95, 0xE0, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20
};

#endif // AUDIO_DATA_CTRLCONNECTED_H