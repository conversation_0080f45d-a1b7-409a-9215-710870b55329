#ifndef AUDIO_DATA_0_H
#define AUDIO_DATA_0_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfo0[] = {0, 24, 49, 102, 243, 373, 514, 656, 791, 929, 1010};
const uint8_t g_audioData0[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x31, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x0A, 0xD2, 0xEC, 0x44, 0x99, 0x61, 0xB4, 0x55, 0x0E, 0x01, 0x72, 0xBB, 0xA0, 0xD1, 0xBB, 0x72, 0x1D, 0x42, 0xC1, 0x32, 0x07, 0xF0, 0x26, 0x69, 0x87, 0x43, 0xEC, 0xF5, 0x04, 0x62, 0x9E, 0x0E, 0x28, 0x12, 0x95, 0xF7, 0x13, 0x29, 0x67, 0x72, 0xE0, 0x4F, 0x00, 0x00, 0x89, 0x58, 0xE7, 0xC8, 0x67, 0xAC, 0x14, 0x28, 0xF8, 0xC0, 0x3A, 0x9E, 0x6C, 0x5D, 0xCC, 0x53, 0xDE, 0xF9, 0x46, 0x5A, 0x67, 0x13, 0xFB, 0xA2, 0x0C, 0x87, 0x0C, 0xFB, 0xEE, 0x4C, 0x91, 0x91, 0x2E, 0x9A, 0x72, 0xB1, 0xDD, 0x5A, 0xBF, 0x0C, 0xEB, 0x1D, 0x5A, 0x04, 0x2E, 0x59, 0x2D, 0x61, 0xD3, 0x8F, 0x8F, 0xA8, 0xA1, 0x10, 0xAC, 0x89, 0xBF, 0xCA, 0xE5, 0xAB, 0x9B, 0xF4, 0x49, 0x8E, 0xAB, 0x10, 0x30, 0x99, 0x26, 0xEA, 0x90, 0xC0, 0x17, 0x94, 0x37, 0xD9, 0xF4, 0x40, 0x84, 0x75, 0x24, 0x42, 0x59, 0xC5, 0xE2, 0xBC, 0xAA, 0x26, 0xB4, 0xBA, 0x08, 0xEE, 0x4B, 0xF1, 0x5D, 0xFE, 0xF6, 0x36, 0x53, 0xB8, 0x49, 0xB7, 0x23, 0xCC, 0xAB, 0x78, 0x70, 0x62, 0xD6, 0xB8, 0x91, 0x56, 0xD5, 0x9F, 0x55, 0xBC, 0x7E, 0x43, 0xC0, 0x51, 0x5A, 0xA5, 0xD8, 0x61, 0xB4, 0xFF, 0xBF, 0x62, 0x11, 0x9E, 0x68, 0x4F, 0xC9, 0xEA, 0x02, 0xE9, 0xE2, 0x44, 0x4F, 0x00, 0x00, 0x7E, 0x58, 0xEA, 0x88, 0xE1, 0xAA, 0x70, 0x41, 0x50, 0x18, 0xF2, 0xAC, 0x67, 0xB5, 0x77, 0x07, 0x2D, 0xBF, 0x1A, 0x64, 0xD3, 0x86, 0xBF, 0x4E, 0x54, 0x95, 0x11, 0x18, 0x92, 0x81, 0x3C, 0x0F, 0x1E, 0x46, 0xF4, 0x0C, 0x08, 0x80, 0x09, 0xD8, 0x04, 0xBB, 0x43, 0xB7, 0x0F, 0x93, 0xCE, 0xE1, 0x72, 0x76, 0x38, 0x6E, 0x76, 0x12, 0xBD, 0x03, 0x83, 0xEE, 0x8D, 0x12, 0x43, 0xEB, 0x44, 0x2C, 0xDD, 0x69, 0x58, 0x2B, 0x04, 0xD6, 0x73, 0x4E, 0x84, 0xA0, 0xC9, 0x6F, 0xFF, 0x16, 0xF3, 0x7F, 0x43, 0xF6, 0x98, 0x1E, 0x91, 0x32, 0xD9, 0x9A, 0x0B, 0x93, 0xE0, 0x58, 0xC1, 0x3F, 0xEC, 0xFE, 0xBC, 0xCD, 0xA0, 0x74, 0xB1, 0x40, 0x09, 0xFF, 0xF7, 0x07, 0xFD, 0x3E, 0x44, 0x80, 0x55, 0x3A, 0x3B, 0x25, 0xA8, 0x68, 0x61, 0xF7, 0x0E, 0xAA, 0xA7, 0x7A, 0xC6, 0xC1, 0xBA, 0x4D, 0x1E, 0x4F, 0x00, 0x00, 0x89, 0x58, 0xED, 0x46, 0x52, 0x70, 0x0F, 0x42, 0x31, 0x9C, 0x6A, 0xB6, 0xB0, 0x89, 0x7B, 0x7A, 0xAB, 0xE0, 0x0B, 0x45, 0xB3, 0xFF, 0x10, 0x8A, 0xE1, 0x48, 0xAE, 0xC1, 0x0A, 0x50, 0x0D, 0x72, 0x25, 0xA1, 0x7D, 0xDF, 0x02, 0x8A, 0x34, 0xA2, 0x53, 0x9B, 0x32, 0x4B, 0x20, 0x24, 0x58, 0x71, 0xDB, 0x20, 0x45, 0x22, 0x6E, 0xFF, 0xCA, 0xA8, 0x84, 0xFC, 0xA0, 0x84, 0x85, 0x5E, 0x31, 0xD2, 0x2C, 0x21, 0x7A, 0xA0, 0x4D, 0x04, 0x41, 0xFB, 0xCA, 0x4F, 0x91, 0xFC, 0xD7, 0xBB, 0x38, 0xD0, 0x16, 0xCA, 0x27, 0xFE, 0x94, 0xFF, 0xD7, 0x36, 0x5A, 0x40, 0x15, 0x3C, 0x83, 0xD7, 0xCB, 0x15, 0x1E, 0x71, 0xBD, 0x76, 0x31, 0x16, 0xF9, 0xBC, 0xB1, 0xDD, 0x2C, 0x87, 0x6F, 0x1C, 0x25, 0x60, 0x83, 0x1A, 0xAF, 0x71, 0x03, 0x11, 0xE7, 0x45, 0xBC, 0x17, 0xAF, 0x31, 0xFF, 0x76, 0xF5, 0xE4, 0x7F, 0x96, 0x66, 0x41, 0x0B, 0x92, 0xC6, 0x64, 0xA7, 0xB8, 0x4F, 0x00, 0x00, 0x8A, 0x58, 0xEE, 0x70, 0x0E, 0x33, 0x63, 0x3D, 0xB2, 0xE2, 0xC7, 0x39, 0x13, 0x7B, 0x8E, 0xFF, 0x59, 0x70, 0x30, 0xE1, 0xCA, 0x8D, 0x33, 0x83, 0x25, 0xC0, 0x4F, 0x81, 0xE2, 0x3F, 0xC1, 0xB1, 0xF0, 0x2B, 0x84, 0x0A, 0x72, 0x69, 0x5D, 0x1F, 0x23, 0x52, 0x2F, 0x86, 0xC0, 0x2A, 0x38, 0xC6, 0x55, 0x0F, 0x34, 0x8C, 0x38, 0x94, 0x8C, 0x44, 0xC6, 0x03, 0xF9, 0xDC, 0x07, 0xA5, 0xDA, 0xF1, 0x29, 0x09, 0x60, 0x28, 0xD8, 0x30, 0x77, 0x12, 0x82, 0x02, 0x95, 0xE0, 0x5C, 0x3B, 0x7E, 0x1E, 0x5F, 0x55, 0x28, 0xC7, 0x02, 0x4E, 0x82, 0x34, 0x6F, 0x6F, 0x63, 0x1F, 0x23, 0x0F, 0xF5, 0xFC, 0xEC, 0x9F, 0xE3, 0x12, 0x3B, 0xCE, 0x7E, 0x81, 0xF7, 0xDB, 0x17, 0xAA, 0xE6, 0x00, 0xF5, 0xF9, 0xB3, 0x91, 0x54, 0x9D, 0x62, 0x08, 0xCF, 0x04, 0x7D, 0x38, 0x3F, 0x27, 0x43, 0x71, 0xA3, 0x9A, 0x73, 0x41, 0x68, 0x3A, 0xED, 0x39, 0x7A, 0x87, 0x91, 0xB6, 0x3C, 0x4F, 0x00, 0x00, 0x83, 0x58, 0xEE, 0x96, 0xD4, 0x80, 0x62, 0xF1, 0xCE, 0xF2, 0x16, 0x41, 0x35, 0xDD, 0xC7, 0x3E, 0xF7, 0x90, 0x7A, 0xA7, 0xFE, 0x3B, 0x1C, 0xA0, 0x11, 0x94, 0x91, 0xE2, 0x22, 0x27, 0x9D, 0x59, 0x23, 0x20, 0x00, 0x64, 0xBB, 0xBC, 0xFC, 0x89, 0x5D, 0x58, 0xB6, 0xC3, 0x76, 0x34, 0x50, 0x17, 0x66, 0x70, 0x52, 0xF6, 0x93, 0x58, 0x64, 0xA0, 0x0D, 0x02, 0xC7, 0x4D, 0x67, 0x17, 0x3F, 0xA9, 0x2D, 0x45, 0xE0, 0xCD, 0x63, 0x3A, 0xE1, 0x12, 0x74, 0xAF, 0x5C, 0xFE, 0x92, 0xBD, 0x7C, 0x7D, 0xA4, 0xD6, 0x99, 0x3D, 0x0A, 0x5E, 0xAB, 0xA8, 0x6C, 0x65, 0x44, 0x36, 0x15, 0x7F, 0xB4, 0x83, 0xA5, 0x01, 0x2A, 0x00, 0xC3, 0x88, 0x6A, 0x62, 0xBA, 0x7F, 0xE4, 0x22, 0xB1, 0xBB, 0x43, 0xE7, 0xDE, 0xC0, 0xD3, 0x38, 0x16, 0x6F, 0x00, 0xAB, 0xDD, 0xC6, 0xC8, 0x49, 0x30, 0xDC, 0xBF, 0xA4, 0xA1, 0xAF, 0xFF, 0x59, 0x4F, 0x00, 0x00, 0x86, 0x58, 0xEB, 0xF2, 0x5C, 0x0C, 0xAF, 0x23, 0x08, 0xC5, 0x71, 0xEE, 0x09, 0xFF, 0x41, 0x50, 0x9B, 0x48, 0xF4, 0xE5, 0xE8, 0x22, 0x8C, 0x3D, 0x0A, 0xB3, 0xA0, 0x3F, 0xA9, 0xF0, 0xB3, 0xF4, 0x1F, 0xDC, 0xD2, 0x8D, 0x55, 0x5A, 0x93, 0x2A, 0x35, 0x19, 0xF6, 0xEF, 0x48, 0x96, 0xBE, 0xA2, 0xFD, 0xC0, 0xA6, 0xC6, 0x97, 0x28, 0xBE, 0x05, 0x3E, 0x43, 0xAA, 0x92, 0x9F, 0xF4, 0x99, 0x26, 0x44, 0x69, 0x39, 0x90, 0x77, 0x92, 0x97, 0xD4, 0x65, 0x72, 0xFD, 0xF8, 0x24, 0xEA, 0x82, 0x21, 0x78, 0x1F, 0x32, 0x04, 0x51, 0x3F, 0x3B, 0x9B, 0x57, 0x71, 0xB4, 0xF2, 0x79, 0xC0, 0xF0, 0x24, 0x3A, 0x7C, 0xB5, 0x95, 0xE2, 0x39, 0xAD, 0xE4, 0xCE, 0xA4, 0xC6, 0x97, 0x60, 0xBA, 0xB9, 0x88, 0xEF, 0xE3, 0xED, 0x13, 0x64, 0x5B, 0x6F, 0xC3, 0xFF, 0x3E, 0x7B, 0x08, 0xED, 0xE1, 0x8C, 0xB4, 0x03, 0x18, 0xF3, 0xDB, 0xBB, 0x81, 0xA8, 0x4F, 0x00, 0x00, 0x4D, 0x58, 0x01, 0x58, 0x81, 0x9F, 0xD9, 0xB8, 0x41, 0x22, 0x80, 0xAA, 0x45, 0x74, 0x6B, 0xCC, 0x4A, 0x7C, 0xEC, 0x28, 0xC2, 0xE3, 0x6C, 0x66, 0x02, 0xE4, 0x56, 0x1A, 0x87, 0xC8, 0xC4, 0xA8, 0x3C, 0x6B, 0xF0, 0x53, 0x9C, 0x3C, 0x40, 0xE0, 0x46, 0xF0, 0xC8, 0x4E, 0xDE, 0xDE, 0x7A, 0xE8, 0xA8, 0xFE, 0x17, 0x20, 0x1A, 0x67, 0xF2, 0xEF, 0x02, 0xB1, 0x2C, 0xC7, 0xF6, 0xFE, 0x2D, 0x62, 0xB4, 0x82, 0x3C, 0xB4, 0x1B, 0x6D, 0x55, 0x25, 0xAB, 0xE8, 0x39, 0xCE, 0xE7, 0x84, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20
};

#endif // AUDIO_DATA_0_H