#ifndef AUDIO_DATA_POINT_H
#define AUDIO_DATA_POINT_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfoPoint[] = {0, 24, 49, 121, 257, 383, 499, 605, 722, 842, 948};
const uint8_t g_audioDataPoint[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x44, 0x58, 0x61, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1E, 0xEF, 0x1C, 0x6F, 0x85, 0xCF, 0x15, 0x52, 0x4B, 0x34, 0x96, 0x6C, 0x34, 0x56, 0xDF, 0xC8, 0x9A, 0x17, 0x81, 0x70, 0x71, 0x5F, 0xFA, 0xC4, 0x55, 0xB4, 0x02, 0x2D, 0x5F, 0x01, 0x55, 0x70, 0x3A, 0xC2, 0x35, 0x45, 0xFC, 0x8D, 0x19, 0x26, 0x04, 0xEA, 0xB7, 0x39, 0x3C, 0x5D, 0x69, 0x57, 0x09, 0xFE, 0xCA, 0xC5, 0x9A, 0x3A, 0x91, 0xF2, 0x85, 0xE1, 0x60, 0x29, 0xE7, 0x4F, 0x00, 0x00, 0x84, 0x58, 0xE1, 0x0C, 0x44, 0x9E, 0x55, 0x5B, 0xDB, 0x7D, 0x78, 0xD3, 0x66, 0xC2, 0xD1, 0xCC, 0xCA, 0xBA, 0x84, 0xDF, 0x8F, 0x88, 0xA4, 0x75, 0x60, 0xDE, 0xB7, 0x72, 0x9E, 0x66, 0xF6, 0xBC, 0xAE, 0x1C, 0x24, 0x37, 0x3F, 0x66, 0x2C, 0xA3, 0xBE, 0x76, 0x6E, 0xF6, 0x4E, 0xE5, 0xA5, 0x26, 0xD1, 0x29, 0xBD, 0x3C, 0xA0, 0xE1, 0x2C, 0xB6, 0x82, 0xC1, 0x00, 0x31, 0xB0, 0xF5, 0x55, 0x09, 0x79, 0xDA, 0x91, 0x58, 0x71, 0x8B, 0x4E, 0x51, 0xCD, 0x8E, 0x29, 0xE3, 0x12, 0x50, 0x03, 0x29, 0xEB, 0x2C, 0x2F, 0x3E, 0xCE, 0x09, 0x6C, 0x69, 0x4D, 0x87, 0x3E, 0x42, 0x7A, 0x25, 0xB1, 0xAE, 0x43, 0xC0, 0xFB, 0x25, 0x42, 0x64, 0x1A, 0xB0, 0x62, 0x52, 0x5D, 0xC3, 0x50, 0x20, 0x6F, 0x36, 0x0D, 0x84, 0xDE, 0x05, 0x99, 0xA5, 0xCC, 0x60, 0xF7, 0x8F, 0x21, 0x37, 0xE4, 0xA2, 0x18, 0xFE, 0xB7, 0x03, 0x1E, 0x76, 0x30, 0x4F, 0x00, 0x00, 0x7A, 0x58, 0xEC, 0x84, 0x4F, 0xE7, 0x94, 0xCC, 0x74, 0x46, 0x06, 0xF5, 0xF6, 0x1A, 0xE9, 0xF6, 0x91, 0x6B, 0xFC, 0x41, 0xCC, 0xCD, 0x94, 0xDE, 0x25, 0x41, 0x7E, 0xB7, 0x4F, 0x8E, 0x49, 0x18, 0xBD, 0x19, 0x53, 0x7A, 0x1F, 0xBD, 0xDC, 0x8F, 0xDA, 0xC1, 0xCD, 0x20, 0xDB, 0xA5, 0xF9, 0x7E, 0x4A, 0x60, 0x82, 0xF9, 0x65, 0x89, 0xC0, 0x5B, 0xBE, 0xC0, 0x06, 0x3D, 0x49, 0x50, 0x97, 0x36, 0xC3, 0x23, 0x52, 0xE9, 0xFA, 0x10, 0xC1, 0x3E, 0x6F, 0x50, 0x71, 0xBC, 0x34, 0x8E, 0xA2, 0x54, 0xB8, 0x34, 0x88, 0x8C, 0xA6, 0xFB, 0x7C, 0x7B, 0xC5, 0xE5, 0xF5, 0x46, 0xF1, 0x5C, 0xBB, 0x72, 0x1F, 0x4D, 0x03, 0x43, 0x42, 0xBC, 0x86, 0xCF, 0xBF, 0xF2, 0x60, 0x79, 0xCA, 0x5B, 0xCF, 0xE6, 0x57, 0x9B, 0x3D, 0xD7, 0xCA, 0xC8, 0xE1, 0x97, 0x3C, 0xF5, 0x70, 0x4F, 0x00, 0x00, 0x70, 0x58, 0xED, 0x4A, 0x39, 0x27, 0x5F, 0xE3, 0x46, 0xB7, 0xD9, 0xB3, 0x6D, 0xE2, 0x7A, 0x35, 0xF6, 0xD5, 0x73, 0x6F, 0x94, 0xE2, 0x2F, 0x18, 0x64, 0x01, 0x52, 0x77, 0x49, 0x36, 0x79, 0xF0, 0x77, 0x90, 0x92, 0xC5, 0x3E, 0xF4, 0x48, 0x1C, 0x59, 0xA6, 0x40, 0x8E, 0x65, 0x35, 0xEB, 0x3D, 0x23, 0x85, 0x16, 0xE1, 0x5A, 0xEF, 0xEE, 0x5A, 0xBE, 0x2D, 0x49, 0x1A, 0x40, 0xD7, 0xE3, 0xA7, 0xB7, 0xD8, 0x93, 0xB3, 0x2C, 0xB8, 0x11, 0x6B, 0xDB, 0xEE, 0x3D, 0xFD, 0xF1, 0x4B, 0xBD, 0xB8, 0xE3, 0xFA, 0x4A, 0x5B, 0xEA, 0x8A, 0x7B, 0xAA, 0x34, 0x23, 0x13, 0xAA, 0xC6, 0x34, 0x0A, 0x2D, 0x34, 0x15, 0xD8, 0xA5, 0x5D, 0xBF, 0x58, 0x02, 0x66, 0xBB, 0x94, 0x58, 0x1C, 0x46, 0x1E, 0xEA, 0xB0, 0x4F, 0x00, 0x00, 0x66, 0x58, 0xED, 0x48, 0xC1, 0xDB, 0x7E, 0xD5, 0x20, 0x5B, 0xB1, 0x28, 0xA9, 0x41, 0xAD, 0x0D, 0x16, 0x2A, 0x0B, 0xA0, 0xCC, 0x9D, 0xC5, 0xF3, 0x35, 0xBC, 0x40, 0xD7, 0x50, 0xCC, 0x7E, 0x46, 0x11, 0x81, 0x3C, 0x08, 0x7F, 0x9E, 0x33, 0x7E, 0xCA, 0x44, 0x5F, 0xF6, 0x98, 0x22, 0xA0, 0xB2, 0xBF, 0xF9, 0xCC, 0x3D, 0x96, 0x83, 0x51, 0x3C, 0xFA, 0x0F, 0x05, 0x22, 0x27, 0xD9, 0xAF, 0x2B, 0x23, 0x9B, 0x77, 0x48, 0x71, 0x71, 0x12, 0xFE, 0x2F, 0x08, 0xB3, 0xAC, 0x28, 0x0F, 0x6B, 0x51, 0x92, 0xC3, 0x68, 0x0E, 0x39, 0x73, 0x65, 0xBF, 0xBE, 0xA0, 0x7B, 0x40, 0x6B, 0x52, 0x33, 0x8D, 0x5C, 0x8F, 0x51, 0x94, 0x6C, 0x12, 0xD6, 0x4F, 0x00, 0x00, 0x71, 0x58, 0xED, 0x7C, 0x37, 0x1B, 0x45, 0x39, 0xA5, 0x99, 0x34, 0xB8, 0xE5, 0x8B, 0x31, 0x44, 0x32, 0x2C, 0xA0, 0x91, 0x77, 0x05, 0xC1, 0x51, 0x04, 0xF0, 0x18, 0xE2, 0xB5, 0x4B, 0xD2, 0x5A, 0x57, 0xCC, 0x06, 0x63, 0xA7, 0x84, 0x23, 0x16, 0x39, 0xED, 0x38, 0xE8, 0x22, 0x2A, 0x2D, 0xCD, 0x32, 0xA6, 0xCF, 0xD6, 0x1B, 0x25, 0xA0, 0xA3, 0x96, 0x23, 0xFE, 0xEA, 0x6D, 0xB2, 0x2B, 0xA5, 0x69, 0xDE, 0x5E, 0x3A, 0x3E, 0xCA, 0x57, 0x26, 0x36, 0xC4, 0x90, 0xA5, 0x5E, 0x77, 0xE2, 0x5B, 0x6D, 0x6B, 0x8A, 0xAA, 0x37, 0xE6, 0xEC, 0xB6, 0xC9, 0xA2, 0x72, 0x6F, 0xF0, 0x27, 0x7A, 0x54, 0x45, 0x34, 0x39, 0xAB, 0x26, 0x0F, 0x36, 0x22, 0x38, 0xE1, 0x07, 0x56, 0x48, 0x04, 0x29, 0xCD, 0xC3, 0xED, 0x4F, 0x00, 0x00, 0x74, 0x58, 0xEE, 0x2B, 0xF8, 0x09, 0x0C, 0x40, 0x4B, 0xA1, 0xB0, 0xD3, 0x48, 0x35, 0x12, 0x3D, 0x14, 0x8D, 0x01, 0xCF, 0x95, 0x97, 0xAC, 0x7F, 0x7F, 0x88, 0x05, 0x9F, 0x5E, 0x0B, 0xDB, 0x07, 0x7E, 0xF5, 0x16, 0x65, 0xC2, 0x23, 0xB0, 0x67, 0x6C, 0x25, 0x98, 0x79, 0x3E, 0x69, 0x92, 0xB2, 0xD4, 0x70, 0x79, 0xFD, 0x8E, 0x3B, 0x05, 0xF6, 0x8E, 0x77, 0xF9, 0x4F, 0x2A, 0xAD, 0x5A, 0x4C, 0xBE, 0x71, 0x65, 0xCE, 0x71, 0x77, 0x74, 0xB2, 0x8B, 0x77, 0x6A, 0x8A, 0xF5, 0x81, 0x3D, 0x67, 0x2E, 0xA9, 0xA4, 0x9B, 0xD5, 0x10, 0xB6, 0x22, 0x87, 0xE3, 0x08, 0x4D, 0x4A, 0xD1, 0x03, 0x49, 0xF1, 0x51, 0x2B, 0x46, 0x41, 0x05, 0x4F, 0xAF, 0xD8, 0x93, 0x66, 0xED, 0xBC, 0x31, 0xEF, 0xF0, 0x06, 0x24, 0x4B, 0x49, 0xC0, 0x4F, 0x00, 0x00, 0x66, 0x58, 0x83, 0xE2, 0xF3, 0xEA, 0x05, 0x12, 0x44, 0xB0, 0xB2, 0xD9, 0x91, 0xB9, 0xAC, 0xEC, 0x37, 0x05, 0xD8, 0x48, 0xD9, 0x38, 0x1A, 0x80, 0x2A, 0x14, 0xE1, 0x56, 0xB3, 0x19, 0x59, 0xC3, 0x07, 0xD7, 0x2D, 0x65, 0x83, 0x62, 0xF4, 0x4B, 0xD6, 0x8B, 0xCF, 0x5B, 0x46, 0x61, 0xE5, 0x22, 0x47, 0x0F, 0x2B, 0xED, 0xAB, 0xA8, 0x91, 0xE9, 0x0E, 0xF4, 0xCD, 0x18, 0x46, 0x0D, 0x65, 0x74, 0xE3, 0x07, 0xC4, 0xA6, 0x82, 0xD8, 0x0F, 0xA6, 0xC2, 0xFE, 0xA6, 0x21, 0x61, 0x26, 0xD0, 0x68, 0x4B, 0x0E, 0xC9, 0x62, 0x13, 0x24, 0xDB, 0xF9, 0x1C, 0x1C, 0xC3, 0xAF, 0xF2, 0x87, 0x98, 0x88, 0x09, 0xB7, 0x27, 0x36, 0x80, 0xC7, 0x74, 0x4F, 0x00, 0x00, 0x2C, 0x58, 0x04, 0x49, 0x0B, 0xEE, 0x4D, 0x19, 0x33, 0x3D, 0xDD, 0xAD, 0x80, 0xED, 0xB4, 0x79, 0x92, 0xDE, 0x5C, 0x88, 0x38, 0x6D, 0xCE, 0xAF, 0xEA, 0xF0, 0x98, 0x23, 0x63, 0xAB, 0xB1, 0x6B, 0x13, 0xCB, 0x73, 0x30, 0xE5, 0x0D, 0x18, 0xFC, 0x9F, 0x7D, 0x8D, 0xF0, 0xD0
};

#endif // AUDIO_DATA_POINT_H