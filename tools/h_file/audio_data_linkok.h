#ifndef AUDIO_DATA_LINKOK_H
#define AUDIO_DATA_LINKOK_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfoLinkOk[] = {0, 24, 49, 104, 260, 415, 561, 691, 835, 943, 1072, 1204, 1333, 1382, 1407, 1474, 1627, 1781, 1923, 2048, 2176, 2292, 2415, 2548, 2654, 2809, 2930, 3042, 3178, 3246};
const uint8_t g_audioDataLinkOk[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x33, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x0A, 0xD6, 0x10, 0x93, 0xC5, 0x11, 0x06, 0xC4, 0xFC, 0x68, 0xED, 0xDC, 0x2F, 0x4F, 0xE4, 0xCA, 0x00, 0xC1, 0x41, 0xEF, 0x8B, 0x72, 0xA0, 0x0F, 0x57, 0x40, 0x20, 0x1D, 0xF8, 0x0C, 0xF9, 0xB8, 0xF4, 0x45, 0x5A, 0xE5, 0x29, 0x9A, 0xB0, 0xDF, 0x26, 0x65, 0x99, 0x4F, 0x00, 0x00, 0x98, 0x58, 0xE7, 0x29, 0xD4, 0x41, 0xCB, 0x77, 0xAA, 0x69, 0x85, 0xA3, 0x1A, 0xE8, 0x8A, 0x6E, 0xF2, 0x50, 0xA4, 0x64, 0x9B, 0xAF, 0xE8, 0x3B, 0x08, 0xB5, 0xAC, 0x42, 0xFA, 0x8C, 0x0F, 0x15, 0x2B, 0xDF, 0x48, 0x09, 0x7D, 0x72, 0x7F, 0x9B, 0x9A, 0x7F, 0x9A, 0x16, 0x45, 0x79, 0x97, 0x58, 0x80, 0xFE, 0x0C, 0xC7, 0x17, 0x1E, 0x03, 0xCD, 0xA8, 0x1C, 0xD1, 0x9B, 0xC4, 0x19, 0x6C, 0x40, 0x8A, 0x61, 0x92, 0xB4, 0xCA, 0x1E, 0x84, 0x35, 0x23, 0x16, 0xA8, 0x22, 0x22, 0x2D, 0x52, 0x3A, 0x09, 0x1E, 0xCE, 0xE3, 0x92, 0xBD, 0xF2, 0x09, 0x41, 0x0F, 0x3B, 0x05, 0xFA, 0x30, 0x19, 0x58, 0x42, 0xA5, 0x4F, 0x85, 0x55, 0xE4, 0xDB, 0x81, 0xC1, 0xC1, 0x16, 0x07, 0x34, 0xB5, 0x36, 0x6B, 0xC0, 0xC6, 0x52, 0x3D, 0x0E, 0x31, 0xD8, 0x65, 0xB8, 0x26, 0xC8, 0xF1, 0x1A, 0x2A, 0x04, 0x46, 0xFF, 0x93, 0xA1, 0x50, 0x2E, 0x65, 0xF1, 0xEA, 0x81, 0x5F, 0x7C, 0xEC, 0x62, 0x53, 0x2F, 0x60, 0xE4, 0x76, 0xFB, 0x17, 0xDB, 0x45, 0xF3, 0x37, 0x92, 0x4F, 0x00, 0x00, 0x97, 0x58, 0xEC, 0x3B, 0x43, 0x0B, 0x6E, 0x53, 0x4B, 0xD0, 0x5D, 0x07, 0x9F, 0x1F, 0x9C, 0x14, 0xED, 0xAE, 0xB4, 0xE4, 0x33, 0x6B, 0x45, 0x9C, 0xFC, 0x76, 0x7C, 0x8D, 0x26, 0xD9, 0xDF, 0xEC, 0x4E, 0xB6, 0xDD, 0x7F, 0x1B, 0xCB, 0x48, 0xB4, 0xA8, 0xA3, 0x54, 0x03, 0xFE, 0x89, 0x7C, 0xFB, 0x74, 0xCE, 0x59, 0x6E, 0x33, 0xCC, 0x38, 0x30, 0x12, 0x15, 0xF3, 0xDF, 0x53, 0xFA, 0x23, 0xAD, 0x1F, 0x6E, 0x86, 0xF5, 0xBD, 0x14, 0x9B, 0x27, 0x13, 0x73, 0x03, 0x8D, 0x35, 0x3D, 0xA1, 0xDF, 0x60, 0x2E, 0xB2, 0xCE, 0xE0, 0x6D, 0x08, 0x1C, 0x76, 0x7B, 0x89, 0xB7, 0xAB, 0x3D, 0x3E, 0x2D, 0x33, 0x63, 0xCA, 0x54, 0x0F, 0xB9, 0x70, 0xA8, 0x3E, 0x2C, 0x59, 0x27, 0x9D, 0xB9, 0x29, 0xE1, 0x9D, 0xAB, 0x69, 0xE7, 0x65, 0x72, 0xF5, 0x92, 0xD3, 0x25, 0x97, 0xA9, 0x10, 0x61, 0x11, 0x9F, 0xAC, 0x33, 0xEE, 0xD8, 0x65, 0x21, 0xE1, 0xED, 0x74, 0xB2, 0x56, 0x16, 0x9B, 0xB9, 0x9B, 0x45, 0x17, 0x8E, 0x15, 0x8A, 0xC8, 0xB5, 0x35, 0x40, 0x4F, 0x00, 0x00, 0x8E, 0x58, 0xEE, 0x67, 0xE9, 0x48, 0x18, 0x5D, 0xAA, 0x03, 0x58, 0xFC, 0xAA, 0xCF, 0x98, 0x4F, 0x71, 0xE8, 0x02, 0x17, 0x97, 0x81, 0xC3, 0xE3, 0x49, 0x41, 0xE1, 0x7F, 0x59, 0x6D, 0xFF, 0x46, 0x5B, 0xF9, 0x13, 0xD2, 0x9D, 0xF8, 0x34, 0xB5, 0xAB, 0x6E, 0x6B, 0x3E, 0x18, 0xBA, 0x5F, 0x6C, 0xFA, 0x2B, 0x4A, 0xC8, 0xA1, 0x44, 0xA5, 0xB1, 0x4A, 0x0F, 0xC8, 0x01, 0x74, 0x76, 0xEA, 0xA9, 0xE3, 0x60, 0xA5, 0x50, 0xE7, 0xAE, 0x9E, 0x48, 0x0D, 0x88, 0xF6, 0x70, 0x28, 0x18, 0xE3, 0xF4, 0x08, 0xAC, 0x22, 0x4A, 0x21, 0xB5, 0xD0, 0x1D, 0xE0, 0x82, 0x98, 0x72, 0xE6, 0x35, 0x6E, 0x87, 0x36, 0x25, 0xE5, 0x1F, 0x8C, 0xF4, 0x13, 0x79, 0x6B, 0xAA, 0xDA, 0x18, 0x74, 0x5F, 0x86, 0x79, 0xA3, 0xC4, 0x12, 0x45, 0x2A, 0x71, 0x01, 0x7B, 0xA5, 0x8A, 0x50, 0x17, 0xB3, 0x05, 0x07, 0x85, 0xFA, 0xD0, 0xBE, 0xA8, 0x62, 0xA8, 0xA7, 0x85, 0x25, 0x4A, 0xEB, 0x7E, 0x7A, 0xEB, 0x60, 0x4F, 0x00, 0x00, 0x7E, 0x58, 0xE5, 0x4C, 0xE8, 0x66, 0xFD, 0x19, 0x69, 0xF5, 0x49, 0xFC, 0x7B, 0xD0, 0xF6, 0xDE, 0x28, 0xE1, 0xCA, 0xCB, 0xFC, 0x72, 0x1E, 0x20, 0xDD, 0xB8, 0x0D, 0xA4, 0x47, 0x5F, 0xF8, 0x0C, 0x90, 0x76, 0x9A, 0xD8, 0xB1, 0x4B, 0x07, 0x7B, 0x8D, 0x15, 0x97, 0xF1, 0xC9, 0xD7, 0x3B, 0x32, 0x08, 0x5D, 0x5A, 0x1C, 0x8B, 0x54, 0xAE, 0x1C, 0xCD, 0x45, 0x49, 0xAE, 0xAF, 0x10, 0xF1, 0x01, 0xF1, 0x69, 0xF1, 0xA4, 0xA5, 0x6B, 0xB0, 0x17, 0x91, 0xDB, 0x24, 0x84, 0xA4, 0xCE, 0xA5, 0x24, 0x27, 0xB9, 0x52, 0x79, 0x4D, 0x06, 0x43, 0x6A, 0x24, 0x95, 0xBF, 0x21, 0x67, 0x1E, 0x4B, 0x95, 0xED, 0xD4, 0x8D, 0x29, 0x10, 0x25, 0x35, 0x41, 0x37, 0xA0, 0xF5, 0x30, 0x92, 0x4C, 0x33, 0x9D, 0x91, 0x82, 0x0C, 0x88, 0x83, 0x51, 0x3C, 0x5F, 0x3A, 0x52, 0x7E, 0xEA, 0x41, 0xCB, 0xA2, 0x4F, 0x00, 0x00, 0x8C, 0x58, 0xED, 0x44, 0xD2, 0x5E, 0xF4, 0xDE, 0x67, 0x66, 0x4E, 0xF7, 0x13, 0x78, 0x06, 0xBF, 0xA7, 0x9D, 0x1C, 0x14, 0xCA, 0x59, 0x94, 0x14, 0xA7, 0x22, 0x6F, 0x63, 0xDC, 0x07, 0xC3, 0x45, 0x4D, 0x80, 0x5F, 0x2F, 0x29, 0xB5, 0xAB, 0x2A, 0xCD, 0xDD, 0x32, 0x15, 0x51, 0x41, 0x6C, 0x1B, 0xC4, 0xEC, 0x54, 0x27, 0x6D, 0x5C, 0xAD, 0x10, 0x7D, 0xE6, 0xFC, 0x25, 0xAE, 0x81, 0xE5, 0x69, 0x30, 0x69, 0xC2, 0x0F, 0xCB, 0x02, 0x67, 0x64, 0x4B, 0x0F, 0x36, 0x42, 0xB5, 0xB4, 0xAE, 0x18, 0x23, 0x59, 0x38, 0x55, 0xDE, 0xE0, 0xAE, 0xCF, 0xBC, 0x2E, 0x1B, 0x70, 0x92, 0x00, 0x81, 0x60, 0x94, 0x34, 0xBE, 0x15, 0xE0, 0x6F, 0xFC, 0xF0, 0x87, 0x10, 0x80, 0xD1, 0xFA, 0x7A, 0xDE, 0x59, 0xC4, 0x1D, 0x78, 0xEF, 0x2E, 0x35, 0x38, 0x27, 0x41, 0xFB, 0xDC, 0x97, 0xAC, 0xE5, 0x6D, 0x08, 0x91, 0x78, 0xCD, 0x57, 0x3F, 0x4E, 0x49, 0x1F, 0xDB, 0x83, 0x06, 0x9F, 0x88, 0x4F, 0x00, 0x00, 0x68, 0x58, 0xEC, 0x70, 0xC1, 0x82, 0xDA, 0x40, 0x92, 0x97, 0xF0, 0x81, 0x80, 0x9E, 0xCA, 0x5D, 0xB1, 0x81, 0x5C, 0x17, 0xDB, 0x2B, 0x70, 0x92, 0x0C, 0x63, 0x4A, 0xD4, 0x0E, 0xFC, 0xEA, 0xF5, 0x05, 0x35, 0x4A, 0x37, 0xA2, 0x2D, 0x16, 0xF6, 0xC8, 0x2A, 0x21, 0xE7, 0x89, 0xFE, 0x42, 0x81, 0x46, 0x1A, 0x57, 0xEB, 0xFF, 0xDF, 0xF7, 0x72, 0xE5, 0x25, 0x49, 0x55, 0xA0, 0xB6, 0xE6, 0xE1, 0x1E, 0xE4, 0x71, 0xCC, 0xC4, 0xB7, 0x87, 0x2B, 0x6A, 0x9F, 0xB3, 0xF2, 0x1F, 0x7A, 0x2A, 0x63, 0x4D, 0x4D, 0x8C, 0x0C, 0xB6, 0xFB, 0xC7, 0x3F, 0x44, 0xE4, 0x20, 0x4A, 0xAA, 0x62, 0xC0, 0xC6, 0x0E, 0x45, 0x05, 0x7B, 0xCC, 0xE1, 0x22, 0xFA, 0x60, 0x4F, 0x00, 0x00, 0x7D, 0x58, 0xEB, 0x41, 0x39, 0x16, 0x87, 0xA6, 0xC2, 0x6F, 0x39, 0xD5, 0x40, 0x8C, 0x31, 0xCF, 0x8E, 0x34, 0x4C, 0x59, 0x5B, 0x60, 0x14, 0xA3, 0x02, 0x5F, 0x45, 0xD7, 0x36, 0xFA, 0xBB, 0x46, 0x67, 0xEB, 0x0D, 0x9B, 0xE4, 0x3F, 0x5A, 0xD6, 0x69, 0x29, 0x6D, 0x43, 0x0A, 0x3A, 0xD0, 0x2F, 0x68, 0xE3, 0x82, 0x0F, 0x07, 0x59, 0x6D, 0xC6, 0x98, 0x61, 0x6C, 0xED, 0xF0, 0xFB, 0x12, 0x02, 0x27, 0x9B, 0xEA, 0xF6, 0xB5, 0xB2, 0x32, 0xB1, 0x2C, 0xCB, 0xF7, 0x69, 0xE4, 0xD4, 0x7B, 0x58, 0x9D, 0xF3, 0xFE, 0x5A, 0x12, 0x00, 0x3B, 0x39, 0x8E, 0x87, 0x16, 0x6B, 0x06, 0x4F, 0xBB, 0x11, 0xE0, 0x03, 0xF5, 0x47, 0x2E, 0xCF, 0xDB, 0x86, 0x1E, 0xEC, 0x9C, 0x6A, 0xC5, 0x2B, 0x43, 0x83, 0x28, 0xF7, 0xA4, 0x4B, 0x5E, 0xCD, 0xEE, 0x5E, 0xDE, 0x24, 0xFF, 0x39, 0x57, 0x48, 0x4F, 0x00, 0x00, 0x80, 0x58, 0xED, 0x01, 0x53, 0xF3, 0x47, 0xCB, 0xA7, 0x42, 0xED, 0x40, 0xFB, 0x0F, 0x49, 0x14, 0x42, 0xF5, 0x6F, 0xBB, 0xDD, 0xF6, 0xEE, 0x29, 0x3A, 0x6D, 0x2D, 0x84, 0x4A, 0x04, 0x97, 0xC6, 0x49, 0x5D, 0xE3, 0x26, 0x05, 0x8A, 0x37, 0x85, 0xD5, 0x4C, 0xC4, 0x23, 0x0E, 0x96, 0x4A, 0xD9, 0x58, 0x19, 0x74, 0x77, 0x3E, 0x01, 0xA2, 0xA5, 0xDC, 0x70, 0x77, 0x73, 0x35, 0x58, 0x6C, 0xF6, 0xF6, 0x93, 0x3C, 0x98, 0xE9, 0xFB, 0xCD, 0x9C, 0x2D, 0xBE, 0x6F, 0x11, 0x02, 0x44, 0x62, 0x1E, 0x45, 0xC5, 0x5E, 0x4A, 0x3A, 0x5D, 0x49, 0xA2, 0x2B, 0x4D, 0xB0, 0xD7, 0x93, 0xCE, 0x1B, 0x00, 0xEE, 0x27, 0x99, 0x0A, 0x90, 0x2E, 0x51, 0x78, 0x3C, 0x10, 0x9D, 0x05, 0x6C, 0x23, 0x86, 0xAC, 0x9B, 0xA3, 0x6C, 0x5F, 0x19, 0x55, 0x5E, 0xB7, 0x4B, 0xC7, 0xBA, 0x62, 0x12, 0x01, 0xEA, 0xCE, 0xCC, 0x4F, 0x00, 0x00, 0x7D, 0x58, 0xCC, 0x55, 0x1D, 0x83, 0xD0, 0x00, 0x24, 0x4B, 0x91, 0xEC, 0x89, 0x6E, 0xE0, 0x0E, 0x91, 0xE8, 0x50, 0x07, 0x11, 0xAA, 0xCB, 0x32, 0x0D, 0xD1, 0xE2, 0xFF, 0x20, 0x42, 0x9C, 0x1B, 0xAA, 0x36, 0x4C, 0x72, 0x80, 0x11, 0xAE, 0x3E, 0x02, 0x19, 0x34, 0x60, 0x40, 0xE7, 0xFE, 0x08, 0x82, 0x07, 0xB2, 0xB8, 0xD1, 0x02, 0x4E, 0xD7, 0xDC, 0xA2, 0x65, 0xB3, 0x24, 0xB5, 0x34, 0x7C, 0xD8, 0xD3, 0xBF, 0x10, 0x5D, 0xAD, 0x0F, 0xC4, 0xD4, 0x92, 0x46, 0x37, 0x1B, 0xB0, 0xAA, 0xB0, 0x13, 0x06, 0xA9, 0x17, 0xFA, 0x36, 0xF1, 0x12, 0x04, 0xAD, 0x60, 0x34, 0xA2, 0x54, 0x69, 0xE8, 0x3D, 0xE7, 0x2E, 0x70, 0x6C, 0xCB, 0x27, 0x00, 0x65, 0xF6, 0xDD, 0xDD, 0x11, 0xA3, 0x1D, 0x7F, 0x8E, 0x5B, 0xA8, 0xEE, 0x91, 0x09, 0x6D, 0xE7, 0xED, 0xF8, 0xD7, 0xA1, 0x0D, 0x32, 0x4F, 0x00, 0x00, 0x2D, 0x58, 0x06, 0xBD, 0x40, 0x59, 0xCC, 0x37, 0x54, 0x93, 0xC9, 0xE7, 0x10, 0xFA, 0x0C, 0x46, 0x81, 0xA5, 0xE7, 0x15, 0xFE, 0x12, 0x38, 0x2E, 0xAD, 0x4F, 0xF3, 0x38, 0xF1, 0x54, 0xDD, 0x8F, 0x48, 0x64, 0xB2, 0x3F, 0x14, 0x33, 0x66, 0x6A, 0xE7, 0x47, 0x27, 0x2E, 0x0D, 0xEB, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x3F, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x0E, 0xF6, 0x7F, 0x18, 0xA8, 0xE6, 0x34, 0x8C, 0xD4, 0x4E, 0x80, 0x2C, 0x60, 0x65, 0x42, 0x92, 0x3D, 0xD5, 0x3E, 0xB5, 0xE8, 0x0A, 0xF7, 0xB6, 0x46, 0x6A, 0xCF, 0x68, 0x56, 0xEA, 0x68, 0xB5, 0x2D, 0xDF, 0x32, 0x01, 0x34, 0x03, 0x9C, 0x65, 0xD4, 0x98, 0x3C, 0x9F, 0x73, 0x78, 0x05, 0x27, 0xE0, 0x41, 0x76, 0x04, 0xF6, 0xE8, 0x80, 0x4F, 0x00, 0x00, 0x95, 0x58, 0xE6, 0x93, 0x7D, 0xA7, 0x5D, 0xA5, 0xAC, 0x7E, 0x47, 0x3E, 0x4C, 0xB2, 0xED, 0xA2, 0x11, 0x05, 0x61, 0x4D, 0x72, 0xFE, 0x11, 0x66, 0xC8, 0x68, 0xD1, 0x66, 0x27, 0x51, 0x9A, 0xAF, 0xCF, 0xAF, 0x2A, 0xB7, 0x94, 0xD7, 0xB8, 0x0B, 0x75, 0x3B, 0x6A, 0xC1, 0x4A, 0x5B, 0x03, 0x20, 0xFB, 0xD2, 0x2E, 0x73, 0xA0, 0x69, 0xA9, 0x5C, 0x9A, 0x2B, 0x9B, 0x18, 0x72, 0x91, 0x32, 0x46, 0x12, 0x02, 0xC7, 0x68, 0x47, 0x63, 0xF9, 0xEA, 0x34, 0x47, 0x12, 0x85, 0xA2, 0xFC, 0x16, 0xE1, 0xD9, 0xDF, 0xD1, 0xB8, 0x39, 0x79, 0x79, 0x99, 0x4A, 0x64, 0xB1, 0xA4, 0xA7, 0x6A, 0xBF, 0x7A, 0x59, 0xF7, 0xC0, 0xC7, 0xC3, 0xC9, 0x78, 0x1D, 0xA0, 0xB5, 0x7D, 0xB5, 0xCC, 0xF5, 0xEE, 0x3F, 0x67, 0xD5, 0xB5, 0x10, 0xA4, 0xB9, 0x55, 0x22, 0x4B, 0x8F, 0x36, 0x52, 0x55, 0xDA, 0x36, 0x0A, 0xEB, 0xAB, 0x18, 0x07, 0x45, 0x55, 0x21, 0xE0, 0x50, 0x85, 0x01, 0x31, 0xAC, 0x67, 0xFB, 0xDB, 0xBA, 0xC1, 0xBD, 0x1D, 0x94, 0x50, 0x4F, 0x00, 0x00, 0x96, 0x58, 0xE9, 0x0E, 0x4F, 0x49, 0x09, 0x56, 0xDC, 0xBF, 0x10, 0xB5, 0xD4, 0x39, 0x9B, 0x47, 0xE9, 0xF6, 0x5A, 0x69, 0xAC, 0xC4, 0x22, 0xD8, 0xC8, 0x3E, 0x1F, 0x56, 0x71, 0x21, 0x82, 0xDE, 0xCC, 0x66, 0x54, 0x72, 0x9B, 0x59, 0xCA, 0x04, 0x9F, 0x42, 0xBE, 0xE7, 0x9D, 0xF4, 0x0F, 0x60, 0x27, 0x30, 0xBB, 0x6B, 0x79, 0x9A, 0x9C, 0xAE, 0x5D, 0xF6, 0xBD, 0xB0, 0x5C, 0xDB, 0x0C, 0x21, 0x18, 0x0F, 0x98, 0xD6, 0xFE, 0xFD, 0x46, 0xFF, 0x32, 0x1B, 0x4C, 0x8D, 0xEB, 0x6E, 0x59, 0x0D, 0x28, 0x34, 0xAB, 0xB0, 0xFF, 0x76, 0x24, 0xBD, 0xC4, 0x89, 0x14, 0x22, 0x01, 0xD5, 0x56, 0xB5, 0x50, 0x3B, 0x96, 0x63, 0x5D, 0x69, 0x2A, 0x88, 0x3F, 0xA0, 0x05, 0x04, 0x5D, 0x86, 0x55, 0xC7, 0x11, 0x9D, 0x8E, 0x43, 0x3C, 0x6F, 0xDB, 0x8B, 0x00, 0xB0, 0xA6, 0xFF, 0x4B, 0xEA, 0xC0, 0x63, 0xDB, 0xBD, 0x2C, 0x87, 0x6E, 0x1C, 0x7A, 0xDC, 0x7B, 0xDB, 0xEC, 0x2F, 0xEF, 0x9D, 0x45, 0x65, 0x0C, 0x54, 0x59, 0x18, 0x21, 0x6A, 0x70, 0x4F, 0x00, 0x00, 0x8A, 0x58, 0xEA, 0x88, 0x6B, 0x4D, 0x9A, 0xCB, 0x8F, 0x64, 0x75, 0xD9, 0xC0, 0x65, 0xB9, 0x77, 0x15, 0x56, 0xC8, 0xFA, 0x78, 0xE0, 0xEC, 0xF7, 0xB2, 0xA9, 0xFF, 0x20, 0xCB, 0x80, 0x30, 0x25, 0xC3, 0xC4, 0x5C, 0x06, 0x65, 0xE2, 0xF5, 0x30, 0xBF, 0x53, 0x52, 0x9C, 0x74, 0x2F, 0xF2, 0x57, 0x65, 0x2C, 0xD0, 0xD4, 0x7E, 0x08, 0x81, 0x28, 0x49, 0x00, 0xFE, 0x8D, 0x30, 0x9C, 0x17, 0xF0, 0x12, 0xAF, 0xCF, 0x8D, 0x67, 0x6A, 0x2E, 0xEC, 0x47, 0xE4, 0xB4, 0x7F, 0xE1, 0x8B, 0x98, 0xBC, 0xC5, 0xC9, 0x95, 0xBE, 0xCD, 0xCD, 0xE8, 0x25, 0x8B, 0x7D, 0x24, 0xF7, 0x14, 0xCF, 0x75, 0x5D, 0x82, 0x96, 0xB6, 0xF5, 0x49, 0x46, 0x48, 0x8F, 0x97, 0x0B, 0x1D, 0x4B, 0xF3, 0xC5, 0x69, 0x33, 0x36, 0x99, 0x81, 0x5B, 0x5B, 0x2F, 0x4A, 0x16, 0x41, 0xF9, 0xEB, 0xF5, 0xE5, 0x5E, 0xDF, 0x68, 0xB6, 0x78, 0x00, 0x69, 0x86, 0x59, 0xC3, 0x3A, 0xCF, 0xCF, 0x80, 0x4F, 0x00, 0x00, 0x79, 0x58, 0xE5, 0x27, 0x5F, 0x7E, 0x8A, 0xD4, 0xC7, 0x08, 0xDB, 0x03, 0x3E, 0x0C, 0xC8, 0x9B, 0x60, 0x94, 0x98, 0x1A, 0x1A, 0xA2, 0x09, 0x49, 0xA5, 0x68, 0xF7, 0xE2, 0xAD, 0xAC, 0x2F, 0x8B, 0xE9, 0xEA, 0xB1, 0x7A, 0xF1, 0x21, 0x82, 0xC1, 0x85, 0x0B, 0xE2, 0x75, 0x3E, 0xDE, 0x6F, 0x55, 0xA8, 0x1B, 0xCB, 0xF0, 0xE6, 0x68, 0xB1, 0x90, 0xCE, 0x34, 0xCF, 0x5E, 0xC8, 0x4A, 0x73, 0x8A, 0xAB, 0x4A, 0xD6, 0x31, 0xFD, 0x0D, 0x06, 0xC8, 0xC1, 0x89, 0xF0, 0x99, 0x93, 0xDA, 0x8C, 0x6F, 0xF7, 0xC1, 0x06, 0xDC, 0x2D, 0xFE, 0x55, 0xFA, 0xEF, 0x7D, 0x04, 0x99, 0x18, 0xB7, 0xFD, 0xEF, 0x9B, 0x12, 0x25, 0x97, 0xA9, 0x44, 0x0F, 0x6A, 0x1B, 0x6D, 0xD3, 0x36, 0x47, 0x1B, 0xCE, 0x26, 0x44, 0x23, 0x86, 0x75, 0xCA, 0xB6, 0x3F, 0xAE, 0x0A, 0x1D, 0x4F, 0x00, 0x00, 0x7C, 0x58, 0xE5, 0x37, 0x98, 0x6D, 0xA8, 0x0D, 0x6D, 0x7F, 0x47, 0x6B, 0x76, 0xB4, 0x19, 0x8A, 0xB7, 0x7F, 0xA0, 0x42, 0xA0, 0x33, 0xE2, 0x32, 0xB1, 0xEF, 0xF2, 0x6A, 0xB5, 0x70, 0xEB, 0xE9, 0x73, 0x04, 0x82, 0xF3, 0x3F, 0xD2, 0x4B, 0x4B, 0x16, 0x31, 0xFE, 0x64, 0xC7, 0xFA, 0x8E, 0xB2, 0x03, 0xD4, 0xBA, 0x00, 0x44, 0x39, 0x1E, 0x93, 0x94, 0x2B, 0xCD, 0xEC, 0xCA, 0x02, 0x9C, 0x38, 0xB2, 0x88, 0x66, 0x49, 0x5B, 0x3C, 0xC9, 0x3B, 0x48, 0xDB, 0x69, 0xE5, 0x2D, 0x22, 0x3C, 0x6B, 0x89, 0xB1, 0xD9, 0x63, 0xAD, 0xA7, 0xE5, 0x1A, 0xFF, 0x4C, 0xD6, 0x61, 0xA4, 0xD8, 0xBE, 0x6B, 0x58, 0xEE, 0xC0, 0x14, 0x23, 0x8D, 0xDD, 0xB2, 0x64, 0x39, 0x22, 0x25, 0x1D, 0x06, 0xF6, 0x56, 0xD3, 0x6C, 0xCC, 0xAE, 0xB0, 0x3B, 0x68, 0x65, 0x3F, 0xF2, 0xD4, 0x0F, 0x86, 0x4F, 0x00, 0x00, 0x70, 0x58, 0xEB, 0x0F, 0x16, 0x75, 0x14, 0x00, 0x66, 0xF9, 0x58, 0x75, 0x8D, 0x84, 0xA2, 0xDB, 0xFC, 0x76, 0x3D, 0xC3, 0xFE, 0x9B, 0x0C, 0xAF, 0x25, 0x39, 0x53, 0x12, 0x3C, 0x32, 0xD0, 0xF8, 0x5C, 0xA0, 0xE3, 0x3B, 0x4E, 0x24, 0x59, 0x2F, 0x4C, 0xB8, 0xE4, 0x4D, 0xC7, 0x6A, 0x03, 0xCE, 0x54, 0x41, 0x16, 0x80, 0x0B, 0xF0, 0xA2, 0x41, 0x09, 0xDC, 0xB3, 0x1B, 0x7B, 0xEB, 0x90, 0xA5, 0x97, 0xEA, 0x9B, 0x65, 0x8F, 0xFA, 0x9B, 0x32, 0xA1, 0x6A, 0x2F, 0x5F, 0xFD, 0x7C, 0x61, 0x7C, 0xFB, 0x76, 0xA0, 0x91, 0x32, 0x69, 0xE3, 0xB0, 0x95, 0x85, 0x60, 0x39, 0x37, 0x4D, 0x65, 0x44, 0x12, 0x01, 0x7B, 0xBB, 0x0D, 0x69, 0x27, 0x99, 0xE7, 0xD4, 0x02, 0xD6, 0xBD, 0xEA, 0x94, 0xD7, 0x20, 0x4F, 0x00, 0x00, 0x77, 0x58, 0xE7, 0xFD, 0x6B, 0x26, 0x08, 0x76, 0xB5, 0x28, 0x8B, 0x1D, 0x14, 0xAC, 0x67, 0x0E, 0xAD, 0x64, 0xA9, 0xCF, 0xAF, 0x5D, 0x41, 0xF8, 0xE4, 0xCF, 0x60, 0x44, 0x5D, 0xDE, 0xC1, 0x32, 0x22, 0x93, 0xEB, 0xA5, 0x08, 0x4D, 0xAA, 0x85, 0x31, 0x0A, 0x0C, 0x76, 0x19, 0x8C, 0xB8, 0x9D, 0x4D, 0xD6, 0x9D, 0xFC, 0x85, 0xF2, 0x9B, 0xB0, 0xAF, 0x15, 0xF7, 0xCD, 0x3D, 0x02, 0xAC, 0x96, 0x14, 0xBC, 0x62, 0x37, 0x74, 0x67, 0xB8, 0x1E, 0xFF, 0xDD, 0xDD, 0xB1, 0x80, 0x48, 0xEE, 0x39, 0x93, 0x63, 0xB3, 0xEF, 0x85, 0x2F, 0x73, 0x9A, 0xD1, 0x4B, 0xB0, 0x0D, 0x53, 0x75, 0x75, 0xCE, 0xA3, 0x31, 0x11, 0xAB, 0x74, 0xE8, 0x08, 0x7D, 0x66, 0x61, 0x15, 0x16, 0x38, 0x06, 0x30, 0x7D, 0xAA, 0x0E, 0x87, 0x7D, 0xA5, 0x3E, 0x5A, 0x8C, 0x4F, 0x00, 0x00, 0x81, 0x58, 0xE8, 0x99, 0xA8, 0xB8, 0x99, 0x6D, 0x8C, 0x48, 0x40, 0xD2, 0x8E, 0x59, 0x4A, 0xE7, 0x58, 0x57, 0x49, 0x81, 0x54, 0x42, 0xFC, 0x07, 0x2D, 0xED, 0x7D, 0xC9, 0x85, 0x75, 0x6A, 0x28, 0xB4, 0xD6, 0xF2, 0xEE, 0xE8, 0x8C, 0xD6, 0x09, 0xB1, 0x0B, 0xA5, 0x6B, 0x04, 0xFB, 0x62, 0x95, 0xDF, 0xEC, 0x62, 0xFD, 0xF1, 0x63, 0x7D, 0x98, 0xCA, 0x82, 0x28, 0x62, 0xC8, 0xE5, 0xDC, 0xE4, 0x36, 0xCF, 0x73, 0x41, 0x36, 0xFF, 0xBE, 0xBF, 0x4E, 0x13, 0x21, 0x89, 0x57, 0x57, 0xBF, 0xAF, 0x00, 0xCA, 0xE2, 0xED, 0x7E, 0x41, 0x51, 0x2C, 0xEE, 0x2A, 0x75, 0x65, 0xF9, 0x13, 0x7D, 0xB5, 0x6E, 0xBF, 0x32, 0x73, 0x62, 0x58, 0x10, 0xA6, 0x5A, 0x32, 0x59, 0x5B, 0xD7, 0x9F, 0x4B, 0xD6, 0x53, 0xDC, 0xEA, 0xEA, 0x57, 0x31, 0xAE, 0x91, 0x61, 0x5B, 0x64, 0x72, 0x17, 0x81, 0x26, 0xA9, 0x81, 0x6A, 0x4F, 0x00, 0x00, 0x66, 0x58, 0xA0, 0x25, 0x53, 0x22, 0x19, 0xDA, 0xFE, 0xDE, 0xBA, 0x26, 0x51, 0x37, 0xD6, 0x44, 0x95, 0x09, 0x6F, 0x65, 0xEF, 0xC0, 0x57, 0x8B, 0xA1, 0x48, 0x67, 0xE6, 0xE4, 0x07, 0xD2, 0xE9, 0x58, 0xC5, 0xAA, 0x63, 0xE3, 0x0B, 0x73, 0x83, 0xF5, 0x50, 0x96, 0xF5, 0x4A, 0xD1, 0x9C, 0x2C, 0xA5, 0x45, 0x45, 0x15, 0x22, 0xD3, 0x5C, 0x0B, 0xDB, 0x6A, 0xB7, 0x73, 0x75, 0xA1, 0x50, 0x17, 0xB1, 0x6C, 0x36, 0x2C, 0xBF, 0x72, 0x6B, 0x0E, 0xF2, 0x73, 0x1B, 0x41, 0xE8, 0xD5, 0x59, 0x47, 0xD5, 0x25, 0x26, 0x6C, 0x48, 0x09, 0xD0, 0xC8, 0x52, 0xEB, 0x26, 0x98, 0x94, 0x35, 0x7C, 0x54, 0xC7, 0x24, 0xF3, 0x59, 0x79, 0x98, 0x6A, 0x4F, 0x00, 0x00, 0x97, 0x58, 0xE3, 0xCE, 0xC9, 0x40, 0x6C, 0x28, 0x69, 0xF1, 0x34, 0xF7, 0x3D, 0xED, 0x34, 0x96, 0x08, 0x33, 0x55, 0x9B, 0x61, 0x76, 0x2E, 0xDB, 0x42, 0x32, 0xCF, 0xE8, 0x64, 0x90, 0x95, 0xC9, 0x79, 0xA0, 0x5E, 0x12, 0x88, 0x2D, 0x0E, 0x0F, 0x0E, 0x23, 0x5C, 0x3B, 0xC5, 0xC2, 0x3B, 0x47, 0x6D, 0xED, 0x99, 0xDF, 0x13, 0x9E, 0xBC, 0x7F, 0xD8, 0xD5, 0x5F, 0x6B, 0xAC, 0x3B, 0xDF, 0xFC, 0x22, 0x94, 0x32, 0x28, 0x28, 0x16, 0xA8, 0x64, 0x6C, 0x87, 0x65, 0xE3, 0xEE, 0xBA, 0x4D, 0xF5, 0x58, 0x79, 0xD2, 0xAE, 0x79, 0x6C, 0x90, 0x50, 0x96, 0xFD, 0x9E, 0x2A, 0xDC, 0xC0, 0xAC, 0x93, 0xEB, 0x92, 0x9B, 0x06, 0x36, 0x57, 0x9F, 0x2F, 0xCB, 0x4A, 0x38, 0xF7, 0x59, 0x49, 0xDA, 0xF2, 0x3A, 0xE3, 0x4A, 0xFC, 0x63, 0x58, 0x94, 0xDD, 0x78, 0x49, 0x60, 0x7B, 0x03, 0xF3, 0x32, 0x5B, 0xFE, 0xE1, 0xE5, 0x14, 0x73, 0x46, 0x9A, 0x8C, 0x4B, 0x5C, 0x78, 0x1E, 0x46, 0x05, 0x7C, 0x8F, 0x5E, 0xBE, 0x32, 0xE7, 0x07, 0x5C, 0x8A, 0x1A, 0x4F, 0x00, 0x00, 0x75, 0x58, 0xEC, 0x1D, 0x7A, 0x50, 0x00, 0xAE, 0x95, 0xBE, 0x4B, 0x68, 0xE3, 0x5E, 0x1D, 0x67, 0x58, 0x53, 0xA6, 0xA7, 0xBD, 0x48, 0x7B, 0x31, 0x6C, 0x3A, 0x4A, 0x80, 0x5B, 0x58, 0x94, 0x5D, 0x01, 0x09, 0x76, 0xBF, 0x26, 0x85, 0x1B, 0xD7, 0xC9, 0x65, 0xD9, 0x05, 0x49, 0xE7, 0x23, 0xBE, 0xCF, 0xDB, 0x5E, 0xFE, 0x89, 0x81, 0xFC, 0x4F, 0x8E, 0xAC, 0x4F, 0x33, 0x17, 0xDD, 0x19, 0xEB, 0x6F, 0xA6, 0xAE, 0x0D, 0x45, 0xC4, 0xF5, 0x5E, 0x5D, 0x7F, 0x42, 0x18, 0x70, 0x0F, 0x8D, 0x1A, 0x37, 0x7A, 0x2B, 0xCE, 0xB1, 0x31, 0x54, 0xB6, 0x27, 0xBC, 0x3F, 0x0A, 0xEB, 0x31, 0x8D, 0xD9, 0x52, 0xF4, 0xF9, 0xEA, 0xD0, 0x85, 0xBC, 0xC3, 0xF7, 0xD4, 0x8D, 0x2A, 0x36, 0x15, 0x55, 0x17, 0xA1, 0xBB, 0x95, 0x7E, 0x02, 0x61, 0x4F, 0x00, 0x00, 0x6C, 0x58, 0xED, 0x44, 0xFD, 0xE1, 0xE9, 0xD7, 0x66, 0xA6, 0x75, 0x19, 0xE4, 0xF5, 0x9F, 0x76, 0x07, 0xEB, 0x6C, 0xCC, 0xA7, 0x06, 0x63, 0xF1, 0x57, 0x54, 0xF3, 0xB4, 0x95, 0x41, 0x21, 0x76, 0xEF, 0xE8, 0xE0, 0x50, 0xB8, 0x3F, 0xF5, 0x35, 0xBD, 0x78, 0x41, 0xBC, 0x1C, 0xF0, 0xA6, 0x18, 0x55, 0xDF, 0xF1, 0x92, 0xE5, 0xDA, 0x89, 0x99, 0x7C, 0x8E, 0xBA, 0x3D, 0xC6, 0x30, 0x90, 0xEB, 0x90, 0x1B, 0xA6, 0x20, 0x6C, 0x02, 0xCF, 0x1A, 0x65, 0xA2, 0xBF, 0x6C, 0xFE, 0x75, 0x6A, 0xDF, 0xC5, 0xF7, 0xF2, 0x1B, 0xAF, 0xFE, 0x7C, 0xE8, 0x31, 0x8F, 0xAD, 0x07, 0x67, 0x1B, 0x8C, 0xEE, 0xD5, 0xA1, 0x13, 0x97, 0x64, 0xF5, 0x51, 0x96, 0xE9, 0x7E, 0xA1, 0x18, 0x5C, 0x4F, 0x00, 0x00, 0x84, 0x58, 0xEA, 0xA4, 0xF5, 0x32, 0x92, 0xDF, 0xA2, 0x28, 0x6B, 0xD1, 0xA0, 0x59, 0xB8, 0x88, 0xF2, 0x6E, 0x73, 0x96, 0x60, 0xF4, 0x34, 0xD8, 0xB6, 0xA8, 0x6A, 0xC6, 0x9D, 0x91, 0xC2, 0xDF, 0x28, 0x30, 0xBD, 0x61, 0xA2, 0x76, 0xB8, 0x47, 0x5C, 0x70, 0xEE, 0xE3, 0xB0, 0x35, 0x5A, 0x30, 0x2A, 0x59, 0xA7, 0xCD, 0xB6, 0x2D, 0x9C, 0x26, 0xB8, 0x90, 0x8E, 0xF5, 0xCC, 0x63, 0xFE, 0x45, 0x76, 0x10, 0x85, 0x4C, 0x18, 0x75, 0x59, 0x92, 0xBB, 0x75, 0xD9, 0x5C, 0xA1, 0x29, 0x06, 0x69, 0x7A, 0x7D, 0x13, 0x70, 0x1A, 0x92, 0xEA, 0xD4, 0x48, 0xC2, 0xC0, 0x8C, 0x1C, 0x5A, 0x0C, 0x38, 0xE0, 0x3F, 0x99, 0x0D, 0xD0, 0xE9, 0x97, 0xC6, 0x70, 0x8E, 0x08, 0x87, 0x29, 0xE5, 0xD8, 0xCF, 0x92, 0x43, 0x67, 0xCA, 0x6B, 0xBE, 0x41, 0x15, 0x6C, 0xAC, 0x27, 0xF0, 0x24, 0x06, 0x36, 0x14, 0x73, 0x7F, 0xA4, 0x7F, 0xF8, 0x4F, 0x00, 0x00, 0x40, 0x58, 0x00, 0xF8, 0x6C, 0x97, 0x8F, 0x4C, 0xBB, 0xB0, 0xF0, 0x79, 0xCF, 0x12, 0xB0, 0x20, 0x6C, 0x4F, 0x75, 0x86, 0xA1, 0xC9, 0x39, 0x5A, 0x4F, 0x5B, 0x8D, 0x20, 0x79, 0x3F, 0x6C, 0x87, 0x8A, 0x8E, 0x58, 0x9A, 0x82, 0x3E, 0xF7, 0x64, 0xD2, 0x2B, 0xE1, 0xDF, 0x8C, 0xDA, 0xC5, 0xF6, 0x14, 0xE1, 0xA5, 0xAD, 0xF9, 0xD8, 0x6F, 0x30, 0x6A, 0x7C, 0xE7, 0x6F, 0x17, 0x8B, 0x0F, 0x6F, 0xC0, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20
};

#endif // AUDIO_DATA_LINKOK_H