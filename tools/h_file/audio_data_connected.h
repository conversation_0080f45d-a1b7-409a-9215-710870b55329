#ifndef AUDIO_DATA_CONNECTED_H
#define AUDIO_DATA_CONNECTED_H

#include <stdint.h>
// Sample Rate: 16000 Hz
const uint16_t g_audioInfoConnected[] = {0, 24, 49, 102, 240, 364, 513, 660, 812, 955, 1068, 1188, 1319, 1454, 1593, 1722, 1834, 1950, 2075, 2131};
const uint8_t g_audioDataConnected[] = {
    0x4F, 0x00, 0x00, 0x14, 0x58, 0x02, 0xF9, 0x30, 0x4D, 0xBB, 0x0D, 0xE5, 0xE3, 0x92, 0x09, 0x89, 0x38, 0xEB, 0xCA, 0xE1, 0xB1, 0xD1, 0xDD, 0x85, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20, 0x4F, 0x00, 0x00, 0x31, 0x58, 0x21, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x0A, 0xD5, 0xBA, 0x24, 0xDF, 0xE1, 0x1B, 0x58, 0x29, 0xA9, 0x8E, 0xFF, 0x11, 0x64, 0xA5, 0xA6, 0x02, 0xF3, 0x8F, 0xDF, 0x65, 0xF6, 0x74, 0x0A, 0x89, 0x86, 0x68, 0x0F, 0x4C, 0xA8, 0xBD, 0xFF, 0x45, 0x8E, 0xF9, 0x30, 0x0D, 0x97, 0x09, 0x27, 0x80, 0x4F, 0x00, 0x00, 0x86, 0x58, 0xE0, 0x5C, 0xEC, 0xB6, 0x2A, 0x62, 0x10, 0x5F, 0x3D, 0xBF, 0x17, 0x7A, 0xD6, 0x19, 0x9D, 0x07, 0x69, 0x48, 0x48, 0xE5, 0x3A, 0x5C, 0x24, 0x8A, 0x07, 0xD7, 0xC6, 0x58, 0xAC, 0x05, 0x49, 0x23, 0x8B, 0x89, 0xF4, 0xF5, 0x75, 0x96, 0xE3, 0x8F, 0x31, 0xC4, 0xA5, 0x0D, 0x80, 0x12, 0xE1, 0x1B, 0x42, 0xAD, 0x1E, 0x11, 0x01, 0xBC, 0x18, 0x40, 0xF3, 0x69, 0xC8, 0x0E, 0x13, 0x6D, 0x90, 0x82, 0x35, 0x3E, 0x13, 0xDE, 0x12, 0x82, 0x73, 0x8E, 0x18, 0xAA, 0x6A, 0x9F, 0xD1, 0x79, 0xBA, 0x06, 0xC1, 0x33, 0xF8, 0x67, 0xE9, 0x22, 0x39, 0x05, 0xD0, 0x62, 0x4B, 0x16, 0x81, 0x9C, 0x10, 0x89, 0xBB, 0xA8, 0x23, 0x2C, 0xF5, 0xF8, 0x37, 0xD5, 0xFB, 0xD1, 0x07, 0x98, 0x1B, 0x09, 0x65, 0x58, 0x09, 0x7C, 0x98, 0xC9, 0xDF, 0x7A, 0x70, 0x9A, 0x4C, 0xA8, 0x44, 0xBB, 0xE8, 0x25, 0xCD, 0x78, 0x56, 0xDD, 0xA9, 0x19, 0xE5, 0x4F, 0x00, 0x00, 0x78, 0x58, 0xEC, 0x84, 0xC8, 0x58, 0x25, 0xA7, 0x06, 0xAE, 0x11, 0x8B, 0x50, 0xAB, 0xD4, 0xAB, 0xF3, 0x6F, 0xD6, 0x27, 0x05, 0x45, 0x1A, 0xD5, 0x3A, 0x0A, 0xF5, 0x08, 0x97, 0x91, 0x8B, 0x8B, 0x63, 0xC8, 0x67, 0xA7, 0xB1, 0x5C, 0x96, 0xFF, 0xFB, 0x02, 0x8F, 0x22, 0xD2, 0xE3, 0x25, 0x81, 0xFB, 0x6B, 0xD7, 0xA0, 0x04, 0xEF, 0xE3, 0xAE, 0x47, 0xB3, 0xC2, 0x54, 0xC2, 0xFE, 0x9D, 0x0B, 0x32, 0x89, 0x2C, 0xFE, 0x69, 0x14, 0x63, 0xF9, 0x6F, 0x32, 0xDA, 0x26, 0xAE, 0x22, 0x8A, 0x7B, 0x50, 0xCB, 0x32, 0xEF, 0xAA, 0x5D, 0xC3, 0x93, 0x98, 0x60, 0xEA, 0x8F, 0x27, 0x54, 0x2B, 0x10, 0xDC, 0x91, 0x13, 0x4A, 0x7D, 0x67, 0xFA, 0x75, 0xF7, 0x43, 0xC8, 0xEC, 0x82, 0x9F, 0xD8, 0xEA, 0xCA, 0xE3, 0x20, 0xFC, 0x14, 0xBC, 0x75, 0x7A, 0xA4, 0x4F, 0x00, 0x00, 0x91, 0x58, 0xED, 0x91, 0x76, 0xCF, 0xC2, 0x4C, 0x2C, 0x34, 0x27, 0x59, 0xA8, 0xF6, 0x50, 0x0B, 0x1B, 0xA9, 0xA9, 0x69, 0xBC, 0x25, 0x29, 0xE0, 0xB7, 0xDD, 0xAC, 0x53, 0xA8, 0xAD, 0xF7, 0x54, 0x55, 0x5E, 0x08, 0xEC, 0xCD, 0xBD, 0x36, 0xFF, 0xC3, 0xD1, 0x81, 0xFD, 0xF4, 0xED, 0x3A, 0x70, 0xED, 0xDF, 0xE7, 0x00, 0x8C, 0x60, 0xE1, 0xEE, 0xC6, 0x22, 0x2D, 0x09, 0xD0, 0x5A, 0xB5, 0xCB, 0x51, 0x65, 0x85, 0x64, 0x3C, 0x0E, 0x33, 0xFD, 0x11, 0xC3, 0xAC, 0x5B, 0xE9, 0x87, 0xEF, 0xF1, 0xE7, 0xEC, 0x05, 0xFB, 0xF5, 0x9B, 0xCA, 0x74, 0x4F, 0x74, 0xC0, 0x5F, 0x1A, 0x6E, 0xBB, 0xBE, 0x6D, 0x9D, 0xDC, 0x89, 0x80, 0x1E, 0x8E, 0x85, 0x3B, 0xBA, 0x6C, 0x61, 0xCD, 0x7D, 0x65, 0x99, 0x5E, 0x14, 0xAC, 0xFA, 0x40, 0xE4, 0x0D, 0x79, 0xF3, 0xB8, 0x81, 0x20, 0x80, 0xC3, 0x2E, 0x38, 0x42, 0x94, 0x9D, 0xF3, 0xAA, 0x28, 0x52, 0x2B, 0xDC, 0x58, 0xB5, 0x71, 0x92, 0x27, 0x8C, 0xB0, 0x79, 0x04, 0x4F, 0x00, 0x00, 0x8F, 0x58, 0xEC, 0x5F, 0xA6, 0xCA, 0x79, 0x51, 0xFB, 0x70, 0xAB, 0x6B, 0x20, 0x7A, 0x99, 0x5A, 0xFB, 0x66, 0x2E, 0xAE, 0xB0, 0x13, 0xC3, 0xF4, 0x42, 0x9C, 0xD7, 0x20, 0x81, 0x07, 0xC5, 0x61, 0x1C, 0x28, 0xF2, 0x2D, 0xAF, 0x73, 0x77, 0x53, 0x22, 0x2C, 0xC0, 0x52, 0x26, 0xA7, 0xCE, 0xB8, 0x29, 0xB9, 0xEC, 0xFF, 0x85, 0x0E, 0x69, 0x8E, 0xA9, 0xBA, 0xB4, 0x3D, 0x14, 0x13, 0xCD, 0x63, 0x8C, 0xA3, 0xBB, 0xCB, 0x60, 0x6A, 0x31, 0x16, 0x1E, 0x53, 0x32, 0x55, 0xED, 0x7B, 0x4B, 0xE3, 0x9A, 0x25, 0x79, 0x54, 0xD7, 0x1C, 0xF5, 0x79, 0x51, 0x66, 0xC7, 0xB4, 0x37, 0x37, 0x09, 0x94, 0xDD, 0xAF, 0x52, 0xBD, 0x5A, 0x8D, 0x6E, 0xAC, 0x54, 0xED, 0x40, 0x88, 0x2C, 0x84, 0xC4, 0x7C, 0x60, 0x4B, 0x71, 0x3E, 0x31, 0xFC, 0xF1, 0xE7, 0xE4, 0xE8, 0x2D, 0xFE, 0x50, 0x3D, 0xA5, 0x3E, 0xF5, 0xEA, 0xFE, 0x7C, 0x43, 0x39, 0xC0, 0x26, 0x4A, 0x10, 0xC8, 0x99, 0xAD, 0x67, 0xC4, 0xD8, 0x4F, 0x00, 0x00, 0x94, 0x58, 0xEA, 0xD7, 0x1D, 0x90, 0x06, 0x14, 0x2C, 0xAF, 0x1E, 0x9B, 0x12, 0x44, 0x1D, 0x61, 0x80, 0x3E, 0x82, 0xA2, 0x82, 0xF9, 0x6C, 0xD2, 0x77, 0xAA, 0x04, 0xF9, 0xAB, 0x5C, 0x2A, 0x8B, 0xC1, 0x07, 0x72, 0x3E, 0xB6, 0xC7, 0x1C, 0xFA, 0x05, 0x9B, 0xCF, 0x9A, 0xA6, 0xDC, 0xED, 0xE7, 0x23, 0xEF, 0x87, 0x21, 0x0D, 0xF0, 0x50, 0xA3, 0x7F, 0xF7, 0x7F, 0xB7, 0x6E, 0x69, 0x13, 0xC2, 0x24, 0xCA, 0xA4, 0xCD, 0x63, 0x41, 0xF0, 0x71, 0x2D, 0xCF, 0xF9, 0xA0, 0x5B, 0x11, 0x89, 0x19, 0x88, 0xCB, 0x02, 0xB2, 0x70, 0x1C, 0xEB, 0x76, 0xCC, 0x7B, 0xA1, 0x72, 0x3C, 0x59, 0xA1, 0x60, 0xA4, 0xDC, 0x29, 0x3E, 0x48, 0xDD, 0x07, 0x11, 0x27, 0xF2, 0x28, 0x25, 0x09, 0x0C, 0xB5, 0x0E, 0x5C, 0x0B, 0x81, 0xA7, 0xAC, 0xC4, 0xFB, 0xF8, 0xD2, 0x5B, 0x1D, 0xD7, 0x52, 0x76, 0xEE, 0xBD, 0xCB, 0xB8, 0x46, 0xED, 0x0F, 0x67, 0x16, 0x56, 0x40, 0x3A, 0x83, 0xFE, 0x94, 0x17, 0x1E, 0xA8, 0x35, 0x8D, 0x01, 0x22, 0x19, 0x4F, 0x00, 0x00, 0x8B, 0x58, 0xED, 0x6B, 0x96, 0x20, 0xCD, 0x4D, 0xF7, 0x3A, 0x08, 0x95, 0x6C, 0x17, 0xB5, 0xCD, 0xD8, 0xFF, 0xCD, 0x7B, 0xB8, 0x00, 0xE4, 0x4C, 0x3C, 0x86, 0xA6, 0x9C, 0x5F, 0x1B, 0x50, 0xE9, 0x8C, 0xF3, 0x46, 0x2A, 0x36, 0x37, 0xFA, 0xBC, 0x0D, 0xFA, 0xEE, 0x59, 0xD7, 0x47, 0x38, 0xE1, 0xD1, 0xC6, 0x85, 0x34, 0x3A, 0x0C, 0xCD, 0xAF, 0xFE, 0x69, 0xFB, 0x80, 0x34, 0x88, 0x23, 0xA5, 0x79, 0x19, 0xBB, 0xAC, 0x36, 0xF5, 0x11, 0xC6, 0x3D, 0xD8, 0x9E, 0xDB, 0xB3, 0x90, 0x58, 0xBC, 0xD5, 0x4C, 0x2B, 0x2F, 0x5B, 0x11, 0xB2, 0xEF, 0x18, 0x5F, 0x6A, 0x1B, 0xF8, 0x50, 0x21, 0xDC, 0x86, 0x4A, 0x2C, 0xFB, 0xC3, 0x43, 0xFF, 0xF7, 0x95, 0x83, 0xA2, 0x08, 0xD8, 0x57, 0x33, 0x2D, 0x66, 0xA7, 0x30, 0x2C, 0x76, 0x56, 0xE0, 0x3E, 0x52, 0x85, 0xE8, 0x7E, 0x74, 0x79, 0x03, 0xE8, 0x5E, 0x47, 0xA3, 0xA5, 0x8B, 0xE9, 0x82, 0xA6, 0x31, 0x1A, 0x96, 0x70, 0x4F, 0x00, 0x00, 0x6D, 0x58, 0xE8, 0xD9, 0xC2, 0x16, 0xA7, 0xA4, 0x60, 0xFB, 0x90, 0x4B, 0xB8, 0xC0, 0x9F, 0xC4, 0x5D, 0x2C, 0x67, 0xDC, 0x03, 0x4D, 0x59, 0x27, 0x00, 0x61, 0x7E, 0xF8, 0x83, 0xCA, 0xBE, 0xFD, 0x81, 0x78, 0x3E, 0x58, 0x40, 0x32, 0xBF, 0xD4, 0x7A, 0x06, 0x90, 0x9A, 0xF5, 0x94, 0x56, 0xEB, 0x63, 0x21, 0xB5, 0x26, 0x18, 0x61, 0xB2, 0xDA, 0x93, 0xDD, 0x34, 0x01, 0x29, 0xD7, 0x2D, 0x00, 0x1F, 0x9E, 0x07, 0x82, 0xD0, 0x34, 0x94, 0xC1, 0x75, 0x1A, 0x1C, 0x84, 0x98, 0x99, 0x06, 0x89, 0xF5, 0x3E, 0x0E, 0x68, 0xFB, 0x2C, 0x25, 0xD2, 0x8D, 0xED, 0xD8, 0xB9, 0xF3, 0x7F, 0x86, 0x89, 0xAC, 0x8B, 0xF3, 0xFC, 0xC2, 0xD5, 0x88, 0x87, 0x79, 0x6C, 0x50, 0xBC, 0x67, 0x66, 0x4F, 0x00, 0x00, 0x74, 0x58, 0xE0, 0xF2, 0x9C, 0xC3, 0x22, 0x6C, 0x47, 0x9E, 0xD9, 0x88, 0x51, 0x6F, 0xA5, 0x0D, 0x4E, 0x6E, 0xEF, 0x3A, 0xE5, 0x74, 0xF7, 0xE6, 0x29, 0x1F, 0x64, 0x8F, 0x94, 0x1D, 0x56, 0xE4, 0xDD, 0x9A, 0xCB, 0x53, 0x1B, 0x04, 0xB6, 0x93, 0xC4, 0xA5, 0x05, 0xD2, 0x30, 0x2D, 0x48, 0x2C, 0x70, 0x80, 0xC2, 0x01, 0xA5, 0x4D, 0xBE, 0x88, 0x4A, 0x93, 0xA0, 0xA6, 0x39, 0xB6, 0xD4, 0x68, 0x7D, 0xEE, 0x0B, 0xFB, 0x46, 0x36, 0x8B, 0xCB, 0x3B, 0x06, 0x9C, 0xC1, 0xD3, 0xA6, 0x44, 0xFB, 0x47, 0x8C, 0x77, 0x1D, 0x2B, 0x83, 0xD4, 0xEC, 0x08, 0x22, 0x00, 0x32, 0xB8, 0x2B, 0x6D, 0x11, 0xFA, 0xD4, 0x2A, 0x2D, 0xD3, 0xCE, 0x51, 0x7F, 0xE8, 0x7E, 0x24, 0x88, 0xB1, 0x16, 0x7F, 0x36, 0x2D, 0xFA, 0x38, 0x5D, 0xC8, 0x4F, 0x00, 0x00, 0x7F, 0x58, 0xE4, 0x79, 0x37, 0xAF, 0xA6, 0x59, 0xD5, 0x06, 0xD6, 0x42, 0x80, 0xEF, 0xD4, 0xDE, 0x4C, 0x1A, 0x7C, 0x69, 0x11, 0xD3, 0x3C, 0xBA, 0xC9, 0xEA, 0x10, 0x84, 0x5E, 0xF6, 0x3B, 0x0A, 0x6E, 0x25, 0xBE, 0x4E, 0x44, 0x77, 0x4D, 0x28, 0x53, 0x3D, 0x84, 0xAE, 0xBE, 0xAD, 0x3C, 0x84, 0x3F, 0x4F, 0xFC, 0xF2, 0xBA, 0xC2, 0x30, 0x77, 0x5D, 0x1E, 0xEF, 0xD8, 0xC2, 0x0F, 0xE1, 0xC0, 0x50, 0xC6, 0xD3, 0xCA, 0x39, 0xA0, 0x6D, 0xA6, 0x90, 0x40, 0x83, 0xC1, 0x9C, 0xA1, 0x64, 0x1F, 0x9E, 0x52, 0x2D, 0x78, 0x77, 0xBC, 0x1C, 0xB8, 0x13, 0x6A, 0xDA, 0xEB, 0x70, 0x4C, 0x02, 0x55, 0x6F, 0x59, 0xF8, 0x4E, 0x94, 0x61, 0x6D, 0x42, 0xC5, 0xB3, 0xE5, 0xB4, 0x80, 0x7E, 0xAD, 0x73, 0x8F, 0xF1, 0x9E, 0x5E, 0x1B, 0xE6, 0xA9, 0xC2, 0xB4, 0xD5, 0xBB, 0x07, 0x3E, 0x05, 0x7D, 0xC0, 0x4F, 0x00, 0x00, 0x83, 0x58, 0xE9, 0x3F, 0xB1, 0x2A, 0x14, 0xFB, 0x45, 0x15, 0x5E, 0x7F, 0x25, 0xB6, 0xFB, 0xA9, 0x19, 0x31, 0xA7, 0x7F, 0xE1, 0x9E, 0xA1, 0x93, 0x33, 0xD8, 0xC9, 0x49, 0x6C, 0x9A, 0xA6, 0x65, 0x29, 0x82, 0x96, 0xE3, 0x1F, 0x52, 0x82, 0x1F, 0x97, 0x7A, 0x3B, 0x8D, 0x86, 0x7F, 0x04, 0xE7, 0xC2, 0xFE, 0x0E, 0xE1, 0x15, 0x2F, 0x1A, 0x97, 0x41, 0xA8, 0x67, 0x23, 0x70, 0xFB, 0x8A, 0x3E, 0xF2, 0xD6, 0xAA, 0x96, 0x92, 0x09, 0xFD, 0xF8, 0xDE, 0x36, 0xDB, 0xE7, 0x77, 0xB9, 0x04, 0x23, 0xE6, 0x9A, 0xD2, 0x78, 0x34, 0xCD, 0x9E, 0xE3, 0x64, 0xDF, 0x58, 0x16, 0x58, 0x82, 0xF3, 0x9D, 0x63, 0x56, 0x9F, 0x47, 0x7D, 0x93, 0xE6, 0x5C, 0x25, 0x90, 0x03, 0xF1, 0x86, 0x0B, 0x00, 0xDC, 0xE9, 0x23, 0x42, 0x5B, 0x36, 0x9B, 0xD9, 0xBF, 0xE9, 0xFB, 0xA3, 0xFD, 0x12, 0x6F, 0xC4, 0x4B, 0xFC, 0x99, 0x49, 0x80, 0x4F, 0x00, 0x00, 0x87, 0x58, 0xE7, 0xAA, 0xAB, 0x0E, 0x70, 0x3C, 0x9D, 0xFE, 0xD5, 0x05, 0x63, 0x41, 0xF5, 0x69, 0x35, 0x29, 0x38, 0xB2, 0x31, 0xE1, 0x99, 0x29, 0x69, 0xC5, 0x21, 0x07, 0x55, 0xC7, 0x79, 0x50, 0x4D, 0x85, 0xB0, 0x15, 0x1B, 0x5F, 0x5B, 0xF6, 0x1A, 0x6A, 0x30, 0x65, 0x27, 0xA7, 0x44, 0xBB, 0x21, 0x67, 0x25, 0xA4, 0xEE, 0xB5, 0x7E, 0xFD, 0x05, 0x5B, 0x5B, 0xE0, 0xFB, 0xE8, 0x47, 0x68, 0xB6, 0x3D, 0xB8, 0x34, 0x55, 0xF0, 0x33, 0x98, 0x7A, 0x5D, 0x87, 0x09, 0x8D, 0x81, 0x66, 0x64, 0xC5, 0x63, 0xA4, 0x4B, 0xE9, 0xAA, 0xD6, 0xBA, 0x6A, 0x56, 0xB6, 0x25, 0xD3, 0x7B, 0x6B, 0xBA, 0xAD, 0x83, 0x5F, 0x0A, 0xC0, 0x01, 0x50, 0x99, 0xC2, 0xDB, 0x7E, 0x42, 0x38, 0xB3, 0xA1, 0xB5, 0x66, 0x4C, 0x42, 0x0A, 0x37, 0xA6, 0xF7, 0x06, 0x83, 0x28, 0x6D, 0xE4, 0x0D, 0xBD, 0xC2, 0x29, 0xF3, 0x19, 0x01, 0x74, 0xC8, 0x47, 0x90, 0x4E, 0x4F, 0x00, 0x00, 0x7D, 0x58, 0x60, 0xFA, 0xF2, 0xAB, 0x56, 0xC7, 0xFA, 0xF0, 0x49, 0x60, 0xF7, 0xD9, 0x31, 0xFB, 0x85, 0x7B, 0x85, 0x12, 0x2C, 0x3F, 0x70, 0x48, 0xA6, 0x66, 0x50, 0x2D, 0x16, 0x4B, 0x9D, 0x91, 0x15, 0x11, 0x72, 0x2C, 0x15, 0x69, 0x22, 0x0B, 0x73, 0x55, 0x8E, 0x5A, 0x6D, 0x30, 0xBC, 0xB1, 0x95, 0x84, 0xAC, 0xA6, 0x9D, 0x77, 0x96, 0x52, 0x1C, 0xA3, 0xE3, 0xF8, 0x6D, 0xA8, 0x53, 0x91, 0x5E, 0xB5, 0x47, 0x87, 0x1C, 0x66, 0xF8, 0xA9, 0x00, 0x75, 0x3C, 0x8B, 0xAB, 0x5F, 0x40, 0x11, 0xAE, 0xD4, 0x94, 0x33, 0x93, 0x66, 0x4B, 0x8E, 0x58, 0xE6, 0xA3, 0xD9, 0x04, 0xC3, 0xF2, 0x0E, 0x66, 0xD4, 0x48, 0xA9, 0x06, 0xE4, 0xD6, 0xE9, 0x98, 0xF4, 0xAE, 0xE1, 0x23, 0xBB, 0x4F, 0x5F, 0x9A, 0xE7, 0xFC, 0x26, 0xD5, 0x1D, 0x45, 0xBF, 0xE9, 0x94, 0xD3, 0xB3, 0xC4, 0x8F, 0x4F, 0x00, 0x00, 0x6C, 0x58, 0xEB, 0x60, 0x4A, 0x0A, 0xC6, 0x39, 0xE1, 0x2B, 0x23, 0x12, 0x67, 0x99, 0xD5, 0x46, 0xFF, 0x72, 0xFF, 0x89, 0x2F, 0x3C, 0x80, 0x2E, 0xB3, 0x04, 0xF2, 0x2A, 0xDF, 0xE5, 0x84, 0x78, 0xBB, 0x1D, 0x97, 0x44, 0x65, 0x4C, 0xEC, 0x37, 0x73, 0x64, 0xA7, 0x7C, 0x02, 0xBF, 0x73, 0x32, 0x6C, 0x1D, 0xF4, 0x33, 0x38, 0xE5, 0x67, 0xC2, 0xDD, 0x4B, 0xF8, 0x81, 0xBC, 0x24, 0xA7, 0x9B, 0x93, 0x27, 0x5E, 0x03, 0xE7, 0x3C, 0x4A, 0xBC, 0x8C, 0x0A, 0x91, 0xFB, 0x48, 0x2D, 0xB4, 0x39, 0x9B, 0xB8, 0x28, 0x09, 0x13, 0xD4, 0xD3, 0x19, 0x8B, 0x01, 0x30, 0xCD, 0x58, 0xB5, 0x1F, 0xE9, 0xC8, 0x9C, 0xD8, 0x53, 0x2F, 0x02, 0xDF, 0x66, 0xE3, 0x42, 0x30, 0x4E, 0xD9, 0x4F, 0x00, 0x00, 0x70, 0x58, 0xEC, 0xEE, 0x84, 0x47, 0x2F, 0x83, 0x81, 0xA4, 0xF2, 0x1E, 0xD0, 0x7B, 0x03, 0xA2, 0x89, 0x43, 0x41, 0x42, 0xEB, 0x4B, 0xC5, 0x8E, 0xAB, 0x49, 0x57, 0xAB, 0x49, 0x90, 0x51, 0x82, 0x80, 0x24, 0x3B, 0x9B, 0xE2, 0x1E, 0x96, 0x59, 0xDF, 0x31, 0x72, 0x3C, 0xAB, 0x4C, 0x7A, 0x7F, 0xBC, 0xE9, 0x2C, 0x9B, 0xCA, 0x87, 0x1B, 0x1D, 0x56, 0x51, 0x49, 0x96, 0xAB, 0x29, 0xEF, 0xDF, 0x29, 0xF5, 0x1C, 0x5F, 0x29, 0xBF, 0x10, 0x45, 0x5B, 0xC2, 0xF2, 0xFC, 0x9D, 0x36, 0xCE, 0xD9, 0x23, 0xE8, 0x85, 0x65, 0x8C, 0x85, 0xDE, 0xBD, 0x21, 0xAF, 0x2C, 0xBC, 0x95, 0xAD, 0x2D, 0x66, 0xFE, 0x91, 0x44, 0xFC, 0x7D, 0x1C, 0x17, 0x38, 0x99, 0x9F, 0x0B, 0xA0, 0x6C, 0x52, 0xC3, 0x13, 0x50, 0x4F, 0x00, 0x00, 0x79, 0x58, 0xC9, 0xD0, 0x30, 0x3A, 0xB5, 0x20, 0x24, 0x87, 0x99, 0x72, 0x62, 0x52, 0x90, 0x53, 0xE8, 0xBA, 0x06, 0x85, 0x1F, 0xEC, 0x09, 0x8B, 0x84, 0x35, 0x61, 0xA6, 0x5A, 0x09, 0xC1, 0xCF, 0xCF, 0x3F, 0x65, 0x09, 0x10, 0xA0, 0x4A, 0x29, 0x84, 0x8E, 0x30, 0xE0, 0xE1, 0x66, 0x36, 0x75, 0x8D, 0x5D, 0x42, 0xB8, 0x5D, 0x13, 0x13, 0x25, 0xCA, 0xEC, 0xAC, 0xCA, 0x15, 0x2E, 0xDA, 0xCD, 0x0A, 0x90, 0x6E, 0x4D, 0xE0, 0xB2, 0x71, 0xBD, 0xEE, 0x6E, 0x46, 0xA1, 0x13, 0x04, 0x2A, 0x90, 0x72, 0x33, 0x1D, 0xB0, 0x5C, 0x67, 0x08, 0x13, 0x27, 0x28, 0x96, 0x9F, 0x4C, 0x46, 0xE9, 0x84, 0xA9, 0xF0, 0x92, 0xA1, 0x4C, 0xF8, 0xB2, 0x33, 0x12, 0xCE, 0x76, 0x77, 0x22, 0x3A, 0x1E, 0xDD, 0xE5, 0x0E, 0x09, 0x93, 0x7C, 0xCD, 0x3C, 0x3D, 0x20, 0x59, 0x4F, 0x00, 0x00, 0x34, 0x58, 0x06, 0x9A, 0x71, 0x0A, 0xFE, 0xC0, 0x85, 0x67, 0xB5, 0xB0, 0xD6, 0x13, 0x8B, 0x13, 0x24, 0x10, 0xE8, 0x41, 0x2D, 0x05, 0x0A, 0x08, 0x58, 0xBF, 0x1C, 0x89, 0x85, 0x3A, 0xF2, 0xE5, 0x64, 0x0C, 0x14, 0xBA, 0xE6, 0xEA, 0xEC, 0x62, 0x74, 0x72, 0xB1, 0x57, 0x15, 0x26, 0x8E, 0xF4, 0x18, 0x0E, 0x78, 0x10, 0xF0, 0x4F, 0x00, 0x00, 0x15, 0x58, 0x01, 0xF2, 0x5E, 0x72, 0x36, 0x92, 0x1F, 0x05, 0xFA, 0x47, 0x8D, 0x69, 0x2C, 0x8F, 0x20, 0x0A, 0xFB, 0xFB, 0x2B, 0x20
};

#endif // AUDIO_DATA_CONNECTED_H