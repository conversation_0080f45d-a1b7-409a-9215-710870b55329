# convert audio files to protocol v3 stream
import librosa
import os
os.environ['OPUSLIB_PATH'] = r'C:\Program Files (x86)\Opus\bin\opus.dll'
import opuslib
import struct
import sys
import tqdm
import numpy as np
import os
import csv

def encode_audio_to_opus(inputFile, outputFile):
    # Load audio file using librosa
    audio, sampleRate = librosa.load(inputFile, sr=None, mono=False, dtype=np.int16)
    
    # Get left channel if stereo
    if audio.ndim == 2:
        audio = audio[0]
    
    # Initialize Opus encoder
    encoder = opuslib.Encoder(sampleRate, 1, opuslib.APPLICATION_VOIP)
    fileName = os.path.splitext(os.path.basename(inputFile))[0]
    
    # 找到音频数据中的最大值
    max_value = np.max(np.abs(audio))

    # 计算放大系数
    amplification_factor = 32768 * 0.9 / max_value

    # 将每个音频值乘以放大系数
    amplified_audio = (audio * amplification_factor).astype(np.int16)
    
    packetInfo = []
    offset = 0
    # Encode audio data to Opus packets
    # Save encoded data to file
    with open(outputFile, 'wb') as f:
        sampleRate = 16000 # 16000Hz
        duration = 60 # 60ms every frame
        frame_size = int(sampleRate * duration / 1000)
        for i in tqdm.tqdm(range(0, len(audio) - frame_size, frame_size)):
            frame = amplified_audio[i:i + frame_size]
            opus_data = encoder.encode(frame.tobytes(), frame_size=frame_size)
            # protocol format, [1u type, 1u reserved, 2u len, data]
            packet = struct.pack('>BBH', 79, 0, len(opus_data)) + opus_data
            packetInfo.append(offset)
            offset = offset + len(packet)
            f.write(packet)
    return packetInfo

def bin_to_hfile(inputFile, outputFile, sampleRate, packetInfo):
    try:
        # 打开输入文件并读取内容
        with open(inputFile, 'rb') as f:
            fileContent = f.read()

        # 获取文件名（不包含扩展名）
        fileName = os.path.splitext(os.path.basename(inputFile))[0]

        # 生成 C 语言数组
        array_name = f"{fileName}HtmlData"
        array_length = len(fileContent)
        array_content = ", ".join(f"0x{byte:02X}" for byte in fileContent)
        packetInfoStr = ", ".join(f"{offset}" for offset in packetInfo)

        # 生成头文件内容
        output_lines = ""
        output_lines += f"#ifndef AUDIO_DATA_{fileName.upper()}_H\n"
        output_lines += f"#define AUDIO_DATA_{fileName.upper()}_H\n\n"
        output_lines += f"#include <stdint.h>\n"
        output_lines += f"// Sample Rate: {sampleRate} Hz\n"
        output_lines += f"const uint16_t g_audioInfo{fileName}[] = {{{packetInfoStr}}};\n"
        output_lines += f"const uint8_t g_audioData{fileName}[] = {{\n"
        output_lines += f"    {array_content}\n"
        output_lines += f"}};\n\n"
        output_lines += f"#endif // AUDIO_DATA_{fileName.upper()}_H"

        # 写入输出文件
        with open(outputFile, 'w') as f:
            f.write(output_lines)

        print(f"Successfully generated {outputFile} from {inputFile}")

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

def gen_info_hfile(fileArray):
    try:
        outputFile = "sk_audio_file_info.h"
        arrayLines = "\n\nconst AudioFileItem g_audioFileList[] = {\n"
        
        # 生成头文件内容
        output_lines = ""
        output_lines += f"#ifndef AUDIO_DATA_INFO_H\n"
        output_lines += f"#define AUDIO_DATA_INFO_H\n\n"
        output_lines += f"#include <stdint.h>\n\n"
        
        output_lines += "typedef struct {\n"
        output_lines += "    char* key;\n"
        output_lines += "    uint16_t* frameOffsetList;\n"
        output_lines += "    uint16_t frameCnt;\n"
        output_lines += "} AudioFileItem;\n\n"
        
        for item in fileArray:
            keyName = item[0]
            file_path = item[1]
            packetInfo = item[2]
            packetInfoStr = ", ".join(f"{offset}" for offset in packetInfo)
            infoName = f"g_audioInfo{keyName}"
            output_lines += f"const uint16_t {infoName}[] = {{{packetInfoStr}}};\n"
            arrayLines = arrayLines + "    {" + "\"" + keyName + "\", " + infoName + ", " + str(len(packetInfo)) + "},\n"
        arrayLines += "};\n\n"
        output_lines += arrayLines
        output_lines += f"#endif // AUDIO_DATA_INFO_H"


        # 写入输出文件
        with open(outputFile, 'w') as f:
            f.write(output_lines)

        print(f"Successfully generated {outputFile} from {inputFile}")

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

def generate_nvs_csv(fileArray, output_csv):
    """
    遍历指定目录中的所有文件，并将文件内容转换为 Base64 编码，
    然后写入 CSV 文件中，用于生成 NVS 分区。
    """
    with open(output_csv, 'w', newline='') as csvfile:
        fieldnames = ['key', 'type', 'encoding', 'value']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        # 遍历目录中的所有文件
        for item in fileArray:
            keyName = item[0]
            file_path = item[1]
            packetInfo = item[2]
            if os.path.isfile(file_path):
                # 读取文件内容并转换为 Base64 编码
                #with open(file_path, 'rb') as file:
                #    file_content = file.read()
                #    base64_content = base64.b64encode(file_content).decode('utf-8')

                # 写入 CSV 文件
                # key,type,encoding,value
                writer.writerow({
                    'key': keyName,  # 文件名作为键名（去掉扩展名）
                    'type': 'file',  # 类型为文件
                    'encoding': 'binary',
                    'value': file_path  # Base64 编码的内容
                })

    print(f"NVS CSV 文件已生成：{output_csv}")

if __name__ == "__main__":
    # Mode=0: data in app, Mode=1: data in 
    mode = 0
    if mode == 1:
        # 指定目录和输出 CSV 文件路径
        output_csv_file = 'audio_data.csv'  # 输出的 CSV 文件
        fileNames = os.listdir(os.path.join("./", "wav16k"))
        fileArray = []
        for fileName in fileNames:
            inputFile = os.path.join("./", "wav16k", fileName)
            outputFile = "./" + "p3_file" + "/" + fileName[:-4]+".p3"
            print(f"convert {inputFile} to {outputFile}")
            packetInfo = encode_audio_to_opus(inputFile, outputFile)
            fileArray.append(["Data" + fileName[:-4], outputFile, packetInfo])
        generate_nvs_csv(fileArray, output_csv_file)
        gen_info_hfile(fileArray)
    else:
        fileNames = os.listdir(os.path.join("./", "source_file"))
        for fileName in fileNames:
            # 获取文件名和扩展名
            name, extension = os.path.splitext(fileName)
            inputFile = os.path.join("./", "source_file", fileName)
            outputFile = os.path.join("./", "p3_file", name+".p3")
            h_file = os.path.join("./", "h_file", "audio_data_" + name.lower()+".h")
            print(f"convert {inputFile} to {outputFile}")
            packetInfo = encode_audio_to_opus(inputFile, outputFile)
            bin_to_hfile(outputFile, h_file, 16000, packetInfo)
