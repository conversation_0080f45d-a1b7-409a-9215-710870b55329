from scipy.signal import resample
import scipy.io.wavfile as wav
import wave
import struct
import numpy as np

def read_binary_file(file_path):
    # 打开二进制文件
    with open(file_path, 'rb') as file:
        # 跳过最前面的44字节
        file.seek(44)
        # 初始化一个空列表来存储解析后的数据
        data = []
        # 每次读取2字节
        while True:
            chunk = file.read(2)
            if not chunk:
                break  # 如果读取到文件末尾，退出循环
            # 使用struct解析为有符号短整数（2字节）
            value = struct.unpack('<h', chunk)[0]  # '<h'表示小端模式的有符号短整数
            data.append(value)
        return data


def resample_wav(file_name, new_rate):
    in_name = file_name + '.wav'
    out_name = file_name + "_" + str(int(new_rate/1000)) + 'k.wav'

    # 打开音频文件
    sample_rate, data = wav.read(in_name)
    data_shape = data.shape
    print(f"data_shape: {data_shape}")
    if data.ndim == 2:
        data = (data[:, 0] + data[:, 1]) / 2
    resampled_data = resample(data, int(len(data) * new_rate / sample_rate))
    resampled_data = resampled_data.astype('int16')
    wav.write(out_name, new_rate, resampled_data)
    wav_file_path = out_name
    return out_name, resampled_data

def to_head_file(data, sample_rate, file_name):
    output_lines = ""
    output_lines += f"#ifndef AUDIO_DATA_{file_name.upper()}_H\n"
    output_lines += f"#define AUDIO_DATA_{file_name.upper()}_H\n\n"

    output_lines += f"// Sample Rate: {sample_rate} Hz\n"
    # 每行输出 8 个数据
    output_lines += f"const uint8_t g_audioData{file_name}[] = {{\n"
    for i in range(0, len(data), 4):  # 每行输出 8 个字节（4 个 int16 数据）
        line_data = data[i:i+4]
        output_lines += "   "
        for sample in line_data:
            # 小端格式：低字节在前
            low_byte = (sample & 0xFF)
            high_byte = ((sample >> 8) & 0xFF)
            output_lines += f" 0x{low_byte:02X}, 0x{high_byte:02X},"
        output_lines += "\n"
    output_lines += "};\n\n"
    output_lines += f"#endif // AUDIO_DATA_{file_name.upper()}_H"

    # 保存为头文件
    with open("audio_data_"+file_name.lower()+".h", "w") as f:
        f.write(output_lines)

def get_file_info(file_name):
    print(f"文件路径: {file_name}")
    with wave.open(file_name, "rb") as wav_file:
        # 获取文件信息
        num_channels = wav_file.getnchannels()  # 声道数
        sample_width = wav_file.getsampwidth()  # 位宽（字节）
        sample_rate = wav_file.getframerate()  # 采样率
        num_frames = wav_file.getnframes()  # 总帧数
        duration = num_frames / sample_rate  # 持续时间（秒）

        # 打印信息
        print(f"声道数: {num_channels}")
        print(f"采样率: {sample_rate} Hz")
        print(f"位宽: {sample_width * 8} bits")
        print(f"总帧数: {num_frames}")
        print(f"持续时间: {duration:.2f} 秒")

def genData(file_list, sample_rate):
    for file_name in file_list:
        wav_file_path, data = resample_wav(file_name, sample_rate)
        to_head_file(data, sample_rate, file_name)
        get_file_info(wav_file_path)

def getOldData(file_name):
    data = read_binary_file(file_name)
    resampled_data = np.array(data)
    resampled_data = resampled_data.astype('int16')
    return resampled_data

def genData2(file_list, sample_rate):
    for file_name in file_list:
        data = getOldData(file_name + ".wav")
        to_head_file(data, sample_rate, file_name)
        wav.write(file_name + "_cov.wav", sample_rate, data)
        get_file_info(file_name + "_cov.wav")

#play_file_list = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "Point"]
#play_file_list = ["Connected", "Reboot"]
#genData(play_file_list, 8000)

#record_file_list = ["CallReq", "Hello", "HelloFirst", "Onhook", "Wait"]
#genData(record_file_list, 16000)

#record_file_list = ["M2", "M3"]
#genData2(record_file_list, 16000)

record_file_list = ["LinkOk"]
genData(record_file_list, 16000)

