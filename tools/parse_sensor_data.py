import os
import math
import struct
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation

startIndex = 0
endIndex = 100

class SensorAnalyse():
    def __init__(self, path):
        self.path = path
        self.sensorRecordTxt = []
        self.gyro = []
        self.data = []

    def parseSensorData(self, payload):
        try:
            accelScale = 2.0 / 32768.0
            gyroScale = 250.0 / 32768.0
            accelTotal = 0.0
            accelX = 0.0
            accelY = 0.0
            accelZ = 0.0
            #uint32_t timestamp
            #int16_t accel[3]
            #int16_t gyro[3]
            #uint16_t ff
            #int16_t temp
            #uint16_t step
            #uint16_t stepState
            #uint16_t resv[2]
            # 解析TimeRecord
            record_size = struct.calcsize('<I6hHh4H')
            for i in range(0, len(payload), record_size):
                if i + record_size > len(payload):
                    break
                rec = struct.unpack('<I6hHh4H', payload[i:i + record_size])
                # 转换为物理量（±2g量程）
                accelX = rec[1] * accelScale
                accelY = rec[2] * accelScale
                accelZ = rec[3] * accelScale
                accelTotal = math.sqrt(accelX * accelX + accelY * accelY + accelZ * accelZ) - 1.0
                # 转换为物理量（±250dps量程）
                gyroX = rec[4] * gyroScale
                gyroY = rec[5] * gyroScale
                gyroZ = rec[6] * gyroScale
                ff = rec[7]
                temp = rec[8] / 128.0 + 25
                stepCnt = rec[9]
                stepState = rec[10]
                self.sensorRecordTxt.append(
                    f"{rec[0]}, {accelX:.3f}, {accelY:.3f}, {accelZ:.3f}, {accelTotal:.3f}, {gyroX:.3f}, {gyroY:.3f}, {gyroZ:.3f}, {ff}, {temp:.1f}, {stepCnt}, {stepState}\n")
                self.gyro.append([accelX, accelY, accelZ, gyroX, gyroY, gyroZ])
        except Exception as e:
            print(e)
        
    def loadAndParse(self):
        with open(self.path, 'rb') as f:
            data = f.read()
            f.close()
            self.parseSensorData(data)
        name, extension = os.path.splitext(self.path)
        text_filename = f"{name}_parse.txt"
        with open(text_filename, 'w') as txt_file:
            txt_file.writelines(self.sensorRecordTxt)
        print(f"Saved {len(self.sensorRecordTxt)} records to {text_filename}")

    def plotData(self):
        # 初始化数据
        data = []
        # 初始化图形
        fig, axs = plt.subplots(2, 1, figsize=(10, 8))
        self.lines = []
        labels = ['X', 'Y', 'Z']
        # 初始化加速度和陀螺仪的子图
        for i in range(2):
            for j in range(3):
                line, = axs[i].plot([], [], label=f'{labels[j]}')
                self.lines.append(line)
        axs[0].set_title('Acceleration')
        axs[1].set_title('Gyro')
        axs[0].set_xlim(0, 100)
        axs[0].set_ylim(-2, 2)
        axs[0].legend()
        axs[1].set_xlim(0, 100)
        axs[1].set_ylim(-360, 360)
        axs[1].legend()
        def onKey(event):
            global startIndex, endIndex
            if event.key == 'left':  # 按左箭头键，显示前 100 个数据
                if startIndex - 100 >= 0:
                    startIndex -= 100
                    endIndex -= 100
                else:
                    startIndex = 0
                    endIndex = 100
                self.updatePlot()
            elif event.key == 'right':  # 按右箭头键，显示后 100 个数据
                if endIndex + 100 < len(self.gyro):
                    startIndex += 100
                    endIndex += 100
                else:
                    startIndex = len(self.gyro) - 100
                    endIndex = len(self.gyro)
                self.updatePlot()
        # 创建动画
        #ani = FuncAnimation(fig, self.update, frames=self.gyro, interval=50, blit=True)
        # 绑定按键事件
        fig.canvas.mpl_connect('key_press_event', onKey)
        # 初始绘制
        self.fig = fig
        self.updatePlot()
        # 显示图形
        plt.show()

    def updatePlot(self):
        global startIndex, endIndex
        # 获取当前显示的数据段
        current_data = self.gyro[startIndex:endIndex]
        # 更新图形
        for i in range(3):
            self.lines[i].set_data(range(len(current_data)), [d[i] for d in current_data])
            self.lines[i + 3].set_data(range(len(current_data)), [d[i + 3] for d in current_data])
        # 重新绘制图形
        self.fig.canvas.draw_idle()

    # 更新函数
    def update(self, frame):
        # 将新数据添加到数据列表
        self.data.append([frame[0], frame[1], frame[2], frame[3], frame[4], frame[5]])

        # 如果数据超过 100 个点，移除旧数据
        if len(self.data) > 100:
            self.data.pop(0)

        # 更新图形
        for i in range(3):
            self.lines[i].set_data(range(len(self.data)), [d[i] for d in self.data])
            self.lines[i + 3].set_data(range(len(self.data)), [d[i + 3] for d in self.data])
        return self.lines

parser = SensorAnalyse("data/20250423_150745_sensor.bin")
parser.loadAndParse()
parser.plotData()