from pydub import AudioSegment
import shutil
import os
from scipy.signal import resample
import scipy.io.wavfile as wav
import wave
import struct
import numpy as np

def ConvMp3ToWav16(dirPath):
    fileNames = os.listdir(os.path.join("./", dirPath))
    print(f'{str(fileNames)}')
    for fileName in fileNames:
        inputFile = os.path.join("./", dirPath, fileName)
        middleFile = os.path.join("./", "wav", fileName[:-4]+".wav")
        outputFile = os.path.join("./", "wav16k", fileName[:-4]+".wav")
        if "mp3" in fileName:
            audio = AudioSegment.from_mp3(inputFile)
            audio.export(middleFile, format="wav")
        else:
            shutil.copy2(inputFile, middleFile)
        print(f'{inputFile} to {outputFile}')
        ResampleWav(middleFile, outputFile, 16000)

def ReadBinaryFile(file_path):
    # 打开二进制文件
    with open(file_path, 'rb') as file:
        # 跳过最前面的44字节
        file.seek(44)
        # 初始化一个空列表来存储解析后的数据
        data = []
        # 每次读取2字节
        while True:
            chunk = file.read(2)
            if not chunk:
                break  # 如果读取到文件末尾，退出循环
            # 使用struct解析为有符号短整数（2字节）
            value = struct.unpack('<h', chunk)[0]  # '<h'表示小端模式的有符号短整数
            data.append(value)
        return data


def ResampleWav(srcFile, dstFile, new_rate):
    # 打开音频文件
    sample_rate, data = wav.read(srcFile)
    data_shape = data.shape
    print(f"data_shape: {data_shape}, {srcFile}")
    if data.ndim == 2:
        data = (data[:, 0] + data[:, 1]) / 2
    resampled_data = resample(data, int(len(data) * new_rate / sample_rate))
    resampled_data = resampled_data.astype('int16')
    wav.write(dstFile, new_rate, resampled_data)
    return dstFile

def GetWavInfo(file_name):
    print(f"文件路径: {file_name}")
    with wave.open(file_name, "rb") as wav_file:
        # 获取文件信息
        num_channels = wav_file.getnchannels()  # 声道数
        sample_width = wav_file.getsampwidth()  # 位宽（字节）
        sample_rate = wav_file.getframerate()  # 采样率
        num_frames = wav_file.getnframes()  # 总帧数
        duration = num_frames / sample_rate  # 持续时间（秒）

        # 打印信息
        print(f"声道数: {num_channels}")
        print(f"采样率: {sample_rate} Hz")
        print(f"位宽: {sample_width * 8} bits")
        print(f"总帧数: {num_frames}")
        print(f"持续时间: {duration:.2f} 秒")

def genData(file_list, sample_rate):
    for file_name in file_list:
        wav_file_path, data = ResampleWav(file_name, sample_rate)
        GetWavInfo(wav_file_path)


def genData2(file_list, sample_rate):
    for file_name in file_list:
        data = getOldData(file_name + ".wav")
        to_head_file(data, sample_rate, file_name)
        wav.write(file_name + "_cov.wav", sample_rate, data)
        GetWavInfo(file_name + "_cov.wav")

#play_file_list = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "Point"]
#play_file_list = ["Connected", "Reboot"]
#genData(play_file_list, 8000)

#record_file_list = ["CallReq", "Hello", "HelloFirst", "Onhook", "Wait"]
#genData(record_file_list, 16000)

#record_file_list = ["M2", "M3"]
#genData2(record_file_list, 16000)

#record_file_list = ["LinkOk"]
#genData(record_file_list, 16000)

ConvMp3ToWav16("source_file")
ConvMp3ToWav16("ring")


