import socket
import wave
import sys
import signal
import datetime

# 服务器配置
SERVER_IP = "0.0.0.0"  # 服务器监听所有IP地址
SERVER_PORT = 8080      # 服务器端口
BUFFER_SIZE = 1024      # 缓冲区大小
SAVE_FILE = "received_data.pcm"  # 保存数据的文件名
save_data = []

def save_wave(data):
    dataB = b''.join(data)
    timeNow = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    SAVE_FILE = f"DBG_{timeNow}.wav"
    if (len(dataB) > 0):
        with wave.open(SAVE_FILE, 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)  # 16-bit PCM
            wav_file.setframerate(16000)
            wav_file.writeframes(dataB)
            print('saved to file')

def signal_handler(sig, frame):
    if (len(save_data) > 0):
        save_wave(save_data)
        print('You pressed Ctrl+C!')
    print('Exiting program...')
    sys.exit(0)

def tcp_server():
    global save_data
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as server_socket:
        server_socket.bind((SERVER_IP, SERVER_PORT))
        server_socket.listen(5)
        print(f"Server listening on {SERVER_IP}:{SERVER_PORT}")
        client_socket, client_address = server_socket.accept()
        client_socket.settimeout(1.0)
        print(f"Connection from {client_address}")
        timeoutCnt = 0
        while True:
            try:
                data = client_socket.recv(BUFFER_SIZE)
                #print(f"Received data: {len(data)}")
                save_data.append(data)
            except KeyboardInterrupt:
                print("KeyboardInterrupt received, stopping server...")
                break
            except socket.timeout:
                timeoutCnt += 1
                if (timeoutCnt >= 100):
                    print(f'{timeoutCnt} >= 100, timeout')
                    break
                continue
            except Exception as e:
                print(f"Error: {e}")
                break
        # 将接收到的数据保存到文件
        client_socket.close()
        save_wave(save_data)
        server_socket.close()

if __name__ == "__main__":
    signal.signal(signal.SIGINT, signal_handler)
    tcp_server()