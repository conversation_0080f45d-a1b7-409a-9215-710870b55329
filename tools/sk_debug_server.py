import socket
import threading
import struct
from datetime import datetime
import signal
import sys
import time
import math

# 全局控制变量
running = True
client_threads = []

FRAME_DPKT_DATE_INFO = 20
FRAME_DPKT_TERM_TIME_RECORD = 21
FRAME_DPKT_TERM_ACC_DATA = 22
FRAME_DPKT_AGENT_TIME_RECORD = 23
FRAME_CMSG_DFX_KEEPALIVE = 24

FrameTypeNameList = {
    FRAME_DPKT_DATE_INFO: "date_info",
    FRAME_DPKT_TERM_TIME_RECORD: "down_delay",
    FRAME_DPKT_TERM_ACC_DATA: "acc_data",
    FRAME_DPKT_AGENT_TIME_RECORD: "up_delay",
    FRAME_CMSG_DFX_KEEPALIVE: "keep_alive",
}
FrameTypeList = [
    FRAME_DPKT_DATE_INFO,
    FRAME_DPKT_TERM_TIME_RECORD,
    FRAME_DPKT_TERM_ACC_DATA,
    FRAME_DPKT_AGENT_TIME_RECORD,
    FRAME_CMSG_DFX_KEEPALIVE,
]


class ClientHandler:
    def __init__(self, client_socket, client_addr):
        self.client_socket = client_socket
        self.client_addr = client_addr
        self.raw_data = bytearray()  # 存储所有原始数据
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.shutdown_requested = False
        self.frame_type = None  # 在解析阶段确定
        self.login_server_time = ""
        self.login_dev_time = ""
        self.timeRecordData = bytearray()
        self.timeRecordTxt = []
        self.sensorRecordData = bytearray()
        self.sensorRecordTxt = []

    def parseSensorData(self, payload):
        try:
            accelScale = 2.0 / 32768.0
            gyroScale = 250.0 / 32768.0
            accelTotal = 0.0
            accelX = 0.0
            accelY = 0.0
            accelZ = 0.0
            #uint32_t timestamp
            #int16_t accel[3]
            #int16_t gyro[3]
            #uint16_t ff
            #int16_t temp
            #uint16_t step
            #uint16_t stepState
            #uint16_t resv[2]
            # 添加到二进制数据
            self.sensorRecordData.extend(payload)
            # 解析TimeRecord
            record_size = struct.calcsize('<I12H')
            for i in range(0, len(payload), record_size):
                if i + record_size > len(payload):
                    break
                rec = struct.unpack('<I12H', payload[i:i + record_size])
                # 转换为物理量（±2g量程）
                accelX = rec[1] * accelScale
                accelY = rec[2] * accelScale
                accelZ = rec[3] * accelScale
                accelTotal = math.sqrt(accelX * accelX + accelY * accelY + accelZ * accelZ) - 1.0
                # 转换为物理量（±250dps量程）
                gyroX = rec[4] * gyroScale
                gyroY = rec[5] * gyroScale
                gyroZ = rec[6] * gyroScale
                ff = rec[7]
                temp = rec[8]
                stepCnt = rec[9]
                stepState = rec[10]
                self.sensorRecordTxt.append(
                    f"{rec[0]}, {accelX:.3f}, {accelY:.3f}, {accelZ:.3f}, {accelTotal:.3f}, {gyroX:.3f}, {gyroY:.3f}, {gyroZ:.3f}, {ff}, {temp}, {stepCnt}, {stepState}\n")
        except Exception as e:
            print(e)

    def parse_and_save_data(self):
        """解析并保存数据"""
        if not self.raw_data:
            return

        # 解析确定frame_type (从第一个有效帧头)
        if len(self.raw_data) < 16:
            return

        # 处理所有完整帧
        processed_data = bytearray()
        text_lines = []
        pos = 0
        total_size = len(self.raw_data)

        while pos <= total_size:
            try:
                # 解析帧头
                header = struct.unpack('!HBBHHHHHH', self.raw_data[pos:pos + 16])
                if header[0] != 0x1981:
                    pos += 1  # 尝试重新对齐
                    continue
                frame_size = 16 + header[4]  # 头+负载
                if pos + frame_size > total_size:
                    break  # 不完整帧

                payload_start = pos + 16
                payload_end = payload_start + header[4]
                payload = self.raw_data[payload_start:payload_end]
                # 只处理目标frameType
                if header[1] == FRAME_DPKT_TERM_TIME_RECORD or header[1] == FRAME_DPKT_AGENT_TIME_RECORD:
                    self.frame_type = header[1]
                    # 添加到二进制数据
                    self.timeRecordData.extend(payload)
                    # 解析TimeRecord
                    record_size = struct.calcsize('<HBB7I')
                    for i in range(0, len(payload), record_size):
                        if i + record_size > len(payload):
                            break
                        rec = struct.unpack('<HBB7I', payload[i:i + record_size])
                        self.timeRecordTxt.append(
                            f"{rec[0]}, {rec[1]}, {rec[2]}, {rec[3]}, {rec[4]}, {rec[5]}, {rec[6]}, {rec[7]}, {rec[8]}, {rec[9]}\n")
                elif header[1] == FRAME_DPKT_DATE_INFO:
                    try:
                        dateInfo = struct.unpack('<QII', payload)
                        localTime = time.localtime(dateInfo[0])
                        timeString = time.strftime('%Y-%m-%d %H:%M:%S', localTime)
                        self.login_dev_time = f"Dev: {timeString}.{str(dateInfo[1]).zfill(3)}, {str(dateInfo[2])}\n"
                        print(f'Date info frame found, {self.login_dev_time}')
                    except Exception as e:
                        print("DateInfo frame parse error" + str(e))
                    self.timeRecordTxt.append(self.login_server_time)
                    self.timeRecordTxt.append(self.login_dev_time)
                    self.sensorRecordTxt.append(self.login_server_time)
                    self.sensorRecordTxt.append(self.login_dev_time)
                elif header[1] == FRAME_DPKT_TERM_ACC_DATA:
                    print(f'Process sensor data {len(payload)}')
                    self.parseSensorData(payload)
                elif header[1] == FRAME_CMSG_DFX_KEEPALIVE:
                    pass
                else:
                    print(f'Unexpected frame type {header[1]}')
                pos += frame_size
            except:
                pos += 1  # 解析错误时跳过1字节

        # 保存文件
        if self.timeRecordData:
            binary_filename = f"data/{self.timestamp}_{FrameTypeNameList[self.frame_type]}.bin"
            text_filename = f"data/{self.timestamp}_{FrameTypeNameList[self.frame_type]}.txt"
            with open(binary_filename, 'wb') as bin_file:
                bin_file.write(self.timeRecordData)
            with open(text_filename, 'w') as txt_file:
                txt_file.writelines(self.timeRecordTxt)
            print(f"Saved {len(self.timeRecordData)} bytes to {binary_filename}")
            print(f"Saved {len(self.timeRecordTxt)} records to {text_filename}")
        print(f"{len(self.sensorRecordData)}")
        if self.sensorRecordData:
            binary_filename = f"data/{self.timestamp}_sensor.bin"
            text_filename = f"data/{self.timestamp}_sensor.txt"
            with open(binary_filename, 'wb') as bin_file:
                bin_file.write(self.sensorRecordData)
            with open(text_filename, 'w') as txt_file:
                txt_file.writelines(self.sensorRecordTxt)
            print(f"Saved {len(self.sensorRecordData)} bytes to {binary_filename}")
            print(f"Saved {len(self.sensorRecordTxt)} records to {text_filename}")

    def handle(self):
        timeoutCnt = 0
        """处理客户端连接的主循环"""
        # 获取当前时间
        now = datetime.now()
        self.login_server_time = f"Server: {now.strftime("%Y-%m-%d %H:%M:%S") + "." + now.strftime("%f")[:3]}"
        print(f"Client connected from {self.client_addr} at {self.login_server_time}")
        try:
            while running and not self.shutdown_requested:
                try:
                    # 设置超时以检查关闭事件
                    self.client_socket.settimeout(1.0)
                    timeoutCnt = 0
                    # 只接收原始数据
                    data = self.client_socket.recv(4096)
                    if not data:
                        break

                    self.raw_data.extend(data)

                except socket.timeout:
                    timeoutCnt += 1
                    if (timeoutCnt >= 10):
                        print(f'{timeoutCnt} >= 10, timeout')
                        break
                    continue
                except Exception as e:
                    print(f"Error during receiving: {e}")
                    break
        finally:
            print(f"Client {self.client_addr} disconnected, received {len(self.raw_data)} bytes")
            self.client_socket.close()
            print(f"Client {self.client_addr} parse data")
            self.parse_and_save_data()
            print(f"Client {self.client_addr} end")

def signal_handler(sig, frame):
    """处理中断信号"""
    global running
    print("\nShutdown signal received, initiating graceful shutdown...")
    running = False

    # 通知所有客户端线程
    for thread in client_threads:
        if hasattr(thread, 'handler'):
            thread.handler.shutdown_requested = True

    # 等待线程结束
    for thread in client_threads:
        if thread.is_alive():
            thread.join(timeout=2)

    sys.exit(0)


def start_server():
    global running, client_threads

    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    server_socket.bind(('0.0.0.0', 9530))
    server_socket.listen(5)

    print("Server started on port 9530. Waiting for connections...")
    signal.signal(signal.SIGINT, signal_handler)

    try:
        while running:
            try:
                server_socket.settimeout(1.0)
                client_socket, client_addr = server_socket.accept()

                handler = ClientHandler(client_socket, client_addr)
                client_thread = threading.Thread(target=handler.handle)
                client_thread.handler = handler
                client_thread.start()
                client_threads.append(client_thread)

                # 清理已结束的线程
                client_threads = [t for t in client_threads if t.is_alive()]

            except socket.timeout:
                continue
            except Exception as e:
                if running:
                    print(f"Error accepting connection: {e}")
                continue

    finally:
        server_socket.close()
        print("Server stopped")


if __name__ == "__main__":
    start_server()