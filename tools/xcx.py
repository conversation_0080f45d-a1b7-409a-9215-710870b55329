#xcx.py创建也给tcp client，连接192.168.4.1 端口9528， 发送数据{"ssid":"123456789","password":"66667777"}
import socket
import time

time.sleep(1)
s = socket.socket()
s.connect(('192.168.4.1', 9528))
s.sendall(b'{"operation":"get_ssid"}')
a = s.recv(512)
print(str(a))
s.close()

time.sleep(1)
s = socket.socket()
s.connect(('192.168.4.1', 9528))
s.sendall(b'{"operation":"clear_ssid"}')
a = s.recv(512)
print(str(a))
s.close()

time.sleep(1)
s = socket.socket()
s.connect(('192.168.4.1', 9528))
s.sendall(b'{"operation":"get_ssid"}')
a = s.recv(512)
print(str(a))
s.close()

time.sleep(2)
s = socket.socket()
s.connect(('192.168.4.1', 9528))
s.sendall(b'{"operation":"add_ssid","ssid":"SK-Net","password":"2025Succ"}')
a = s.recv(64)
print(str(a))
s.close()

time.sleep(1)
s = socket.socket()
s.connect(('192.168.4.1', 9528))
s.sendall(b'{"operation":"get_ssid"}')
a = s.recv(512)
print(str(a))
s.close()

time.sleep(10)
s = socket.socket()
s.connect(('192.168.4.1', 9528))
s.sendall(b'{"operation":"reset"}')
a = s.recv(64)
print(str(a))
s.close()