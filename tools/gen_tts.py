from pydub import AudioSegment
from pydub.generators import Sine
import numpy as np

# 定义参数
sample_rate = 16000  # 采样率
duration_beep = 300  # 每个“嘟”声的持续时间（毫秒）
silence_duration = 400  # 每次“嘟”声之间的间隔（毫秒）
num_beeps = 3
frequency = 350  # 频率（Hz）

# 生成一个“嘟”声
beep = Sine(frequency).to_audio_segment(duration=duration_beep)

# 生成静音
silence = AudioSegment.silent(duration=silence_duration)

# 构造完整的电话“嘟”声
phone_beep = beep
for _ in range(num_beeps - 1):
    phone_beep += silence

# 导出为WAV文件
phone_beep.export("phone_beep.wav", format="wav")

print("电话“嘟”声已生成并保存为 phone_beep.wav")