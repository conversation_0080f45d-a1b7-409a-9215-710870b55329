import asyncio
from websockets.server import serve

async def echo(websocket):
    """
    处理 WebSocket 连接：接收消息并原样返回
    """
    async for message in websocket:
        print(f"收到消息: {message}")
        # 原样返回消息给客户端
        await websocket.send(message)

async def main():
    # 启动 WebSocket 服务器
    async with serve(echo, "0.0.0.0", 8765):
        print("WebSocket 服务器已在 ws://localhost:8765 启动")
        # 保持服务器运行
        await asyncio.Future()  # 永久运行

if __name__ == "__main__":
    asyncio.run(main())