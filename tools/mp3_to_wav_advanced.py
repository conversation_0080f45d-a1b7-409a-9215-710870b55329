#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整版本：MP3转WAV格式转换器
支持批量转换、参数配置、进度显示等功能
需要安装：pip install pydub tqdm
"""

from pydub import AudioSegment
import os
import sys
import argparse
import glob
from pathlib import Path
try:
    from tqdm import tqdm
    HAS_TQDM = True
except ImportError:
    HAS_TQDM = False
    print("提示：安装tqdm可以显示进度条 (pip install tqdm)")

class Mp3ToWavConverter:
    """MP3转WAV转换器类"""
    
    def __init__(self, sample_rate=44100, channels=2, bit_depth=16):
        """
        初始化转换器
        
        Args:
            sample_rate (int): 采样率，默认44100Hz
            channels (int): 声道数，默认2（立体声）
            bit_depth (int): 位深度，默认16位
        """
        self.sample_rate = sample_rate
        self.channels = channels
        self.bit_depth = bit_depth
        self.success_count = 0
        self.fail_count = 0
    
    def convertSingleFile(self, mp3_file, wav_file=None, show_info=True):
        """
        转换单个MP3文件
        
        Args:
            mp3_file (str): 输入的MP3文件路径
            wav_file (str): 输出的WAV文件路径
            show_info (bool): 是否显示转换信息
        
        Returns:
            bool: 转换成功返回True，失败返回False
        """
        try:
            # 检查输入文件
            if not os.path.exists(mp3_file):
                if show_info:
                    print(f"错误：文件 {mp3_file} 不存在")
                return False
            
            # 生成输出文件名
            if wav_file is None:
                wav_file = os.path.splitext(mp3_file)[0] + '.wav'
            
            if show_info:
                print(f"正在转换: {os.path.basename(mp3_file)}")
            
            # 加载MP3文件
            audio = AudioSegment.from_mp3(mp3_file)
            
            # 应用音频参数
            if self.sample_rate != audio.frame_rate:
                audio = audio.set_frame_rate(self.sample_rate)
            
            if self.channels != audio.channels:
                if self.channels == 1:
                    audio = audio.set_channels(1)  # 转为单声道
                elif self.channels == 2:
                    audio = audio.set_channels(2)  # 转为立体声
            
            # 导出为WAV格式
            audio.export(
                wav_file, 
                format="wav",
                parameters=["-acodec", "pcm_s16le"]  # 16位PCM编码
            )
            
            if show_info:
                file_size = os.path.getsize(wav_file) / (1024 * 1024)  # MB
                duration = len(audio) / 1000.0  # 秒
                print(f"完成: {os.path.basename(wav_file)} ({file_size:.1f}MB, {duration:.1f}s)")
            
            self.success_count += 1
            return True
            
        except Exception as e:
            if show_info:
                print(f"转换失败 {os.path.basename(mp3_file)}: {str(e)}")
            self.fail_count += 1
            return False
    
    def convertBatchFiles(self, input_dir, output_dir=None, recursive=False):
        """
        批量转换MP3文件
        
        Args:
            input_dir (str): 输入目录
            output_dir (str): 输出目录，如果为None则在原目录生成
            recursive (bool): 是否递归搜索子目录
        
        Returns:
            tuple: (成功数量, 失败数量)
        """
        # 查找MP3文件
        if recursive:
            pattern = os.path.join(input_dir, "**", "*.mp3")
            mp3_files = glob.glob(pattern, recursive=True)
        else:
            pattern = os.path.join(input_dir, "*.mp3")
            mp3_files = glob.glob(pattern)
        
        if not mp3_files:
            print(f"在目录 {input_dir} 中没有找到MP3文件")
            return 0, 0
        
        print(f"找到 {len(mp3_files)} 个MP3文件")
        
        # 创建输出目录
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 转换文件
        iterator = tqdm(mp3_files, desc="转换进度") if HAS_TQDM else mp3_files
        
        for mp3_file in iterator:
            if output_dir:
                # 保持相对路径结构
                rel_path = os.path.relpath(mp3_file, input_dir)
                wav_file = os.path.join(output_dir, os.path.splitext(rel_path)[0] + '.wav')
                # 确保输出目录存在
                os.makedirs(os.path.dirname(wav_file), exist_ok=True)
            else:
                wav_file = None
            
            self.convertSingleFile(mp3_file, wav_file, show_info=not HAS_TQDM)
        
        return self.success_count, self.fail_count
    
    def printSummary(self):
        """打印转换摘要"""
        total = self.success_count + self.fail_count
        if total > 0:
            print(f"\n转换摘要:")
            print(f"总文件数: {total}")
            print(f"成功: {self.success_count}")
            print(f"失败: {self.fail_count}")
            print(f"成功率: {self.success_count/total*100:.1f}%")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="MP3转WAV格式转换器")
    parser.add_argument("input", help="输入文件或目录路径")
    parser.add_argument("-o", "--output", help="输出文件或目录路径")
    parser.add_argument("-r", "--recursive", action="store_true", help="递归搜索子目录")
    parser.add_argument("--sample-rate", type=int, default=44100, help="采样率 (默认: 44100)")
    parser.add_argument("--channels", type=int, choices=[1, 2], default=2, help="声道数 (1=单声道, 2=立体声)")
    parser.add_argument("--bit-depth", type=int, choices=[16, 24, 32], default=16, help="位深度")
    
    args = parser.parse_args()
    
    # 创建转换器
    converter = Mp3ToWavConverter(
        sample_rate=args.sample_rate,
        channels=args.channels,
        bit_depth=args.bit_depth
    )
    
    print(f"音频参数: {args.sample_rate}Hz, {args.channels}声道, {args.bit_depth}位")
    
    # 判断是文件还是目录
    if os.path.isfile(args.input):
        # 单文件转换
        success = converter.convertSingleFile(args.input, args.output)
        if not success:
            sys.exit(1)
    elif os.path.isdir(args.input):
        # 批量转换
        success_count, fail_count = converter.convertBatchFiles(
            args.input, args.output, args.recursive
        )
        converter.printSummary()
        if fail_count > 0:
            sys.exit(1)
    else:
        print(f"错误：路径 {args.input} 不存在")
        sys.exit(1)
    
    print("转换完成！")

if __name__ == "__main__":
    main()
