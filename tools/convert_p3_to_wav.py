import os
import struct
import numpy as np
import opuslib
import wave


def load_sk_opus_file(input_file, inputOffset):
    # 打开 Opus 文件
    with open(input_file, 'rb') as f:
        opus_data = f.read()
    print(f'org length {len(opus_data)}')
    opus_data = opus_data[inputOffset:]
    print(f'new length {len(opus_data)} {str(opus_data[:16])}')

    # 解析 Opus 数据包
    packets = []
    offset = 0
    startTime = 0
    while offset < len(opus_data):
        # 解析协议格式 [1u type, 1u vad, 2u len, data]
        packet_type = opus_data[offset]
        vad = opus_data[offset + 1]
        packet_len = struct.unpack('>H', opus_data[offset + 2:offset + 4])[0]
        packet = opus_data[offset + 4:offset + 4 + packet_len]
        packets.append(packet)
        offset += 4 + packet_len
        startTime += 60
        # starTime单位为毫秒, 转为分:秒.毫秒文本
        startTime_str = str(startTime // 60000) + ':' + str((startTime % 60000) // 1000) + '.' + str((startTime % 1000))
        print(f'{startTime_str} {packet_type} {vad} {packet_len} {len(packet)} {offset}')
    return packets

#+++++++++++++++++++++++++++++++++++++++++++++
#｜             Oggs Tag('OggS')             |
#+++++++++++++++++++++++++++++++++++++++++++++
#|ver(1byte)|headerflag|      gp             |
#+++++++++++++++++++++++++++++++++++++++++++++
#|                   gp                      |
#+++++++++++++++++++++++++++++++++++++++++++++
#|                 gp  |    stream_serial    |
#+++++++++++++++++++++++++++++++++++++++++++++
#|     stream_serial   |    page_sequence    |
#+++++++++++++++++++++++++++++++++++++++++++++
#|     page_sequence   |       CRC           |
#+++++++++++++++++++++++++++++++++++++++++++++
#|         CRC         |size count|   size0  |
#+++++++++++++++++++++++++++++++++++++++++++++
#|   size1  |   size2  |   size3  |   size4  |
#+++++++++++++++++++++++++++++++++++++++++++++
#|.......
#headerflag:
#define OGG_NULL_HEADER_TYPE        0x00
#define OGG_CONTINUE_HEADER_TYPE    0x01
#define OGG_FIRST_HEADER_TYPE       0x02
#define OGG_LAST_HEADER_TYPE        0x04
def parse_ogg_header(data):
    # 解析固定部分 (共27字节)
    fixed_fmt = '<4sBBQIIIB'  # 小端字节序
    fixed_size = struct.calcsize(fixed_fmt)
    (capture, version, flags, granule, serial, seq, crc, segments) = \
        struct.unpack(fixed_fmt, data[:fixed_size])

    # 检查标识符是否合法
    if capture != b'OggS':
        raise ValueError("Invalid Ogg header capture pattern")

    if segments == 1:
        seg_table = [data[fixed_size]]
    else:
        # 解析动态段长度表 (每个段占1字节)
        seg_table_fmt = f'<{segments}B'
        seg_table = struct.unpack(seg_table_fmt, data[fixed_size:fixed_size + segments])
    # 计算总数据长度
    total_data_size = sum(seg_table)

    return {
        'capture': capture,
        'version': version,
        'flags': flags,
        'granule': granule,
        'serial': serial,
        'seq': seq,
        'crc': crc,
        'segments': segments,
        'seg_table': seg_table,
        'total_data_size': total_data_size
    }

def load_ogg_file(input_file):
   # 打开 Opus 文件
    with open(input_file, 'rb') as f:
        ogg_data = f.read()
    print(f'org length {len(ogg_data)}')

    # 解析 Opus 数据包
    opus_packets = []
    packets_info = []
    offset = 0
    while offset < len(ogg_data):
        header = parse_ogg_header(ogg_data[offset:offset + 255])
        header['start_pos'] = offset
        print(header)
        offset += 27
        offset += header['segments']
        if header['seq'] < 2:
            # 前两页为头部信息，跳过
            offset += header['total_data_size']
            continue
        packet_data = b''
        for i in range(header['segments']):
            packet_len = header['seg_table'][i]
            packet = ogg_data[offset:offset + packet_len]
            packet_data += packet
            if (packet_len != 255):                    
                opus_packets.append(packet_data)
                packets_info.append(f'{header["seq"]}-{i}-{header["segments"]} {packet_len} {offset}')
                packet_data = b''
            offset += packet_len
            # print(f'{header["seq"]}-{i} {packet_len} {offset}')
            
    print(f'new length {len(opus_packets)}')
    return opus_packets, packets_info

def decode_data(packet_list, sample_rate=16000, channels=1, packets_info = None):
    # 初始化 Opus 解码器
    decoder = opuslib.Decoder(sample_rate, channels)
    decoded_frames = []
    length_info = []
    try:
        # 解码 Opus 数据包
        i = 0
        for packet in packet_list:
            if packets_info is not None:
                print(f'decodeing {packets_info[i]} {packet[0:16]}')
            else:
                print(f'decodeing {packet[0:16]}')
            try:
                decoded_frame = decoder.decode(packet, frame_size=960)
                decoded_frames.append(decoded_frame)
            except Exception as e:
                print("Error decoding packet:", e)
            i += 1
            length_info.append(len(decoded_frame))
        print(f'decoded {len(decoded_frames)} frames {length_info}')
    except Exception as e:
        print(e)
    return decoded_frames

def save_to_wave(output_file, decoded_frames, sample_rate=16000, channels=1):
    # 将解码后的数据转换为 NumPy 数组
    decoded_audio = np.concatenate([np.frombuffer(frame, dtype=np.int16) for frame in decoded_frames], axis=0)
    # 保存为 WAV 文件
    with wave.open(output_file, 'wb') as wav_file:
        wav_file.setnchannels(channels)
        wav_file.setsampwidth(2)  # 16-bit PCM
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(decoded_audio.tobytes())
    print(f"Decoded Opus data saved to {output_file}")

def convert_opus_to_wav(input_file, output_file, inputOffset=0, sample_rate=16000, channels=1):
    packets = load_sk_opus_file(input_file, inputOffset)
    decoded_frames = decode_data(packets, sample_rate, channels)
    save_to_wave(output_file, decoded_frames, sample_rate, channels)

def convert_ogg_to_wav(input_file, output_file):
    opus_packets, packets_info = load_ogg_file(input_file)
    decoded_frames = decode_data(opus_packets, 16000, 1, packets_info[1:])
    save_to_wave(output_file, decoded_frames, 16000, 1)

#p3_dir_name = "p3_file"
#inputOffset = 0
p3_dir_name = "rx"
inputOffset = 0
fileNames = os.listdir(os.path.join("./", p3_dir_name))
for fileName in fileNames:
    name, extension = os.path.splitext(fileName)
    if 'txt' in extension:
        continue
    inputFile = os.path.join("./", p3_dir_name, fileName)
    outputFile = os.path.join("./", "data", name+".wav")
    print(f'convert {fileName}')
    if 'ogg' in extension:
        convert_ogg_to_wav(inputFile, outputFile)
        continue
    convert_opus_to_wav(inputFile, outputFile, inputOffset)
