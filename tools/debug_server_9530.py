import socket
import wave
import sys
import signal
import datetime
import struct

# 服务器配置
SERVER_IP = "0.0.0.0"  # 服务器监听所有IP地址
SERVER_PORT = 9530      # 服务器端口
BUFFER_SIZE = 1460      # 缓冲区大小
SAVE_FILE = "received_data.pcm"  # 保存数据的文件名
save_data = b''

def read_binary_to_txt(input_file, output_file):
    # 打开二进制文件进行读取
    with open(input_file, 'rb') as infile:
        # 打开文本文件进行写入
        with open(output_file, 'w') as outfile:
            # 每次读取4字节
            while True:
                chunk = infile.read(4 * 8)  # 一次读取32字节，即8个4字节的数据
                if not chunk:
                    break  # 如果读取到文件末尾，退出循环

                # 解析二进制数据为无符号整数
                data = struct.unpack('8I', chunk)  # 'I' 表示无符号4字节整数
                outfile.write(str(data[0] >> 16) + ' ' + str(data[0] & 0xFFFF) + ' ')
                # 将数据写入文本文件，每行8个数据
                outfile.write(' '.join(map(str, data[1:])) + '\n')

def save_wave(data):
    timeNow = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    SAVE_FILE = f"DBG_{timeNow}_DEV"
    if (len(data) > 0):
        with open(f'{SAVE_FILE}.bin', 'wb') as f:
            f.write(data)
            print('saved to file ' + SAVE_FILE)
            f.close()
        read_binary_to_txt(SAVE_FILE+".bin", f"{SAVE_FILE}.txt")

def signal_handler(sig, frame):
    if (len(save_data) > 0):
        save_wave(save_data)
        print('You pressed Ctrl+C!')
    sys.exit(0)

def tcp_server():
    global save_data
    exitFlag = False
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as server_socket:
        server_socket.bind((SERVER_IP, SERVER_PORT))
        server_socket.listen(5)
        server_socket.settimeout(10)
        print(f"Server listening on {SERVER_IP}:{SERVER_PORT}")
        while not exitFlag:
            try:
                client_socket, client_address = server_socket.accept()
            except socket.timeout:
                continue
            print(f"Connection from {client_address}")
            client_socket.settimeout(10)
            while not exitFlag:
                try:
                    data = client_socket.recv(BUFFER_SIZE)
                    if (data is None or len(data) == 0):
                        continue
                    #print(f"Received data: {len(data)}")
                    save_data = save_data + data
                except KeyboardInterrupt:
                    exitFlag = True
                    print("KeyboardInterrupt received, stopping server...")
                    break
                except Exception as e:
                    if (isinstance(e, socket.timeout)):
                        continue
                    else:
                        exitFlag = True
                        print(f"Error: {e}")
                        break
        # 将接收到的数据保存到文件
        client_socket.close()
        save_wave(save_data)
        server_socket.close()

if __name__ == "__main__":
    signal.signal(signal.SIGINT, signal_handler)
    tcp_server()