import os
import sys

def txt_to_c_array(input_file, output_file):
    try:
        # 打开输入文件并读取内容
        with open(input_file, 'rb') as f:
            file_content = f.read()

        # 获取文件名（不包含扩展名）
        file_name = os.path.splitext(os.path.basename(input_file))[0]

        # 生成 C 语言数组
        array_name = f"{file_name}HtmlData"
        array_length = len(file_content)
        array_content = ", ".join(f"0x{byte:02X}" for byte in file_content)

        # 生成头文件内容
        header_content = f"""#ifndef __{file_name.upper()}_H__
#define __{file_name.upper()}_H__

#include <stdint.h>

// Automatically generated from {input_file}
static const uint8_t {array_name}[{array_length}] = {{
    {array_content}
}};

#endif // __{file_name.upper()}_H__
"""

        # 写入输出文件
        with open(output_file, 'w') as f:
            f.write(header_content)

        print(f"Successfully generated {output_file} from {input_file}")

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python txt_to_c_array.py <input_file.txt> <output_file.h>")
        sys.exit(1)

    input_file = sys.argv[1]
    output_file = sys.argv[2]

    if not output_file.endswith('.h'):
        print("Error: Output file must be a .h file")
        sys.exit(1)

    txt_to_c_array(input_file, output_file)