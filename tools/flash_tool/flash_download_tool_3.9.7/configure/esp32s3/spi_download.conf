[EFUSE CHECK]
efuse_mode = 1
efuse_err_halt = 1

[MULTI_UI_CONFIG]
multi_col = 2

[DOWNLOAD PATH]
file_sel0 = 1
file_path0 = E:\Workspace\smartkid\build\bootloader\bootloader.bin
file_flag0 = False
file_offset0 = 0x0000
file_sel1 = 1
file_path1 = E:\Workspace\smartkid\build\partition_table\partition-table.bin
file_flag1 = False
file_offset1 = 0x8000
file_sel2 = 1
file_path2 = E:\Workspace\smartkid\build\smart_kid.bin
file_flag2 = False
file_offset2 = 0x100000
file_sel3 = 1
file_path3 = E:\Workspace\smartkid\build\smart_kid.bin
file_flag3 = False
file_offset3 = 0x300000
file_sel4 = 1
file_path4 = E:\Workspace\smartkid\build\srmodels\srmodels.bin
file_flag4 = False
file_offset4 = 0x500000
file_sel5 = 0
file_path5 = 
file_flag5 = False
file_offset5 = 
file_sel6 = 0
file_path6 = 
file_flag6 = False
file_offset6 = 
file_sel7 = 0
file_path7 = 
file_flag7 = False
file_offset7 = 
file_sel8 = 0
file_path8 = 
file_flag8 = False
file_offset8 = 
file_sel9 = 0
file_path9 = 
file_flag9 = False
file_offset9 = 
file_sel10 = 0
file_path10 = 
file_flag10 = False
file_offset10 = 
file_sel11 = 0
file_path11 = 
file_flag11 = False
file_offset11 = 
file_sel12 = 0
file_path12 = 
file_flag12 = False
file_offset12 = 
file_sel13 = 0
file_path13 = 
file_flag13 = False
file_offset13 = 
default_path = E:\Workspace\smartkid\build\srmodels

[FLASH_CRYSTAL]
spicfgdis = 1
spispeed = 0
spimode = 2

[DOWNLOAD]
erase_button_en = True
autostart1 = 0
com_port1 = COM6
baudrate1 = 0
checkmac1 = 1
new_status = 0
num_bytes = 0
non_volatile = False

[LOG_CHECK]
log_check_enable = False
log_check_baud = 115200
log_check_str = 1.0.0
log_check_delaytime = 3
log_check_timeout = 3
log_check_cmd_str = AT+GMR
log_check_enable_cmd = False

[MAC_SAVE]
mac_save_enable = False

[ESPTOOL_PARAM]
after = no_reset
before = default_reset
compress = True
flash_size = keep
no_stub = False
verify = True
flash_freq = keep
flash_mode = keep

