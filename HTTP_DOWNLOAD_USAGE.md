# HTTP下载模块使用说明

## 已实现的调用

我已经为您实现了HTTP下载模块的调用，集成到了现有的音乐播放状态机中。

## 修改的文件

### 1. `main/app/sm_music.c` - 音乐状态机
- **添加了HTTP下载功能**：在音乐开始时自动下载OPUS文件
- **资源管理**：在音乐停止时自动释放下载的数据
- **错误处理**：下载失败时回退到原来的服务器模式

### 2. `main/app/main.c` - 主程序
- **添加了测试函数**：`TestHttpDownload()` 用于测试HTTP下载功能
- **集成调用**：在WiFi连接后自动测试下载功能

## 使用方法

### 1. 设置测试URL

在 `main.c` 的 `TestHttpDownload()` 函数中，修改测试URL：

```c
const char *testUrl = "http://*************:8080/test.opus";  // 改为您的实际URL
```

在 `sm_music.c` 的 `SmMusicStart()` 函数中，修改音乐文件URL：

```c
const char *testUrl = "http://*************:8080/test.opus";  // 改为您的实际URL
```

### 2. 准备测试文件

使用您的 `wav_to_opus.py` 脚本生成测试文件：

```bash
# 生成OGG格式的OPUS文件
python wav_to_opus.py input.wav test.opus

# 启动简单的HTTP服务器
python -m http.server 8080
```

### 3. 运行测试

1. **编译并烧录**程序到ESP32
2. **连接WiFi**：确保ESP32能访问您的HTTP服务器
3. **查看日志**：程序会自动执行以下步骤：
   - 测试HTTP下载功能
   - 启动音乐状态（使用下载的文件）

## 日志输出示例

### 成功情况：
```
I SmartKid: Testing HTTP download...
I SkHttpDownloader: Starting download: http://*************:8080/test.opus
I SkHttpDownloader: File size: 1048576 bytes
I SkHttpDownloader: Created ringbuffer in PSRAM: 2097152 bytes
I SkHttpDownloader: Download progress: 104857/1048576 bytes (10%)
I SkHttpDownloader: Download progress: 209715/1048576 bytes (20%)
...
I SkHttpDownloader: Download completed: 1048576 bytes
I SmartKid: ✅ HTTP download success!
I SmartKid:    File size: 1048576 bytes
I SmartKid:    PSRAM address: 0x3f800000
I SmartKid:    Ringbuffer handle: 0x3ffb1234
I SmartKid:    First data chunk: 4096 bytes
I SmartKid:    First 4 bytes: 4F 67 67 53
I SkHttpDownloader: Download data freed from PSRAM
I SmartKid:    Resources freed
I SmMusic: Start music - downloading from HTTP
I SmMusic: HTTP download success: 1048576 bytes in PSRAM
I SmMusic: Music ready to play from downloaded data
```

### 失败情况：
```
I SmartKid: Testing HTTP download...
E SkHttpDownloader: Failed to open HTTP connection: ESP_ERR_HTTP_CONNECT
E SmartKid: ❌ HTTP download failed: 1
E SmartKid:    Error: Network error or file too large
I SmMusic: Start music - downloading from HTTP
E SmMusic: HTTP download failed: 1, fallback to server mode
I SmMusic: Sent music start command to server
```

## 工作流程

1. **程序启动** → WiFi连接 → **测试下载**
2. **语音命令"音乐"** → 触发 `SPEECH_CMD_EVENT_MUSIC`
3. **状态机切换** → `STATE_MUSIC` → 调用 `SmMusicStart()`
4. **HTTP下载** → 下载OPUS文件到PSRAM环形缓冲区
5. **播放准备** → 设置音频回调，准备播放

## 下一步开发

当前实现了下载功能，接下来需要：

1. **OGG解析器**：解析下载的OGG/OPUS文件
2. **OPUS解码**：将OPUS数据解码为PCM
3. **音频播放**：将PCM数据送入播放器

## 配置参数

可以在 `sk_http_downloader.c` 中调整：

```c
#define HTTP_BUFFER_SIZE        4096                    // HTTP读取缓冲区
#define HTTP_TIMEOUT_MS         30000                   // 30秒超时
#define RINGBUF_SIZE            (2 * 1024 * 1024)      // 2MB环形缓冲区
```

## 错误排查

1. **网络连接**：确保ESP32能ping通HTTP服务器
2. **文件大小**：确保文件小于2MB
3. **PSRAM**：确保ESP32配置了PSRAM
4. **URL格式**：确保URL格式正确，包含http://前缀

## 内存使用

- **PSRAM分配**：每次下载分配约2MB PSRAM
- **自动清理**：状态机切换时自动释放内存
- **错误处理**：任何错误都会正确清理已分配的内存

现在您可以编译运行，测试HTTP下载功能了！
