menu "mbedTLS"

    choice MBEDTLS_MEM_ALLOC_MODE
        prompt "Memory allocation strategy"
        default MBEDTLS_INTERNAL_MEM_ALLOC
        help
            Allocation strategy for mbedTLS, essentially provides ability to
            allocate all required dynamic allocations from,

            - Internal DRAM memory only
            - External SPIRAM memory only
            - Either internal or external memory based on default malloc()
              behavior in ESP-IDF
            - Custom allocation mode, by overwriting calloc()/free() using
              mbedtls_platform_set_calloc_free() function
            - Internal IRAM memory wherever applicable else internal DRAM

            Recommended mode here is always internal (*), since that is most preferred
            from security perspective. But if application requirement does not
            allow sufficient free internal memory then alternate mode can be
            selected.

            (*) In case of ESP32-S2/ESP32-S3, hardware allows encryption of external
            SPIRAM contents provided hardware flash encryption feature is enabled.
            In that case, using external SPIRAM allocation strategy is also safe choice
            from security perspective.

        config MBEDTLS_INTERNAL_MEM_ALLOC
            bool "Internal memory"

        config MBEDTLS_EXTERNAL_MEM_ALLOC
            bool "External SPIRAM"
            depends on SPIRAM_USE_CAPS_ALLOC || SPIRAM_USE_MALLOC

        config MBEDTLS_DEFAULT_MEM_ALLOC
            bool "Default alloc mode"

        config MBEDTLS_CUSTOM_MEM_ALLOC
            bool "Custom alloc mode"

        config MBEDTLS_IRAM_8BIT_MEM_ALLOC
            bool "Internal IRAM"
            depends on ESP32_IRAM_AS_8BIT_ACCESSIBLE_MEMORY
            help
                Allows to use IRAM memory region as 8bit accessible region.

                TLS input and output buffers will be allocated in IRAM section which is 32bit aligned
                memory. Every unaligned (8bit or 16bit) access will result in an exception
                and incur penalty of certain clock cycles per unaligned read/write.

    endchoice #MBEDTLS_MEM_ALLOC_MODE

    config MBEDTLS_SSL_MAX_CONTENT_LEN
        int "TLS maximum message content length"
        default 16384
        range 512 16384
        depends on !MBEDTLS_ASYMMETRIC_CONTENT_LEN
        help
            Maximum TLS message length (in bytes) supported by mbedTLS.

            16384 is the default and this value is required to comply
            fully with TLS standards.

            However you can set a lower value in order to save RAM. This
            is safe if the other end of the connection supports Maximum
            Fragment Length Negotiation Extension (max_fragment_length,
            see RFC6066) or you know for certain that it will never send a
            message longer than a certain number of bytes.

            If the value is set too low, symptoms are a failed TLS
            handshake or a return value of MBEDTLS_ERR_SSL_INVALID_RECORD
            (-0x7200).

    config MBEDTLS_ASYMMETRIC_CONTENT_LEN
        bool "Asymmetric in/out fragment length"
        default y
        help
            If enabled, this option allows customizing TLS in/out fragment length
            in asymmetric way. Please note that enabling this with default values
            saves 12KB of dynamic memory per TLS connection.

    config MBEDTLS_SSL_IN_CONTENT_LEN
        int "TLS maximum incoming fragment length"
        default 16384
        range 512 16384
        depends on MBEDTLS_ASYMMETRIC_CONTENT_LEN
        help
            This defines maximum incoming fragment length, overriding default
            maximum content length (MBEDTLS_SSL_MAX_CONTENT_LEN).

    config MBEDTLS_SSL_OUT_CONTENT_LEN
        int "TLS maximum outgoing fragment length"
        default 4096
        range 512 16384
        depends on MBEDTLS_ASYMMETRIC_CONTENT_LEN
        help
            This defines maximum outgoing fragment length, overriding default
            maximum content length (MBEDTLS_SSL_MAX_CONTENT_LEN).

    config MBEDTLS_DYNAMIC_BUFFER
        bool "Using dynamic TX/RX buffer"
        default n
        select MBEDTLS_ASYMMETRIC_CONTENT_LEN
        # Dynamic buffer feature is not supported with DTLS
        depends on !IDF_TARGET_LINUX && !MBEDTLS_SSL_PROTO_DTLS && !MBEDTLS_SSL_VARIABLE_BUFFER_LENGTH
        help
            Using dynamic TX/RX buffer. After enabling this option, mbedTLS will
            allocate TX buffer when need to send data and then free it if all data
            is sent, allocate RX buffer when need to receive data and then free it
            when all data is used or read by upper layer.

            By default, when SSL is initialized, mbedTLS also allocate TX and
            RX buffer with the default value of "MBEDTLS_SSL_OUT_CONTENT_LEN" or
            "MBEDTLS_SSL_IN_CONTENT_LEN", so to save more heap, users can set
            the options to be an appropriate value.

    config MBEDTLS_DYNAMIC_FREE_CONFIG_DATA
        bool "Free private key and DHM data after its usage"
        default n
        depends on MBEDTLS_DYNAMIC_BUFFER
        help
            Free private key and DHM data after its usage in handshake process.

            The option will decrease heap cost when handshake, but also lead to problem:

            Because all certificate, private key and DHM data are freed so users should register
            certificate and private key to ssl config object again.

    config MBEDTLS_DYNAMIC_FREE_CA_CERT
        bool "Free SSL CA certificate after its usage"
        default y
        depends on MBEDTLS_DYNAMIC_FREE_CONFIG_DATA
        help
            Free CA certificate after its usage in the handshake process.
            This option will decrease the heap footprint for the TLS handshake, but may lead to a problem:
            If the respective ssl object needs to perform the TLS handshake again,
            the CA certificate should once again be registered to the ssl object.

    config MBEDTLS_DEBUG
        bool "Enable mbedTLS debugging"
        default n
        help
            Enable mbedTLS debugging functions at compile time.

            If this option is enabled, you can include
            "mbedtls/esp_debug.h" and call mbedtls_esp_enable_debug_log()
            at runtime in order to enable mbedTLS debug output via the ESP
            log mechanism.

    choice MBEDTLS_DEBUG_LEVEL
        bool "Set mbedTLS debugging level"
        depends on MBEDTLS_DEBUG
        default MBEDTLS_DEBUG_LEVEL_VERBOSE
        help
            Set mbedTLS debugging level

        config MBEDTLS_DEBUG_LEVEL_WARN
            bool "Warning"
        config MBEDTLS_DEBUG_LEVEL_INFO
            bool "Info"
        config MBEDTLS_DEBUG_LEVEL_DEBUG
            bool "Debug"
        config MBEDTLS_DEBUG_LEVEL_VERBOSE
            bool "Verbose"
    endchoice

    config MBEDTLS_DEBUG_LEVEL
        int
        default 1 if MBEDTLS_DEBUG_LEVEL_WARN
        default 2 if MBEDTLS_DEBUG_LEVEL_INFO
        default 3 if MBEDTLS_DEBUG_LEVEL_DEBUG
        default 4 if MBEDTLS_DEBUG_LEVEL_VERBOSE

    menu "mbedTLS v3.x related"
        config MBEDTLS_SSL_PROTO_TLS1_3
            bool "Support TLS 1.3 protocol"
            depends on MBEDTLS_TLS_ENABLED && MBEDTLS_SSL_KEEP_PEER_CERTIFICATE
            select MBEDTLS_CLIENT_SSL_SESSION_TICKETS if MBEDTLS_DYNAMIC_BUFFER
            select MBEDTLS_HKDF_C
            default n

        menu "TLS 1.3 related configurations"
            depends on MBEDTLS_SSL_PROTO_TLS1_3

            config MBEDTLS_SSL_TLS1_3_COMPATIBILITY_MODE
                bool "TLS 1.3 middlebox compatibility mode"
                default y

            config MBEDTLS_SSL_TLS1_3_KEXM_PSK
                bool "TLS 1.3 PSK key exchange mode"
                default y

            config MBEDTLS_SSL_TLS1_3_KEXM_EPHEMERAL
                bool "TLS 1.3 ephemeral key exchange mode"
                default y

            config MBEDTLS_SSL_TLS1_3_KEXM_PSK_EPHEMERAL
                bool "TLS 1.3 PSK ephemeral key exchange mode"
                default y

        endmenu

        config MBEDTLS_SSL_VARIABLE_BUFFER_LENGTH
            bool "Variable SSL buffer length"
            default n
            help
                This enables the SSL buffer to be resized automatically
                based on the negotiated maximum fragment length in each direction.

        config MBEDTLS_ECDH_LEGACY_CONTEXT
            bool "Use a backward compatible ECDH context (Experimental)"
            default n
            depends on MBEDTLS_ECDH_C && MBEDTLS_ECP_RESTARTABLE
            help
                Use the legacy ECDH context format.
                Define this option only if you enable MBEDTLS_ECP_RESTARTABLE or if you
                want to access ECDH context fields directly.

        config MBEDTLS_X509_TRUSTED_CERT_CALLBACK
            bool "Enable trusted certificate callbacks"
            default n
            help
                Enables users to configure the set of trusted certificates
                through a callback instead of a linked list.

                See mbedTLS documentation for required API and more details.

        config MBEDTLS_SSL_CONTEXT_SERIALIZATION
            bool "Enable serialization of the TLS context structures"
            default n
            depends on MBEDTLS_GCM_C || MBEDTLS_CCM_C || MBEDTLS_CHACHAPOLY_C
            help
                Enable serialization of the TLS context structures
                This is a local optimization in handling a single, potentially long-lived connection.

                See mbedTLS documentation for required API and more details.
                Disabling this option will save some code size.

        config MBEDTLS_SSL_KEEP_PEER_CERTIFICATE
            bool "Keep peer certificate after handshake completion"
            default y
            help
                Keep the peer's certificate after completion of the handshake.
                Disabling this option will save about 4kB of heap and some code size.

                See mbedTLS documentation for required API and more details.

        config MBEDTLS_PKCS7_C
            bool "Enable PKCS number 7"
            default y
            depends on MBEDTLS_X509_CRL_PARSE_C
            help
                Enable PKCS number 7 core for using PKCS number 7-formatted signatures.

        config MBEDTLS_SSL_CID_PADDING_GRANULARITY
            int "Record plaintext padding"
            default 16
            range 0 32
            depends on MBEDTLS_SSL_PROTO_TLS1_3 || MBEDTLS_SSL_DTLS_CONNECTION_ID
            help
                Controls the use of record plaintext padding in TLS 1.3 and
                when using the Connection ID extension in DTLS 1.2.

                The padding will always be chosen so that the length of the
                padded plaintext is a multiple of the value of this option.

                Notes:
                    A value of 1 means that no padding will be used for outgoing records.
                    On systems lacking division instructions, a power of two should be preferred.

        menu "DTLS-based configurations"
            depends on MBEDTLS_SSL_PROTO_DTLS

            config MBEDTLS_SSL_DTLS_CONNECTION_ID
                bool "Support for the DTLS Connection ID extension"
                default n
                help
                    Enable support for the DTLS Connection ID extension which allows to
                    identify DTLS connections across changes in the underlying transport.

            config MBEDTLS_SSL_CID_IN_LEN_MAX
                int "Maximum length of CIDs used for incoming DTLS messages"
                default 32
                range 0 32
                depends on MBEDTLS_SSL_DTLS_CONNECTION_ID
                help
                    Maximum length of CIDs used for incoming DTLS messages

            config MBEDTLS_SSL_CID_OUT_LEN_MAX
                int "Maximum length of CIDs used for outgoing DTLS messages"
                default 32
                range 0 32
                depends on MBEDTLS_SSL_DTLS_CONNECTION_ID
                help
                    Maximum length of CIDs used for outgoing DTLS messages

            config MBEDTLS_SSL_DTLS_SRTP
                bool "Enable support for negotiation of DTLS-SRTP (RFC 5764)"
                default n
                help
                    Enable support for negotiation of DTLS-SRTP (RFC 5764) through the use_srtp extension.

                    See mbedTLS documentation for required API and more details.
                    Disabling this option will save some code size.

        endmenu

    endmenu

    menu "Certificate Bundle"

        config MBEDTLS_CERTIFICATE_BUNDLE
            bool "Enable trusted root certificate bundle"
            default y
            help
                Enable support for large number of default root certificates

                When enabled this option allows user to store default as well
                as customer specific root certificates in compressed format rather
                than storing full certificate. For the root certificates the public key and the subject name
                will be stored.

        choice MBEDTLS_DEFAULT_CERTIFICATE_BUNDLE
            bool "Default certificate bundle options"
            depends on MBEDTLS_CERTIFICATE_BUNDLE
            default MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_FULL

            config MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_FULL
                bool "Use the full default certificate bundle"
            config MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_CMN
                bool "Use only the most common certificates from the default bundles"
                help
                    Use only the most common certificates from the default bundles, reducing the size with 50%,
                    while still having around 99% coverage.
            config MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_NONE
                bool "Do not use the default certificate bundle"
        endchoice

        config MBEDTLS_CUSTOM_CERTIFICATE_BUNDLE
            depends on MBEDTLS_CERTIFICATE_BUNDLE
            default n
            bool "Add custom certificates to the default bundle"
        config MBEDTLS_CUSTOM_CERTIFICATE_BUNDLE_PATH
            depends on MBEDTLS_CUSTOM_CERTIFICATE_BUNDLE
            string "Custom certificate bundle path"
            help
                Name of the custom certificate directory or file. This path is evaluated
                relative to the project root directory.

        config MBEDTLS_CERTIFICATE_BUNDLE_DEPRECATED_LIST
            bool "Add deprecated root certificates"
            depends on MBEDTLS_CERTIFICATE_BUNDLE && !MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_NONE
            help
                Include the deprecated list of root certificates in the bundle.
                This list gets updated when a certificate is removed from the Mozilla's
                NSS root certificate store. This config can be enabled if you would like
                to ensure that none of the certificates that were deployed in the product
                are affected because of the update to bundle. In turn, enabling this
                config keeps expired, retracted certificates in the bundle and it may
                pose a security risk.

                - Deprecated cert list may grow based based on sync with upstream bundle
                - Deprecated certs would be be removed in ESP-IDF (next) major release

        config MBEDTLS_CERTIFICATE_BUNDLE_MAX_CERTS
            int "Maximum no of certificates allowed in certificate bundle"
            default 200
            depends on MBEDTLS_CERTIFICATE_BUNDLE

    endmenu

    config MBEDTLS_ECP_RESTARTABLE
        bool "Enable mbedTLS ecp restartable"
        select MBEDTLS_ECDH_LEGACY_CONTEXT
        depends on MBEDTLS_ECP_C
        default n
        help
            Enable "non-blocking" ECC operations that can return early and be resumed.

    config MBEDTLS_CMAC_C
        bool "Enable CMAC mode for block ciphers"
        default n
        depends on MBEDTLS_AES_C || MBEDTLS_DES_C
        help
            Enable the CMAC (Cipher-based Message Authentication Code) mode for
            block ciphers.

    config MBEDTLS_HARDWARE_AES
        bool "Enable hardware AES acceleration"
        default y
        depends on !SPIRAM_CACHE_WORKAROUND_STRATEGY_DUPLDST && SOC_AES_SUPPORTED
        help
            Enable hardware accelerated AES encryption & decryption.

            Note that if the ESP32 CPU is running at 240MHz, hardware AES does not
            offer any speed boost over software AES.

    config MBEDTLS_AES_USE_INTERRUPT
        bool "Use interrupt for long AES operations"
        depends on !IDF_TARGET_ESP32 && MBEDTLS_HARDWARE_AES
        default y
        help
            Use an interrupt to coordinate long AES operations.

            This allows other code to run on the CPU while an AES operation is pending.
            Otherwise the CPU busy-waits.

    config MBEDTLS_AES_INTERRUPT_LEVEL
        int "AES hardware interrupt level"
        default 0
        depends on MBEDTLS_AES_USE_INTERRUPT
        range 0 3
        help
            This config helps to set the interrupt priority level for the AES peripheral.
            Value 0 (default) means that there is no preference regarding the interrupt
            priority level and any level from 1 to 3 can be selected (based on the availability).
            Note: Higher value indicates high interrupt priority.

    config MBEDTLS_AES_USE_PSEUDO_ROUND_FUNC
        bool "Enable AES hardware's pseudo round function"
        default n
        depends on SOC_AES_SUPPORT_PSEUDO_ROUND_FUNCTION
        help
            Enables the pseudo round function of the AES peripheral.
            Enabling this would impact the performance of the AES operations.
            For more info regarding the performance impact, please checkout the pseudo round function section of the
            security guide.

    choice MBEDTLS_AES_USE_PSEUDO_ROUND_FUNC_STRENGTH
        prompt "Strength of the pseudo rounds function"
        depends on MBEDTLS_AES_USE_PSEUDO_ROUND_FUNC
        default MBEDTLS_AES_USE_PSEUDO_ROUND_FUNC_STRENGTH_LOW
        help
            The strength of the pseudo rounds functions can be configured to low, medium and high.
            You can configure the strength of the pseudo rounds functions according to your use cases,
            for example, increasing the strength would provide higher security but would slow down the
            hardware AES encryption/decryption operations.

        config MBEDTLS_AES_USE_PSEUDO_ROUND_FUNC_STRENGTH_LOW
            bool "Low"

        config MBEDTLS_AES_USE_PSEUDO_ROUND_FUNC_STRENGTH_MEDIUM
            bool "Medium"

        config MBEDTLS_AES_USE_PSEUDO_ROUND_FUNC_STRENGTH_HIGH
            bool "High"
    endchoice

    config MBEDTLS_AES_USE_PSEUDO_ROUND_FUNC_STRENGTH
        int
        default 1 if MBEDTLS_AES_USE_PSEUDO_ROUND_FUNC_STRENGTH_LOW
        default 2 if MBEDTLS_AES_USE_PSEUDO_ROUND_FUNC_STRENGTH_MEDIUM
        default 3 if MBEDTLS_AES_USE_PSEUDO_ROUND_FUNC_STRENGTH_HIGH

    config MBEDTLS_HARDWARE_GCM
        bool "Enable partially hardware accelerated GCM"
        depends on SOC_AES_SUPPORT_GCM && MBEDTLS_HARDWARE_AES
        default y
        help
            Enable partially hardware accelerated GCM. GHASH calculation is still done
            in software.

            If MBEDTLS_HARDWARE_GCM is disabled and MBEDTLS_HARDWARE_AES is enabled then
            mbedTLS will still use the hardware accelerated AES block operation, but
            on a single block at a time.

    config MBEDTLS_GCM_SUPPORT_NON_AES_CIPHER
        bool "Enable support for non-AES ciphers in GCM operation"
        depends on MBEDTLS_HARDWARE_AES
        default y
        help
            Enable this config to support fallback to software definitions for a non-AES
            cipher GCM operation as we support hardware acceleration only for AES cipher.
            Some of the non-AES ciphers used in a GCM operation are DES, ARIA, CAMELLIA,
            CHACHA20, BLOWFISH.

            If this config is disabled, performing a non-AES cipher GCM operation with
            the config MBEDTLS_HARDWARE_AES enabled will result in calculation of an
            AES-GCM operation instead for the given input values and thus could lead
            to failure in certificate validation which would ultimately lead to a SSL
            handshake failure.

            This config being by-default enabled leads to an increase in binary size
            footprint of ~2.5KB.
            In case you are sure that your use case (for example, client and server
            configurations in case of a TLS handshake) would not involve any GCM
            operations using a non-AES cipher, you can safely disable this config,
            leading to reduction in binary size footprint.

    config MBEDTLS_HARDWARE_MPI
        bool "Enable hardware MPI (bignum) acceleration"
        default y
        depends on !SPIRAM_CACHE_WORKAROUND_STRATEGY_DUPLDST && SOC_MPI_SUPPORTED
        help
            Enable hardware accelerated multiple precision integer operations.

            Hardware accelerated multiplication, modulo multiplication,
            and modular exponentiation for up to SOC_RSA_MAX_BIT_LEN bit results.

            These operations are used by RSA.

    config MBEDTLS_LARGE_KEY_SOFTWARE_MPI
        bool "Fallback to software implementation for larger MPI values"
        depends on MBEDTLS_HARDWARE_MPI
        default y if SOC_RSA_MAX_BIT_LEN <= 3072 # HW max 3072 bits
        default n
        help
            Fallback to software implementation for RSA key lengths
            larger than SOC_RSA_MAX_BIT_LEN. If this is not active
            then the ESP will be unable to process keys greater
            than SOC_RSA_MAX_BIT_LEN.

    config MBEDTLS_MPI_USE_INTERRUPT
        bool "Use interrupt for MPI exp-mod operations"
        depends on !IDF_TARGET_ESP32 && MBEDTLS_HARDWARE_MPI
        default y
        help
            Use an interrupt to coordinate long MPI operations.

            This allows other code to run on the CPU while an MPI operation is pending.
            Otherwise the CPU busy-waits.

    config MBEDTLS_MPI_INTERRUPT_LEVEL
        int "MPI hardware interrupt level"
        default 0
        depends on MBEDTLS_MPI_USE_INTERRUPT
        range 0 3
        help
            This config helps to set the interrupt priority level for the MPI peripheral.
            Value 0 (default) means that there is no preference regarding the interrupt
            priority level and any level from 1 to 3 can be selected (based on the availability).
            Note: Higher value indicates high interrupt priority.

    config MBEDTLS_HARDWARE_SHA
        bool "Enable hardware SHA acceleration"
        default y
        depends on !SPIRAM_CACHE_WORKAROUND_STRATEGY_DUPLDST && SOC_SHA_SUPPORTED
        help
            Enable hardware accelerated SHA1, SHA256, SHA384 & SHA512 in mbedTLS.

            Due to a hardware limitation, on the ESP32 hardware acceleration is only
            guaranteed if SHA digests are calculated one at a time. If more
            than one SHA digest is calculated at the same time, one will
            be calculated fully in hardware and the rest will be calculated
            (at least partially calculated) in software. This happens automatically.

            SHA hardware acceleration is faster than software in some situations but
            slower in others. You should benchmark to find the best setting for you.

    config MBEDTLS_HARDWARE_ECC
        bool "Enable hardware ECC acceleration"
        default y
        depends on SOC_ECC_SUPPORTED
        help
            Enable hardware accelerated ECC point multiplication and point verification for points
            on curve SECP192R1 and SECP256R1 in mbedTLS

    config MBEDTLS_ECC_OTHER_CURVES_SOFT_FALLBACK
        bool "Fallback to software implementation for curves not supported in hardware"
        depends on MBEDTLS_HARDWARE_ECC
        default y
        help
            Fallback to software implementation of ECC point multiplication and point verification
            for curves not supported in hardware.

    config MBEDTLS_ROM_MD5
        bool "Use MD5 implementation in ROM"
        default y
        help
            Use ROM MD5 in mbedTLS.

    config MBEDTLS_HARDWARE_ECDSA_SIGN
        bool "Enable ECDSA signing using on-chip ECDSA peripheral"
        default n
        depends on SOC_ECDSA_SUPPORTED
        help
            Enable hardware accelerated ECDSA peripheral to sign data
            on curve SECP192R1 and SECP256R1 in mbedTLS.

            Note that for signing, the private key has to be burnt in an efuse key block
            with key purpose set to ECDSA_KEY.
            If no key is burnt, it will report an error

            The key should be burnt in little endian format. espefuse.py utility handles it internally
            but care needs to be taken while burning using esp_efuse APIs

    config MBEDTLS_TEE_SEC_STG_ECDSA_SIGN
        bool "Enable ECDSA signing using TEE secure storage"
        default y
        depends on SECURE_ENABLE_TEE

    menu "Enable Software Countermeasure for ECDSA signing using on-chip ECDSA peripheral"
        depends on MBEDTLS_HARDWARE_ECDSA_SIGN
        depends on IDF_TARGET_ESP32H2
        config MBEDTLS_HARDWARE_ECDSA_SIGN_MASKING_CM
            bool "Mask original ECDSA sign operation under dummy sign operations"
            select HAL_ECDSA_GEN_SIG_CM
            default y
            help
                The ECDSA peripheral before ESP32-H2 v1.2 does not offer constant time ECDSA sign operation.
                This time can be observed through power profiling of the device,
                making the ECDSA private key vulnerable to side-channel timing attacks.
                This countermeasure masks the real ECDSA sign operation
                under dummy sign operations to add randomness in the generated power signature.
                It is highly recommended to also enable Secure Boot for the device in addition to this countermeasure
                so that only trusted software can execute on the device.
                This countermeasure can be safely disabled for ESP32-H2 v1.2 and above.

        config MBEDTLS_HARDWARE_ECDSA_SIGN_CONSTANT_TIME_CM
            bool "Make ECDSA signature operation pseudo constant time for software"
            default y
            help
                This option adds a delay after the actual ECDSA signature operation
                so that the entire operation appears to be constant time for the software.
                This fix helps in protecting the device only in case of remote timing attack on the ECDSA private key.
                For e.g., When an interface is exposed by the device to perform ECDSA signature
                of an arbitrary message.
                The signature time would appear to be constant to the external entity after enabling
                this option.
                This countermeasure can be safely disabled for ESP32-H2 v1.2 and above.

    endmenu

    config MBEDTLS_HARDWARE_ECDSA_VERIFY
        bool "Enable ECDSA signature verification using on-chip ECDSA peripheral"
        default y
        depends on SOC_ECDSA_SUPPORTED
        help
            Enable hardware accelerated ECDSA peripheral to verify signature
            on curve SECP192R1 and SECP256R1 in mbedTLS.

    config MBEDTLS_ATCA_HW_ECDSA_SIGN
        bool "Enable hardware ECDSA sign acceleration when using ATECC608A"
        default n
        help
            This option enables hardware acceleration for ECDSA sign function, only
            when using ATECC608A cryptoauth chip.

    config MBEDTLS_ATCA_HW_ECDSA_VERIFY
        bool "Enable hardware ECDSA verify acceleration when using ATECC608A"
        default n
        help
            This option enables hardware acceleration for ECDSA sign function, only
            when using ATECC608A cryptoauth chip.

    config MBEDTLS_HAVE_TIME
        bool "Enable mbedtls time support"
        depends on !ESP_TIME_FUNCS_USE_NONE
        default y
        help
            Enable use of time.h functions (time() and gmtime()) by mbedTLS.

            This option doesn't require the system time to be correct, but enables
            functionality that requires relative timekeeping - for example periodic
            expiry of TLS session tickets or session cache entries.

            Disabling this option will save some firmware size, particularly if
            the rest of the firmware doesn't call any standard timekeeping
            functions.

    config MBEDTLS_PLATFORM_TIME_ALT
        bool "Enable mbedtls time support: platform-specific"
        depends on MBEDTLS_HAVE_TIME
        default n
        help
            Enabling this config will provide users with a function
            "mbedtls_platform_set_time()" that allows to set an alternative
            time function pointer.

    config MBEDTLS_HAVE_TIME_DATE
        bool "Enable mbedtls certificate expiry check"
        depends on MBEDTLS_HAVE_TIME
        default n
        help
            Enables X.509 certificate expiry checks in mbedTLS.

            If this option is disabled (default) then X.509 certificate
            "valid from" and "valid to" timestamp fields are ignored.

            If this option is enabled, these fields are compared with the
            current system date and time. The time is retrieved using the
            standard time() and gmtime() functions. If the certificate is not
            valid for the current system time then verification will fail with
            code MBEDTLS_X509_BADCERT_FUTURE or MBEDTLS_X509_BADCERT_EXPIRED.

            Enabling this option requires adding functionality in the firmware
            to set the system clock to a valid timestamp before using TLS. The
            recommended way to do this is via ESP-IDF's SNTP functionality, but
            any method can be used.

            In the case where only a small number of certificates are trusted by
            the device, please carefully consider the tradeoffs of enabling this
            option. There may be undesired consequences, for example if all
            trusted certificates expire while the device is offline and a TLS
            connection is required to update. Or if an issue with the SNTP
            server means that the system time is invalid for an extended period
            after a reset.

    config MBEDTLS_ECDSA_DETERMINISTIC
        bool "Enable deterministic ECDSA"
        default y
        help
            Standard ECDSA is "fragile" in the sense that lack of entropy when signing
            may result in a compromise of the long-term signing key.

    config MBEDTLS_SHA1_C
        bool "Enable the SHA-1 cryptographic hash algorithm"
        default y
        help
            Enabling MBEDTLS_SHA1_C adds support for SHA-1.
            SHA-1 is considered a weak message digest and its use constitutes
            a security risk.
            Disabling this configuration option could impact TLS 1.2 / Wi-Fi Enterprise compatibility
            with certain older certificates that rely on SHA-1 for digital signatures.
            Before proceeding, ensure that all your certificates are using stronger hash algorithms,
            such as SHA-256 (part of the SHA-2 family).
            If you're using older certificates or if you're unsure about the impact on your product,
            please consider testing the changes in a controlled environment for individual features
            like OTA updates, cloud connectivity, secure local control, etc.

    config MBEDTLS_SHA512_C
        bool "Enable the SHA-384 and SHA-512 cryptographic hash algorithms"
        default y
        help
            Enable MBEDTLS_SHA512_C adds support for SHA-384 and SHA-512.

    config MBEDTLS_SHA3_C
        bool "Enable the SHA3 cryptographic hash algorithm"
        default n
        help
            Enabling MBEDTLS_SHA3_C adds support for SHA3.
            Enabling this configuration option increases the flash footprint
            by almost 4KB.

    choice MBEDTLS_TLS_MODE
        bool "TLS Protocol Role"
        default MBEDTLS_TLS_SERVER_AND_CLIENT
        help
            mbedTLS can be compiled with protocol support for the TLS
            server, TLS client, or both server and client.

            Reducing the number of TLS roles supported saves code size.

        config MBEDTLS_TLS_SERVER_AND_CLIENT
            bool "Server & Client"
            select MBEDTLS_TLS_SERVER
            select MBEDTLS_TLS_CLIENT
        config MBEDTLS_TLS_SERVER_ONLY
            bool "Server"
            select MBEDTLS_TLS_SERVER
        config MBEDTLS_TLS_CLIENT_ONLY
            bool "Client"
            select MBEDTLS_TLS_CLIENT
        config MBEDTLS_TLS_DISABLED
            bool "None"

    endchoice

    config MBEDTLS_TLS_SERVER
        bool
        select MBEDTLS_TLS_ENABLED
    config MBEDTLS_TLS_CLIENT
        bool
        select MBEDTLS_TLS_ENABLED
    config MBEDTLS_TLS_ENABLED
        bool

    menu "TLS Key Exchange Methods"
        depends on MBEDTLS_TLS_ENABLED

        config MBEDTLS_PSK_MODES
            bool "Enable pre-shared-key ciphersuites"
            default n
            help
                Enable to show configuration for different types of pre-shared-key TLS authentatication methods.

                Leaving this options disabled will save code size if they are not used.

        config MBEDTLS_KEY_EXCHANGE_PSK
            bool "Enable PSK based ciphersuite modes"
            depends on MBEDTLS_PSK_MODES
            default n
            help
                Enable to support symmetric key PSK (pre-shared-key) TLS key exchange modes.

        config MBEDTLS_KEY_EXCHANGE_DHE_PSK
            bool "Enable DHE-PSK based ciphersuite modes"
            depends on MBEDTLS_PSK_MODES && MBEDTLS_DHM_C
            default y
            help
                Enable to support Diffie-Hellman PSK (pre-shared-key) TLS authentication modes.

        config MBEDTLS_KEY_EXCHANGE_ECDHE_PSK
            bool "Enable ECDHE-PSK based ciphersuite modes"
            depends on MBEDTLS_PSK_MODES && MBEDTLS_ECDH_C
            default y
            help
                Enable to support Elliptic-Curve-Diffie-Hellman PSK (pre-shared-key) TLS authentication modes.

        config MBEDTLS_KEY_EXCHANGE_RSA_PSK
            bool "Enable RSA-PSK based ciphersuite modes"
            depends on MBEDTLS_PSK_MODES
            default y
            help
                Enable to support RSA PSK (pre-shared-key) TLS authentication modes.

        config MBEDTLS_KEY_EXCHANGE_RSA
            bool "Enable RSA-only based ciphersuite modes"
            default y
            help
                Enable to support ciphersuites with prefix TLS-RSA-WITH-

        config MBEDTLS_KEY_EXCHANGE_DHE_RSA
            bool "Enable DHE-RSA based ciphersuite modes"
            default y
            depends on MBEDTLS_DHM_C
            help
                Enable to support ciphersuites with prefix TLS-DHE-RSA-WITH-

        config MBEDTLS_KEY_EXCHANGE_ELLIPTIC_CURVE
            bool "Support Elliptic Curve based ciphersuites"
            depends on MBEDTLS_ECP_C
            default y
            help
                Enable to show Elliptic Curve based ciphersuite mode options.

                Disabling all Elliptic Curve ciphersuites saves code size and
                can give slightly faster TLS handshakes, provided the server supports
                RSA-only ciphersuite modes.

        config MBEDTLS_KEY_EXCHANGE_ECDHE_RSA
            bool "Enable ECDHE-RSA based ciphersuite modes"
            depends on MBEDTLS_KEY_EXCHANGE_ELLIPTIC_CURVE && MBEDTLS_ECDH_C
            default y
            help
                Enable to support ciphersuites with prefix TLS-ECDHE-RSA-WITH-

        config MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA
            bool "Enable ECDHE-ECDSA based ciphersuite modes"
            depends on MBEDTLS_KEY_EXCHANGE_ELLIPTIC_CURVE && MBEDTLS_ECDH_C && MBEDTLS_ECDSA_C
            default y
            help
                Enable to support ciphersuites with prefix TLS-ECDHE-ECDSA-WITH-

        config MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA
            bool "Enable ECDH-ECDSA based ciphersuite modes"
            depends on MBEDTLS_KEY_EXCHANGE_ELLIPTIC_CURVE && MBEDTLS_ECDH_C && MBEDTLS_ECDSA_C
            default y
            help
                Enable to support ciphersuites with prefix TLS-ECDH-ECDSA-WITH-

        config MBEDTLS_KEY_EXCHANGE_ECDH_RSA
            bool "Enable ECDH-RSA based ciphersuite modes"
            depends on MBEDTLS_KEY_EXCHANGE_ELLIPTIC_CURVE && MBEDTLS_ECDH_C
            default y
            help
                Enable to support ciphersuites with prefix TLS-ECDH-RSA-WITH-

        config MBEDTLS_KEY_EXCHANGE_ECJPAKE
            bool "Enable ECJPAKE based ciphersuite modes"
            depends on MBEDTLS_ECJPAKE_C && MBEDTLS_ECP_DP_SECP256R1_ENABLED
            default n
            help
                Enable to support ciphersuites with prefix TLS-ECJPAKE-WITH-

    endmenu # TLS key exchange modes

    config MBEDTLS_SSL_RENEGOTIATION
        bool "Support TLS renegotiation"
        depends on MBEDTLS_TLS_ENABLED && MBEDTLS_SSL_PROTO_TLS1_2
        default y
        help
            The two main uses of renegotiation are (1) refresh keys on long-lived
            connections and (2) client authentication after the initial handshake.
            If you don't need renegotiation, disabling it will save code size and
            reduce the possibility of abuse/vulnerability.

    config MBEDTLS_SSL_PROTO_TLS1_2
        bool "Support TLS 1.2 protocol"
        depends on MBEDTLS_TLS_ENABLED
        default y

    config MBEDTLS_SSL_PROTO_GMTSSL1_1
        bool "Support GM/T SSL 1.1 protocol"
        depends on MBEDTLS_TLS_ENABLED
        default n
        help
            Provisions for GM/T SSL 1.1 support

    config MBEDTLS_SSL_PROTO_DTLS
        bool "Support DTLS protocol (all versions)"
        default n
        depends on MBEDTLS_SSL_PROTO_TLS1_2
        help
            Requires TLS 1.2 to be enabled for DTLS 1.2

    config MBEDTLS_SSL_ALPN
        bool "Support ALPN (Application Layer Protocol Negotiation)"
        depends on MBEDTLS_TLS_ENABLED
        default y
        help
            Disabling this option will save some code size if it is not needed.

    config MBEDTLS_CLIENT_SSL_SESSION_TICKETS
        bool "TLS: Client Support for RFC 5077 SSL session tickets"
        default y
        depends on MBEDTLS_TLS_ENABLED
        help
            Client support for RFC 5077 session tickets. See mbedTLS documentation for more details.
            Disabling this option will save some code size.

    config MBEDTLS_SERVER_SSL_SESSION_TICKETS
        bool "TLS: Server Support for RFC 5077 SSL session tickets"
        default y
        depends on MBEDTLS_TLS_ENABLED && (MBEDTLS_GCM_C || MBEDTLS_CCM_C || MBEDTLS_CHACHAPOLY_C)
        help
            Server support for RFC 5077 session tickets. See mbedTLS documentation for more details.
            Disabling this option will save some code size.

    menu "Symmetric Ciphers"

        config MBEDTLS_AES_C
            bool "AES block cipher"
            default y

        config MBEDTLS_CAMELLIA_C
            bool "Camellia block cipher"
            default n

        config MBEDTLS_DES_C
            bool "DES block cipher (legacy, insecure)"
            default n
            help
                Enables the DES block cipher to support 3DES-based TLS ciphersuites.

                3DES is vulnerable to the Sweet32 attack and should only be enabled
                if absolutely necessary.

        config MBEDTLS_BLOWFISH_C
            bool "Blowfish block cipher (read help)"
            default n
            help
                    Enables the Blowfish block cipher (not used for TLS sessions.)

                    The Blowfish cipher is not used for mbedTLS TLS sessions but can be
                    used for other purposes. Read up on the limitations of Blowfish (including
                    Sweet32) before enabling.

        config MBEDTLS_XTEA_C
            bool "XTEA block cipher"
            default n
            help
                    Enables the XTEA block cipher.


        config MBEDTLS_CCM_C
            bool "CCM (Counter with CBC-MAC) block cipher modes"
            default y
            depends on MBEDTLS_AES_C || MBEDTLS_CAMELLIA_C
            help
                    Enable Counter with CBC-MAC (CCM) modes for AES and/or Camellia ciphers.

                    Disabling this option saves some code size.

        config MBEDTLS_GCM_C
            bool "GCM (Galois/Counter) block cipher modes"
            default y
            depends on MBEDTLS_AES_C || MBEDTLS_CAMELLIA_C
            help
                    Enable Galois/Counter Mode for AES and/or Camellia ciphers.

                    This option is generally faster than CCM.

        config MBEDTLS_NIST_KW_C
            bool "NIST key wrapping (KW) and KW padding (KWP)"
            default n
            depends on MBEDTLS_AES_C
            help
                    Enable NIST key wrapping and key wrapping padding.

    endmenu # Symmetric Ciphers

    config MBEDTLS_RIPEMD160_C
        bool "Enable RIPEMD-160 hash algorithm"
        default n
        help
            Enable the RIPEMD-160 hash algorithm.

    menu "Certificates"

        config MBEDTLS_PEM_PARSE_C
            bool "Read & Parse PEM formatted certificates"
            default y
            help
                Enable decoding/parsing of PEM formatted certificates.

                If your certificates are all in the simpler DER format, disabling
                this option will save some code size.

        config MBEDTLS_PEM_WRITE_C
            bool "Write PEM formatted certificates"
            default y
            help
                Enable writing of PEM formatted certificates.

                If writing certificate data only in DER format, disabling this
                option will save some code size.

        config MBEDTLS_X509_CRL_PARSE_C
            bool "X.509 CRL parsing"
            default y
            help
                Support for parsing X.509 Certificate Revocation Lists.

        config MBEDTLS_X509_CSR_PARSE_C
            bool "X.509 CSR parsing"
            default y
            help
                Support for parsing X.509 Certificate Signing Requests

    endmenu # Certificates

    menuconfig MBEDTLS_ECP_C
        bool  "Elliptic Curve Ciphers"
        default y

    config MBEDTLS_PK_PARSE_EC_EXTENDED
        bool "Enhance support for reading EC keys"
        default y
        depends on MBEDTLS_ECP_C
        help
            Enhance support for reading EC keys using variants of SEC1 not allowed by
            RFC 5915 and RFC 5480.

    config MBEDTLS_PK_PARSE_EC_COMPRESSED
        bool "Enable the support for parsing public keys of type Short Weierstrass"
        default y
        depends on MBEDTLS_ECP_C
        help
            Enable the support for parsing public keys of type Short Weierstrass
            (MBEDTLS_ECP_DP_SECP_XXX and MBEDTLS_ECP_DP_BP_XXX) which are using the
            compressed point format. This parsing is done through ECP module's functions.

    config MBEDTLS_DHM_C
        bool "Diffie-Hellman-Merkle key exchange (DHM)"
        default n
        help
            Enable DHM. Needed to use DHE-xxx TLS ciphersuites.

            Note that the security of Diffie-Hellman key exchanges depends on
            a suitable prime being used for the exchange. Please see detailed
            warning text about this in file `mbedtls/dhm.h` file.

    config MBEDTLS_ECDH_C
        bool "Elliptic Curve Diffie-Hellman (ECDH)"
        depends on MBEDTLS_ECP_C
        default y
        help
            Enable ECDH. Needed to use ECDHE-xxx TLS ciphersuites.

    config MBEDTLS_ECDSA_C
        bool "Elliptic Curve DSA"
        depends on MBEDTLS_ECDH_C
        default y
        help
            Enable ECDSA. Needed to use ECDSA-xxx TLS ciphersuites.

    config MBEDTLS_ECJPAKE_C
        bool "Elliptic curve J-PAKE"
        depends on MBEDTLS_ECP_C
        default n
        help
            Enable ECJPAKE. Needed to use ECJPAKE-xxx TLS ciphersuites.

    config MBEDTLS_ECP_DP_SECP192R1_ENABLED
        bool "Enable SECP192R1 curve"
        depends on MBEDTLS_ECP_C
        default y if !(MBEDTLS_ATCA_HW_ECDSA_SIGN || MBEDTLS_ATCA_HW_ECDSA_VERIFY)
        help
            Enable support for SECP192R1 Elliptic Curve.

    config MBEDTLS_ECP_DP_SECP224R1_ENABLED
        bool "Enable SECP224R1 curve"
        depends on MBEDTLS_ECP_C
        default y if !(MBEDTLS_ATCA_HW_ECDSA_SIGN || MBEDTLS_ATCA_HW_ECDSA_VERIFY)
        help
            Enable support for SECP224R1 Elliptic Curve.

    config MBEDTLS_ECP_DP_SECP256R1_ENABLED
        bool "Enable SECP256R1 curve"
        depends on MBEDTLS_ECP_C
        default y
        help
            Enable support for SECP256R1 Elliptic Curve.

    config MBEDTLS_ECP_DP_SECP384R1_ENABLED
        bool "Enable SECP384R1 curve"
        depends on MBEDTLS_ECP_C
        default y if !(MBEDTLS_ATCA_HW_ECDSA_SIGN || MBEDTLS_ATCA_HW_ECDSA_VERIFY)
        help
            Enable support for SECP384R1 Elliptic Curve.

    config MBEDTLS_ECP_DP_SECP521R1_ENABLED
        bool "Enable SECP521R1 curve"
        depends on MBEDTLS_ECP_C
        default y if !(MBEDTLS_ATCA_HW_ECDSA_SIGN || MBEDTLS_ATCA_HW_ECDSA_VERIFY)
        help
            Enable support for SECP521R1 Elliptic Curve.

    config MBEDTLS_ECP_DP_SECP192K1_ENABLED
        bool "Enable SECP192K1 curve"
        depends on MBEDTLS_ECP_C
        default y if !(MBEDTLS_ATCA_HW_ECDSA_SIGN || MBEDTLS_ATCA_HW_ECDSA_VERIFY)
        help
            Enable support for SECP192K1 Elliptic Curve.

    config MBEDTLS_ECP_DP_SECP224K1_ENABLED
        bool "Enable SECP224K1 curve"
        depends on MBEDTLS_ECP_C
        default y if !(MBEDTLS_ATCA_HW_ECDSA_SIGN || MBEDTLS_ATCA_HW_ECDSA_VERIFY)
        help
            Enable support for SECP224K1 Elliptic Curve.

    config MBEDTLS_ECP_DP_SECP256K1_ENABLED
        bool "Enable SECP256K1 curve"
        depends on MBEDTLS_ECP_C
        default y if !(MBEDTLS_ATCA_HW_ECDSA_SIGN || MBEDTLS_ATCA_HW_ECDSA_VERIFY)
        help
            Enable support for SECP256K1 Elliptic Curve.

    config MBEDTLS_ECP_DP_BP256R1_ENABLED
        bool "Enable BP256R1 curve"
        depends on MBEDTLS_ECP_C
        default y if !(MBEDTLS_ATCA_HW_ECDSA_SIGN || MBEDTLS_ATCA_HW_ECDSA_VERIFY)
        help
            support for DP Elliptic Curve.

    config MBEDTLS_ECP_DP_BP384R1_ENABLED
        bool "Enable BP384R1 curve"
        depends on MBEDTLS_ECP_C
        default y if !(MBEDTLS_ATCA_HW_ECDSA_SIGN || MBEDTLS_ATCA_HW_ECDSA_VERIFY)
        help
            support for DP Elliptic Curve.

    config MBEDTLS_ECP_DP_BP512R1_ENABLED
        bool "Enable BP512R1 curve"
        depends on MBEDTLS_ECP_C
        default y if !(MBEDTLS_ATCA_HW_ECDSA_SIGN || MBEDTLS_ATCA_HW_ECDSA_VERIFY)
        help
            support for DP Elliptic Curve.

    config MBEDTLS_ECP_DP_CURVE25519_ENABLED
        bool "Enable CURVE25519 curve"
        depends on MBEDTLS_ECP_C
        default y if !(MBEDTLS_ATCA_HW_ECDSA_SIGN || MBEDTLS_ATCA_HW_ECDSA_VERIFY)
        help
            Enable support for CURVE25519 Elliptic Curve.

    config MBEDTLS_ECP_NIST_OPTIM
        bool "NIST 'modulo p' optimisations"
        depends on MBEDTLS_ECP_C
        default y
        help
            NIST 'modulo p' optimisations increase Elliptic Curve operation performance.

            Disabling this option saves some code size.

    config MBEDTLS_ECP_FIXED_POINT_OPTIM
        bool "Enable fixed-point multiplication optimisations"
        depends on MBEDTLS_ECP_C
        default n
        help
            This configuration option enables optimizations to speedup (about 3 ~ 4 times) the ECP
            fixed point multiplication using pre-computed tables in the flash memory.
            Enabling this configuration option increases the flash footprint
            (about 29KB if all Elliptic Curve selected) in the application binary.

            # end of Elliptic Curve options

    config MBEDTLS_POLY1305_C
        bool "Poly1305 MAC algorithm"
        default n
        help
            Enable support for Poly1305 MAC algorithm.

    config MBEDTLS_CHACHA20_C
        bool "Chacha20 stream cipher"
        default n
        help
            Enable support for Chacha20 stream cipher.

    config MBEDTLS_CHACHAPOLY_C
        bool "ChaCha20-Poly1305 AEAD algorithm"
        default n
        depends on MBEDTLS_CHACHA20_C && MBEDTLS_POLY1305_C
        help
            Enable support for ChaCha20-Poly1305 AEAD algorithm.

    config MBEDTLS_HKDF_C
        bool "HKDF algorithm (RFC 5869)"
        default n
        help
            Enable support for the Hashed Message Authentication Code
            (HMAC)-based key derivation function (HKDF).

    config MBEDTLS_THREADING_C
        bool "Enable the threading abstraction layer"
        default n
        help
            If you do intend to use contexts between threads, you will need to enable
            this layer to prevent race conditions.

    config MBEDTLS_THREADING_ALT
        bool "Enable threading alternate implementation"
        depends on MBEDTLS_THREADING_C
        default y
        help
            Enable threading alt to allow your own alternate threading implementation.

    config MBEDTLS_THREADING_PTHREAD
        bool "Enable threading pthread implementation"
        depends on MBEDTLS_THREADING_C
        default n
        help
            Enable the pthread wrapper layer for the threading layer.

    config MBEDTLS_ERROR_STRINGS
        bool "Enable error code to error string conversion"
        default y
        help
            Enables mbedtls_strerror() for converting error codes to error strings.
            Disabling this config can save some code/rodata size as the error
            string conversion implementation is replaced with an empty stub.

    config MBEDTLS_USE_CRYPTO_ROM_IMPL_BOOTLOADER
        bool "Use ROM implementation of the crypto algorithm in the bootloader"
        depends on ESP_ROM_HAS_MBEDTLS_CRYPTO_LIB
        default "n"
        select MBEDTLS_AES_C
        help
            Enable this flag to use mbedtls crypto algorithm from ROM instead of ESP-IDF
            in case of a bootloader build.
            Similar to the MBEDTLS_USE_CRYPTO_ROM_IMPL config but enables usage of the
            mbedtls crypto algorithm from ROM for the bootloader build.

    config MBEDTLS_USE_CRYPTO_ROM_IMPL
        bool "Use ROM implementation of the crypto algorithm"
        depends on ESP_ROM_HAS_MBEDTLS_CRYPTO_LIB
        default "n"
        select MBEDTLS_SHA512_C
        select MBEDTLS_AES_C
        select MBEDTLS_CCM_C
        select MBEDTLS_CMAC_C
        select MBEDTLS_ROM_MD5
        select MBEDTLS_HARDWARE_SHA
        select MBEDTLS_ECP_RESTARTABLE
        select MBEDTLS_THREADING_C
        help
            Enable this flag to use mbedtls crypto algorithm from ROM instead of ESP-IDF.

            This configuration option saves flash footprint in the application binary.
            Note that the version of mbedtls crypto algorithm library in ROM(ECO1~ECO3) is v2.16.12,
            and the version of mbedtls crypto algorithm library in ROM(ECO4) is v3.6.0.
            We have done the security analysis of the mbedtls revision in ROM (ECO1~ECO4)
            and ensured that affected symbols have been patched (removed). If in the future
            mbedtls revisions there are security issues that also affects the version in
            ROM (ECO1~ECO4) then we shall patch the relevant symbols. This would increase
            the flash footprint and hence care must be taken to keep some reserved space
            for the application binary in flash layout.

    config MBEDTLS_FS_IO
        bool "Enable functions that use the filesystem"
        default y
        depends on (VFS_SUPPORT_IO && VFS_SUPPORT_DIR) || IDF_TARGET_LINUX
        help
            This option enables functions in mbedTLS that use the filesystem.
            It uses the default filesystem support for the target,
            which is added through vfs component for ESP32 based targets or by
            the host system when the target is Linux.

    config MBEDTLS_ALLOW_WEAK_CERTIFICATE_VERIFICATION
        bool "Allow weak certificate verification"
        default n
        help
            This options allows weak certificate verification by skipping the hostname verification.
            It is not recommended to use this option.

endmenu  # mbedTLS
