## Description

Please write a few sentences describing the overall goals of the pull request's commits.



## PR checklist

Please tick as appropriate and edit the reasons (e.g.: "backport: not needed because this is a new feature")

- [ ] **changelog** provided, or not required
- [ ] **3.6 backport** done, or not required
- [ ] **2.28 backport** done, or not required
- [ ] **tests** provided, or not required



## Notes for the submitter

Please refer to the [contributing guidelines](https://github.com/Mbed-TLS/mbedtls/blob/development/CONTRIBUTING.md), especially the
checklist for PR contributors.

Help make review efficient:
* Multiple simple commits
  - please structure your PR into a series of small commits, each of which does one thing
* Avoid force-push
  - please do not force-push to update your PR - just add new commit(s)
* See our [Guidelines for Contributors](https://mbed-tls.readthedocs.io/en/latest/reviews/review-for-contributors/) for more details about the review process.
