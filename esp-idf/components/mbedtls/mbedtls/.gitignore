# Random seed file created by test scripts and sample programs
seedfile
# MBEDTLS_PSA_INJECT_ENTROPY seed file created by the test framework
00000000ffffff52.psa_its
# Log files created by all.sh to reduce the logs in case a component runs
# successfully
quiet-make.*

# CMake build artifacts:
CMakeCache.txt
CMakeFiles
CTestTestfile.cmake
cmake_install.cmake
Testing
# CMake generates *.dir/ folders for in-tree builds (used by MSVC projects), ignore all of those:
*.dir/
# MSVC files generated by CMake:
/*.sln
/*.vcxproj
/*.filters

# Test coverage build artifacts:
Coverage
*.gcno
*.gcda
coverage-summary.txt

# generated by scripts/memory.sh
massif-*

# Eclipse project files
.cproject
.project
/.settings

# Unix-like build artifacts:
*.o
*.s

# MSVC build artifacts:
*.exe
*.pdb
*.ilk
*.lib

# Python build artifacts:
*.pyc

# CMake generates *.dir/ folders for in-tree builds (used by MSVC projects), ignore all of those:
*.dir/

# Microsoft CMake extension for Visual Studio Code generates a build directory by default
/build/

# Generated documentation:
/apidoc

# PSA Crypto compliance test repo, cloned by test_psa_compliance.py
/psa-arch-tests

# Editor navigation files:
/GPATH
/GRTAGS
/GSYMS
/GTAGS
/TAGS
/cscope*.out
/tags

# clangd compilation database
compile_commands.json
# clangd index files
/.cache/clangd/index/

# VScode folder to store local debug files and configurations
.vscode
