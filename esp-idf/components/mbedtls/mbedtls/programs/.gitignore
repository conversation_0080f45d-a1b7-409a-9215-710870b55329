# Ignore makefiles generated by <PERSON><PERSON><PERSON>, but not the makefile that's checked in.
*/Makefile
!fuzz/Makefile

*.sln
*.vcxproj

aes/crypt_and_hash
cipher/cipher_aead_demo
hash/generic_sum
hash/hello
hash/md_hmac_demo
hash/md5sum
hash/sha1sum
hash/sha2sum
pkey/dh_client
pkey/dh_genprime
pkey/dh_server
pkey/ecdh_curve25519
pkey/ecdsa
pkey/gen_key
pkey/key_app
pkey/key_app_writer
pkey/mpi_demo
pkey/pk_decrypt
pkey/pk_encrypt
pkey/pk_sign
pkey/pk_verify
pkey/rsa_decrypt
pkey/rsa_encrypt
pkey/rsa_genkey
pkey/rsa_sign
pkey/rsa_sign_pss
pkey/rsa_verify
pkey/rsa_verify_pss
psa/aead_demo
psa/crypto_examples
psa/hmac_demo
psa/key_ladder_demo
psa/psa_constant_names
psa/psa_hash
random/gen_entropy
random/gen_random_ctr_drbg
ssl/dtls_client
ssl/dtls_server
ssl/mini_client
ssl/ssl_client1
ssl/ssl_client2
ssl/ssl_context_info
ssl/ssl_fork_server
ssl/ssl_mail_client
ssl/ssl_pthread_server
ssl/ssl_server
ssl/ssl_server2
test/benchmark
test/cpp_dummy_build
test/cpp_dummy_build.cpp
test/dlopen
test/ecp-bench
test/metatest
test/query_compile_time_config
test/query_included_headers
test/selftest
test/ssl_cert_test
test/udp_proxy
test/zeroize
util/pem2der
util/strerror
x509/cert_app
x509/cert_req
x509/cert_write
x509/crl_app
x509/load_roots
x509/req_app

####START_COMMENTED_GENERATED_FILES###
## Generated source files
#/psa/psa_constant_names_generated.c
#/test/query_config.c
#
## Generated data files
#pkey/keyfile.key
####END_COMMENTED_GENERATED_FILES###
