*.sln
*.vcxproj

*.log
/test_suite*
/data_files/mpi_write
/data_files/hmac_drbg_seed
/data_files/ctr_drbg_seed
/data_files/entropy_seed

/include/alt-extra/psa/crypto_platform_alt.h
/include/alt-extra/psa/crypto_struct_alt.h
/include/test/instrument_record_status.h

/src/libmbed*

/libtestdriver1/*

####START_COMMENTED_GENERATED_FILES###
## Generated source files
#/opt-testcases/handshake-generated.sh
#/opt-testcases/tls13-compat.sh
#/suites/*.generated.data
#/suites/test_suite_config.mbedtls_boolean.data
#/suites/test_suite_config.psa_boolean.data
#/suites/test_suite_psa_crypto_storage_format.v[0-9]*.data
#/suites/test_suite_psa_crypto_storage_format.current.data
#/src/test_keys.h
#/src/test_certs.h
####END_COMMENTED_GENERATED_FILES###
