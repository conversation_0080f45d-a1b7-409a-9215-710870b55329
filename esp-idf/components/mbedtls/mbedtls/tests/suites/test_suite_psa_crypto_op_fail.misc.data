# Most operation failure test cases are automatically generated in
# test_suite_psa_crypto_op_fail.generated.data. The manually written
# test cases in this file are only intended to help with debugging the
# test code.

PSA hash: invalid algorithm
hash_fail:PSA_ALG_ECDSA_ANY:PSA_ERROR_INVALID_ARGUMENT

PSA sign RSA_PSS(SHA_256): incompatible key type
depends_on:PSA_WANT_ALG_RSA_PSS:PSA_WANT_ALG_SHA_256:PSA_WANT_KEY_TYPE_AES
sign_fail:PSA_KEY_TYPE_AES:"48657265006973206b6579a064617461":PSA_ALG_RSA_PSS(PSA_ALG_SHA_256):0:PSA_ERROR_INVALID_ARGUMENT

PSA sign RSA_PSS(SHA_256): RSA_PSS not enabled, key pair
depends_on:!PSA_WANT_ALG_RSA_PSS:PSA_WANT_ALG_SHA_256:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_BASIC:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_IMPORT
sign_fail:PSA_KEY_TYPE_RSA_KEY_PAIR:"3082025e02010002818100af057d396ee84fb75fdbb5c2b13c7fe5a654aa8aa2470b541ee1feb0b12d25c79711531249e1129628042dbbb6c120d1443524ef4c0e6e1d8956eeb2077af12349ddeee54483bc06c2c61948cd02b202e796aebd94d3a7cbf859c2c1819c324cb82b9cd34ede263a2abffe4733f077869e8660f7d6834da53d690ef7985f6bc3020301000102818100874bf0ffc2f2a71d14671ddd0171c954d7fdbf50281e4f6d99ea0e1ebcf82faa58e7b595ffb293d1abe17f110b37c48cc0f36c37e84d876621d327f64bbe08457d3ec4098ba2fa0a319fba411c2841ed7be83196a8cdf9daa5d00694bc335fc4c32217fe0488bce9cb7202e59468b1ead119000477db2ca797fac19eda3f58c1024100e2ab760841bb9d30a81d222de1eb7381d82214407f1b975cbbfe4e1a9467fd98adbd78f607836ca5be1928b9d160d97fd45c12d6b52e2c9871a174c66b488113024100c5ab27602159ae7d6f20c3c2ee851e46dc112e689e28d5fcbbf990a99ef8a90b8bb44fd36467e7fc1789ceb663abda338652c3c73f111774902e840565927091024100b6cdbd354f7df579a63b48b3643e353b84898777b48b15f94e0bfc0567a6ae5911d57ad6409cf7647bf96264e9bd87eb95e263b7110b9a1f9f94acced0fafa4d024071195eec37e8d257decfc672b07ae639f10cbb9b0c739d0c809968d644a94e3fd6ed9287077a14583f379058f76a8aecd43c62dc8c0f41766650d725275ac4a1024100bb32d133edc2e048d463388b7be9cb4be29f4b6250be603e70e3647501c97ddde20a4e71be95fd5e71784e25aca4baf25be5738aae59bbfe1c997781447a2b24":PSA_ALG_RSA_PSS(PSA_ALG_SHA_256):0:PSA_ERROR_NOT_SUPPORTED

# There is a special case with ECDSA: deterministic and randomized ECDSA are
# different signature algorithms that can be enabled independently, but
# the verification algorithms are the same. Mbed TLS supports verification
# of either variant when either variant is enabled. (It would also be correct
# to reject the not-supported algorithm, but it would require a few more lines
# of code.) In the automatically generated test cases, we avoid this difficulty
# by making the not-supported test cases require neither variant to be
# enabled. Here, test the signature operation when one variant is supported
# but not the other. Testing the positive cases for the verification
# operation is the job of test_suite_psa_crypto.
#
# We only test with one curve and one hash, because we know from a gray-box
# approach that the curve and hash don't matter here.
PSA sign DETERMINISTIC_ECDSA(SHA_256): !DETERMINISTIC_ECDSA but ECDSA with ECC_KEY_PAIR(SECP_R1)
depends_on:!PSA_WANT_ALG_DETERMINISTIC_ECDSA:PSA_WANT_ALG_ECDSA:PSA_WANT_ALG_SHA_256:PSA_WANT_ECC_SECP_R1_192:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_IMPORT
sign_fail:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECP_R1):"d83b57a59c51358d9c8bbb898aff507f44dd14cf16917190":PSA_ALG_DETERMINISTIC_ECDSA(PSA_ALG_SHA_256):1:PSA_ERROR_NOT_SUPPORTED

PSA sign DETERMINISTIC_ECDSA(SHA_256): !ECDSA but DETERMINISTIC_ECDSA with ECC_KEY_PAIR(SECP_R1)
depends_on:PSA_WANT_ALG_DETERMINISTIC_ECDSA:!PSA_WANT_ALG_ECDSA:PSA_WANT_ALG_SHA_256:PSA_WANT_ECC_SECP_R1_192:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_IMPORT
sign_fail:PSA_KEY_TYPE_ECC_KEY_PAIR(PSA_ECC_FAMILY_SECP_R1):"d83b57a59c51358d9c8bbb898aff507f44dd14cf16917190":PSA_ALG_ECDSA(PSA_ALG_SHA_256):1:PSA_ERROR_NOT_SUPPORTED
