AES - legacy only
depends_on:MBEDTLS_BLOCK_CIPHER_AES_VIA_LEGACY:!MBEDTLS_BLOCK_CIPHER_AES_VIA_PSA
block_cipher_psa_dynamic_dispatch:MBEDTLS_CIPHER_ID_AES:0:MBEDTLS_BLOCK_CIPHER_ENGINE_LEGACY

AES - driver only
depends_on:!MBEDTLS_BLOCK_CIPHER_AES_VIA_LEGACY:MBEDTLS_BLOCK_CIPHER_AES_VIA_PSA
block_cipher_psa_dynamic_dispatch:MBEDTLS_CIPHER_ID_AES:MBEDTLS_ERR_CIPHER_BAD_INPUT_DATA:MBEDTLS_BLOCK_CIPHER_ENGINE_PSA

AES - legacy + driver
depends_on:MBEDTLS_BLOCK_CIPHER_AES_VIA_LEGACY:MBEDTLS_BLOCK_CIPHER_AES_VIA_PSA
block_cipher_psa_dynamic_dispatch:MBEDTLS_CIPHER_ID_AES:0:MBEDTLS_BLOCK_CIPHER_ENGINE_PSA

ARIA - legacy only
depends_on:MBEDTLS_BLOCK_CIPHER_ARIA_VIA_LEGACY:!MBEDTLS_BLOCK_CIPHER_ARIA_VIA_PSA
block_cipher_psa_dynamic_dispatch:MBEDTLS_CIPHER_ID_ARIA:0:MBEDTLS_BLOCK_CIPHER_ENGINE_LEGACY

ARIA - driver only
depends_on:!MBEDTLS_BLOCK_CIPHER_ARIA_VIA_LEGACY:MBEDTLS_BLOCK_CIPHER_ARIA_VIA_PSA
block_cipher_psa_dynamic_dispatch:MBEDTLS_CIPHER_ID_ARIA:MBEDTLS_ERR_CIPHER_BAD_INPUT_DATA:MBEDTLS_BLOCK_CIPHER_ENGINE_PSA

ARIA - legacy + driver
depends_on:MBEDTLS_BLOCK_CIPHER_ARIA_VIA_LEGACY:MBEDTLS_BLOCK_CIPHER_ARIA_VIA_PSA
block_cipher_psa_dynamic_dispatch:MBEDTLS_CIPHER_ID_ARIA:0:MBEDTLS_BLOCK_CIPHER_ENGINE_PSA

Camellia - legacy only
depends_on:MBEDTLS_BLOCK_CIPHER_CAMELLIA_VIA_LEGACY:!MBEDTLS_BLOCK_CIPHER_CAMELLIA_VIA_PSA
block_cipher_psa_dynamic_dispatch:MBEDTLS_CIPHER_ID_CAMELLIA:0:MBEDTLS_BLOCK_CIPHER_ENGINE_LEGACY

Camellia - driver only
depends_on:!MBEDTLS_BLOCK_CIPHER_CAMELLIA_VIA_LEGACY:MBEDTLS_BLOCK_CIPHER_CAMELLIA_VIA_PSA
block_cipher_psa_dynamic_dispatch:MBEDTLS_CIPHER_ID_CAMELLIA:MBEDTLS_ERR_CIPHER_BAD_INPUT_DATA:MBEDTLS_BLOCK_CIPHER_ENGINE_PSA

Camellia - legacy + driver
depends_on:MBEDTLS_BLOCK_CIPHER_CAMELLIA_VIA_LEGACY:MBEDTLS_BLOCK_CIPHER_CAMELLIA_VIA_PSA
block_cipher_psa_dynamic_dispatch:MBEDTLS_CIPHER_ID_CAMELLIA:0:MBEDTLS_BLOCK_CIPHER_ENGINE_PSA
