ECDSA primitive hash zero #1
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecdsa_prim_zero:MBEDTLS_ECP_DP_SECP192R1

ECDSA primitive hash zero #2
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecdsa_prim_zero:MBEDTLS_ECP_DP_SECP224R1

ECDSA primitive hash zero #3
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecdsa_prim_zero:MBEDTLS_ECP_DP_SECP256R1

ECDSA primitive hash zero #4
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecdsa_prim_zero:MBEDTLS_ECP_DP_SECP384R1

ECDSA primitive hash zero #5
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecdsa_prim_zero:MBEDTLS_ECP_DP_SECP521R1

ECDSA primitive random #1
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecdsa_prim_random:MBEDTLS_ECP_DP_SECP192R1

ECDSA primitive random #2
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecdsa_prim_random:MBEDTLS_ECP_DP_SECP224R1

ECDSA primitive random #3
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecdsa_prim_random:MBEDTLS_ECP_DP_SECP256R1

ECDSA primitive random #4
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecdsa_prim_random:MBEDTLS_ECP_DP_SECP384R1

ECDSA primitive random #5
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecdsa_prim_random:MBEDTLS_ECP_DP_SECP521R1

ECDSA primitive rfc 4754 p256
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecdsa_prim_test_vectors:MBEDTLS_ECP_DP_SECP256R1:"DC51D3866A15BACDE33D96F992FCA99DA7E6EF0934E7097559C27F1614C88A7F":"2442A5CC0ECD015FA3CA31DC8E2BBC70BF42D60CBCA20085E0822CB04235E970":"6FC98BD7E50211A4A27102FA3549DF79EBCB4BF246B80945CDDFE7D509BBFD7D":"9E56F509196784D963D1C0A401510EE7ADA3DCC5DEE04B154BF61AF1D5A6DECE":"BA7816BF8F01CFEA414140DE5DAE2223B00361A396177A9CB410FF61F20015AD":"CB28E0999B9C7715FD0A80D8E47A77079716CBBF917DD72E97566EA1C066957C":"86FA3BB4E26CAD5BF90B7F81899256CE7594BB1EA0C89212748BFF3B3D5B0315":0

ECDSA primitive rfc 4754 p384
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecdsa_prim_test_vectors:MBEDTLS_ECP_DP_SECP384R1:"0BEB646634BA87735D77AE4809A0EBEA865535DE4C1E1DCB692E84708E81A5AF62E528C38B2A81B35309668D73524D9F":"96281BF8DD5E0525CA049C048D345D3082968D10FEDF5C5ACA0C64E6465A97EA5CE10C9DFEC21797415710721F437922":"447688BA94708EB6E2E4D59F6AB6D7EDFF9301D249FE49C33096655F5D502FAD3D383B91C5E7EDAA2B714CC99D5743CA":"B4B74E44D71A13D568003D7489908D564C7761E229C58CBFA18950096EB7463B854D7FA992F934D927376285E63414FA":"CB00753F45A35E8BB5A03D699AC65007272C32AB0EDED1631A8B605A43FF5BED8086072BA1E7CC2358BAECA134C825A7":"FB017B914E29149432D8BAC29A514640B46F53DDAB2C69948084E2930F1C8F7E08E07C9C63F2D21A07DCB56A6AF56EB3":"B263A1305E057F984D38726A1B46874109F417BCA112674C528262A40A629AF1CBB9F516CE0FA7D2FF630863A00E8B9F":0

ECDSA primitive rfc 4754 p521
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecdsa_prim_test_vectors:MBEDTLS_ECP_DP_SECP521R1:"0065FDA3409451DCAB0A0EAD45495112A3D813C17BFD34BDF8C1209D7DF5849120597779060A7FF9D704ADF78B570FFAD6F062E95C7E0C5D5481C5B153B48B375FA1":"0151518F1AF0F563517EDD5485190DF95A4BF57B5CBA4CF2A9A3F6474725A35F7AFE0A6DDEB8BEDBCD6A197E592D40188901CECD650699C9B5E456AEA5ADD19052A8":"006F3B142EA1BFFF7E2837AD44C9E4FF6D2D34C73184BBAD90026DD5E6E85317D9DF45CAD7803C6C20035B2F3FF63AFF4E1BA64D1C077577DA3F4286C58F0AEAE643":"00C1C2B305419F5A41344D7E4359933D734096F556197A9B244342B8B62F46F9373778F9DE6B6497B1EF825FF24F42F9B4A4BD7382CFC3378A540B1B7F0C1B956C2F":"DDAF35A193617ABACC417349AE20413112E6FA4E89A97EA20A9EEEE64B55D39A2192992A274FC1A836BA3C23A3FEEBBD454D4423643CE80E2A9AC94FA54CA49F":"0154FD3836AF92D0DCA57DD5341D3053988534FDE8318FC6AAAAB68E2E6F4339B19F2F281A7E0B22C269D93CF8794A9278880ED7DBB8D9362CAEACEE544320552251":"017705A7030290D1CEB605A9A1BB03FF9CDD521E87A696EC926C8C10C8362DF4975367101F67D1CF9BCCBF2F3D239534FA509E70AAC851AE01AAC68D62F866472660":0

ECDSA write-read hash zero #1
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecdsa_write_read_zero:MBEDTLS_ECP_DP_SECP192R1

ECDSA write-read hash zero #2
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecdsa_write_read_zero:MBEDTLS_ECP_DP_SECP224R1

ECDSA write-read hash zero #3
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecdsa_write_read_zero:MBEDTLS_ECP_DP_SECP256R1

ECDSA write-read hash zero #4
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecdsa_write_read_zero:MBEDTLS_ECP_DP_SECP384R1

ECDSA write-read hash zero #5
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecdsa_write_read_zero:MBEDTLS_ECP_DP_SECP521R1

ECDSA write-read random #1
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecdsa_write_read_random:MBEDTLS_ECP_DP_SECP192R1

ECDSA write-read random #2
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecdsa_write_read_random:MBEDTLS_ECP_DP_SECP224R1

ECDSA write-read random #3
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecdsa_write_read_random:MBEDTLS_ECP_DP_SECP256R1

ECDSA write-read random #4
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecdsa_write_read_random:MBEDTLS_ECP_DP_SECP384R1

ECDSA write-read random #5
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecdsa_write_read_random:MBEDTLS_ECP_DP_SECP521R1

ECDSA deterministic test vector rfc 6979 p192 sha1 [#1]
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED:MBEDTLS_MD_CAN_SHA1
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP192R1:"6FAB034934E4C0FC9AE67F5B5659A9D7D1FEFD187EE09FD4":MBEDTLS_MD_SHA1:"8151325DCDBAE9E0FF95F9F9658432DBEDFDB209":"98C6BD12B23EAF5E2A2045132086BE3EB8EBD62ABF6698FF":"57A22B07DEA9530F8DE9471B1DC6624472E8E2844BC25B64"

ECDSA deterministic test vector rfc 6979 p192 sha224 [#1]
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED:MBEDTLS_MD_CAN_SHA224
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP192R1:"6FAB034934E4C0FC9AE67F5B5659A9D7D1FEFD187EE09FD4":MBEDTLS_MD_SHA224:"9003E374BC726550C2C289447FD0533160F875709386DFA377BFD41C":"A1F00DAD97AEEC91C95585F36200C65F3C01812AA60378F5":"E07EC1304C7C6C9DEBBE980B9692668F81D4DE7922A0F97A"

ECDSA deterministic test vector rfc 6979 p192 sha256 [#1]
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED:MBEDTLS_MD_CAN_SHA256
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP192R1:"6FAB034934E4C0FC9AE67F5B5659A9D7D1FEFD187EE09FD4":MBEDTLS_MD_SHA256:"AF2BDBE1AA9B6EC1E2ADE1D694F41FC71A831D0268E9891562113D8A62ADD1BF":"4B0B8CE98A92866A2820E20AA6B75B56382E0F9BFD5ECB55":"CCDB006926EA9565CBADC840829D8C384E06DE1F1E381B85"

ECDSA deterministic test vector rfc 6979 p192 sha384 [#1]
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED:MBEDTLS_MD_CAN_SHA384
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP192R1:"6FAB034934E4C0FC9AE67F5B5659A9D7D1FEFD187EE09FD4":MBEDTLS_MD_SHA384:"9A9083505BC92276AEC4BE312696EF7BF3BF603F4BBD381196A029F340585312313BCA4A9B5B890EFEE42C77B1EE25FE":"DA63BF0B9ABCF948FBB1E9167F136145F7A20426DCC287D5":"C3AA2C960972BD7A2003A57E1C4C77F0578F8AE95E31EC5E"

ECDSA deterministic test vector rfc 6979 p192 sha512 [#1]
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED:MBEDTLS_MD_CAN_SHA512
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP192R1:"6FAB034934E4C0FC9AE67F5B5659A9D7D1FEFD187EE09FD4":MBEDTLS_MD_SHA512:"39A5E04AAFF7455D9850C605364F514C11324CE64016960D23D5DC57D3FFD8F49A739468AB8049BF18EEF820CDB1AD6C9015F838556BC7FAD4138B23FDF986C7":"4D60C5AB1996BD848343B31C00850205E2EA6922DAC2E4B8":"3F6E837448F027A1BF4B34E796E32A811CBB4050908D8F67"

ECDSA deterministic test vector rfc 6979 p192 sha1 [#2]
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED:MBEDTLS_MD_CAN_SHA1
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP192R1:"6FAB034934E4C0FC9AE67F5B5659A9D7D1FEFD187EE09FD4":MBEDTLS_MD_SHA1:"A94A8FE5CCB19BA61C4C0873D391E987982FBBD3":"0F2141A0EBBC44D2E1AF90A50EBCFCE5E197B3B7D4DE036D":"EB18BC9E1F3D7387500CB99CF5F7C157070A8961E38700B7"

ECDSA deterministic test vector rfc 6979 p192 sha224 [#2]
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED:MBEDTLS_MD_CAN_SHA224
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP192R1:"6FAB034934E4C0FC9AE67F5B5659A9D7D1FEFD187EE09FD4":MBEDTLS_MD_SHA224:"90A3ED9E32B2AAF4C61C410EB925426119E1A9DC53D4286ADE99A809":"6945A1C1D1B2206B8145548F633BB61CEF04891BAF26ED34":"B7FB7FDFC339C0B9BD61A9F5A8EAF9BE58FC5CBA2CB15293"

ECDSA deterministic test vector rfc 6979 p192 sha256 [#2]
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED:MBEDTLS_MD_CAN_SHA256
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP192R1:"6FAB034934E4C0FC9AE67F5B5659A9D7D1FEFD187EE09FD4":MBEDTLS_MD_SHA256:"9F86D081884C7D659A2FEAA0C55AD015A3BF4F1B2B0B822CD15D6C15B0F00A08":"3A718BD8B4926C3B52EE6BBE67EF79B18CB6EB62B1AD97AE":"5662E6848A4A19B1F1AE2F72ACD4B8BBE50F1EAC65D9124F"

ECDSA deterministic test vector rfc 6979 p192 sha384 [#2]
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED:MBEDTLS_MD_CAN_SHA384
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP192R1:"6FAB034934E4C0FC9AE67F5B5659A9D7D1FEFD187EE09FD4":MBEDTLS_MD_SHA384:"768412320F7B0AA5812FCE428DC4706B3CAE50E02A64CAA16A782249BFE8EFC4B7EF1CCB126255D196047DFEDF17A0A9":"B234B60B4DB75A733E19280A7A6034BD6B1EE88AF5332367":"7994090B2D59BB782BE57E74A44C9A1C700413F8ABEFE77A"

ECDSA deterministic test vector rfc 6979 p192 sha512 [#2]
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED:MBEDTLS_MD_CAN_SHA512
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP192R1:"6FAB034934E4C0FC9AE67F5B5659A9D7D1FEFD187EE09FD4":MBEDTLS_MD_SHA512:"EE26B0DD4AF7E749AA1A8EE3C10AE9923F618980772E473F8819A5D4940E0DB27AC185F8A0E1D5F84F88BC887FD67B143732C304CC5FA9AD8E6F57F50028A8FF":"FE4F4AE86A58B6507946715934FE2D8FF9D95B6B098FE739":"74CF5605C98FBA0E1EF34D4B5A1577A7DCF59457CAE52290"

ECDSA deterministic test vector rfc 6979 p224 sha1 [#1]
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED:MBEDTLS_MD_CAN_SHA1
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP224R1:"F220266E1105BFE3083E03EC7A3A654651F45E37167E88600BF257C1":MBEDTLS_MD_SHA1:"8151325DCDBAE9E0FF95F9F9658432DBEDFDB209":"22226F9D40A96E19C4A301CE5B74B115303C0F3A4FD30FC257FB57AC":"66D1CDD83E3AF75605DD6E2FEFF196D30AA7ED7A2EDF7AF475403D69"

ECDSA deterministic test vector rfc 6979 p224 sha224 [#1]
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED:MBEDTLS_MD_CAN_SHA224
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP224R1:"F220266E1105BFE3083E03EC7A3A654651F45E37167E88600BF257C1":MBEDTLS_MD_SHA224:"9003E374BC726550C2C289447FD0533160F875709386DFA377BFD41C":"1CDFE6662DDE1E4A1EC4CDEDF6A1F5A2FB7FBD9145C12113E6ABFD3E":"A6694FD7718A21053F225D3F46197CA699D45006C06F871808F43EBC"

ECDSA deterministic test vector rfc 6979 p224 sha256 [#1]
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED:MBEDTLS_MD_CAN_SHA256
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP224R1:"F220266E1105BFE3083E03EC7A3A654651F45E37167E88600BF257C1":MBEDTLS_MD_SHA256:"AF2BDBE1AA9B6EC1E2ADE1D694F41FC71A831D0268E9891562113D8A62ADD1BF":"61AA3DA010E8E8406C656BC477A7A7189895E7E840CDFE8FF42307BA":"BC814050DAB5D23770879494F9E0A680DC1AF7161991BDE692B10101"

ECDSA deterministic test vector rfc 6979 p224 sha384 [#1]
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED:MBEDTLS_MD_CAN_SHA384
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP224R1:"F220266E1105BFE3083E03EC7A3A654651F45E37167E88600BF257C1":MBEDTLS_MD_SHA384:"9A9083505BC92276AEC4BE312696EF7BF3BF603F4BBD381196A029F340585312313BCA4A9B5B890EFEE42C77B1EE25FE":"0B115E5E36F0F9EC81F1325A5952878D745E19D7BB3EABFABA77E953":"830F34CCDFE826CCFDC81EB4129772E20E122348A2BBD889A1B1AF1D"

ECDSA deterministic test vector rfc 6979 p224 sha512 [#1]
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED:MBEDTLS_MD_CAN_SHA512
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP224R1:"F220266E1105BFE3083E03EC7A3A654651F45E37167E88600BF257C1":MBEDTLS_MD_SHA512:"39A5E04AAFF7455D9850C605364F514C11324CE64016960D23D5DC57D3FFD8F49A739468AB8049BF18EEF820CDB1AD6C9015F838556BC7FAD4138B23FDF986C7":"074BD1D979D5F32BF958DDC61E4FB4872ADCAFEB2256497CDAC30397":"A4CECA196C3D5A1FF31027B33185DC8EE43F288B21AB342E5D8EB084"

ECDSA deterministic test vector rfc 6979 p224 sha1 [#2]
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED:MBEDTLS_MD_CAN_SHA1
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP224R1:"F220266E1105BFE3083E03EC7A3A654651F45E37167E88600BF257C1":MBEDTLS_MD_SHA1:"A94A8FE5CCB19BA61C4C0873D391E987982FBBD3":"DEAA646EC2AF2EA8AD53ED66B2E2DDAA49A12EFD8356561451F3E21C":"95987796F6CF2062AB8135271DE56AE55366C045F6D9593F53787BD2"

ECDSA deterministic test vector rfc 6979 p224 sha224 [#2]
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED:MBEDTLS_MD_CAN_SHA224
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP224R1:"F220266E1105BFE3083E03EC7A3A654651F45E37167E88600BF257C1":MBEDTLS_MD_SHA224:"90A3ED9E32B2AAF4C61C410EB925426119E1A9DC53D4286ADE99A809":"C441CE8E261DED634E4CF84910E4C5D1D22C5CF3B732BB204DBEF019":"902F42847A63BDC5F6046ADA114953120F99442D76510150F372A3F4"

ECDSA deterministic test vector rfc 6979 p224 sha256 [#2]
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED:MBEDTLS_MD_CAN_SHA256
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP224R1:"F220266E1105BFE3083E03EC7A3A654651F45E37167E88600BF257C1":MBEDTLS_MD_SHA256:"9F86D081884C7D659A2FEAA0C55AD015A3BF4F1B2B0B822CD15D6C15B0F00A08":"AD04DDE87B84747A243A631EA47A1BA6D1FAA059149AD2440DE6FBA6":"178D49B1AE90E3D8B629BE3DB5683915F4E8C99FDF6E666CF37ADCFD"

ECDSA deterministic test vector rfc 6979 p224 sha384 [#2]
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED:MBEDTLS_MD_CAN_SHA384
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP224R1:"F220266E1105BFE3083E03EC7A3A654651F45E37167E88600BF257C1":MBEDTLS_MD_SHA384:"768412320F7B0AA5812FCE428DC4706B3CAE50E02A64CAA16A782249BFE8EFC4B7EF1CCB126255D196047DFEDF17A0A9":"389B92682E399B26518A95506B52C03BC9379A9DADF3391A21FB0EA4":"414A718ED3249FF6DBC5B50C27F71F01F070944DA22AB1F78F559AAB"

ECDSA deterministic test vector rfc 6979 p224 sha512 [#2]
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED:MBEDTLS_MD_CAN_SHA512
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP224R1:"F220266E1105BFE3083E03EC7A3A654651F45E37167E88600BF257C1":MBEDTLS_MD_SHA512:"EE26B0DD4AF7E749AA1A8EE3C10AE9923F618980772E473F8819A5D4940E0DB27AC185F8A0E1D5F84F88BC887FD67B143732C304CC5FA9AD8E6F57F50028A8FF":"049F050477C5ADD858CAC56208394B5A55BAEBBE887FDF765047C17C":"077EB13E7005929CEFA3CD0403C7CDCC077ADF4E44F3C41B2F60ECFF"

ECDSA deterministic test vector rfc 6979 p256 sha1 [#1]
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_MD_CAN_SHA1
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP256R1:"C9AFA9D845BA75166B5C215767B1D6934E50C3DB36E89B127B8A622B120F6721":MBEDTLS_MD_SHA1:"8151325DCDBAE9E0FF95F9F9658432DBEDFDB209":"61340C88C3AAEBEB4F6D667F672CA9759A6CCAA9FA8811313039EE4A35471D32":"6D7F147DAC089441BB2E2FE8F7A3FA264B9C475098FDCF6E00D7C996E1B8B7EB"

ECDSA deterministic test vector rfc 6979 p256 sha224 [#1]
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_MD_CAN_SHA224
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP256R1:"C9AFA9D845BA75166B5C215767B1D6934E50C3DB36E89B127B8A622B120F6721":MBEDTLS_MD_SHA224:"9003E374BC726550C2C289447FD0533160F875709386DFA377BFD41C":"53B2FFF5D1752B2C689DF257C04C40A587FABABB3F6FC2702F1343AF7CA9AA3F":"B9AFB64FDC03DC1A131C7D2386D11E349F070AA432A4ACC918BEA988BF75C74C"

ECDSA deterministic test vector rfc 6979 p256 sha256 [#1]
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_MD_CAN_SHA256
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP256R1:"C9AFA9D845BA75166B5C215767B1D6934E50C3DB36E89B127B8A622B120F6721":MBEDTLS_MD_SHA256:"AF2BDBE1AA9B6EC1E2ADE1D694F41FC71A831D0268E9891562113D8A62ADD1BF":"EFD48B2AACB6A8FD1140DD9CD45E81D69D2C877B56AAF991C34D0EA84EAF3716":"F7CB1C942D657C41D436C7A1B6E29F65F3E900DBB9AFF4064DC4AB2F843ACDA8"

ECDSA deterministic test vector rfc 6979 p256 sha384 [#1]
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_MD_CAN_SHA384
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP256R1:"C9AFA9D845BA75166B5C215767B1D6934E50C3DB36E89B127B8A622B120F6721":MBEDTLS_MD_SHA384:"9A9083505BC92276AEC4BE312696EF7BF3BF603F4BBD381196A029F340585312313BCA4A9B5B890EFEE42C77B1EE25FE":"0EAFEA039B20E9B42309FB1D89E213057CBF973DC0CFC8F129EDDDC800EF7719":"4861F0491E6998B9455193E34E7B0D284DDD7149A74B95B9261F13ABDE940954"

ECDSA deterministic test vector rfc 6979 p256 sha512 [#1]
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_MD_CAN_SHA512
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP256R1:"C9AFA9D845BA75166B5C215767B1D6934E50C3DB36E89B127B8A622B120F6721":MBEDTLS_MD_SHA512:"39A5E04AAFF7455D9850C605364F514C11324CE64016960D23D5DC57D3FFD8F49A739468AB8049BF18EEF820CDB1AD6C9015F838556BC7FAD4138B23FDF986C7":"8496A60B5E9B47C825488827E0495B0E3FA109EC4568FD3F8D1097678EB97F00":"2362AB1ADBE2B8ADF9CB9EDAB740EA6049C028114F2460F96554F61FAE3302FE"

ECDSA deterministic test vector rfc 6979 p256 sha1 [#2]
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_MD_CAN_SHA1
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP256R1:"C9AFA9D845BA75166B5C215767B1D6934E50C3DB36E89B127B8A622B120F6721":MBEDTLS_MD_SHA1:"A94A8FE5CCB19BA61C4C0873D391E987982FBBD3":"0CBCC86FD6ABD1D99E703E1EC50069EE5C0B4BA4B9AC60E409E8EC5910D81A89":"01B9D7B73DFAA60D5651EC4591A0136F87653E0FD780C3B1BC872FFDEAE479B1"

ECDSA deterministic test vector rfc 6979 p256 sha224 [#2]
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_MD_CAN_SHA224
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP256R1:"C9AFA9D845BA75166B5C215767B1D6934E50C3DB36E89B127B8A622B120F6721":MBEDTLS_MD_SHA224:"90A3ED9E32B2AAF4C61C410EB925426119E1A9DC53D4286ADE99A809":"C37EDB6F0AE79D47C3C27E962FA269BB4F441770357E114EE511F662EC34A692":"C820053A05791E521FCAAD6042D40AEA1D6B1A540138558F47D0719800E18F2D"

ECDSA deterministic test vector rfc 6979 p256 sha256 [#2]
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_MD_CAN_SHA256
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP256R1:"C9AFA9D845BA75166B5C215767B1D6934E50C3DB36E89B127B8A622B120F6721":MBEDTLS_MD_SHA256:"9F86D081884C7D659A2FEAA0C55AD015A3BF4F1B2B0B822CD15D6C15B0F00A08":"F1ABB023518351CD71D881567B1EA663ED3EFCF6C5132B354F28D3B0B7D38367":"019F4113742A2B14BD25926B49C649155F267E60D3814B4C0CC84250E46F0083"

ECDSA deterministic test vector rfc 6979 p256 sha384 [#2]
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_MD_CAN_SHA384
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP256R1:"C9AFA9D845BA75166B5C215767B1D6934E50C3DB36E89B127B8A622B120F6721":MBEDTLS_MD_SHA384:"768412320F7B0AA5812FCE428DC4706B3CAE50E02A64CAA16A782249BFE8EFC4B7EF1CCB126255D196047DFEDF17A0A9":"83910E8B48BB0C74244EBDF7F07A1C5413D61472BD941EF3920E623FBCCEBEB6":"8DDBEC54CF8CD5874883841D712142A56A8D0F218F5003CB0296B6B509619F2C"

ECDSA deterministic test vector rfc 6979 p256 sha512 [#2]
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_MD_CAN_SHA512
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP256R1:"C9AFA9D845BA75166B5C215767B1D6934E50C3DB36E89B127B8A622B120F6721":MBEDTLS_MD_SHA512:"EE26B0DD4AF7E749AA1A8EE3C10AE9923F618980772E473F8819A5D4940E0DB27AC185F8A0E1D5F84F88BC887FD67B143732C304CC5FA9AD8E6F57F50028A8FF":"461D93F31B6540894788FD206C07CFA0CC35F46FA3C91816FFF1040AD1581A04":"39AF9F15DE0DB8D97E72719C74820D304CE5226E32DEDAE67519E840D1194E55"

ECDSA deterministic test vector rfc 6979 p384 sha1 [#1]
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED:MBEDTLS_MD_CAN_SHA1
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP384R1:"6B9D3DAD2E1B8C1C05B19875B6659F4DE23C3B667BF297BA9AA47740787137D896D5724E4C70A825F872C9EA60D2EDF5":MBEDTLS_MD_SHA1:"8151325DCDBAE9E0FF95F9F9658432DBEDFDB209":"EC748D839243D6FBEF4FC5C4859A7DFFD7F3ABDDF72014540C16D73309834FA37B9BA002899F6FDA3A4A9386790D4EB2":"A3BCFA947BEEF4732BF247AC17F71676CB31A847B9FF0CBC9C9ED4C1A5B3FACF26F49CA031D4857570CCB5CA4424A443"

ECDSA deterministic test vector rfc 6979 p384 sha224 [#1]
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED:MBEDTLS_MD_CAN_SHA224
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP384R1:"6B9D3DAD2E1B8C1C05B19875B6659F4DE23C3B667BF297BA9AA47740787137D896D5724E4C70A825F872C9EA60D2EDF5":MBEDTLS_MD_SHA224:"9003E374BC726550C2C289447FD0533160F875709386DFA377BFD41C":"42356E76B55A6D9B4631C865445DBE54E056D3B3431766D0509244793C3F9366450F76EE3DE43F5A125333A6BE060122":"9DA0C81787064021E78DF658F2FBB0B042BF304665DB721F077A4298B095E4834C082C03D83028EFBF93A3C23940CA8D"

ECDSA deterministic test vector rfc 6979 p384 sha256 [#1]
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED:MBEDTLS_MD_CAN_SHA256
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP384R1:"6B9D3DAD2E1B8C1C05B19875B6659F4DE23C3B667BF297BA9AA47740787137D896D5724E4C70A825F872C9EA60D2EDF5":MBEDTLS_MD_SHA256:"AF2BDBE1AA9B6EC1E2ADE1D694F41FC71A831D0268E9891562113D8A62ADD1BF":"21B13D1E013C7FA1392D03C5F99AF8B30C570C6F98D4EA8E354B63A21D3DAA33BDE1E888E63355D92FA2B3C36D8FB2CD":"F3AA443FB107745BF4BD77CB3891674632068A10CA67E3D45DB2266FA7D1FEEBEFDC63ECCD1AC42EC0CB8668A4FA0AB0"

ECDSA deterministic test vector rfc 6979 p384 sha384 [#1]
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED:MBEDTLS_MD_CAN_SHA384
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP384R1:"6B9D3DAD2E1B8C1C05B19875B6659F4DE23C3B667BF297BA9AA47740787137D896D5724E4C70A825F872C9EA60D2EDF5":MBEDTLS_MD_SHA384:"9A9083505BC92276AEC4BE312696EF7BF3BF603F4BBD381196A029F340585312313BCA4A9B5B890EFEE42C77B1EE25FE":"94EDBB92A5ECB8AAD4736E56C691916B3F88140666CE9FA73D64C4EA95AD133C81A648152E44ACF96E36DD1E80FABE46":"99EF4AEB15F178CEA1FE40DB2603138F130E740A19624526203B6351D0A3A94FA329C145786E679E7B82C71A38628AC8"

ECDSA deterministic test vector rfc 6979 p384 sha512 [#1]
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED:MBEDTLS_MD_CAN_SHA512
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP384R1:"6B9D3DAD2E1B8C1C05B19875B6659F4DE23C3B667BF297BA9AA47740787137D896D5724E4C70A825F872C9EA60D2EDF5":MBEDTLS_MD_SHA512:"39A5E04AAFF7455D9850C605364F514C11324CE64016960D23D5DC57D3FFD8F49A739468AB8049BF18EEF820CDB1AD6C9015F838556BC7FAD4138B23FDF986C7":"ED0959D5880AB2D869AE7F6C2915C6D60F96507F9CB3E047C0046861DA4A799CFE30F35CC900056D7C99CD7882433709":"512C8CCEEE3890A84058CE1E22DBC2198F42323CE8ACA9135329F03C068E5112DC7CC3EF3446DEFCEB01A45C2667FDD5"

ECDSA deterministic test vector rfc 6979 p384 sha1 [#2]
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED:MBEDTLS_MD_CAN_SHA1
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP384R1:"6B9D3DAD2E1B8C1C05B19875B6659F4DE23C3B667BF297BA9AA47740787137D896D5724E4C70A825F872C9EA60D2EDF5":MBEDTLS_MD_SHA1:"A94A8FE5CCB19BA61C4C0873D391E987982FBBD3":"4BC35D3A50EF4E30576F58CD96CE6BF638025EE624004A1F7789A8B8E43D0678ACD9D29876DAF46638645F7F404B11C7":"D5A6326C494ED3FF614703878961C0FDE7B2C278F9A65FD8C4B7186201A2991695BA1C84541327E966FA7B50F7382282"

ECDSA deterministic test vector rfc 6979 p384 sha224 [#2]
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED:MBEDTLS_MD_CAN_SHA224
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP384R1:"6B9D3DAD2E1B8C1C05B19875B6659F4DE23C3B667BF297BA9AA47740787137D896D5724E4C70A825F872C9EA60D2EDF5":MBEDTLS_MD_SHA224:"90A3ED9E32B2AAF4C61C410EB925426119E1A9DC53D4286ADE99A809":"E8C9D0B6EA72A0E7837FEA1D14A1A9557F29FAA45D3E7EE888FC5BF954B5E62464A9A817C47FF78B8C11066B24080E72":"07041D4A7A0379AC7232FF72E6F77B6DDB8F09B16CCE0EC3286B2BD43FA8C6141C53EA5ABEF0D8231077A04540A96B66"

ECDSA deterministic test vector rfc 6979 p384 sha256 [#2]
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED:MBEDTLS_MD_CAN_SHA256
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP384R1:"6B9D3DAD2E1B8C1C05B19875B6659F4DE23C3B667BF297BA9AA47740787137D896D5724E4C70A825F872C9EA60D2EDF5":MBEDTLS_MD_SHA256:"9F86D081884C7D659A2FEAA0C55AD015A3BF4F1B2B0B822CD15D6C15B0F00A08":"6D6DEFAC9AB64DABAFE36C6BF510352A4CC27001263638E5B16D9BB51D451559F918EEDAF2293BE5B475CC8F0188636B":"2D46F3BECBCC523D5F1A1256BF0C9B024D879BA9E838144C8BA6BAEB4B53B47D51AB373F9845C0514EEFB14024787265"

ECDSA deterministic test vector rfc 6979 p384 sha384 [#2]
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED:MBEDTLS_MD_CAN_SHA384
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP384R1:"6B9D3DAD2E1B8C1C05B19875B6659F4DE23C3B667BF297BA9AA47740787137D896D5724E4C70A825F872C9EA60D2EDF5":MBEDTLS_MD_SHA384:"768412320F7B0AA5812FCE428DC4706B3CAE50E02A64CAA16A782249BFE8EFC4B7EF1CCB126255D196047DFEDF17A0A9":"8203B63D3C853E8D77227FB377BCF7B7B772E97892A80F36AB775D509D7A5FEB0542A7F0812998DA8F1DD3CA3CF023DB":"DDD0760448D42D8A43AF45AF836FCE4DE8BE06B485E9B61B827C2F13173923E06A739F040649A667BF3B828246BAA5A5"

ECDSA deterministic test vector rfc 6979 p384 sha512 [#2]
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED:MBEDTLS_MD_CAN_SHA512
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP384R1:"6B9D3DAD2E1B8C1C05B19875B6659F4DE23C3B667BF297BA9AA47740787137D896D5724E4C70A825F872C9EA60D2EDF5":MBEDTLS_MD_SHA512:"EE26B0DD4AF7E749AA1A8EE3C10AE9923F618980772E473F8819A5D4940E0DB27AC185F8A0E1D5F84F88BC887FD67B143732C304CC5FA9AD8E6F57F50028A8FF":"A0D5D090C9980FAF3C2CE57B7AE951D31977DD11C775D314AF55F76C676447D06FB6495CD21B4B6E340FC236584FB277":"976984E59B4C77B0E8E4460DCA3D9F20E07B9BB1F63BEEFAF576F6B2E8B224634A2092CD3792E0159AD9CEE37659C736"

ECDSA deterministic test vector rfc 6979 p521 sha1 [#1]
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED:MBEDTLS_MD_CAN_SHA1
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP521R1:"0FAD06DAA62BA3B25D2FB40133DA757205DE67F5BB0018FEE8C86E1B68C7E75CAA896EB32F1F47C70855836A6D16FCC1466F6D8FBEC67DB89EC0C08B0E996B83538":MBEDTLS_MD_SHA1:"8151325DCDBAE9E0FF95F9F9658432DBEDFDB209":"0343B6EC45728975EA5CBA6659BBB6062A5FF89EEA58BE3C80B619F322C87910FE092F7D45BB0F8EEE01ED3F20BABEC079D202AE677B243AB40B5431D497C55D75D":"0E7B0E675A9B24413D448B8CC119D2BF7B2D2DF032741C096634D6D65D0DBE3D5694625FB9E8104D3B842C1B0E2D0B98BEA19341E8676AEF66AE4EBA3D5475D5D16"

ECDSA deterministic test vector rfc 6979 p521 sha224 [#1]
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED:MBEDTLS_MD_CAN_SHA224
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP521R1:"0FAD06DAA62BA3B25D2FB40133DA757205DE67F5BB0018FEE8C86E1B68C7E75CAA896EB32F1F47C70855836A6D16FCC1466F6D8FBEC67DB89EC0C08B0E996B83538":MBEDTLS_MD_SHA224:"9003E374BC726550C2C289447FD0533160F875709386DFA377BFD41C":"1776331CFCDF927D666E032E00CF776187BC9FDD8E69D0DABB4109FFE1B5E2A30715F4CC923A4A5E94D2503E9ACFED92857B7F31D7152E0F8C00C15FF3D87E2ED2E":"050CB5265417FE2320BBB5A122B8E1A32BD699089851128E360E620A30C7E17BA41A666AF126CE100E5799B153B60528D5300D08489CA9178FB610A2006C254B41F"

ECDSA deterministic test vector rfc 6979 p521 sha256 [#1]
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED:MBEDTLS_MD_CAN_SHA256
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP521R1:"0FAD06DAA62BA3B25D2FB40133DA757205DE67F5BB0018FEE8C86E1B68C7E75CAA896EB32F1F47C70855836A6D16FCC1466F6D8FBEC67DB89EC0C08B0E996B83538":MBEDTLS_MD_SHA256:"AF2BDBE1AA9B6EC1E2ADE1D694F41FC71A831D0268E9891562113D8A62ADD1BF":"1511BB4D675114FE266FC4372B87682BAECC01D3CC62CF2303C92B3526012659D16876E25C7C1E57648F23B73564D67F61C6F14D527D54972810421E7D87589E1A7":"04A171143A83163D6DF460AAF61522695F207A58B95C0644D87E52AA1A347916E4F7A72930B1BC06DBE22CE3F58264AFD23704CBB63B29B931F7DE6C9D949A7ECFC"

ECDSA deterministic test vector rfc 6979 p521 sha384 [#1]
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED:MBEDTLS_MD_CAN_SHA384
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP521R1:"0FAD06DAA62BA3B25D2FB40133DA757205DE67F5BB0018FEE8C86E1B68C7E75CAA896EB32F1F47C70855836A6D16FCC1466F6D8FBEC67DB89EC0C08B0E996B83538":MBEDTLS_MD_SHA384:"9A9083505BC92276AEC4BE312696EF7BF3BF603F4BBD381196A029F340585312313BCA4A9B5B890EFEE42C77B1EE25FE":"1EA842A0E17D2DE4F92C15315C63DDF72685C18195C2BB95E572B9C5136CA4B4B576AD712A52BE9730627D16054BA40CC0B8D3FF035B12AE75168397F5D50C67451":"1F21A3CEE066E1961025FB048BD5FE2B7924D0CD797BABE0A83B66F1E35EEAF5FDE143FA85DC394A7DEE766523393784484BDF3E00114A1C857CDE1AA203DB65D61"

ECDSA deterministic test vector rfc 6979 p521 sha512 [#1]
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED:MBEDTLS_MD_CAN_SHA512
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP521R1:"0FAD06DAA62BA3B25D2FB40133DA757205DE67F5BB0018FEE8C86E1B68C7E75CAA896EB32F1F47C70855836A6D16FCC1466F6D8FBEC67DB89EC0C08B0E996B83538":MBEDTLS_MD_SHA512:"39A5E04AAFF7455D9850C605364F514C11324CE64016960D23D5DC57D3FFD8F49A739468AB8049BF18EEF820CDB1AD6C9015F838556BC7FAD4138B23FDF986C7":"0C328FAFCBD79DD77850370C46325D987CB525569FB63C5D3BC53950E6D4C5F174E25A1EE9017B5D450606ADD152B534931D7D4E8455CC91F9B15BF05EC36E377FA":"0617CCE7CF5064806C467F678D3B4080D6F1CC50AF26CA209417308281B68AF282623EAA63E5B5C0723D8B8C37FF0777B1A20F8CCB1DCCC43997F1EE0E44DA4A67A"

ECDSA deterministic test vector rfc 6979 p521 sha1 [#2]
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED:MBEDTLS_MD_CAN_SHA1
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP521R1:"0FAD06DAA62BA3B25D2FB40133DA757205DE67F5BB0018FEE8C86E1B68C7E75CAA896EB32F1F47C70855836A6D16FCC1466F6D8FBEC67DB89EC0C08B0E996B83538":MBEDTLS_MD_SHA1:"A94A8FE5CCB19BA61C4C0873D391E987982FBBD3":"13BAD9F29ABE20DE37EBEB823C252CA0F63361284015A3BF430A46AAA80B87B0693F0694BD88AFE4E661FC33B094CD3B7963BED5A727ED8BD6A3A202ABE009D0367":"1E9BB81FF7944CA409AD138DBBEE228E1AFCC0C890FC78EC8604639CB0DBDC90F717A99EAD9D272855D00162EE9527567DD6A92CBD629805C0445282BBC916797FF"

ECDSA deterministic test vector rfc 6979 p521 sha224 [#2]
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED:MBEDTLS_MD_CAN_SHA224
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP521R1:"0FAD06DAA62BA3B25D2FB40133DA757205DE67F5BB0018FEE8C86E1B68C7E75CAA896EB32F1F47C70855836A6D16FCC1466F6D8FBEC67DB89EC0C08B0E996B83538":MBEDTLS_MD_SHA224:"90A3ED9E32B2AAF4C61C410EB925426119E1A9DC53D4286ADE99A809":"1C7ED902E123E6815546065A2C4AF977B22AA8EADDB68B2C1110E7EA44D42086BFE4A34B67DDC0E17E96536E358219B23A706C6A6E16BA77B65E1C595D43CAE17FB":"177336676304FCB343CE028B38E7B4FBA76C1C1B277DA18CAD2A8478B2A9A9F5BEC0F3BA04F35DB3E4263569EC6AADE8C92746E4C82F8299AE1B8F1739F8FD519A4"

ECDSA deterministic test vector rfc 6979 p521 sha256 [#2]
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED:MBEDTLS_MD_CAN_SHA256
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP521R1:"0FAD06DAA62BA3B25D2FB40133DA757205DE67F5BB0018FEE8C86E1B68C7E75CAA896EB32F1F47C70855836A6D16FCC1466F6D8FBEC67DB89EC0C08B0E996B83538":MBEDTLS_MD_SHA256:"9F86D081884C7D659A2FEAA0C55AD015A3BF4F1B2B0B822CD15D6C15B0F00A08":"00E871C4A14F993C6C7369501900C4BC1E9C7B0B4BA44E04868B30B41D8071042EB28C4C250411D0CE08CD197E4188EA4876F279F90B3D8D74A3C76E6F1E4656AA8":"0CD52DBAA33B063C3A6CD8058A1FB0A46A4754B034FCC644766CA14DA8CA5CA9FDE00E88C1AD60CCBA759025299079D7A427EC3CC5B619BFBC828E7769BCD694E86"

ECDSA deterministic test vector rfc 6979 p521 sha384 [#2]
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED:MBEDTLS_MD_CAN_SHA384
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP521R1:"0FAD06DAA62BA3B25D2FB40133DA757205DE67F5BB0018FEE8C86E1B68C7E75CAA896EB32F1F47C70855836A6D16FCC1466F6D8FBEC67DB89EC0C08B0E996B83538":MBEDTLS_MD_SHA384:"768412320F7B0AA5812FCE428DC4706B3CAE50E02A64CAA16A782249BFE8EFC4B7EF1CCB126255D196047DFEDF17A0A9":"14BEE21A18B6D8B3C93FAB08D43E739707953244FDBE924FA926D76669E7AC8C89DF62ED8975C2D8397A65A49DCC09F6B0AC62272741924D479354D74FF6075578C":"133330865C067A0EAF72362A65E2D7BC4E461E8C8995C3B6226A21BD1AA78F0ED94FE536A0DCA35534F0CD1510C41525D163FE9D74D134881E35141ED5E8E95B979"

ECDSA deterministic test vector rfc 6979 p521 sha512 [#2]
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED:MBEDTLS_MD_CAN_SHA512
ecdsa_det_test_vectors:MBEDTLS_ECP_DP_SECP521R1:"0FAD06DAA62BA3B25D2FB40133DA757205DE67F5BB0018FEE8C86E1B68C7E75CAA896EB32F1F47C70855836A6D16FCC1466F6D8FBEC67DB89EC0C08B0E996B83538":MBEDTLS_MD_SHA512:"EE26B0DD4AF7E749AA1A8EE3C10AE9923F618980772E473F8819A5D4940E0DB27AC185F8A0E1D5F84F88BC887FD67B143732C304CC5FA9AD8E6F57F50028A8FF":"13E99020ABF5CEE7525D16B69B229652AB6BDF2AFFCAEF38773B4B7D08725F10CDB93482FDCC54EDCEE91ECA4166B2A7C6265EF0CE2BD7051B7CEF945BABD47EE6D":"1FBD0013C674AA79CB39849527916CE301C66EA7CE8B80682786AD60F98F7E78A19CA69EFF5C57400E3B3A0AD66CE0978214D13BAF4E9AC60752F7B155E2DE4DCE3"

ECDSA restartable read-verify: max_ops=0 (disabled)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecdsa_read_restart:MBEDTLS_ECP_DP_SECP256R1:"04e8f573412a810c5f81ecd2d251bb94387e72f28af70dced90ebe75725c97a6428231069c2b1ef78509a22c59044319f6ed3cb750dfe64c2a282b35967a458ad6":"dee9d4d8b0e40a034602d6e638197998060f6e9f353ae1d10c94cd56476d3c92":"304502210098a5a1392abe29e4b0a4da3fefe9af0f8c32e5b839ab52ba6a05da9c3b7edd0f0220596f0e195ae1e58c1e53e9e7f0f030b274348a8c11232101778d89c4943f5ad2":0:0:0

ECDSA restartable read-verify: max_ops=1
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecdsa_read_restart:MBEDTLS_ECP_DP_SECP256R1:"04e8f573412a810c5f81ecd2d251bb94387e72f28af70dced90ebe75725c97a6428231069c2b1ef78509a22c59044319f6ed3cb750dfe64c2a282b35967a458ad6":"dee9d4d8b0e40a034602d6e638197998060f6e9f353ae1d10c94cd56476d3c92":"304502210098a5a1392abe29e4b0a4da3fefe9af0f8c32e5b839ab52ba6a05da9c3b7edd0f0220596f0e195ae1e58c1e53e9e7f0f030b274348a8c11232101778d89c4943f5ad2":1:42:10000

ECDSA restartable read-verify: max_ops=10000
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecdsa_read_restart:MBEDTLS_ECP_DP_SECP256R1:"04e8f573412a810c5f81ecd2d251bb94387e72f28af70dced90ebe75725c97a6428231069c2b1ef78509a22c59044319f6ed3cb750dfe64c2a282b35967a458ad6":"dee9d4d8b0e40a034602d6e638197998060f6e9f353ae1d10c94cd56476d3c92":"304502210098a5a1392abe29e4b0a4da3fefe9af0f8c32e5b839ab52ba6a05da9c3b7edd0f0220596f0e195ae1e58c1e53e9e7f0f030b274348a8c11232101778d89c4943f5ad2":10000:0:0

ECDSA restartable read-verify: max_ops=250
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecdsa_read_restart:MBEDTLS_ECP_DP_SECP256R1:"04e8f573412a810c5f81ecd2d251bb94387e72f28af70dced90ebe75725c97a6428231069c2b1ef78509a22c59044319f6ed3cb750dfe64c2a282b35967a458ad6":"dee9d4d8b0e40a034602d6e638197998060f6e9f353ae1d10c94cd56476d3c92":"304502210098a5a1392abe29e4b0a4da3fefe9af0f8c32e5b839ab52ba6a05da9c3b7edd0f0220596f0e195ae1e58c1e53e9e7f0f030b274348a8c11232101778d89c4943f5ad2":250:4:64

ECDSA restartable sign-write: secp256r1 max_ops=0 (disabled)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_MD_CAN_SHA256
ecdsa_write_restart:MBEDTLS_ECP_DP_SECP256R1:"C9AFA9D845BA75166B5C215767B1D6934E50C3DB36E89B127B8A622B120F6721":MBEDTLS_MD_SHA256:"9F86D081884C7D659A2FEAA0C55AD015A3BF4F1B2B0B822CD15D6C15B0F00A08":"3045022100f1abb023518351cd71d881567b1ea663ed3efcf6c5132b354f28d3b0b7d383670220019f4113742a2b14bd25926b49c649155f267e60d3814b4c0cc84250e46f0083":0:0:0

ECDSA restartable sign-write: secp256r1 restart max_ops=1
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_MD_CAN_SHA256
ecdsa_write_restart:MBEDTLS_ECP_DP_SECP256R1:"C9AFA9D845BA75166B5C215767B1D6934E50C3DB36E89B127B8A622B120F6721":MBEDTLS_MD_SHA256:"9F86D081884C7D659A2FEAA0C55AD015A3BF4F1B2B0B822CD15D6C15B0F00A08":"3045022100f1abb023518351cd71d881567b1ea663ed3efcf6c5132b354f28d3b0b7d383670220019f4113742a2b14bd25926b49c649155f267e60d3814b4c0cc84250e46f0083":1:1:10000

ECDSA restartable sign-write: secp256r1 restart max_ops=10000
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_MD_CAN_SHA256
ecdsa_write_restart:MBEDTLS_ECP_DP_SECP256R1:"C9AFA9D845BA75166B5C215767B1D6934E50C3DB36E89B127B8A622B120F6721":MBEDTLS_MD_SHA256:"9F86D081884C7D659A2FEAA0C55AD015A3BF4F1B2B0B822CD15D6C15B0F00A08":"3045022100f1abb023518351cd71d881567b1ea663ed3efcf6c5132b354f28d3b0b7d383670220019f4113742a2b14bd25926b49c649155f267e60d3814b4c0cc84250e46f0083":10000:0:0

ECDSA restartable sign-write: secp256r1 restart max_ops=250
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_MD_CAN_SHA256
ecdsa_write_restart:MBEDTLS_ECP_DP_SECP256R1:"C9AFA9D845BA75166B5C215767B1D6934E50C3DB36E89B127B8A622B120F6721":MBEDTLS_MD_SHA256:"9F86D081884C7D659A2FEAA0C55AD015A3BF4F1B2B0B822CD15D6C15B0F00A08":"3045022100f1abb023518351cd71d881567b1ea663ed3efcf6c5132b354f28d3b0b7d383670220019f4113742a2b14bd25926b49c649155f267e60d3814b4c0cc84250e46f0083":250:2:32

ECDSA zero private parameter p192
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecdsa_prim_test_vectors:MBEDTLS_ECP_DP_SECP192R1:"0":"2442A5CC0ECD015FA3CA31DC8E2BBC70BF42D60CBCA20085":"6FC98BD7E50211A4A27102FA3549DF79EBCB4BF246B80945":"9E56F509196784D963D1C0A401510EE7ADA3DCC5DEE04B15":"BA7816BF8F01CFEA414140DE5DAE2223B00361A396177A9C":"98C6BD12B23EAF5E2A2045132086BE3EB8EBD62ABF6698FF":"57A22B07DEA9530F8DE9471B1DC6624472E8E2844BC25B64":MBEDTLS_ERR_ECP_INVALID_KEY

ECDSA private parameter greater than n p192
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecdsa_prim_test_vectors:MBEDTLS_ECP_DP_SECP192R1:"6FAB034934E4C0FC9AE67F5B5659A9D7D1FEFD187EE09FD41":"2442A5CC0ECD015FA3CA31DC8E2BBC70BF42D60CBCA20085":"6FC98BD7E50211A4A27102FA3549DF79EBCB4BF246B80945":"9E56F509196784D963D1C0A401510EE7ADA3DCC5DEE04B15":"BA7816BF8F01CFEA414140DE5DAE2223B00361A396177A9CB410FF61":"98C6BD12B23EAF5E2A2045132086BE3EB8EBD62ABF6698FF":"57A22B07DEA9530F8DE9471B1DC6624472E8E2844BC25B64":MBEDTLS_ERR_ECP_INVALID_KEY

ECDSA zero private parameter p224
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecdsa_prim_test_vectors:MBEDTLS_ECP_DP_SECP224R1:"0":"2442A5CC0ECD015FA3CA31DC8E2BBC70BF42D60CBCA20085E0822CB04235E970":"6FC98BD7E50211A4A27102FA3549DF79EBCB4BF246B80945CDDFE7D5":"9E56F509196784D963D1C0A401510EE7ADA3DCC5DEE04B154BF61AF1":"BA7816BF8F01CFEA414140DE5DAE2223B00361A396177A9CB410FF61":"22226F9D40A96E19C4A301CE5B74B115303C0F3A4FD30FC257FB57AC":"66D1CDD83E3AF75605DD6E2FEFF196D30AA7ED7A2EDF7AF475403D69":MBEDTLS_ERR_ECP_INVALID_KEY

ECDSA private parameter greater than n p224
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecdsa_prim_test_vectors:MBEDTLS_ECP_DP_SECP224R1:"F220266E1105BFE3083E03EC7A3A654651F45E37167E88600BF257C11":"2442A5CC0ECD015FA3CA31DC8E2BBC70BF42D60CBCA20085E0822CB04235E970":"6FC98BD7E50211A4A27102FA3549DF79EBCB4BF246B80945CDDFE7D5":"9E56F509196784D963D1C0A401510EE7ADA3DCC5DEE04B154BF61AF1":"BA7816BF8F01CFEA414140DE5DAE2223B00361A396177A9CB410FF61F20015AD":"22226F9D40A96E19C4A301CE5B74B115303C0F3A4FD30FC257FB57AC":"66D1CDD83E3AF75605DD6E2FEFF196D30AA7ED7A2EDF7AF475403D69":MBEDTLS_ERR_ECP_INVALID_KEY

ECDSA zero private parameter p256
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecdsa_prim_test_vectors:MBEDTLS_ECP_DP_SECP256R1:"0":"2442A5CC0ECD015FA3CA31DC8E2BBC70BF42D60CBCA20085E0822CB04235E970":"6FC98BD7E50211A4A27102FA3549DF79EBCB4BF246B80945CDDFE7D509BBFD7D":"9E56F509196784D963D1C0A401510EE7ADA3DCC5DEE04B154BF61AF1D5A6DECE":"BA7816BF8F01CFEA414140DE5DAE2223B00361A396177A9CB410FF61F20015AD":"CB28E0999B9C7715FD0A80D8E47A77079716CBBF917DD72E97566EA1C066957C":"86FA3BB4E26CAD5BF90B7F81899256CE7594BB1EA0C89212748BFF3B3D5B0315":MBEDTLS_ERR_ECP_INVALID_KEY

ECDSA private parameter greater than n p256
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecdsa_prim_test_vectors:MBEDTLS_ECP_DP_SECP256R1:"DC51D3866A15BACDE33D96F992FCA99DA7E6EF0934E7097559C27F1614C88A7F1":"2442A5CC0ECD015FA3CA31DC8E2BBC70BF42D60CBCA20085E0822CB04235E970":"6FC98BD7E50211A4A27102FA3549DF79EBCB4BF246B80945CDDFE7D509BBFD7D":"9E56F509196784D963D1C0A401510EE7ADA3DCC5DEE04B154BF61AF1D5A6DECE":"BA7816BF8F01CFEA414140DE5DAE2223B00361A396177A9CB410FF61F20015AD":"CB28E0999B9C7715FD0A80D8E47A77079716CBBF917DD72E97566EA1C066957C":"86FA3BB4E26CAD5BF90B7F81899256CE7594BB1EA0C89212748BFF3B3D5B0315":MBEDTLS_ERR_ECP_INVALID_KEY

ECDSA zero private parameter p384
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecdsa_prim_test_vectors:MBEDTLS_ECP_DP_SECP384R1:"0":"96281BF8DD5E0525CA049C048D345D3082968D10FEDF5C5ACA0C64E6465A97EA5CE10C9DFEC21797415710721F437922":"447688BA94708EB6E2E4D59F6AB6D7EDFF9301D249FE49C33096655F5D502FAD3D383B91C5E7EDAA2B714CC99D5743CA":"B4B74E44D71A13D568003D7489908D564C7761E229C58CBFA18950096EB7463B854D7FA992F934D927376285E63414FA":"CB00753F45A35E8BB5A03D699AC65007272C32AB0EDED1631A8B605A43FF5BED8086072BA1E7CC2358BAECA134C825A7":"FB017B914E29149432D8BAC29A514640B46F53DDAB2C69948084E2930F1C8F7E08E07C9C63F2D21A07DCB56A6AF56EB3":"B263A1305E057F984D38726A1B46874109F417BCA112674C528262A40A629AF1CBB9F516CE0FA7D2FF630863A00E8B9F":MBEDTLS_ERR_ECP_INVALID_KEY

ECDSA private parameter greater than n p384
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecdsa_prim_test_vectors:MBEDTLS_ECP_DP_SECP384R1:"10BEB646634BA87735D77AE4809A0EBEA865535DE4C1E1DCB692E84708E81A5AF62E528C38B2A81B35309668D73524D9F":"96281BF8DD5E0525CA049C048D345D3082968D10FEDF5C5ACA0C64E6465A97EA5CE10C9DFEC21797415710721F437922":"447688BA94708EB6E2E4D59F6AB6D7EDFF9301D249FE49C33096655F5D502FAD3D383B91C5E7EDAA2B714CC99D5743CA":"B4B74E44D71A13D568003D7489908D564C7761E229C58CBFA18950096EB7463B854D7FA992F934D927376285E63414FA":"CB00753F45A35E8BB5A03D699AC65007272C32AB0EDED1631A8B605A43FF5BED8086072BA1E7CC2358BAECA134C825A7":"FB017B914E29149432D8BAC29A514640B46F53DDAB2C69948084E2930F1C8F7E08E07C9C63F2D21A07DCB56A6AF56EB3":"B263A1305E057F984D38726A1B46874109F417BCA112674C528262A40A629AF1CBB9F516CE0FA7D2FF630863A00E8B9F":MBEDTLS_ERR_ECP_INVALID_KEY

ECDSA zero private parameter p521
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecdsa_prim_test_vectors:MBEDTLS_ECP_DP_SECP521R1:"0":"0151518F1AF0F563517EDD5485190DF95A4BF57B5CBA4CF2A9A3F6474725A35F7AFE0A6DDEB8BEDBCD6A197E592D40188901CECD650699C9B5E456AEA5ADD19052A8":"006F3B142EA1BFFF7E2837AD44C9E4FF6D2D34C73184BBAD90026DD5E6E85317D9DF45CAD7803C6C20035B2F3FF63AFF4E1BA64D1C077577DA3F4286C58F0AEAE643":"00C1C2B305419F5A41344D7E4359933D734096F556197A9B244342B8B62F46F9373778F9DE6B6497B1EF825FF24F42F9B4A4BD7382CFC3378A540B1B7F0C1B956C2F":"DDAF35A193617ABACC417349AE20413112E6FA4E89A97EA20A9EEEE64B55D39A2192992A274FC1A836BA3C23A3FEEBBD454D4423643CE80E2A9AC94FA54CA49F":"0154FD3836AF92D0DCA57DD5341D3053988534FDE8318FC6AAAAB68E2E6F4339B19F2F281A7E0B22C269D93CF8794A9278880ED7DBB8D9362CAEACEE544320552251":"017705A7030290D1CEB605A9A1BB03FF9CDD521E87A696EC926C8C10C8362DF4975367101F67D1CF9BCCBF2F3D239534FA509E70AAC851AE01AAC68D62F866472660":MBEDTLS_ERR_ECP_INVALID_KEY

ECDSA private parameter greater than n p521
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecdsa_prim_test_vectors:MBEDTLS_ECP_DP_SECP521R1:"0065FDA3409451DCAB0A0EAD45495112A3D813C17BFD34BDF8C1209D7DF5849120597779060A7FF9D704ADF78B570FFAD6F062E95C7E0C5D5481C5B153B48B375FA11":"0151518F1AF0F563517EDD5485190DF95A4BF57B5CBA4CF2A9A3F6474725A35F7AFE0A6DDEB8BEDBCD6A197E592D40188901CECD650699C9B5E456AEA5ADD19052A8":"006F3B142EA1BFFF7E2837AD44C9E4FF6D2D34C73184BBAD90026DD5E6E85317D9DF45CAD7803C6C20035B2F3FF63AFF4E1BA64D1C077577DA3F4286C58F0AEAE643":"00C1C2B305419F5A41344D7E4359933D734096F556197A9B244342B8B62F46F9373778F9DE6B6497B1EF825FF24F42F9B4A4BD7382CFC3378A540B1B7F0C1B956C2F":"DDAF35A193617ABACC417349AE20413112E6FA4E89A97EA20A9EEEE64B55D39A2192992A274FC1A836BA3C23A3FEEBBD454D4423643CE80E2A9AC94FA54CA49F":"0154FD3836AF92D0DCA57DD5341D3053988534FDE8318FC6AAAAB68E2E6F4339B19F2F281A7E0B22C269D93CF8794A9278880ED7DBB8D9362CAEACEE544320552251":"017705A7030290D1CEB605A9A1BB03FF9CDD521E87A696EC926C8C10C8362DF4975367101F67D1CF9BCCBF2F3D239534FA509E70AAC851AE01AAC68D62F866472660":MBEDTLS_ERR_ECP_INVALID_KEY

ECDSA verify invalid pub key (not on curve), zero bytes of data
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecdsa_verify:MBEDTLS_ECP_DP_SECP256K1:"1":"2":"1":"1":"":MBEDTLS_ERR_ECP_INVALID_KEY

ECDSA verify invalid pub key (not on curve), one byte of data
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecdsa_verify:MBEDTLS_ECP_DP_SECP256K1:"1":"2":"1":"1":"00":MBEDTLS_ERR_ECP_INVALID_KEY

ECDSA verify invalid pub key (not on curve), r=1, s=1
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecdsa_verify:MBEDTLS_ECP_DP_SECP256K1:"1":"2":"1":"1":"0000000000000000000000000000000000000000000000000000000000000000":MBEDTLS_ERR_ECP_INVALID_KEY

ECDSA verify invalid pub key (also not on curve), r=1, s=1
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecdsa_verify:MBEDTLS_ECP_DP_SECP256K1:"1":"12345":"1":"1":"0000000000000000000000000000000000000000000000000000000000000000":MBEDTLS_ERR_ECP_INVALID_KEY

ECDSA verify invalid pub key (not on curve), r=12345, s=1
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecdsa_verify:MBEDTLS_ECP_DP_SECP256K1:"1":"2":"12345":"1":"0000000000000000000000000000000000000000000000000000000000000000":MBEDTLS_ERR_ECP_INVALID_KEY

ECDSA verify invalid pub key (not on curve), r=1, s=12345
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecdsa_verify:MBEDTLS_ECP_DP_SECP256K1:"1":"2":"1":"12345":"0000000000000000000000000000000000000000000000000000000000000000":MBEDTLS_ERR_ECP_INVALID_KEY

ECDSA verify valid pub key, invalid sig (r=0), 0 bytes of data
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecdsa_verify:MBEDTLS_ECP_DP_SECP256K1:"79BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798":"483ADA7726A3C4655DA4FBFC0E1108A8FD17B448A68554199C47D08FFB10D4B8":"0":"1":"":MBEDTLS_ERR_ECP_VERIFY_FAILED

ECDSA verify valid pub key, invalid sig (r=0), 1 byte of data
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecdsa_verify:MBEDTLS_ECP_DP_SECP256K1:"79BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798":"483ADA7726A3C4655DA4FBFC0E1108A8FD17B448A68554199C47D08FFB10D4B8":"0":"1":"00":MBEDTLS_ERR_ECP_VERIFY_FAILED

ECDSA verify valid pub key, invalid sig (r>n-1), 32 bytes of data
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecdsa_verify:MBEDTLS_ECP_DP_SECP256K1:"79BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798":"483ADA7726A3C4655DA4FBFC0E1108A8FD17B448A68554199C47D08FFB10D4B8":"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141":"12":"0000000000000000000000000000000000000000000000000000000000000000":MBEDTLS_ERR_ECP_VERIFY_FAILED

ECDSA verify valid pub key, valid/incorrect sig, 0 bytes of data
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecdsa_verify:MBEDTLS_ECP_DP_SECP256K1:"79BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798":"483ADA7726A3C4655DA4FBFC0E1108A8FD17B448A68554199C47D08FFB10D4B8":"ed3bace23c5e17652e174c835fb72bf53ee306b3406a26890221b4cef7500f88":"84eead3fb3cdbdac882412af64cc125b6784690bebf575f1c32162ab65080037":"":MBEDTLS_ERR_ECP_VERIFY_FAILED

ECDSA verify valid pub key, valid/incorrect sig, 1 byte of data
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecdsa_verify:MBEDTLS_ECP_DP_SECP256K1:"79BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798":"483ADA7726A3C4655DA4FBFC0E1108A8FD17B448A68554199C47D08FFB10D4B8":"ed3bace23c5e17652e174c835fb72bf53ee306b3406a26890221b4cef7500f88":"84eead3fb3cdbdac882412af64cc125b6784690bebf575f1c32162ab65080037":"00":MBEDTLS_ERR_ECP_VERIFY_FAILED

ECDSA verify valid pub key, valid/incorrect sig, 32 bytes of data
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecdsa_verify:MBEDTLS_ECP_DP_SECP256K1:"79BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798":"483ADA7726A3C4655DA4FBFC0E1108A8FD17B448A68554199C47D08FFB10D4B8":"ed3bace23c5e17652e174c835fb72bf53ee306b3406a26890221b4cef7500f88":"84eead3fb3cdbdac882412af64cc125b6784690bebf575f1c32162ab65080037":"0000000000000000000000000000000000000000000000000000000000000000":MBEDTLS_ERR_ECP_VERIFY_FAILED

ECDSA verify valid public key, correct sig, 0 bytes of data
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecdsa_verify:MBEDTLS_ECP_DP_SECP256K1:"79BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798":"483ADA7726A3C4655DA4FBFC0E1108A8FD17B448A68554199C47D08FFB10D4B8":"ed3bace23c5e17652e174c835fb72bf53ee306b3406a26890221b4cef7500f88":"c9cc1ba95156bc103055a5d7946f3a3ae7f0657d1e53f1d5c2c9782950aa69b":"":0

ECDSA verify valid pub key, correct sig, 1 byte of data
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecdsa_verify:MBEDTLS_ECP_DP_SECP256K1:"79BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798":"483ADA7726A3C4655DA4FBFC0E1108A8FD17B448A68554199C47D08FFB10D4B8":"ed3bace23c5e17652e174c835fb72bf53ee306b3406a26890221b4cef7500f88":"c9cc1ba95156bc103055a5d7946f3a3ae7f0657d1e53f1d5c2c9782950aa69b":"00":0

ECDSA verify valid pub key, correct sig, 32 bytes of data
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecdsa_verify:MBEDTLS_ECP_DP_SECP256K1:"79BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798":"483ADA7726A3C4655DA4FBFC0E1108A8FD17B448A68554199C47D08FFB10D4B8":"ed3bace23c5e17652e174c835fb72bf53ee306b3406a26890221b4cef7500f88":"c9cc1ba95156bc103055a5d7946f3a3ae7f0657d1e53f1d5c2c9782950aa69b":"0000000000000000000000000000000000000000000000000000000000000000":0
