AES-128-CCM test vector NIST #1 (P=0, N=7, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"4ae701103c63deca5b5a3939d7d05992":"5a8aa485c316e9":"":"":"02209f55":"":"":0

AES-128-CCM test vector NIST #2 (P=0, N=7, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"4ae701103c63deca5b5a3939d7d05992":"3796cf51b87266":"":"":"9a04c241":"FAIL":"":0

AES-128-CCM test vector NIST #3 (P=0, N=7, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"4bb3c4a4f893ad8c9bdc833c325d62b3":"5a8aa485c316e9":"":"":"75d582db43ce9b13ab4b6f7f14341330":"":"":0

AES-128-CCM test vector NIST #4 (P=0, N=7, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"4bb3c4a4f893ad8c9bdc833c325d62b3":"3796cf51b87266":"":"":"3a65e03af37b81d05acc7ec1bc39deb0":"FAIL":"":0

AES-128-CCM test vector NIST #5 (P=0, N=13, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"4bb3c4a4f893ad8c9bdc833c325d62b3":"5a8aa485c316e9403aff859fbb":"":"":"90156f3f":"":"":0

AES-128-CCM test vector NIST #6 (P=0, N=13, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"4bb3c4a4f893ad8c9bdc833c325d62b3":"a16a2e741f1cd9717285b6d882":"":"":"88909016":"FAIL":"":0

AES-128-CCM test vector NIST #7 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"19ebfde2d5468ba0a3031bde629b11fd":"5a8aa485c316e9403aff859fbb":"":"":"fb04dc5a44c6bb000f2440f5154364b4":"":"":0

AES-128-CCM test vector NIST #8 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"19ebfde2d5468ba0a3031bde629b11fd":"a16a2e741f1cd9717285b6d882":"":"":"5447075bf42a59b91f08064738b015ab":"FAIL":"":0

AES-128-CCM test vector NIST #9 (P=24, N=7, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"19ebfde2d5468ba0a3031bde629b11fd":"5a8aa485c316e9":"":"a90e8ea44085ced791b2fdb7fd44b5cf0bd7d27718029bb7":"03e1fa6b":"":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22":0

AES-128-CCM test vector NIST #10 (P=24, N=7, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"19ebfde2d5468ba0a3031bde629b11fd":"31f8fa25827d48":"":"50aafe0578c115c4a8e126ff7b3ccb64dce8ccaa8ceda69f":"23e5d81c":"FAIL":"":0

AES-128-CCM test vector NIST #11 (P=24, N=7, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"197afb02ffbd8f699dacae87094d5243":"5a8aa485c316e9":"":"24ab9eeb0e5508cae80074f1070ee188a637171860881f1f":"2d9a3fbc210595b7b8b1b41523111a8e":"":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22":0

AES-128-CCM test vector NIST #12 (P=24, N=7, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"197afb02ffbd8f699dacae87094d5243":"31f8fa25827d48":"":"7ebfda6fa5da1dbffd82dc29b875798fbcef8ba0084fbd24":"63af747cc88a001fa94e060290f209c4":"FAIL":"":0

AES-128-CCM test vector NIST #13 (P=24, N=13, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"197afb02ffbd8f699dacae87094d5243":"5a8aa485c316e9403aff859fbb":"":"4a550134f94455979ec4bf89ad2bd80d25a77ae94e456134":"a3e138b9":"":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697":0

AES-128-CCM test vector NIST #14 (P=24, N=13, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"197afb02ffbd8f699dacae87094d5243":"49004912fdd7269279b1f06a89":"":"************************************************":"091a5ae9":"FAIL":"":0

AES-128-CCM test vector NIST #15 (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"90929a4b0ac65b350ad1591611fe4829":"5a8aa485c316e9403aff859fbb":"":"4bfe4e35784f0a65b545477e5e2f4bae0e1e6fa717eaf2cb":"6a9a970b9beb2ac1bd4fd62168f8378a":"":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697":0

AES-128-CCM test vector NIST #16 (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"90929a4b0ac65b350ad1591611fe4829":"49004912fdd7269279b1f06a89":"":"0c56a503aa2c12e87450d45a7b714db980fd348f327c0065":"a65666144994bad0c8195bcb4ade1337":"FAIL":"":0

AES-128-CCM test vector NIST #17 (P=0, N=7, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"90929a4b0ac65b350ad1591611fe4829":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":"":"782e4318":"":"":0

AES-128-CCM test vector NIST #18 (P=0, N=7, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"90929a4b0ac65b350ad1591611fe4829":"a265480ca88d5f":"a2248a882ecbf850daf91933a389e78e81623d233dfd47bf8321361a38f138fe":"":"a04f270a":"FAIL":"":0

AES-128-CCM test vector NIST #19 (P=0, N=7, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"6a798d7c5e1a72b43e20ad5c7b08567b":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":"":"41b476013f45e4a781f253a6f3b1e530":"":"":0

AES-128-CCM test vector NIST #20 (P=0, N=7, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"6a798d7c5e1a72b43e20ad5c7b08567b":"a265480ca88d5f":"a2248a882ecbf850daf91933a389e78e81623d233dfd47bf8321361a38f138fe":"":"f9f018fcd125822616083fffebc4c8e6":"FAIL":"":0

AES-128-CCM test vector NIST #21 (P=0, N=13, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"6a798d7c5e1a72b43e20ad5c7b08567b":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":"":"9f69f24f":"":"":0

AES-128-CCM test vector NIST #22 (P=0, N=13, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"6a798d7c5e1a72b43e20ad5c7b08567b":"8739b4bea1a099fe547499cbc6":"f6107696edb332b2ea059d8860fee26be42e5e12e1a4f79a8d0eafce1b2278a7":"":"e17afaa4":"FAIL":"":0

AES-128-CCM test vector NIST #23 (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"f9fdca4ac64fe7f014de0f43039c7571":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":"":"1859ac36a40a6b28b34266253627797a":"":"":0

AES-128-CCM test vector NIST #24 (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"f9fdca4ac64fe7f014de0f43039c7571":"8739b4bea1a099fe547499cbc6":"f6107696edb332b2ea059d8860fee26be42e5e12e1a4f79a8d0eafce1b2278a7":"":"edf8b46eb69ac0044116019dec183072":"FAIL":"":0

AES-128-CCM test vector NIST #25 (P=24, N=7, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"f9fdca4ac64fe7f014de0f43039c7571":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":"6be31860ca271ef448de8f8d8b39346daf4b81d7e92d65b3":"38f125fa":"":"a265480ca88d5f536db0dc6abc40faf0d05be7a966977768":0

AES-128-CCM test vector NIST #26 (P=24, N=7, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"f9fdca4ac64fe7f014de0f43039c7571":"fdd2d6f503c915":"5b92394f21ddc3ad49d9b0881b829a5935cb3a4d23e292a62fb66b5e7ab7020e":"4cc57a9927a6bc401441870d3193bf89ebd163f5c01501c7":"28a66b69":"FAIL":"":0

AES-128-CCM test vector NIST #27 (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"a7aa635ea51b0bb20a092bd5573e728c":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":"b351ab96b2e45515254558d5212673ee6c776d42dbca3b51":"2cf3a20b7fd7c49e6e79bef475c2906f":"":"a265480ca88d5f536db0dc6abc40faf0d05be7a966977768":0

AES-128-CCM test vector NIST #28 (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"a7aa635ea51b0bb20a092bd5573e728c":"fdd2d6f503c915":"5b92394f21ddc3ad49d9b0881b829a5935cb3a4d23e292a62fb66b5e7ab7020e":"df1a5285caa41b4bb47f6e5ceceba4e82721828d68427a30":"81d18ca149d6766bfaccec88f194eb5b":"FAIL":"":0

AES-128-CCM test vector NIST #29 (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"a7aa635ea51b0bb20a092bd5573e728c":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":"934f893824e880f743d196b22d1f340a52608155087bd28a":"c25e5329":"":"8739b4bea1a099fe547499cbc6d1b13d849b8084c9b6acc5":0

AES-128-CCM test vector NIST #30 (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"a7aa635ea51b0bb20a092bd5573e728c":"0812757ad0cc4d17c4cfe7a642":"ec6c44a7e94e51a3ca6dee229098391575ec7213c85267fbf7492fdbeee61b10":"f43ba9d834ad85dfab3f1c0c27c3441fe4e411a38a261a65":"59b3b3ee":"FAIL":"":0

AES-128-CCM test vector NIST #31 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"26511fb51fcfa75cb4b44da75a6e5a0e":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":"50038b5fdd364ee747b70d00bd36840ece4ea19998123375":"c0a458bfcafa3b2609afe0f825cbf503":"":"8739b4bea1a099fe547499cbc6d1b13d849b8084c9b6acc5":0

AES-128-CCM test vector NIST #32 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"26511fb51fcfa75cb4b44da75a6e5a0e":"0812757ad0cc4d17c4cfe7a642":"ec6c44a7e94e51a3ca6dee229098391575ec7213c85267fbf7492fdbeee61b10":"78ed8ff6b5a1255d0fbd0a719a9c27b059ff5f83d0c4962c":"390042ba8bb5f6798dab01c5afad7306":"FAIL":"":0

AES-192-CCM test vector NIST #1 (P=0, N=7, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"c98ad7f38b2c7e970c9b965ec87a08208384718f78206c6c":"5a8aa485c316e9":"":"":"9d4b7f3b":"":"":0

AES-192-CCM test vector NIST #2 (P=0, N=7, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"c98ad7f38b2c7e970c9b965ec87a08208384718f78206c6c":"3796cf51b87266":"":"":"80745de9":"FAIL":"":0

AES-192-CCM test vector NIST #3 (P=0, N=7, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"4bb3c4a4f893ad8c9bdc833c325d62b3d3ad1bccf9282a65":"5a8aa485c316e9":"":"":"17223038fa99d53681ca1beabe78d1b4":"":"":0

AES-192-CCM test vector NIST #4 (P=0, N=7, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"4bb3c4a4f893ad8c9bdc833c325d62b3d3ad1bccf9282a65":"3796cf51b87266":"":"":"d0e1eeef4d2a264536bb1c2c1bde7c35":"FAIL":"":0

AES-192-CCM test vector NIST #5 (P=0, N=13, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"4bb3c4a4f893ad8c9bdc833c325d62b3d3ad1bccf9282a65":"5a8aa485c316e9403aff859fbb":"":"":"fe69ed84":"":"":0

AES-192-CCM test vector NIST #6 (P=0, N=13, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"4bb3c4a4f893ad8c9bdc833c325d62b3d3ad1bccf9282a65":"a16a2e741f1cd9717285b6d882":"":"":"db7ffc82":"FAIL":"":0

AES-192-CCM test vector NIST #7 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"19ebfde2d5468ba0a3031bde629b11fd4094afcb205393fa":"5a8aa485c316e9403aff859fbb":"":"":"0c66a8e547ed4f8c2c9a9a1eb5d455b9":"":"":0

AES-192-CCM test vector NIST #8 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"19ebfde2d5468ba0a3031bde629b11fd4094afcb205393fa":"a16a2e741f1cd9717285b6d882":"":"":"38757b3a61a4dc97ca3ab88bf1240695":"FAIL":"":0

AES-192-CCM test vector NIST #9 (P=24, N=7, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"19ebfde2d5468ba0a3031bde629b11fd4094afcb205393fa":"5a8aa485c316e9":"":"411986d04d6463100bff03f7d0bde7ea2c3488784378138c":"ddc93a54":"":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22":0

AES-192-CCM test vector NIST #10 (P=24, N=7, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"19ebfde2d5468ba0a3031bde629b11fd4094afcb205393fa":"31f8fa25827d48":"":"32b649ab56162e55d4148a1292d6a225a988eb1308298273":"b6889036":"FAIL":"":0

AES-192-CCM test vector NIST #11 (P=24, N=7, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"197afb02ffbd8f699dacae87094d524324576b99844f75e1":"5a8aa485c316e9":"":"cba4b4aeb85f0492fd8d905c4a6d8233139833373ef188a8":"c5a5ebecf7ac8607fe412189e83d9d20":"":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22":0

AES-192-CCM test vector NIST #12 (P=24, N=7, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"197afb02ffbd8f699dacae87094d524324576b99844f75e1":"31f8fa25827d48":"":"ca62713728b5c9d652504b0ae8fd4fee5d297ee6a8d19cb6":"e699f15f14d34dcaf9ba8ed4b877c97d":"FAIL":"":0

AES-192-CCM test vector NIST #13 (P=24, N=13, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"197afb02ffbd8f699dacae87094d524324576b99844f75e1":"5a8aa485c316e9403aff859fbb":"":"042653c674ef2a90f7fb11d30848e530ae59478f1051633a":"34fad277":"":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697":0

AES-192-CCM test vector NIST #14 (P=24, N=13, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"197afb02ffbd8f699dacae87094d524324576b99844f75e1":"49004912fdd7269279b1f06a89":"":"1902d9769a7ba3d3268e1257395c8c2e5f98eef295dcbfa5":"a35df775":"FAIL":"":0

AES-192-CCM test vector NIST #15 (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"90929a4b0ac65b350ad1591611fe48297e03956f6083e451":"5a8aa485c316e9403aff859fbb":"":"a5b7d8cca2069908d1ed88e6a9fe2c9bede3131dad54671e":"a7ade30a07d185692ab0ebdf4c78cf7a":"":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697":0

AES-192-CCM test vector NIST #16 (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"90929a4b0ac65b350ad1591611fe48297e03956f6083e451":"49004912fdd7269279b1f06a89":"":"9a98617fb97a0dfe466be692272dcdaec1c5443a3b51312e":"f042c86363cc05afb98c66e16be8a445":"FAIL":"":0

AES-192-CCM test vector NIST #17 (P=0, N=7, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"90929a4b0ac65b350ad1591611fe48297e03956f6083e451":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":"":"1d089a5f":"":"":0

AES-192-CCM test vector NIST #18 (P=0, N=7, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"90929a4b0ac65b350ad1591611fe48297e03956f6083e451":"a265480ca88d5f":"a2248a882ecbf850daf91933a389e78e81623d233dfd47bf8321361a38f138fe":"":"2f46022a":"FAIL":"":0

AES-192-CCM test vector NIST #19 (P=0, N=7, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"6a798d7c5e1a72b43e20ad5c7b08567b12ab744b61c070e2":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":"":"5280a2137fee3deefcfe9b63a1199fb3":"":"":0

AES-192-CCM test vector NIST #20 (P=0, N=7, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"6a798d7c5e1a72b43e20ad5c7b08567b12ab744b61c070e2":"a265480ca88d5f":"a2248a882ecbf850daf91933a389e78e81623d233dfd47bf8321361a38f138fe":"":"d40a7318c5f2d82f838c0beeefe0d598":"FAIL":"":0

AES-192-CCM test vector NIST #21 (P=0, N=13, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"6a798d7c5e1a72b43e20ad5c7b08567b12ab744b61c070e2":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":"":"5e0eaebd":"":"":0

AES-192-CCM test vector NIST #22 (P=0, N=13, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"6a798d7c5e1a72b43e20ad5c7b08567b12ab744b61c070e2":"8739b4bea1a099fe547499cbc6":"f6107696edb332b2ea059d8860fee26be42e5e12e1a4f79a8d0eafce1b2278a7":"":"71b7fc33":"FAIL":"":0

AES-192-CCM test vector NIST #23 (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"f9fdca4ac64fe7f014de0f43039c757194d544ce5d15eed4":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":"":"d07ccf9fdc3d33aa94cda3d230da707c":"":"":0

AES-192-CCM test vector NIST #24 (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"f9fdca4ac64fe7f014de0f43039c757194d544ce5d15eed4":"8739b4bea1a099fe547499cbc6":"f6107696edb332b2ea059d8860fee26be42e5e12e1a4f79a8d0eafce1b2278a7":"":"65fe32b649dc328c9f531584897e85b3":"FAIL":"":0

AES-192-CCM test vector NIST #25 (P=24, N=7, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"f9fdca4ac64fe7f014de0f43039c757194d544ce5d15eed4":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":"9f6ca4af9b159148c889a6584d1183ea26e2614874b05045":"75dea8d1":"":"a265480ca88d5f536db0dc6abc40faf0d05be7a966977768":0

AES-192-CCM test vector NIST #26 (P=24, N=7, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"f9fdca4ac64fe7f014de0f43039c757194d544ce5d15eed4":"fdd2d6f503c915":"5b92394f21ddc3ad49d9b0881b829a5935cb3a4d23e292a62fb66b5e7ab7020e":"84d8212e9cfc2121252baa3b065b1edcf50497b9594db1eb":"d7965825":"FAIL":"":0

AES-192-CCM test vector NIST #27 (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"a7aa635ea51b0bb20a092bd5573e728ccd4b3e8cdd2ab33d":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":"6aab64c4787599d8f213446beadb16e08dba60e97f56dbd1":"4d1d980d6fe0fb44b421992662b97975":"":"a265480ca88d5f536db0dc6abc40faf0d05be7a966977768":0

AES-192-CCM test vector NIST #28 (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"a7aa635ea51b0bb20a092bd5573e728ccd4b3e8cdd2ab33d":"fdd2d6f503c915":"5b92394f21ddc3ad49d9b0881b829a5935cb3a4d23e292a62fb66b5e7ab7020e":"4980b2ee49b1aaf393175f5ab9bae95ec7904557dfa20660":"3c51d36c826f01384100886198a7f6a3":"FAIL":"":0

AES-192-CCM test vector NIST #29 (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"a7aa635ea51b0bb20a092bd5573e728ccd4b3e8cdd2ab33d":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":"16e543d0e20615ff0df15acd9927ddfe40668a54bb854ccc":"c25e9fce":"":"8739b4bea1a099fe547499cbc6d1b13d849b8084c9b6acc5":0

AES-192-CCM test vector NIST #30 (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"a7aa635ea51b0bb20a092bd5573e728ccd4b3e8cdd2ab33d":"0812757ad0cc4d17c4cfe7a642":"ec6c44a7e94e51a3ca6dee229098391575ec7213c85267fbf7492fdbeee61b10":"df35b109caf690656ae278bbd8f8bba687a2ce11b105dae9":"8ecedb3e":"FAIL":"":0

AES-192-CCM test vector NIST #31 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"26511fb51fcfa75cb4b44da75a6e5a0eb8d9c8f3b906f886":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":"c5b0b2ef17498c5570eb335df4588032958ba3d69bf6f317":"8464a6f7fa2b76744e8e8d95691cecb8":"":"8739b4bea1a099fe547499cbc6d1b13d849b8084c9b6acc5":0

AES-192-CCM test vector NIST #32 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"26511fb51fcfa75cb4b44da75a6e5a0eb8d9c8f3b906f886":"0812757ad0cc4d17c4cfe7a642":"ec6c44a7e94e51a3ca6dee229098391575ec7213c85267fbf7492fdbeee61b10":"d1f0518929f4ae2f0543de2a7dfe4bb0110bb3057e524a1c":"06bd6dc2e6bcc3436cffb969ae900388":"FAIL":"":0

AES-256-CCM test vector NIST #1 (P=0, N=7, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"eda32f751456e33195f1f499cf2dc7c97ea127b6d488f211ccc5126fbb24afa6":"a544218dadd3c1":"":"":"469c90bb":"":"":0

AES-256-CCM test vector NIST #2 (P=0, N=7, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"eda32f751456e33195f1f499cf2dc7c97ea127b6d488f211ccc5126fbb24afa6":"d3d5424e20fbec":"":"":"46a908ed":"FAIL":"":0

AES-256-CCM test vector NIST #3 (P=0, N=7, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"e1b8a927a95efe94656677b692662000278b441c79e879dd5c0ddc758bdc9ee8":"a544218dadd3c1":"":"":"8207eb14d33855a52acceed17dbcbf6e":"":"":0

AES-256-CCM test vector NIST #4 (P=0, N=7, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"e1b8a927a95efe94656677b692662000278b441c79e879dd5c0ddc758bdc9ee8":"d3d5424e20fbec":"":"":"60f8e127cb4d30db6df0622158cd931d":"FAIL":"":0

AES-256-CCM test vector NIST #5 (P=0, N=13, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"e1b8a927a95efe94656677b692662000278b441c79e879dd5c0ddc758bdc9ee8":"a544218dadd3c10583db49cf39":"":"":"8a19a133":"":"":0

AES-256-CCM test vector NIST #6 (P=0, N=13, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"e1b8a927a95efe94656677b692662000278b441c79e879dd5c0ddc758bdc9ee8":"3c0e2815d37d844f7ac240ba9d":"":"":"2e317f1b":"FAIL":"":0

AES-256-CCM test vector NIST #7 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"af063639e66c284083c5cf72b70d8bc277f5978e80d9322d99f2fdc718cda569":"a544218dadd3c10583db49cf39":"":"":"97e1a8dd4259ccd2e431e057b0397fcf":"":"":0

AES-256-CCM test vector NIST #8 (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"af063639e66c284083c5cf72b70d8bc277f5978e80d9322d99f2fdc718cda569":"3c0e2815d37d844f7ac240ba9d":"":"":"5a9596c511ea6a8671adefc4f2157d8b":"FAIL":"":0

AES-256-CCM test vector NIST #9 (P=24, N=7, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"af063639e66c284083c5cf72b70d8bc277f5978e80d9322d99f2fdc718cda569":"a544218dadd3c1":"":"64a1341679972dc5869fcf69b19d5c5ea50aa0b5e985f5b7":"22aa8d59":"":"d3d5424e20fbec43ae495353ed830271515ab104f8860c98":0

AES-256-CCM test vector NIST #10 (P=24, N=7, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"af063639e66c284083c5cf72b70d8bc277f5978e80d9322d99f2fdc718cda569":"bfcda8b5a2d0d2":"":"c5b7f802bffc498c1626e3774f1d9f94045dfd8e1a10a202":"77d00a75":"FAIL":"":0

AES-256-CCM test vector NIST #11 (P=24, N=7, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"f7079dfa3b5c7b056347d7e437bcded683abd6e2c9e069d333284082cbb5d453":"a544218dadd3c1":"":"bc51c3925a960e7732533e4ef3a4f69ee6826de952bcb0fd":"374f3bb6db8377ebfc79674858c4f305":"":"d3d5424e20fbec43ae495353ed830271515ab104f8860c98":0

AES-256-CCM test vector NIST #12 (P=24, N=7, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"f7079dfa3b5c7b056347d7e437bcded683abd6e2c9e069d333284082cbb5d453":"bfcda8b5a2d0d2":"":"afa1fa8e8a70e26b02161150556d604101fdf423f332c336":"3275f2a4907d51b734fe7238cebbd48f":"FAIL":"":0

AES-256-CCM test vector NIST #13 (P=24, N=13, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"f7079dfa3b5c7b056347d7e437bcded683abd6e2c9e069d333284082cbb5d453":"a544218dadd3c10583db49cf39":"":"63e00d30e4b08fd2a1cc8d70fab327b2368e77a93be4f412":"3d14fb3f":"":"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e":0

AES-256-CCM test vector NIST #14 (P=24, N=13, A=0, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"f7079dfa3b5c7b056347d7e437bcded683abd6e2c9e069d333284082cbb5d453":"894dcaa61008eb8fb052c60d41":"":"bb5425b3869b76856ec58e39886fb6f6f2ac13fe44cb132d":"8d0c0099":"FAIL":"":0

AES-256-CCM test vector NIST #15 (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"1b0e8df63c57f05d9ac457575ea764524b8610ae5164e6215f426f5a7ae6ede4":"a544218dadd3c10583db49cf39":"":"f0050ad16392021a3f40207bed3521fb1e9f808f49830c42":"3a578d179902f912f9ea1afbce1120b3":"":"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e":0

AES-256-CCM test vector NIST #16 (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"1b0e8df63c57f05d9ac457575ea764524b8610ae5164e6215f426f5a7ae6ede4":"894dcaa61008eb8fb052c60d41":"":"c408190d0fbf5034f83b24a8ed9657331a7ce141de4fae76":"9084607b83bd06e6442eac8dacf583cc":"FAIL":"":0

AES-256-CCM test vector NIST #17 (P=0, N=7, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"1b0e8df63c57f05d9ac457575ea764524b8610ae5164e6215f426f5a7ae6ede4":"a544218dadd3c1":"d3d5424e20fbec43ae495353ed830271515ab104f8860c988d15b6d36c038eab":"":"92d00fbe":"":"":0

AES-256-CCM test vector NIST #18 (P=0, N=7, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"1b0e8df63c57f05d9ac457575ea764524b8610ae5164e6215f426f5a7ae6ede4":"78c46e3249ca28":"232e957c65ffa11988e830d4617d500f1c4a35c1221f396c41ab214f074ca2dc":"":"9143e5c4":"FAIL":"":0

AES-256-CCM test vector NIST #19 (P=0, N=7, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"a4bc10b1a62c96d459fbaf3a5aa3face7313bb9e1253e696f96a7a8e36801088":"a544218dadd3c1":"d3d5424e20fbec43ae495353ed830271515ab104f8860c988d15b6d36c038eab":"":"93af11a08379eb37a16aa2837f09d69d":"":"":0

AES-256-CCM test vector NIST #20 (P=0, N=7, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"a4bc10b1a62c96d459fbaf3a5aa3face7313bb9e1253e696f96a7a8e36801088":"78c46e3249ca28":"232e957c65ffa11988e830d4617d500f1c4a35c1221f396c41ab214f074ca2dc":"":"d19b0c14ec686a7961ca7c386d125a65":"FAIL":"":0

AES-256-CCM test vector NIST #21 (P=0, N=13, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"a4bc10b1a62c96d459fbaf3a5aa3face7313bb9e1253e696f96a7a8e36801088":"a544218dadd3c10583db49cf39":"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e09a1005e024f6907":"":"866d4227":"":"":0

AES-256-CCM test vector NIST #22 (P=0, N=13, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"a4bc10b1a62c96d459fbaf3a5aa3face7313bb9e1253e696f96a7a8e36801088":"e8de970f6ee8e80ede933581b5":"89f8b068d34f56bc49d839d8e47b347e6dae737b903b278632447e6c0485d26a":"":"94cb1127":"FAIL":"":0

AES-256-CCM test vector NIST #23 (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"8c5cf3457ff22228c39c051c4e05ed4093657eb303f859a9d4b0f8be0127d88a":"a544218dadd3c10583db49cf39":"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e09a1005e024f6907":"":"867b0d87cf6e0f718200a97b4f6d5ad5":"":"":0

AES-256-CCM test vector NIST #24 (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"8c5cf3457ff22228c39c051c4e05ed4093657eb303f859a9d4b0f8be0127d88a":"e8de970f6ee8e80ede933581b5":"89f8b068d34f56bc49d839d8e47b347e6dae737b903b278632447e6c0485d26a":"":"677a040d46ee3f2b7838273bdad14f16":"FAIL":"":0

AES-256-CCM test vector NIST #25 (P=24, N=7, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"8c5cf3457ff22228c39c051c4e05ed4093657eb303f859a9d4b0f8be0127d88a":"a544218dadd3c1":"d3d5424e20fbec43ae495353ed830271515ab104f8860c988d15b6d36c038eab":"c2fe12658139f5d0dd22cadf2e901695b579302a72fc5608":"3ebc7720":"":"78c46e3249ca28e1ef0531d80fd37c124d9aecb7be6668e3":0

AES-256-CCM test vector NIST #26 (P=24, N=7, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"8c5cf3457ff22228c39c051c4e05ed4093657eb303f859a9d4b0f8be0127d88a":"6ba004fd176791":"5a053b2a1bb87e85d56527bfcdcd3ecafb991bb10e4c862bb0751c700a29f54b":"94748ba81229e53c38583a8564b23ebbafc6f6efdf4c2a81":"c44db2c9":"FAIL":"":0

AES-256-CCM test vector NIST #27 (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"705334e30f53dd2f92d190d2c1437c8772f940c55aa35e562214ed45bd458ffe":"a544218dadd3c1":"d3d5424e20fbec43ae495353ed830271515ab104f8860c988d15b6d36c038eab":"3341168eb8c48468c414347fb08f71d2086f7c2d1bd581ce":"1ac68bd42f5ec7fa7e068cc0ecd79c2a":"":"78c46e3249ca28e1ef0531d80fd37c124d9aecb7be6668e3":0

AES-256-CCM test vector NIST #28 (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"705334e30f53dd2f92d190d2c1437c8772f940c55aa35e562214ed45bd458ffe":"6ba004fd176791":"5a053b2a1bb87e85d56527bfcdcd3ecafb991bb10e4c862bb0751c700a29f54b":"d543acda712b898cbb27b8f598b2e4438ce587a836e27851":"47c3338a2400809e739b63ba8227d2f9":"FAIL":"":0

AES-256-CCM test vector NIST #29 (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"705334e30f53dd2f92d190d2c1437c8772f940c55aa35e562214ed45bd458ffe":"a544218dadd3c10583db49cf39":"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e09a1005e024f6907":"c0ea400b599561e7905b99262b4565d5c3dc49fad84d7c69":"ef891339":"":"e8de970f6ee8e80ede933581b5bcf4d837e2b72baa8b00c3":0

AES-256-CCM test vector NIST #30 (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"705334e30f53dd2f92d190d2c1437c8772f940c55aa35e562214ed45bd458ffe":"8fa501c5dd9ac9b868144c9fa5":"5bb40e3bb72b4509324a7edc852f72535f1f6283156e63f6959ffaf39dcde800":"60871e03ea0eb968536c99f926ea24ef43d41272ad9fb7f6":"3d488623":"FAIL":"":0

AES-256-CCM test vector NIST #31 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"314a202f836f9f257e22d8c11757832ae5131d357a72df88f3eff0ffcee0da4e":"a544218dadd3c10583db49cf39":"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e09a1005e024f6907":"8d34cdca37ce77be68f65baf3382e31efa693e63f914a781":"367f30f2eaad8c063ca50795acd90203":"":"e8de970f6ee8e80ede933581b5bcf4d837e2b72baa8b00c3":0

AES-256-CCM test vector NIST #32 (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_AES_C:MBEDTLS_CCM_C:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"314a202f836f9f257e22d8c11757832ae5131d357a72df88f3eff0ffcee0da4e":"8fa501c5dd9ac9b868144c9fa5":"5bb40e3bb72b4509324a7edc852f72535f1f6283156e63f6959ffaf39dcde800":"516c0095cc3d85fd55e48da17c592e0c7014b9daafb82bdc":"4b41096dfdbe9cc1ab610f8f3e038d16":"FAIL":"":0

Camellia-CCM test vector RFC 5528 #1
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"00000003020100A0A1A2A3A4A5":"0001020304050607":"BA737185E719310492F38A5F1251DA55FAFBC949848A0D":"FCAECE746B3DB9AD":"":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E":0

Camellia-CCM test vector RFC 5528 #2
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"00000004030201A0A1A2A3A4A5":"0001020304050607":"5D2564BF8EAFE1D99526EC016D1BF0424CFBD2CD62848F33":"60B2295DF24283E8":"":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F":0

Camellia-CCM test vector RFC 5528 #3
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"00000005040302A0A1A2A3A4A5":"0001020304050607":"81F663D6C7787817F9203608B982AD15DC2BBD87D756F79204":"F551D6682F23AA46":"":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F20":0

Camellia-CCM test vector RFC 5528 #4
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"00000006050403A0A1A2A3A4A5":"000102030405060708090A0B":"CAEF1E827211B08F7BD90F08C77288C070A4A0":"8B3A933A63E497A0":"":"0C0D0E0F101112131415161718191A1B1C1D1E":0

Camellia-CCM test vector RFC 5528 #5
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"00000007060504A0A1A2A3A4A5":"000102030405060708090A0B":"2AD3BAD94FC52E92BE438E827C1023B96A8A7725":"8FA17BA7F331DB09":"":"0C0D0E0F101112131415161718191A1B1C1D1E1F":0

Camellia-CCM test vector RFC 5528 #6
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"00000008070605A0A1A2A3A4A5":"000102030405060708090A0B":"FEA5480BA53FA8D3C34422AACE4DE67FFA3BB73BAB":"AB36A1EE4FE0FE28":"":"0C0D0E0F101112131415161718191A1B1C1D1E1F20":0

Camellia-CCM test vector RFC 5528 #7
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"00000009080706A0A1A2A3A4A5":"0001020304050607":"54532026E54C119A8D36D9EC6E1ED97416C8708C4B5C2C":"ACAFA3BCCF7A4EBF9573":"":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E":0

Camellia-CCM test vector RFC 5528 #8
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"0000000A090807A0A1A2A3A4A5":"0001020304050607":"8AD19B001A87D148F4D92BEF34525CCCE3A63C6512A6F575":"7388E4913EF14701F441":"":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F":0

Camellia-CCM test vector RFC 5528 #9
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"0000000B0A0908A0A1A2A3A4A5":"0001020304050607":"5DB08D62407E6E31D60F9CA2C60474219AC0BE50C0D4A57787":"94D6E230CD25C9FEBF87":"":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F20":0

Camellia-CCM test vector RFC 5528 #10
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"0000000C0B0A09A0A1A2A3A4A5":"000102030405060708090A0B":"DB118CCEC1B8761C877CD8963A67D6F3BBBC5C":"D09299EB11F312F23237":"":"0C0D0E0F101112131415161718191A1B1C1D1E":0

Camellia-CCM test vector RFC 5528 #11
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"0000000D0C0B0AA0A1A2A3A4A5":"000102030405060708090A0B":"7CC83D8DC49103525B483DC5CA7EA9AB812B7056":"079DAFFADA16CCCF2C4E":"":"0C0D0E0F101112131415161718191A1B1C1D1E1F":0

Camellia-CCM test vector RFC 5528 #12
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"0000000E0D0C0BA0A1A2A3A4A5":"000102030405060708090A0B":"2CD35B8820D23E7AA351B0E92FC79367238B2CC748":"CBB94C2947793D64AF75":"":"0C0D0E0F101112131415161718191A1B1C1D1E1F20":0

Camellia-CCM test vector RFC 5528 #13
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"D75C2778078CA93D971F96FDE720F4CD":"00A970110E1927B160B6A31C1C":"6B7F464507FAE496":"A435D727348DDD22907F7EB8F5FDBB4D939DA6524DB4F6":"4558C02D25B127EE":"":"C6B5F3E6CA2311AEF7472B203E735EA561ADB17D56C5A3":0

Camellia-CCM test vector RFC 5528 #14
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"D75C2778078CA93D971F96FDE720F4CD":"0083CD8CE0CB42B160B6A31C1C":"986605B43DF15DE7":"8AE052508FBECA932E346F05E0DC0DFBCF939EAFFA3E587C":"867D6E1C48703806":"":"01F6CE6764C574483BB02E6BBF1E0ABD26A22572B4D80EE7":0

Camellia-CCM test vector RFC 5528 #15
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"D75C2778078CA93D971F96FDE720F4CD":"005F54950B18F2B160B6A31C1C":"48F2E7E1A7671A51":"08B67EE21C8BF26E473E408599E9C0836D6AF0BB18DF55466C":"A80878A790476DE5":"":"CDF1D8406FC2E9014953897005FBFB8BA57276F92404608E08":0

Camellia-CCM test vector RFC 5528 #16
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"D75C2778078CA93D971F96FDE720F4CD":"00EC600863319AB160B6A31C1C":"DE97DF3B8CBD6D8E5030DA4C":"63B78B4967B19EDBB733CD1114F64EB2260893":"68C354828D950CC5":"":"B005DCFA0B59181426A961685A993D8C43185B":0

Camellia-CCM test vector RFC 5528 #17
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"D75C2778078CA93D971F96FDE720F4CD":"0060CFF1A31EA1B160B6A31C1C":"A5EE93E457DF05466E782DCF":"0BC6BBE2A8B909F4629EE6DC148DA44410E18AF4":"3147383276F66A9F":"":"2E20211298105F129D5ED95B93F72D30B2FACCD7":0

Camellia-CCM test vector RFC 5528 #18
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"D75C2778078CA93D971F96FDE720F4CD":"000F85CD995C97B160B6A31C1C":"24AA1BF9A5CD876182A25074":"222AD632FA31D6AF970C345F7E77CA3BD0DC25B340":"A1A3D31F8D4B44B7":"":"2645941E75632D3491AF0FC0C9876C3BE4AA7468C9":0

Camellia-CCM test vector RFC 5528 #19
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"D75C2778078CA93D971F96FDE720F4CD":"00C29B2CAAC4CDB160B6A31C1C":"691946B9CA07BE87":"05B8E1B9C49CFD56CF130AA6251DC2ECC06CCC508FE697":"A0066D57C84BEC182768":"":"070135A6437C9DB120CD61D8F6C39C3EA125FD95A0D23D":0

Camellia-CCM test vector RFC 5528 #20
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"D75C2778078CA93D971F96FDE720F4CD":"002C6B7595EE62B160B6A31C1C":"D0C54ECB84627DC4":"54CEB968DEE23611575EC003DFAA1CD48849BDF5AE2EDB6B":"7FA775B150ED4383C5A9":"":"C8C0880E6C636E20093DD6594217D2E18877DB264E71A5CC":0

Camellia-CCM test vector RFC 5528 #21
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"D75C2778078CA93D971F96FDE720F4CD":"00C53CD4C2AA24B160B6A31C1C":"E285E0E4808CDA3D":"B1404546BF667210CA28E309B39BD6CA7E9FC8285FE698D43C":"D20A02E0BDCAED2010D3":"":"F75DAA0710C4E64297794DC2B7D2A20757B1AA4E448002FFAB":0

Camellia-CCM test vector RFC 5528 #22
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"D75C2778078CA93D971F96FDE720F4CD":"00BEE9267FBADCB160B6A31C1C":"6CAEF9941141570D7C813405":"94C8959C11569A297831A721005857AB61B87A":"2DEA0936B6EB5F625F5D":"":"C238822FAC5F98FF929405B0AD127A4E41854E":0

Camellia-CCM test vector RFC 5528 #23
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"D75C2778078CA93D971F96FDE720F4CD":"00DFA8B1245007B160B6A31C1C":"36A52CF16B19A2037AB7011E":"5869E3AAD2447C74E0FC05F9A4EA74577F4DE8CA":"8924764296AD04119CE7":"":"4DBF3E774AD245E5D5891F9D1C32A0AE022C85D7":0

Camellia-CCM test vector RFC 5528 #24
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
auth_crypt_tv:MBEDTLS_CIPHER_CAMELLIA_128_CCM:"D75C2778078CA93D971F96FDE720F4CD":"003B8FD8D3A937B160B6A31C1C":"A4D499F78419728C19178B0C":"4B198156393B0F7796086AAFB454F8C3F034CCA966":"945F1FCEA7E11BEE6A2F":"":"9DC9EDAE2FF5DF8636E8C6DE0EED55F7867E33337D":0

AES-128-CCM test vector NIST #1 PSA (P=0, N=7, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"4ae701103c63deca5b5a3939d7d05992":"5a8aa485c316e9":"":"":"02209f55":"":"":1

AES-128-CCM test vector NIST #2 PSA (P=0, N=7, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"4ae701103c63deca5b5a3939d7d05992":"3796cf51b87266":"":"":"9a04c241":"FAIL":"":1

AES-128-CCM test vector NIST #3 PSA (P=0, N=7, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"4bb3c4a4f893ad8c9bdc833c325d62b3":"5a8aa485c316e9":"":"":"75d582db43ce9b13ab4b6f7f14341330":"":"":1

AES-128-CCM test vector NIST #4 PSA (P=0, N=7, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"4bb3c4a4f893ad8c9bdc833c325d62b3":"3796cf51b87266":"":"":"3a65e03af37b81d05acc7ec1bc39deb0":"FAIL":"":1

AES-128-CCM test vector NIST #5 PSA (P=0, N=13, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"4bb3c4a4f893ad8c9bdc833c325d62b3":"5a8aa485c316e9403aff859fbb":"":"":"90156f3f":"":"":1

AES-128-CCM test vector NIST #6 PSA (P=0, N=13, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"4bb3c4a4f893ad8c9bdc833c325d62b3":"a16a2e741f1cd9717285b6d882":"":"":"88909016":"FAIL":"":1

AES-128-CCM test vector NIST #7 PSA (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"19ebfde2d5468ba0a3031bde629b11fd":"5a8aa485c316e9403aff859fbb":"":"":"fb04dc5a44c6bb000f2440f5154364b4":"":"":1

AES-128-CCM test vector NIST #8 PSA (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"19ebfde2d5468ba0a3031bde629b11fd":"a16a2e741f1cd9717285b6d882":"":"":"5447075bf42a59b91f08064738b015ab":"FAIL":"":1

AES-128-CCM test vector NIST #9 PSA (P=24, N=7, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"19ebfde2d5468ba0a3031bde629b11fd":"5a8aa485c316e9":"":"a90e8ea44085ced791b2fdb7fd44b5cf0bd7d27718029bb7":"03e1fa6b":"":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22":1

AES-128-CCM test vector NIST #10 PSA (P=24, N=7, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"19ebfde2d5468ba0a3031bde629b11fd":"31f8fa25827d48":"":"50aafe0578c115c4a8e126ff7b3ccb64dce8ccaa8ceda69f":"23e5d81c":"FAIL":"":1

AES-128-CCM test vector NIST #11 PSA (P=24, N=7, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"197afb02ffbd8f699dacae87094d5243":"5a8aa485c316e9":"":"24ab9eeb0e5508cae80074f1070ee188a637171860881f1f":"2d9a3fbc210595b7b8b1b41523111a8e":"":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22":1

AES-128-CCM test vector NIST #12 PSA (P=24, N=7, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"197afb02ffbd8f699dacae87094d5243":"31f8fa25827d48":"":"7ebfda6fa5da1dbffd82dc29b875798fbcef8ba0084fbd24":"63af747cc88a001fa94e060290f209c4":"FAIL":"":1

AES-128-CCM test vector NIST #13 PSA (P=24, N=13, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"197afb02ffbd8f699dacae87094d5243":"5a8aa485c316e9403aff859fbb":"":"4a550134f94455979ec4bf89ad2bd80d25a77ae94e456134":"a3e138b9":"":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697":1

AES-128-CCM test vector NIST #14 PSA (P=24, N=13, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"197afb02ffbd8f699dacae87094d5243":"49004912fdd7269279b1f06a89":"":"************************************************":"091a5ae9":"FAIL":"":1

AES-128-CCM test vector NIST #15 PSA (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"90929a4b0ac65b350ad1591611fe4829":"5a8aa485c316e9403aff859fbb":"":"4bfe4e35784f0a65b545477e5e2f4bae0e1e6fa717eaf2cb":"6a9a970b9beb2ac1bd4fd62168f8378a":"":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697":1

AES-128-CCM test vector NIST #16 PSA (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"90929a4b0ac65b350ad1591611fe4829":"49004912fdd7269279b1f06a89":"":"0c56a503aa2c12e87450d45a7b714db980fd348f327c0065":"a65666144994bad0c8195bcb4ade1337":"FAIL":"":1

AES-128-CCM test vector NIST #17 PSA (P=0, N=7, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"90929a4b0ac65b350ad1591611fe4829":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":"":"782e4318":"":"":1

AES-128-CCM test vector NIST #18 PSA (P=0, N=7, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"90929a4b0ac65b350ad1591611fe4829":"a265480ca88d5f":"a2248a882ecbf850daf91933a389e78e81623d233dfd47bf8321361a38f138fe":"":"a04f270a":"FAIL":"":1

AES-128-CCM test vector NIST #19 PSA (P=0, N=7, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"6a798d7c5e1a72b43e20ad5c7b08567b":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":"":"41b476013f45e4a781f253a6f3b1e530":"":"":1

AES-128-CCM test vector NIST #20 PSA (P=0, N=7, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"6a798d7c5e1a72b43e20ad5c7b08567b":"a265480ca88d5f":"a2248a882ecbf850daf91933a389e78e81623d233dfd47bf8321361a38f138fe":"":"f9f018fcd125822616083fffebc4c8e6":"FAIL":"":1

AES-128-CCM test vector NIST #21 PSA (P=0, N=13, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"6a798d7c5e1a72b43e20ad5c7b08567b":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":"":"9f69f24f":"":"":1

AES-128-CCM test vector NIST #22 PSA (P=0, N=13, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"6a798d7c5e1a72b43e20ad5c7b08567b":"8739b4bea1a099fe547499cbc6":"f6107696edb332b2ea059d8860fee26be42e5e12e1a4f79a8d0eafce1b2278a7":"":"e17afaa4":"FAIL":"":1

AES-128-CCM test vector NIST #23 PSA (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"f9fdca4ac64fe7f014de0f43039c7571":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":"":"1859ac36a40a6b28b34266253627797a":"":"":1

AES-128-CCM test vector NIST #24 PSA (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"f9fdca4ac64fe7f014de0f43039c7571":"8739b4bea1a099fe547499cbc6":"f6107696edb332b2ea059d8860fee26be42e5e12e1a4f79a8d0eafce1b2278a7":"":"edf8b46eb69ac0044116019dec183072":"FAIL":"":1

AES-128-CCM test vector NIST #25 PSA (P=24, N=7, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"f9fdca4ac64fe7f014de0f43039c7571":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":"6be31860ca271ef448de8f8d8b39346daf4b81d7e92d65b3":"38f125fa":"":"a265480ca88d5f536db0dc6abc40faf0d05be7a966977768":1

AES-128-CCM test vector NIST #26 PSA (P=24, N=7, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"f9fdca4ac64fe7f014de0f43039c7571":"fdd2d6f503c915":"5b92394f21ddc3ad49d9b0881b829a5935cb3a4d23e292a62fb66b5e7ab7020e":"4cc57a9927a6bc401441870d3193bf89ebd163f5c01501c7":"28a66b69":"FAIL":"":1

AES-128-CCM test vector NIST #27 PSA (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"a7aa635ea51b0bb20a092bd5573e728c":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":"b351ab96b2e45515254558d5212673ee6c776d42dbca3b51":"2cf3a20b7fd7c49e6e79bef475c2906f":"":"a265480ca88d5f536db0dc6abc40faf0d05be7a966977768":1

AES-128-CCM test vector NIST #28 PSA (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"a7aa635ea51b0bb20a092bd5573e728c":"fdd2d6f503c915":"5b92394f21ddc3ad49d9b0881b829a5935cb3a4d23e292a62fb66b5e7ab7020e":"df1a5285caa41b4bb47f6e5ceceba4e82721828d68427a30":"81d18ca149d6766bfaccec88f194eb5b":"FAIL":"":1

AES-128-CCM test vector NIST #29 PSA (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"a7aa635ea51b0bb20a092bd5573e728c":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":"934f893824e880f743d196b22d1f340a52608155087bd28a":"c25e5329":"":"8739b4bea1a099fe547499cbc6d1b13d849b8084c9b6acc5":1

AES-128-CCM test vector NIST #30 PSA (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"a7aa635ea51b0bb20a092bd5573e728c":"0812757ad0cc4d17c4cfe7a642":"ec6c44a7e94e51a3ca6dee229098391575ec7213c85267fbf7492fdbeee61b10":"f43ba9d834ad85dfab3f1c0c27c3441fe4e411a38a261a65":"59b3b3ee":"FAIL":"":1

AES-128-CCM test vector NIST #31 PSA (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"26511fb51fcfa75cb4b44da75a6e5a0e":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":"50038b5fdd364ee747b70d00bd36840ece4ea19998123375":"c0a458bfcafa3b2609afe0f825cbf503":"":"8739b4bea1a099fe547499cbc6d1b13d849b8084c9b6acc5":1

AES-128-CCM test vector NIST #32 PSA (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM
auth_crypt_tv:MBEDTLS_CIPHER_AES_128_CCM:"26511fb51fcfa75cb4b44da75a6e5a0e":"0812757ad0cc4d17c4cfe7a642":"ec6c44a7e94e51a3ca6dee229098391575ec7213c85267fbf7492fdbeee61b10":"78ed8ff6b5a1255d0fbd0a719a9c27b059ff5f83d0c4962c":"390042ba8bb5f6798dab01c5afad7306":"FAIL":"":1

AES-192-CCM test vector NIST #1 PSA (P=0, N=7, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"c98ad7f38b2c7e970c9b965ec87a08208384718f78206c6c":"5a8aa485c316e9":"":"":"9d4b7f3b":"":"":1

AES-192-CCM test vector NIST #2 PSA (P=0, N=7, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"c98ad7f38b2c7e970c9b965ec87a08208384718f78206c6c":"3796cf51b87266":"":"":"80745de9":"FAIL":"":1

AES-192-CCM test vector NIST #3 PSA (P=0, N=7, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"4bb3c4a4f893ad8c9bdc833c325d62b3d3ad1bccf9282a65":"5a8aa485c316e9":"":"":"17223038fa99d53681ca1beabe78d1b4":"":"":1

AES-192-CCM test vector NIST #4 PSA (P=0, N=7, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"4bb3c4a4f893ad8c9bdc833c325d62b3d3ad1bccf9282a65":"3796cf51b87266":"":"":"d0e1eeef4d2a264536bb1c2c1bde7c35":"FAIL":"":1

AES-192-CCM test vector NIST #5 PSA (P=0, N=13, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"4bb3c4a4f893ad8c9bdc833c325d62b3d3ad1bccf9282a65":"5a8aa485c316e9403aff859fbb":"":"":"fe69ed84":"":"":1

AES-192-CCM test vector NIST #6 PSA (P=0, N=13, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"4bb3c4a4f893ad8c9bdc833c325d62b3d3ad1bccf9282a65":"a16a2e741f1cd9717285b6d882":"":"":"db7ffc82":"FAIL":"":1

AES-192-CCM test vector NIST #7 PSA (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"19ebfde2d5468ba0a3031bde629b11fd4094afcb205393fa":"5a8aa485c316e9403aff859fbb":"":"":"0c66a8e547ed4f8c2c9a9a1eb5d455b9":"":"":1

AES-192-CCM test vector NIST #8 PSA (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"19ebfde2d5468ba0a3031bde629b11fd4094afcb205393fa":"a16a2e741f1cd9717285b6d882":"":"":"38757b3a61a4dc97ca3ab88bf1240695":"FAIL":"":1

AES-192-CCM test vector NIST #9 PSA (P=24, N=7, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"19ebfde2d5468ba0a3031bde629b11fd4094afcb205393fa":"5a8aa485c316e9":"":"411986d04d6463100bff03f7d0bde7ea2c3488784378138c":"ddc93a54":"":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22":1

AES-192-CCM test vector NIST #10 PSA (P=24, N=7, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"19ebfde2d5468ba0a3031bde629b11fd4094afcb205393fa":"31f8fa25827d48":"":"32b649ab56162e55d4148a1292d6a225a988eb1308298273":"b6889036":"FAIL":"":1

AES-192-CCM test vector NIST #11 PSA (P=24, N=7, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"197afb02ffbd8f699dacae87094d524324576b99844f75e1":"5a8aa485c316e9":"":"cba4b4aeb85f0492fd8d905c4a6d8233139833373ef188a8":"c5a5ebecf7ac8607fe412189e83d9d20":"":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22":1

AES-192-CCM test vector NIST #12 PSA (P=24, N=7, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"197afb02ffbd8f699dacae87094d524324576b99844f75e1":"31f8fa25827d48":"":"ca62713728b5c9d652504b0ae8fd4fee5d297ee6a8d19cb6":"e699f15f14d34dcaf9ba8ed4b877c97d":"FAIL":"":1

AES-192-CCM test vector NIST #13 PSA (P=24, N=13, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"197afb02ffbd8f699dacae87094d524324576b99844f75e1":"5a8aa485c316e9403aff859fbb":"":"042653c674ef2a90f7fb11d30848e530ae59478f1051633a":"34fad277":"":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697":1

AES-192-CCM test vector NIST #14 PSA (P=24, N=13, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"197afb02ffbd8f699dacae87094d524324576b99844f75e1":"49004912fdd7269279b1f06a89":"":"1902d9769a7ba3d3268e1257395c8c2e5f98eef295dcbfa5":"a35df775":"FAIL":"":1

AES-192-CCM test vector NIST #15 PSA (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"90929a4b0ac65b350ad1591611fe48297e03956f6083e451":"5a8aa485c316e9403aff859fbb":"":"a5b7d8cca2069908d1ed88e6a9fe2c9bede3131dad54671e":"a7ade30a07d185692ab0ebdf4c78cf7a":"":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697":1

AES-192-CCM test vector NIST #16 PSA (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"90929a4b0ac65b350ad1591611fe48297e03956f6083e451":"49004912fdd7269279b1f06a89":"":"9a98617fb97a0dfe466be692272dcdaec1c5443a3b51312e":"f042c86363cc05afb98c66e16be8a445":"FAIL":"":1

AES-192-CCM test vector NIST #17 PSA (P=0, N=7, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"90929a4b0ac65b350ad1591611fe48297e03956f6083e451":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":"":"1d089a5f":"":"":1

AES-192-CCM test vector NIST #18 PSA (P=0, N=7, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"90929a4b0ac65b350ad1591611fe48297e03956f6083e451":"a265480ca88d5f":"a2248a882ecbf850daf91933a389e78e81623d233dfd47bf8321361a38f138fe":"":"2f46022a":"FAIL":"":1

AES-192-CCM test vector NIST #19 PSA (P=0, N=7, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"6a798d7c5e1a72b43e20ad5c7b08567b12ab744b61c070e2":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":"":"5280a2137fee3deefcfe9b63a1199fb3":"":"":1

AES-192-CCM test vector NIST #20 PSA (P=0, N=7, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"6a798d7c5e1a72b43e20ad5c7b08567b12ab744b61c070e2":"a265480ca88d5f":"a2248a882ecbf850daf91933a389e78e81623d233dfd47bf8321361a38f138fe":"":"d40a7318c5f2d82f838c0beeefe0d598":"FAIL":"":1

AES-192-CCM test vector NIST #21 PSA (P=0, N=13, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"6a798d7c5e1a72b43e20ad5c7b08567b12ab744b61c070e2":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":"":"5e0eaebd":"":"":1

AES-192-CCM test vector NIST #22 PSA (P=0, N=13, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"6a798d7c5e1a72b43e20ad5c7b08567b12ab744b61c070e2":"8739b4bea1a099fe547499cbc6":"f6107696edb332b2ea059d8860fee26be42e5e12e1a4f79a8d0eafce1b2278a7":"":"71b7fc33":"FAIL":"":1

AES-192-CCM test vector NIST #23 PSA (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"f9fdca4ac64fe7f014de0f43039c757194d544ce5d15eed4":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":"":"d07ccf9fdc3d33aa94cda3d230da707c":"":"":1

AES-192-CCM test vector NIST #24 PSA (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"f9fdca4ac64fe7f014de0f43039c757194d544ce5d15eed4":"8739b4bea1a099fe547499cbc6":"f6107696edb332b2ea059d8860fee26be42e5e12e1a4f79a8d0eafce1b2278a7":"":"65fe32b649dc328c9f531584897e85b3":"FAIL":"":1

AES-192-CCM test vector NIST #25 PSA (P=24, N=7, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"f9fdca4ac64fe7f014de0f43039c757194d544ce5d15eed4":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":"9f6ca4af9b159148c889a6584d1183ea26e2614874b05045":"75dea8d1":"":"a265480ca88d5f536db0dc6abc40faf0d05be7a966977768":1

AES-192-CCM test vector NIST #26 PSA (P=24, N=7, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"f9fdca4ac64fe7f014de0f43039c757194d544ce5d15eed4":"fdd2d6f503c915":"5b92394f21ddc3ad49d9b0881b829a5935cb3a4d23e292a62fb66b5e7ab7020e":"84d8212e9cfc2121252baa3b065b1edcf50497b9594db1eb":"d7965825":"FAIL":"":1

AES-192-CCM test vector NIST #27 PSA (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"a7aa635ea51b0bb20a092bd5573e728ccd4b3e8cdd2ab33d":"5a8aa485c316e9":"3796cf51b8726652a4204733b8fbb047cf00fb91a9837e22ec22b1a268f88e2c":"6aab64c4787599d8f213446beadb16e08dba60e97f56dbd1":"4d1d980d6fe0fb44b421992662b97975":"":"a265480ca88d5f536db0dc6abc40faf0d05be7a966977768":1

AES-192-CCM test vector NIST #28 PSA (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"a7aa635ea51b0bb20a092bd5573e728ccd4b3e8cdd2ab33d":"fdd2d6f503c915":"5b92394f21ddc3ad49d9b0881b829a5935cb3a4d23e292a62fb66b5e7ab7020e":"4980b2ee49b1aaf393175f5ab9bae95ec7904557dfa20660":"3c51d36c826f01384100886198a7f6a3":"FAIL":"":1

AES-192-CCM test vector NIST #29 PSA (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"a7aa635ea51b0bb20a092bd5573e728ccd4b3e8cdd2ab33d":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":"16e543d0e20615ff0df15acd9927ddfe40668a54bb854ccc":"c25e9fce":"":"8739b4bea1a099fe547499cbc6d1b13d849b8084c9b6acc5":1

AES-192-CCM test vector NIST #30 PSA (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"a7aa635ea51b0bb20a092bd5573e728ccd4b3e8cdd2ab33d":"0812757ad0cc4d17c4cfe7a642":"ec6c44a7e94e51a3ca6dee229098391575ec7213c85267fbf7492fdbeee61b10":"df35b109caf690656ae278bbd8f8bba687a2ce11b105dae9":"8ecedb3e":"FAIL":"":1

AES-192-CCM test vector NIST #31 PSA (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"26511fb51fcfa75cb4b44da75a6e5a0eb8d9c8f3b906f886":"5a8aa485c316e9403aff859fbb":"a16a2e741f1cd9717285b6d882c1fc53655e9773761ad697a7ee6410184c7982":"c5b0b2ef17498c5570eb335df4588032958ba3d69bf6f317":"8464a6f7fa2b76744e8e8d95691cecb8":"":"8739b4bea1a099fe547499cbc6d1b13d849b8084c9b6acc5":1

AES-192-CCM test vector NIST #32 PSA (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_192_CCM:"26511fb51fcfa75cb4b44da75a6e5a0eb8d9c8f3b906f886":"0812757ad0cc4d17c4cfe7a642":"ec6c44a7e94e51a3ca6dee229098391575ec7213c85267fbf7492fdbeee61b10":"d1f0518929f4ae2f0543de2a7dfe4bb0110bb3057e524a1c":"06bd6dc2e6bcc3436cffb969ae900388":"FAIL":"":1

AES-256-CCM test vector NIST #1 PSA (P=0, N=7, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"eda32f751456e33195f1f499cf2dc7c97ea127b6d488f211ccc5126fbb24afa6":"a544218dadd3c1":"":"":"469c90bb":"":"":1

AES-256-CCM test vector NIST #2 PSA (P=0, N=7, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"eda32f751456e33195f1f499cf2dc7c97ea127b6d488f211ccc5126fbb24afa6":"d3d5424e20fbec":"":"":"46a908ed":"FAIL":"":1

AES-256-CCM test vector NIST #3 PSA (P=0, N=7, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"e1b8a927a95efe94656677b692662000278b441c79e879dd5c0ddc758bdc9ee8":"a544218dadd3c1":"":"":"8207eb14d33855a52acceed17dbcbf6e":"":"":1

AES-256-CCM test vector NIST #4 PSA (P=0, N=7, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"e1b8a927a95efe94656677b692662000278b441c79e879dd5c0ddc758bdc9ee8":"d3d5424e20fbec":"":"":"60f8e127cb4d30db6df0622158cd931d":"FAIL":"":1

AES-256-CCM test vector NIST #5 PSA (P=0, N=13, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"e1b8a927a95efe94656677b692662000278b441c79e879dd5c0ddc758bdc9ee8":"a544218dadd3c10583db49cf39":"":"":"8a19a133":"":"":1

AES-256-CCM test vector NIST #6 PSA (P=0, N=13, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"e1b8a927a95efe94656677b692662000278b441c79e879dd5c0ddc758bdc9ee8":"3c0e2815d37d844f7ac240ba9d":"":"":"2e317f1b":"FAIL":"":1

AES-256-CCM test vector NIST #7 PSA (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"af063639e66c284083c5cf72b70d8bc277f5978e80d9322d99f2fdc718cda569":"a544218dadd3c10583db49cf39":"":"":"97e1a8dd4259ccd2e431e057b0397fcf":"":"":1

AES-256-CCM test vector NIST #8 PSA (P=0, N=13, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"af063639e66c284083c5cf72b70d8bc277f5978e80d9322d99f2fdc718cda569":"3c0e2815d37d844f7ac240ba9d":"":"":"5a9596c511ea6a8671adefc4f2157d8b":"FAIL":"":1

AES-256-CCM test vector NIST #9 PSA (P=24, N=7, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"af063639e66c284083c5cf72b70d8bc277f5978e80d9322d99f2fdc718cda569":"a544218dadd3c1":"":"64a1341679972dc5869fcf69b19d5c5ea50aa0b5e985f5b7":"22aa8d59":"":"d3d5424e20fbec43ae495353ed830271515ab104f8860c98":1

AES-256-CCM test vector NIST #10 PSA (P=24, N=7, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"af063639e66c284083c5cf72b70d8bc277f5978e80d9322d99f2fdc718cda569":"bfcda8b5a2d0d2":"":"c5b7f802bffc498c1626e3774f1d9f94045dfd8e1a10a202":"77d00a75":"FAIL":"":1

AES-256-CCM test vector NIST #11 PSA (P=24, N=7, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"f7079dfa3b5c7b056347d7e437bcded683abd6e2c9e069d333284082cbb5d453":"a544218dadd3c1":"":"bc51c3925a960e7732533e4ef3a4f69ee6826de952bcb0fd":"374f3bb6db8377ebfc79674858c4f305":"":"d3d5424e20fbec43ae495353ed830271515ab104f8860c98":1

AES-256-CCM test vector NIST #12 PSA (P=24, N=7, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"f7079dfa3b5c7b056347d7e437bcded683abd6e2c9e069d333284082cbb5d453":"bfcda8b5a2d0d2":"":"afa1fa8e8a70e26b02161150556d604101fdf423f332c336":"3275f2a4907d51b734fe7238cebbd48f":"FAIL":"":1

AES-256-CCM test vector NIST #13 PSA (P=24, N=13, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"f7079dfa3b5c7b056347d7e437bcded683abd6e2c9e069d333284082cbb5d453":"a544218dadd3c10583db49cf39":"":"63e00d30e4b08fd2a1cc8d70fab327b2368e77a93be4f412":"3d14fb3f":"":"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e":1

AES-256-CCM test vector NIST #14 PSA (P=24, N=13, A=0, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"f7079dfa3b5c7b056347d7e437bcded683abd6e2c9e069d333284082cbb5d453":"894dcaa61008eb8fb052c60d41":"":"bb5425b3869b76856ec58e39886fb6f6f2ac13fe44cb132d":"8d0c0099":"FAIL":"":1

AES-256-CCM test vector NIST #15 PSA (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"1b0e8df63c57f05d9ac457575ea764524b8610ae5164e6215f426f5a7ae6ede4":"a544218dadd3c10583db49cf39":"":"f0050ad16392021a3f40207bed3521fb1e9f808f49830c42":"3a578d179902f912f9ea1afbce1120b3":"":"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e":1

AES-256-CCM test vector NIST #16 PSA (P=24, N=13, A=0, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"1b0e8df63c57f05d9ac457575ea764524b8610ae5164e6215f426f5a7ae6ede4":"894dcaa61008eb8fb052c60d41":"":"c408190d0fbf5034f83b24a8ed9657331a7ce141de4fae76":"9084607b83bd06e6442eac8dacf583cc":"FAIL":"":1

AES-256-CCM test vector NIST #17 PSA (P=0, N=7, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"1b0e8df63c57f05d9ac457575ea764524b8610ae5164e6215f426f5a7ae6ede4":"a544218dadd3c1":"d3d5424e20fbec43ae495353ed830271515ab104f8860c988d15b6d36c038eab":"":"92d00fbe":"":"":1

AES-256-CCM test vector NIST #18 PSA (P=0, N=7, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"1b0e8df63c57f05d9ac457575ea764524b8610ae5164e6215f426f5a7ae6ede4":"78c46e3249ca28":"232e957c65ffa11988e830d4617d500f1c4a35c1221f396c41ab214f074ca2dc":"":"9143e5c4":"FAIL":"":1

AES-256-CCM test vector NIST #19 PSA (P=0, N=7, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"a4bc10b1a62c96d459fbaf3a5aa3face7313bb9e1253e696f96a7a8e36801088":"a544218dadd3c1":"d3d5424e20fbec43ae495353ed830271515ab104f8860c988d15b6d36c038eab":"":"93af11a08379eb37a16aa2837f09d69d":"":"":1

AES-256-CCM test vector NIST #20 PSA (P=0, N=7, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"a4bc10b1a62c96d459fbaf3a5aa3face7313bb9e1253e696f96a7a8e36801088":"78c46e3249ca28":"232e957c65ffa11988e830d4617d500f1c4a35c1221f396c41ab214f074ca2dc":"":"d19b0c14ec686a7961ca7c386d125a65":"FAIL":"":1

AES-256-CCM test vector NIST #21 PSA (P=0, N=13, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"a4bc10b1a62c96d459fbaf3a5aa3face7313bb9e1253e696f96a7a8e36801088":"a544218dadd3c10583db49cf39":"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e09a1005e024f6907":"":"866d4227":"":"":1

AES-256-CCM test vector NIST #22 PSA (P=0, N=13, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"a4bc10b1a62c96d459fbaf3a5aa3face7313bb9e1253e696f96a7a8e36801088":"e8de970f6ee8e80ede933581b5":"89f8b068d34f56bc49d839d8e47b347e6dae737b903b278632447e6c0485d26a":"":"94cb1127":"FAIL":"":1

AES-256-CCM test vector NIST #23 PSA (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"8c5cf3457ff22228c39c051c4e05ed4093657eb303f859a9d4b0f8be0127d88a":"a544218dadd3c10583db49cf39":"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e09a1005e024f6907":"":"867b0d87cf6e0f718200a97b4f6d5ad5":"":"":1

AES-256-CCM test vector NIST #24 PSA (P=0, N=13, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"8c5cf3457ff22228c39c051c4e05ed4093657eb303f859a9d4b0f8be0127d88a":"e8de970f6ee8e80ede933581b5":"89f8b068d34f56bc49d839d8e47b347e6dae737b903b278632447e6c0485d26a":"":"677a040d46ee3f2b7838273bdad14f16":"FAIL":"":1

AES-256-CCM test vector NIST #25 PSA (P=24, N=7, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"8c5cf3457ff22228c39c051c4e05ed4093657eb303f859a9d4b0f8be0127d88a":"a544218dadd3c1":"d3d5424e20fbec43ae495353ed830271515ab104f8860c988d15b6d36c038eab":"c2fe12658139f5d0dd22cadf2e901695b579302a72fc5608":"3ebc7720":"":"78c46e3249ca28e1ef0531d80fd37c124d9aecb7be6668e3":1

AES-256-CCM test vector NIST #26 PSA (P=24, N=7, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"8c5cf3457ff22228c39c051c4e05ed4093657eb303f859a9d4b0f8be0127d88a":"6ba004fd176791":"5a053b2a1bb87e85d56527bfcdcd3ecafb991bb10e4c862bb0751c700a29f54b":"94748ba81229e53c38583a8564b23ebbafc6f6efdf4c2a81":"c44db2c9":"FAIL":"":1

AES-256-CCM test vector NIST #27 PSA (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"705334e30f53dd2f92d190d2c1437c8772f940c55aa35e562214ed45bd458ffe":"a544218dadd3c1":"d3d5424e20fbec43ae495353ed830271515ab104f8860c988d15b6d36c038eab":"3341168eb8c48468c414347fb08f71d2086f7c2d1bd581ce":"1ac68bd42f5ec7fa7e068cc0ecd79c2a":"":"78c46e3249ca28e1ef0531d80fd37c124d9aecb7be6668e3":1

AES-256-CCM test vector NIST #28 PSA (P=24, N=7, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"705334e30f53dd2f92d190d2c1437c8772f940c55aa35e562214ed45bd458ffe":"6ba004fd176791":"5a053b2a1bb87e85d56527bfcdcd3ecafb991bb10e4c862bb0751c700a29f54b":"d543acda712b898cbb27b8f598b2e4438ce587a836e27851":"47c3338a2400809e739b63ba8227d2f9":"FAIL":"":1

AES-256-CCM test vector NIST #29 PSA (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"705334e30f53dd2f92d190d2c1437c8772f940c55aa35e562214ed45bd458ffe":"a544218dadd3c10583db49cf39":"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e09a1005e024f6907":"c0ea400b599561e7905b99262b4565d5c3dc49fad84d7c69":"ef891339":"":"e8de970f6ee8e80ede933581b5bcf4d837e2b72baa8b00c3":1

AES-256-CCM test vector NIST #30 PSA (P=24, N=13, A=32, T=4)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"705334e30f53dd2f92d190d2c1437c8772f940c55aa35e562214ed45bd458ffe":"8fa501c5dd9ac9b868144c9fa5":"5bb40e3bb72b4509324a7edc852f72535f1f6283156e63f6959ffaf39dcde800":"60871e03ea0eb968536c99f926ea24ef43d41272ad9fb7f6":"3d488623":"FAIL":"":1

AES-256-CCM test vector NIST #31 PSA (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"314a202f836f9f257e22d8c11757832ae5131d357a72df88f3eff0ffcee0da4e":"a544218dadd3c10583db49cf39":"3c0e2815d37d844f7ac240ba9d6e3a0b2a86f706e885959e09a1005e024f6907":"8d34cdca37ce77be68f65baf3382e31efa693e63f914a781":"367f30f2eaad8c063ca50795acd90203":"":"e8de970f6ee8e80ede933581b5bcf4d837e2b72baa8b00c3":1

AES-256-CCM test vector NIST #32 PSA (P=24, N=13, A=32, T=16)
depends_on:MBEDTLS_USE_PSA_CRYPTO:MBEDTLS_TEST_DEPRECATED:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
auth_crypt_tv:MBEDTLS_CIPHER_AES_256_CCM:"314a202f836f9f257e22d8c11757832ae5131d357a72df88f3eff0ffcee0da4e":"8fa501c5dd9ac9b868144c9fa5":"5bb40e3bb72b4509324a7edc852f72535f1f6283156e63f6959ffaf39dcde800":"516c0095cc3d85fd55e48da17c592e0c7014b9daafb82bdc":"4b41096dfdbe9cc1ab610f8f3e038d16":"FAIL":"":1
