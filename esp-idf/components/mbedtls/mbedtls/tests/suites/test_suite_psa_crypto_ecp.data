ECC generate: unknown family (0)
generate_key:0:256:64:PSA_ERROR_NOT_SUPPORTED

ECC generate: unknown family (0xff)
generate_key:0xff:256:64:PSA_ERROR_NOT_SUPPORTED

ECC generate: SECP_R1 bad bit-size (0)
generate_key:PSA_ECC_FAMILY_SECP_R1:0:64:PSA_ERROR_NOT_SUPPORTED

ECC generate: SECP_R1 bad bit-size (512)
generate_key:PSA_ECC_FAMILY_SECP_R1:512:64:PSA_ERROR_NOT_SUPPORTED

ECC generate: SECP_R1 bad bit-size (528)
generate_key:PSA_ECC_FAMILY_SECP_R1:528:64:PSA_ERROR_NOT_SUPPORTED

ECC generate: SECP_R1 256-bit not supported
depends_on:!MBEDTLS_PSA_BUILTIN_ECC_SECP_R1_256
generate_key:PSA_ECC_FAMILY_SECP_R1:256:32:PSA_ERROR_NOT_SUPPORTED

ECC generate: SECP_R1 384-bit not supported
depends_on:!MBEDTLS_PSA_BUILTIN_ECC_SECP_R1_384
generate_key:PSA_ECC_FAMILY_SECP_R1:384:48:PSA_ERROR_NOT_SUPPORTED

ECC generate: SECP_R1 521-bit not supported
depends_on:!MBEDTLS_PSA_BUILTIN_ECC_SECP_R1_521
generate_key:PSA_ECC_FAMILY_SECP_R1:521:66:PSA_ERROR_NOT_SUPPORTED

ECC generate: SECP_K1 256-bit not supported
depends_on:!MBEDTLS_PSA_BUILTIN_ECC_SECP_K1_256
generate_key:PSA_ECC_FAMILY_SECP_K1:256:32:PSA_ERROR_NOT_SUPPORTED

ECC generate: Curve25519 not supported
depends_on:!MBEDTLS_PSA_BUILTIN_ECC_MONTGOMERY_255
generate_key:PSA_ECC_FAMILY_MONTGOMERY:255:32:PSA_ERROR_NOT_SUPPORTED

ECC generate: Curve448 not supported
depends_on:!MBEDTLS_PSA_BUILTIN_ECC_MONTGOMERY_448
generate_key:PSA_ECC_FAMILY_MONTGOMERY:448:56:PSA_ERROR_NOT_SUPPORTED

ECC generate: SECP_R1 256-bit, size=31, too small
depends_on:MBEDTLS_PSA_BUILTIN_ECC_SECP_R1_256
generate_key:PSA_ECC_FAMILY_SECP_R1:256:31:PSA_ERROR_BUFFER_TOO_SMALL

ECC generate: SECP_R1 256-bit, size=32, ok
depends_on:MBEDTLS_PSA_BUILTIN_ECC_SECP_R1_256
generate_key:PSA_ECC_FAMILY_SECP_R1:256:32:PSA_SUCCESS

ECC generate: SECP_R1 256-bit, size=33, ok
depends_on:MBEDTLS_PSA_BUILTIN_ECC_SECP_R1_256
generate_key:PSA_ECC_FAMILY_SECP_R1:256:33:PSA_SUCCESS

ECC generate: SECP_R1 521-bit, size=65, too small
depends_on:MBEDTLS_PSA_BUILTIN_ECC_SECP_R1_521
generate_key:PSA_ECC_FAMILY_SECP_R1:521:65:PSA_ERROR_BUFFER_TOO_SMALL

ECC generate: SECP_R1 521-bit, size=66, ok
depends_on:MBEDTLS_PSA_BUILTIN_ECC_SECP_R1_521
generate_key:PSA_ECC_FAMILY_SECP_R1:521:66:PSA_SUCCESS

ECC generate: Curve25519, size=31, too small
depends_on:MBEDTLS_PSA_BUILTIN_ECC_MONTGOMERY_255
generate_key:PSA_ECC_FAMILY_MONTGOMERY:255:31:PSA_ERROR_BUFFER_TOO_SMALL

ECC generate: Curve25519, size=32, ok
depends_on:MBEDTLS_PSA_BUILTIN_ECC_MONTGOMERY_255
generate_key:PSA_ECC_FAMILY_MONTGOMERY:255:32:PSA_SUCCESS

ECC generate: Curve25519, size=33, ok
depends_on:MBEDTLS_PSA_BUILTIN_ECC_MONTGOMERY_255
generate_key:PSA_ECC_FAMILY_MONTGOMERY:255:33:PSA_SUCCESS

ECC generate: Curve448, size=55, too small
depends_on:MBEDTLS_PSA_BUILTIN_ECC_MONTGOMERY_448
generate_key:PSA_ECC_FAMILY_MONTGOMERY:448:55:PSA_ERROR_BUFFER_TOO_SMALL

ECC generate: Curve448, size=56, ok
depends_on:MBEDTLS_PSA_BUILTIN_ECC_MONTGOMERY_448
generate_key:PSA_ECC_FAMILY_MONTGOMERY:448:56:PSA_SUCCESS

ECC generate: Curve448, size=57, ok
depends_on:MBEDTLS_PSA_BUILTIN_ECC_MONTGOMERY_448
generate_key:PSA_ECC_FAMILY_MONTGOMERY:448:57:PSA_SUCCESS
