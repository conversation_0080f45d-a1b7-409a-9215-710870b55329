PBKDF2 RFC 6070 Test Vector #1 (SHA1)
depends_on:MBEDTLS_MD_CAN_SHA1
pbkdf2_hmac:MBEDTLS_MD_SHA1:"70617373776f7264":"73616c74":1:20:"0c60c80f961f0e71f3a9b524af6012062fe037a6"

PBKDF2 RFC 6070 Test Vector #2 (SHA1)
depends_on:MBEDTLS_MD_CAN_SHA1
pbkdf2_hmac:MBEDTLS_MD_SHA1:"70617373776f7264":"73616c74":2:20:"ea6c014dc72d6f8ccd1ed92ace1d41f0d8de8957"

PBKDF2 RFC 6070 Test Vector #3 (SHA1)
depends_on:MBEDTLS_MD_CAN_SHA1
pbkdf2_hmac:MBEDTLS_MD_SHA1:"70617373776f7264":"73616c74":4096:20:"4b007901b765489abead49d926f721d065a429c1"

PBKDF2 RFC 6070 Test Vector #5 (SHA1)
depends_on:MBEDTLS_MD_CAN_SHA1
pbkdf2_hmac:MBEDTLS_MD_SHA1:"70617373776f726450415353574f524470617373776f7264":"73616c7453414c5473616c7453414c5473616c7453414c5473616c7453414c5473616c74":4096:25:"3d2eec4fe41c849b80c8d83662c0e44a8b291a964cf2f07038"

PBKDF2 RFC 6070 Test Vector #6 (SHA1)
depends_on:MBEDTLS_MD_CAN_SHA1
pbkdf2_hmac:MBEDTLS_MD_SHA1:"7061737300776f7264":"7361006c74":4096:16:"56fa6aa75548099dcc37d7f03425e0c3"

PBKDF2 Python hashlib Test Vector #1 (SHA224)
depends_on:MBEDTLS_MD_CAN_SHA224
pbkdf2_hmac:MBEDTLS_MD_SHA224:"70617373776f7264":"73616c74":1:20:"3c198cbdb9464b7857966bd05b7bc92bc1cc4e6e"

PBKDF2 Python hashlib Test Vector #2 (SHA224)
depends_on:MBEDTLS_MD_CAN_SHA224
pbkdf2_hmac:MBEDTLS_MD_SHA224:"70617373776f7264":"73616c74":2:20:"93200ffa96c5776d38fa10abdf8f5bfc0054b971"

PBKDF2 Python hashlib Test Vector #3 (SHA224)
depends_on:MBEDTLS_MD_CAN_SHA224
pbkdf2_hmac:MBEDTLS_MD_SHA224:"70617373776f7264":"73616c74":4096:20:"218c453bf90635bd0a21a75d172703ff6108ef60"

PBKDF2 Python hashlib Test Vector #5 (SHA224)
depends_on:MBEDTLS_MD_CAN_SHA224
pbkdf2_hmac:MBEDTLS_MD_SHA224:"70617373776f726450415353574f524470617373776f7264":"73616c7453414c5473616c7453414c5473616c7453414c5473616c7453414c5473616c74":4096:25:"056c4ba438ded91fc14e0594e6f52b87e1f3690c0dc0fbc057"

PBKDF2 Python hashlib Test Vector #6 (SHA224)
depends_on:MBEDTLS_MD_CAN_SHA224
pbkdf2_hmac:MBEDTLS_MD_SHA224:"7061737300776f7264":"7361006c74":4096:16:"9b4011b641f40a2a500a31d4a392d15c"

PBKDF2 RFC 7914 Sec 11 Test Vector #1 (SHA256)
depends_on:MBEDTLS_MD_CAN_SHA256
pbkdf2_hmac:MBEDTLS_MD_SHA256:"706173737764":"73616c74":1:64:"55ac046e56e3089fec1691c22544b605f94185216dde0465e68b9d57c20dacbc49ca9cccf179b645991664b39d77ef317c71b845b1e30bd509112041d3a19783"

PBKDF2 RFC 7914 Sec 11 Test Vector #2 (SHA256)
depends_on:MBEDTLS_MD_CAN_SHA256
pbkdf2_hmac:MBEDTLS_MD_SHA256:"50617373776f7264":"4e61436c":80000:64:"4ddcd8f60b98be21830cee5ef22701f9641a4418d04c0414aeff08876b34ab56a1d425a1225833549adb841b51c9b3176a272bdebba1d078478f62b397f33c8d"

PBKDF2 Python hashlib Test Vector #1 (SHA256)
depends_on:MBEDTLS_MD_CAN_SHA256
pbkdf2_hmac:MBEDTLS_MD_SHA256:"70617373776f7264":"73616c74":1:20:"120fb6cffcf8b32c43e7225256c4f837a86548c9"

PBKDF2 Python hashlib Test Vector #2 (SHA256)
depends_on:MBEDTLS_MD_CAN_SHA256
pbkdf2_hmac:MBEDTLS_MD_SHA256:"70617373776f7264":"73616c74":2:20:"ae4d0c95af6b46d32d0adff928f06dd02a303f8e"

PBKDF2 Python hashlib Test Vector #3 (SHA256)
depends_on:MBEDTLS_MD_CAN_SHA256
pbkdf2_hmac:MBEDTLS_MD_SHA256:"70617373776f7264":"73616c74":4096:20:"c5e478d59288c841aa530db6845c4c8d962893a0"

PBKDF2 Python hashlib Test Vector #5 (SHA256)
depends_on:MBEDTLS_MD_CAN_SHA256
pbkdf2_hmac:MBEDTLS_MD_SHA256:"70617373776f726450415353574f524470617373776f7264":"73616c7453414c5473616c7453414c5473616c7453414c5473616c7453414c5473616c74":4096:25:"348c89dbcbd32b2f32d814b8116e84cf2b17347ebc1800181c"

PBKDF2 Python hashlib Test Vector #6 (SHA256)
depends_on:MBEDTLS_MD_CAN_SHA256
pbkdf2_hmac:MBEDTLS_MD_SHA256:"7061737300776f7264":"7361006c74":4096:16:"89b69d0516f829893c696226650a8687"

PBKDF2 Python hashlib Test Vector #1 (SHA384)
depends_on:MBEDTLS_MD_CAN_SHA384
pbkdf2_hmac:MBEDTLS_MD_SHA384:"70617373776f7264":"73616c74":1:20:"c0e14f06e49e32d73f9f52ddf1d0c5c719160923"

PBKDF2 Python hashlib Test Vector #2 (SHA384)
depends_on:MBEDTLS_MD_CAN_SHA384
pbkdf2_hmac:MBEDTLS_MD_SHA384:"70617373776f7264":"73616c74":2:20:"54f775c6d790f21930459162fc535dbf04a93918"

PBKDF2 Python hashlib Test Vector #3 (SHA384)
depends_on:MBEDTLS_MD_CAN_SHA384
pbkdf2_hmac:MBEDTLS_MD_SHA384:"70617373776f7264":"73616c74":4096:20:"559726be38db125bc85ed7895f6e3cf574c7a01c"

PBKDF2 Python hashlib Test Vector #5 (SHA384)
depends_on:MBEDTLS_MD_CAN_SHA384
pbkdf2_hmac:MBEDTLS_MD_SHA384:"70617373776f726450415353574f524470617373776f7264":"73616c7453414c5473616c7453414c5473616c7453414c5473616c7453414c5473616c74":4096:25:"819143ad66df9a552559b9e131c52ae6c5c1b0eed18f4d283b"

PBKDF2 Python hashlib Test Vector #6 (SHA384)
depends_on:MBEDTLS_MD_CAN_SHA384
pbkdf2_hmac:MBEDTLS_MD_SHA384:"7061737300776f7264":"7361006c74":4096:16:"a3f00ac8657e095f8e0823d232fc60b3"

PBKDF2 Python hashlib Test Vector #1 (SHA512)
depends_on:MBEDTLS_MD_CAN_SHA512
pbkdf2_hmac:MBEDTLS_MD_SHA512:"70617373776f7264":"73616c74":1:20:"867f70cf1ade02cff3752599a3a53dc4af34c7a6"

PBKDF2 Python hashlib Test Vector #2 (SHA512)
depends_on:MBEDTLS_MD_CAN_SHA512
pbkdf2_hmac:MBEDTLS_MD_SHA512:"70617373776f7264":"73616c74":2:20:"e1d9c16aa681708a45f5c7c4e215ceb66e011a2e"

PBKDF2 Python hashlib Test Vector #3 (SHA512)
depends_on:MBEDTLS_MD_CAN_SHA512
pbkdf2_hmac:MBEDTLS_MD_SHA512:"70617373776f7264":"73616c74":4096:20:"d197b1b33db0143e018b12f3d1d1479e6cdebdcc"

PBKDF2 Python hashlib Test Vector #5 (SHA512)
depends_on:MBEDTLS_MD_CAN_SHA512
pbkdf2_hmac:MBEDTLS_MD_SHA512:"70617373776f726450415353574f524470617373776f7264":"73616c7453414c5473616c7453414c5473616c7453414c5473616c7453414c5473616c74":4096:25:"8c0511f4c6e597c6ac6315d8f0362e225f3c501495ba23b868"

PBKDF2 Python hashlib Test Vector #6 (SHA512)
depends_on:MBEDTLS_MD_CAN_SHA512
pbkdf2_hmac:MBEDTLS_MD_SHA512:"7061737300776f7264":"7361006c74":4096:16:"9d9e9c4cd21fe4be24d5b8244c759665"

PBES2 Encrypt, pad=6 (OK)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pbes2_encrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301B06092A864886F70D01050C300E04082ED7F24A1D516DD702020800301406082A864886F70D030704088A4FCC9DCC394910":"70617373776f7264":"308187020100301306072A8648CE3D020106082A8648CE3D030107046D306B0201010420F12A1320760270A83CBFFD53F6031EF76A5D86C8A204F2C30CA9EBF51F0F0EA7A1440342000437CC56D976091E5A723EC7592DFF206EEE7CF9069174D0AD14B5F768225962924EE500D82311FFEA2FD2345D5D16BD8A88C26B770D55CD8A2A0EFA01C8B4EDFF":144:0:"1B60098D4834CA752D37B430E70B7A085CFF86E21F4849F969DD1DF623342662443F8BD1252BF83CEF6917551B08EF55A69C8F2BFFC93BCB2DFE2E354DA28F896D1BD1BFB972A1251219A6EC7183B0A4CF2C4998449ED786CAE2138437289EB2203974000C38619DA57A4E685D29649284602BD1806131772DA11A682674DC22B2CF109128DDB7FD980E1C5741FC0DB7"

PBES2 Encrypt, pad=8 (OK)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pbes2_encrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301B06092A864886F70D01050C300E04082ED7F24A1D516DD702020800301406082A864886F70D030704088A4FCC9DCC394910":"70617373776f7264":"308187020100301306072A8648CE3D020106082A8648CE3D030107046D306B0201010420F12A1320760270A83CBFFD53F6031EF76A5D86C8A204F2C30CA9EBF51F0F0EA7A1440342000437CC56D976091E5A723EC7592DFF206EEE7CF9069174D0AD14B5F768225962924EE500D82311FFEA2FD2345D5D16BD8A88C26B770D55":136:0:"1B60098D4834CA752D37B430E70B7A085CFF86E21F4849F969DD1DF623342662443F8BD1252BF83CEF6917551B08EF55A69C8F2BFFC93BCB2DFE2E354DA28F896D1BD1BFB972A1251219A6EC7183B0A4CF2C4998449ED786CAE2138437289EB2203974000C38619DA57A4E685D29649284602BD1806131772DA11A682674DC2262AD99FBD6C3B0AB"

PBES2 Encrypt, pad=8 (Invalid output size)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pbes2_encrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301B06092A864886F70D01050C300E04082ED7F24A1D516DD702020800301406082A864886F70D030704088A4FCC9DCC394910":"70617373776f7264":"308187020100301306072A8648CE3D020106082A8648CE3D030107046D306B0201010420F12A1320760270A83CBFFD53F6031EF76A5D86C8A204F2C30CA9EBF51F0F0EA7A1440342000437CC56D976091E5A723EC7592DFF206EEE7CF9069174D0AD14B5F768225962924EE500D82311FFEA2FD2345D5D16BD8A88C26B770D5510101010101010101010101010101010":151:MBEDTLS_ERR_ASN1_BUF_TOO_SMALL:"1B60098D4834CA752D37B430E70B7A085CFF86E21F4849F969DD1DF623342662443F8BD1252BF83CEF6917551B08EF55A69C8F2BFFC93BCB2DFE2E354DA28F896D1BD1BFB972A1251219A6EC7183B0A4CF2C4998449ED786CAE2138437289EB2203974000C38619DA57A4E685D29649284602BD1806131772DA11A682674DC22D8D337E00CB5D1B5B76BE4AE393414050646A102DEF61A9F"

PBES2 Encrypt, pad=6 (PKCS7 padding disabled)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_CIPHER_PADDING_PKCS7
pbes2_encrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301B06092A864886F70D01050C300E04082ED7F24A1D516DD702020800301406082A864886F70D030704088A4FCC9DCC394910":"70617373776f7264":"308187020100301306072A8648CE3D020106082A8648CE3D030107046D306B0201010420F12A1320760270A83CBFFD53F6031EF76A5D86C8A204F2C30CA9EBF51F0F0EA7A1440342000437CC56D976091E5A723EC7592DFF206EEE7CF9069174D0AD14B5F768225962924EE500D82311FFEA2FD2345D5D16BD8A88C26B770D55CD8A2A0EFA01C8B4EDFF":138:MBEDTLS_ERR_CIPHER_FEATURE_UNAVAILABLE:""

PBES2 Encrypt, pad=8 (PKCS7 padding disabled)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_CIPHER_PADDING_PKCS7
pbes2_encrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301B06092A864886F70D01050C300E04082ED7F24A1D516DD702020800301406082A864886F70D030704088A4FCC9DCC394910":"70617373776f7264":"308187020100301306072A8648CE3D020106082A8648CE3D030107046D306B0201010420F12A1320760270A83CBFFD53F6031EF76A5D86C8A204F2C30CA9EBF51F0F0EA7A1440342000437CC56D976091E5A723EC7592DFF206EEE7CF9069174D0AD14B5F768225962924EE500D82311FFEA2FD2345D5D16BD8A88C26B770D5510101010101010101010101010101010":138:MBEDTLS_ERR_CIPHER_FEATURE_UNAVAILABLE:""

PBES2 Encrypt, AES-128-CBC (OK, generated with OpenSSL)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pbes2_encrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301B06092A864886F70D01050C300E0408010203040506070802022710301D060960864801650304010204102F904F75B47B48A618068D79BD9A826C":"50617373776F726450617373776F726450617373776F7264":"5468697320697320612066696c6520746f20626520656e6372797074656420776974682050424553322c20776974682061206b65792067656e657261746564207573696e67206120707266202853484132353629":96:0:"0c953c3a9be1502f4d1df1b82df9d99a61ebd2f60ed570d16b241f70b9e61f329174747d052efe5c39bec9d0f7404f84af242914e2ecb74e6e36e8b4147bd0a092a82df789aa3351e0de9f75b285ccb742806526771c8353ffb66176188b556e"

PBES2 Encrypt, AES-192-CBC (OK, generated with OpenSSL)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:MBEDTLS_CIPHER_PADDING_PKCS7
pbes2_encrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"302a06092A864886F70D01050C301d0408010203040506070802022710020118300a06082A864886F70D0209301D060960864801650304011604102F904F75B47B48A618068D79BD9A826C":"50617373776F726450617373776F726450617373776F7264":"5468697320697320612066696c6520746f20626520656e6372797074656420776974682050424553322c20776974682061206b65792067656e657261746564207573696e67206120707266202853484132353629":96:0:"7c648a9df9759ba49283c261269a71bc06d45f6c24ab6431d77b2ecec1fd6d1aa751bd05b1c026fc8ff91baeb1b1838aa0f825b23be79bc09331d0607181e234dfea4ab3cbf7997747516486b6865e85de95dc9b64d45462197c891b31af6c94"

PBES2 Encrypt, AES-256-CBC (OK, generated with OpenSSL)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:MBEDTLS_CIPHER_PADDING_PKCS7
pbes2_encrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"302706092A864886F70D01050C301a0408010203040506070802022710300a06082A864886F70D0209301D060960864801650304012A04102F904F75B47B48A618068D79BD9A826C":"50617373776F726450617373776F726450617373776F7264":"5468697320697320612066696c6520746f20626520656e6372797074656420776974682050424553322c20776974682061206b65792067656e657261746564207573696e67206120707266202853484132353629":96:0:"7077e6e8e679962c6feda69c704d58507d143cf77be284f773dfcaa4a5f5e7c1cd5527204916302d435071c01126f4aa76205ce61998d7b8fdf065a77d4a77634376b9968e4722c27f2ac85d79565ff4fca9204d3e4bc8c5bd53c1785bb6e566"

PBES2 Decrypt (OK)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301B06092A864886F70D01050C300E04082ED7F24A1D516DD702020800301406082A864886F70D030704088A4FCC9DCC394910":"70617373776f7264":"1B60098D4834CA752D37B430E70B7A085CFF86E21F4849F969DD1DF623342662443F8BD1252BF83CEF6917551B08EF55A69C8F2BFFC93BCB2DFE2E354DA28F896D1BD1BFB972A1251219A6EC7183B0A4CF2C4998449ED786CAE2138437289EB2203974000C38619DA57A4E685D29649284602BD1806131772DA11A682674DC22B2CF109128DDB7FD980E1C5741FC0DB7":144:0:"308187020100301306072A8648CE3D020106082A8648CE3D030107046D306B0201010420F12A1320760270A83CBFFD53F6031EF76A5D86C8A204F2C30CA9EBF51F0F0EA7A1440342000437CC56D976091E5A723EC7592DFF206EEE7CF9069174D0AD14B5F768225962924EE500D82311FFEA2FD2345D5D16BD8A88C26B770D55CD8A2A0EFA01C8B4EDFF"

PBES2 Decrypt (Invalid output size)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301B06092A864886F70D01050C300E04082ED7F24A1D516DD702020800301406082A864886F70D030704088A4FCC9DCC394910":"70617373776f7264":"1B60098D4834CA752D37B430E70B7A085CFF86E21F4849F969DD1DF623342662443F8BD1252BF83CEF6917551B08EF55A69C8F2BFFC93BCB2DFE2E354DA28F896D1BD1BFB972A1251219A6EC7183B0A4CF2C4998449ED786CAE2138437289EB2203974000C38619DA57A4E685D29649284602BD1806131772DA11A682674DC22B2CF109128DDB7FD980E1C5741FC0DB7":143:MBEDTLS_ERR_ASN1_BUF_TOO_SMALL:"308187020100301306072A8648CE3D020106082A8648CE3D030107046D306B0201010420F12A1320760270A83CBFFD53F6031EF76A5D86C8A204F2C30CA9EBF51F0F0EA7A1440342000437CC56D976091E5A723EC7592DFF206EEE7CF9069174D0AD14B5F768225962924EE500D82311FFEA2FD2345D5D16BD8A88C26B770D55CD8A2A0EFA01C8B4EDFF060606060606"

PBES2 Decrypt (Invalid padding & PKCS7 padding disabled)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_CIPHER_PADDING_PKCS7
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301B06092A864886F70D01050C300E04082ED7F24A1D516DD702020800301406082A864886F70D030704088A4FCC9DCC394910":"70617373776f7264":"1B60098D4834CA752D37B430E70B7A085CFF86E21F4849F969DD1DF623342662443F8BD1252BF83CEF6917551B08EF55A69C8F2BFFC93BCB2DFE2E354DA28F896D1BD1BFB972A1251219A6EC7183B0A4CF2C4998449ED786CAE2138437289EB2203974000C38619DA57A4E685D29649284602BD1806131772DA11A682674DC22B2CF109128DDB7FDA3488A7144097565":144:0:"308187020100301306072A8648CE3D020106082A8648CE3D030107046D306B0201010420F12A1320760270A83CBFFD53F6031EF76A5D86C8A204F2C30CA9EBF51F0F0EA7A1440342000437CC56D976091E5A723EC7592DFF206EEE7CF9069174D0AD14B5F768225962924EE500D82311FFEA2FD2345D5D16BD8A88C26B770D55CD8A2A0EFA01C8B4EDFF060606060607"

PBES2 Decrypt (Invalid padding & PKCS7 padding enabled)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301B06092A864886F70D01050C300E04082ED7F24A1D516DD702020800301406082A864886F70D030704088A4FCC9DCC394910":"70617373776f7264":"1B60098D4834CA752D37B430E70B7A085CFF86E21F4849F969DD1DF623342662443F8BD1252BF83CEF6917551B08EF55A69C8F2BFFC93BCB2DFE2E354DA28F896D1BD1BFB972A1251219A6EC7183B0A4CF2C4998449ED786CAE2138437289EB2203974000C38619DA57A4E685D29649284602BD1806131772DA11A682674DC22B2CF109128DDB7FDA3488A7144097565":144:MBEDTLS_ERR_PKCS5_PASSWORD_MISMATCH:"308187020100301306072A8648CE3D020106082A8648CE3D030107046D306B0201010420F12A1320760270A83CBFFD53F6031EF76A5D86C8A204F2C30CA9EBF51F0F0EA7A1440342000437CC56D976091E5A723EC7592DFF206EEE7CF9069174D0AD14B5F768225962924EE500D82311FFEA2FD2345D5D16BD8A88C26B770D55CD8A2A0EFA01C8B4EDFF060606060607"

PBES2 Decrypt AES-128-CBC (OK, generated with OpenSSL)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301B06092A864886F70D01050C300E0408010203040506070802022710301D060960864801650304010204102F904F75B47B48A618068D79BD9A826C":"50617373776F726450617373776F726450617373776F7264":"0c953c3a9be1502f4d1df1b82df9d99a61ebd2f60ed570d16b241f70b9e61f329174747d052efe5c39bec9d0f7404f84af242914e2ecb74e6e36e8b4147bd0a092a82df789aa3351e0de9f75b285ccb742806526771c8353ffb66176188b556e":96:0:"5468697320697320612066696c6520746f20626520656e6372797074656420776974682050424553322c20776974682061206b65792067656e657261746564207573696e67206120707266202853484132353629"

PBES2 Decrypt AES-192-CBC (OK, generated with OpenSSL)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"302a06092A864886F70D01050C301d0408010203040506070802022710020118300a06082A864886F70D0209301D060960864801650304011604102F904F75B47B48A618068D79BD9A826C":"50617373776F726450617373776F726450617373776F7264":"7c648a9df9759ba49283c261269a71bc06d45f6c24ab6431d77b2ecec1fd6d1aa751bd05b1c026fc8ff91baeb1b1838aa0f825b23be79bc09331d0607181e234dfea4ab3cbf7997747516486b6865e85de95dc9b64d45462197c891b31af6c94":96:0:"5468697320697320612066696c6520746f20626520656e6372797074656420776974682050424553322c20776974682061206b65792067656e657261746564207573696e67206120707266202853484132353629"

PBES2 Decrypt AES-256-CBC (OK, generated with OpenSSL)
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_AES_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"302706092A864886F70D01050C301a0408010203040506070802022710300a06082A864886F70D0209301D060960864801650304012A04102F904F75B47B48A618068D79BD9A826C":"50617373776F726450617373776F726450617373776F7264":"7077e6e8e679962c6feda69c704d58507d143cf77be284f773dfcaa4a5f5e7c1cd5527204916302d435071c01126f4aa76205ce61998d7b8fdf065a77d4a77634376b9968e4722c27f2ac85d79565ff4fca9204d3e4bc8c5bd53c1785bb6e566":96:0:"5468697320697320612066696c6520746f20626520656e6372797074656420776974682050424553322c20776974682061206b65792067656e657261746564207573696e67206120707266202853484132353629"

PBES2 Decrypt (bad params tag)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C
pbes2_decrypt:MBEDTLS_ASN1_SEQUENCE:"":"":"":0:MBEDTLS_ERR_PKCS5_INVALID_FORMAT + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG:""

PBES2 Decrypt (bad KDF AlgId: not a sequence)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"31":"":"":0:MBEDTLS_ERR_PKCS5_INVALID_FORMAT + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG:""

PBES2 Decrypt (bad KDF AlgId: overlong)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"3001":"":"":0:MBEDTLS_ERR_PKCS5_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA:""

PBES2 Decrypt (KDF != PBKDF2)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"300B06092A864886F70D01050D":"":"":0:MBEDTLS_ERR_PKCS5_FEATURE_UNAVAILABLE:""

PBES2 Decrypt (bad PBKDF2 params: not a sequence)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"300D06092A864886F70D01050C3100":"":"":0:MBEDTLS_ERR_PKCS5_INVALID_FORMAT + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG:""

PBES2 Decrypt (bad PBKDF2 params: overlong)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"300D06092A864886F70D01050C3001":"":"":0:MBEDTLS_ERR_PKCS5_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA:""

PBES2 Decrypt (bad PBKDF2 params salt: not an octet string)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"300E06092A864886F70D01050C30010500":"":"":0:MBEDTLS_ERR_PKCS5_INVALID_FORMAT + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG:""

PBES2 Decrypt (bad PBKDF2 params salt: overlong)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"300E06092A864886F70D01050C30010401":"":"":0:MBEDTLS_ERR_PKCS5_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA:""

PBES2 Decrypt (bad PBKDF2 params iter: not an int)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301906092A864886F70D01050C300C04082ED7F24A1D516DD70300":"":"":0:MBEDTLS_ERR_PKCS5_INVALID_FORMAT + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG:""

PBES2 Decrypt (bad PBKDF2 params iter: overlong)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301906092A864886F70D01050C300C04082ED7F24A1D516DD70201":"":"":0:MBEDTLS_ERR_PKCS5_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA:""

PBES2 Decrypt (OK, PBKDF2 params explicit keylen)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301E06092A864886F70D01050C301104082ED7F24A1D516DD702020800020118301406082A864886F70D030704088A4FCC9DCC394910":"70617373776f7264":"1B60098D4834CA752D37B430E70B7A085CFF86E21F4849F969DD1DF623342662443F8BD1252BF83CEF6917551B08EF55A69C8F2BFFC93BCB2DFE2E354DA28F896D1BD1BFB972A1251219A6EC7183B0A4CF2C4998449ED786CAE2138437289EB2203974000C38619DA57A4E685D29649284602BD1806131772DA11A682674DC22B2CF109128DDB7FD980E1C5741FC0DB7":144:0:"308187020100301306072A8648CE3D020106082A8648CE3D030107046D306B0201010420F12A1320760270A83CBFFD53F6031EF76A5D86C8A204F2C30CA9EBF51F0F0EA7A1440342000437CC56D976091E5A723EC7592DFF206EEE7CF9069174D0AD14B5F768225962924EE500D82311FFEA2FD2345D5D16BD8A88C26B770D55CD8A2A0EFA01C8B4EDFF"

PBES2 Decrypt (bad PBKDF2 params explicit keylen: overlong)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301D06092A864886F70D01050C301004082ED7F24A1D516DD7020208000201":"":"":0:MBEDTLS_ERR_PKCS5_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA:""

PBES2 Decrypt (OK, PBKDF2 params explicit prf_alg)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"302706092A864886F70D01050C301A04082ED7F24A1D516DD702020800300A06082A864886F70D0207301406082A864886F70D030704088A4FCC9DCC394910":"70617373776f7264":"1B60098D4834CA752D37B430E70B7A085CFF86E21F4849F969DD1DF623342662443F8BD1252BF83CEF6917551B08EF55A69C8F2BFFC93BCB2DFE2E354DA28F896D1BD1BFB972A1251219A6EC7183B0A4CF2C4998449ED786CAE2138437289EB2203974000C38619DA57A4E685D29649284602BD1806131772DA11A682674DC22B2CF109128DDB7FD980E1C5741FC0DB7":144:0:"308187020100301306072A8648CE3D020106082A8648CE3D030107046D306B0201010420F12A1320760270A83CBFFD53F6031EF76A5D86C8A204F2C30CA9EBF51F0F0EA7A1440342000437CC56D976091E5A723EC7592DFF206EEE7CF9069174D0AD14B5F768225962924EE500D82311FFEA2FD2345D5D16BD8A88C26B770D55CD8A2A0EFA01C8B4EDFF"

PBES2 Decrypt (bad, PBKDF2 params explicit prf_alg not a sequence)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301D06092A864886F70D01050C301004082ED7F24A1D516DD7020208003100":"":"":0:MBEDTLS_ERR_PKCS5_INVALID_FORMAT + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG:""

PBES2 Decrypt (bad, PBKDF2 params explicit prf_alg overlong)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301D06092A864886F70D01050C301004082ED7F24A1D516DD7020208003001":"":"":0:MBEDTLS_ERR_PKCS5_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA:""

PBES2 Decrypt (bad, PBKDF2 params explicit prf_alg != HMAC-SHA*)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"302706092A864886F70D01050C301A04082ED7F24A1D516DD702020800300A06082A864886F70D0206":"":"":0:MBEDTLS_ERR_PKCS5_FEATURE_UNAVAILABLE:""

PBES2 Decrypt (bad, PBKDF2 params extra data)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"302806092A864886F70D01050C301B04082ED7F24A1D516DD702020800300A06082A864886F70D020700":"":"":0:MBEDTLS_ERR_PKCS5_INVALID_FORMAT + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH:""

PBES2 Decrypt (bad enc_scheme_alg: not a sequence)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301B06092A864886F70D01050C300E04082ED7F24A1D516DD7020208003100":"":"":0:MBEDTLS_ERR_PKCS5_INVALID_FORMAT + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG:""

PBES2 Decrypt (bad enc_scheme_alg: overlong)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301B06092A864886F70D01050C300E04082ED7F24A1D516DD7020208003001":"":"":0:MBEDTLS_ERR_PKCS5_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA:""

PBES2 Decrypt (bad enc_scheme_alg: unknown oid)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301B06092A864886F70D01050C300E04082ED7F24A1D516DD702020800300A06082A864886F70D03FF":"":"":0:MBEDTLS_ERR_PKCS5_FEATURE_UNAVAILABLE:""

PBES2 Decrypt (bad enc_scheme_alg params: not an octet string)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301B06092A864886F70D01050C300E04082ED7F24A1D516DD702020800300C06082A864886F70D03070500":"":"":0:MBEDTLS_ERR_PKCS5_INVALID_FORMAT:""

PBES2 Decrypt (bad enc_scheme_alg params: overlong)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301B06092A864886F70D01050C300E04082ED7F24A1D516DD702020800300C06082A864886F70D03070401":"":"":0:MBEDTLS_ERR_PKCS5_INVALID_FORMAT + MBEDTLS_ERR_ASN1_OUT_OF_DATA:""

PBES2 Decrypt (bad enc_scheme_alg params: len != iv_len)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301B06092A864886F70D01050C300E04082ED7F24A1D516DD702020800301306082A864886F70D030704078A4FCC9DCC3949":"":"":0:MBEDTLS_ERR_PKCS5_INVALID_FORMAT:""

PBES2 Decrypt (bad password)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301B06092A864886F70D01050C300E04082ED7F24A1D516DD702020800301406082A864886F70D030704088A4FCC9DCC394910":"F0617373776f7264":"1B60098D4834CA752D37B430E70B7A085CFF86E21F4849F969DD1DF623342662443F8BD1252BF83CEF6917551B08EF55A69C8F2BFFC93BCB2DFE2E354DA28F896D1BD1BFB972A1251219A6EC7183B0A4CF2C4998449ED786CAE2138437289EB2203974000C38619DA57A4E685D29649284602BD1806131772DA11A682674DC22B2CF109128DDB7FD980E1C5741FC0DB7":144:MBEDTLS_ERR_PKCS5_PASSWORD_MISMATCH:"308187020100301306072A8648CE3D020106082A8648CE3D030107046D306B0201010420F12A1320760270A83CBFFD53F6031EF76A5D86C8A204F2C30CA9EBF51F0F0EA7A1440342000437CC56D976091E5A723EC7592DFF206EEE7CF9069174D0AD14B5F768225962924EE500D82311FFEA2FD2345D5D16BD8A88C26B770D55CD8A2A0EFA01C8B4EDFF060606060606"

PBES2 Decrypt (bad iter value)
depends_on:MBEDTLS_MD_CAN_SHA1:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pbes2_decrypt:MBEDTLS_ASN1_CONSTRUCTED | MBEDTLS_ASN1_SEQUENCE:"301B06092A864886F70D01050C300E04082ED7F24A1D516DD702020801301406082A864886F70D030704088A4FCC9DCC394910":"70617373776f7264":"1B60098D4834CA752D37B430E70B7A085CFF86E21F4849F969DD1DF623342662443F8BD1252BF83CEF6917551B08EF55A69C8F2BFFC93BCB2DFE2E354DA28F896D1BD1BFB972A1251219A6EC7183B0A4CF2C4998449ED786CAE2138437289EB2203974000C38619DA57A4E685D29649284602BD1806131772DA11A682674DC22B2CF109128DDB7FD980E1C5741FC0DB7":144:MBEDTLS_ERR_PKCS5_PASSWORD_MISMATCH:"308187020100301306072A8648CE3D020106082A8648CE3D030107046D306B0201010420F12A1320760270A83CBFFD53F6031EF76A5D86C8A204F2C30CA9EBF51F0F0EA7A1440342000437CC56D976091E5A723EC7592DFF206EEE7CF9069174D0AD14B5F768225962924EE500D82311FFEA2FD2345D5D16BD8A88C26B770D55CD8A2A0EFA01C8B4EDFF060606060606"

PKCS#5 Selftest
pkcs5_selftest:
