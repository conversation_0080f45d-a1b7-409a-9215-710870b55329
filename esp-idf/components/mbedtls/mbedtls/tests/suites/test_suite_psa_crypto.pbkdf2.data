PSA key derivation: PB<PERSON><PERSON>2-<PERSON><PERSON>(SHA-1), RFC6070 #4
depends_on:PSA_WANT_ALG_PBKDF2_HMAC:PSA_WANT_ALG_SHA_1
derive_output:PSA_ALG_PBKDF2_HMAC(PSA_ALG_SHA_1):PSA_KEY_DERIVATION_INPUT_COST:"01000000":PSA_SUCCESS:PSA_KEY_DERIVATION_INPUT_SALT:"73616c74":PSA_SUCCESS:PSA_KEY_DERIVATION_INPUT_PASSWORD:"70617373776f7264":PSA_SUCCESS:0:"":PSA_SUCCESS:"":20:"eefe3d61cd4da4e4e9945b3d6ba2158c2634e984":"":0:1:0

PSA key derivation: PBKDF2-HMAC(SHA-256), RFC7914 #2
depends_on:PSA_WANT_ALG_PBKDF2_HMAC:PSA_WANT_ALG_SHA_256
derive_output:PSA_ALG_PBKDF2_HMAC(PSA_ALG_SHA_256):PSA_KEY_DERIVATION_INPUT_COST:"013880":PSA_SUCCESS:PSA_KEY_DERIVATION_INPUT_SALT:"4e61436c":PSA_SUCCESS:PSA_KEY_DERIVATION_INPUT_PASSWORD:"50617373776f7264":PSA_SUCCESS:0:"":PSA_SUCCESS:"":64:"4ddcd8f60b98be21830cee5ef22701f9641a4418d04c0414aeff08876b34ab56a1d425a1225833549adb841b51c9b3176a272bdebba1d078478f62b397f33c8d":"":0:1:0

# For PBKDF2_AES_CMAC_PRF_128 the output for the test vectors was generated using a python script. Refer commit message for details.
PSA key derivation: PBKDF2-AES-CMAC-PRF-128, inputs from RFC6070 #4
depends_on:PSA_WANT_ALG_PBKDF2_AES_CMAC_PRF_128:PSA_WANT_ALG_CMAC:PSA_WANT_KEY_TYPE_AES
derive_output:PSA_ALG_PBKDF2_AES_CMAC_PRF_128:PSA_KEY_DERIVATION_INPUT_COST:"01000000":PSA_SUCCESS:PSA_KEY_DERIVATION_INPUT_SALT:"73616c74":PSA_SUCCESS:PSA_KEY_DERIVATION_INPUT_PASSWORD:"70617373776f7264":PSA_SUCCESS:0:"":PSA_SUCCESS:"":20:"c19b71d2daf483abc9e04fbc78928b4204398d1e":"":0:1:0

PSA key derivation: PBKDF2-AES-CMAC-PRF-128, inputs from RFC7914 #2
depends_on:PSA_WANT_ALG_PBKDF2_AES_CMAC_PRF_128:PSA_WANT_ALG_CMAC:PSA_WANT_KEY_TYPE_AES
derive_output:PSA_ALG_PBKDF2_AES_CMAC_PRF_128:PSA_KEY_DERIVATION_INPUT_COST:"013880":PSA_SUCCESS:PSA_KEY_DERIVATION_INPUT_SALT:"4e61436c":PSA_SUCCESS:PSA_KEY_DERIVATION_INPUT_PASSWORD:"50617373776f7264":PSA_SUCCESS:0:"":PSA_SUCCESS:"":64:"3298e89bc3560e61b59aef2c104f93380b5fa26e2e011cb5ac5895fcd5a3bd5a92e617d7cae020fa2c6ef895182d9ffa0cc8f9c22778beb02856127719d95570":"":0:1:0
