AES-128-CFB128 Encrypt NIST KAT #1
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_encrypt_cfb128:"f0000000000000000000000000000000":"00000000000000000000000000000000":"00000000000000000000000000000000":"970014d634e2b7650777e8e84d03ccd8"

AES-128-CFB128 Encrypt NIST KAT #2
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_encrypt_cfb128:"f8000000000000000000000000000000":"00000000000000000000000000000000":"00000000000000000000000000000000":"f17e79aed0db7e279e955b5f493875a7"

AES-128-CFB128 Encrypt NIST KAT #3
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_encrypt_cfb128:"fc000000000000000000000000000000":"00000000000000000000000000000000":"00000000000000000000000000000000":"9ed5a75136a940d0963da379db4af26a"

AES-128-CFB128 Encrypt NIST KAT #4
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_encrypt_cfb128:"64cf9c7abc50b888af65f49d521944b2":"00000000000000000000000000000000":"00000000000000000000000000000000":"f7efc89d5dba578104016ce5ad659c05"

AES-128-CFB128 Encrypt NIST KAT #5
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_encrypt_cfb128:"47d6742eefcc0465dc96355e851b64d9":"00000000000000000000000000000000":"00000000000000000000000000000000":"0306194f666d183624aa230a8b264ae7"

AES-128-CFB128 Encrypt NIST KAT #6
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_encrypt_cfb128:"3eb39790678c56bee34bbcdeccf6cdb5":"00000000000000000000000000000000":"00000000000000000000000000000000":"858075d536d79ccee571f7d7204b1f67"

AES-128-CFB128 Encrypt NIST KAT #7
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_encrypt_cfb128:"00000000000000000000000000000000":"6a118a874519e64e9963798a503f1d35":"00000000000000000000000000000000":"dc43be40be0e53712f7e2bf5ca707209"

AES-128-CFB128 Encrypt NIST KAT #8
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_encrypt_cfb128:"00000000000000000000000000000000":"cb9fceec81286ca3e989bd979b0cb284":"00000000000000000000000000000000":"92beedab1895a94faa69b632e5cc47ce"

AES-128-CFB128 Encrypt NIST KAT #9
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_encrypt_cfb128:"00000000000000000000000000000000":"b26aeb1874e47ca8358ff22378f09144":"00000000000000000000000000000000":"459264f4798f6a78bacb89c15ed3d601"

AES-128-CFB128 Encrypt NIST KAT #10
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_encrypt_cfb128:"00000000000000000000000000000000":"fffffffffffffffffffffffffffffff0":"00000000000000000000000000000000":"f9b0fda0c4a898f5b9e6f661c4ce4d07"

AES-128-CFB128 Encrypt NIST KAT #11
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_encrypt_cfb128:"00000000000000000000000000000000":"fffffffffffffffffffffffffffffff8":"00000000000000000000000000000000":"8ade895913685c67c5269f8aae42983e"

AES-128-CFB128 Encrypt NIST KAT #12
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_encrypt_cfb128:"00000000000000000000000000000000":"fffffffffffffffffffffffffffffffc":"00000000000000000000000000000000":"39bde67d5c8ed8a8b1c37eb8fa9f5ac0"

AES-128-CFB128 Decrypt NIST KAT #1
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_decrypt_cfb128:"fffffffe000000000000000000000000":"00000000000000000000000000000000":"1114bc2028009b923f0b01915ce5e7c4":"00000000000000000000000000000000"

AES-128-CFB128 Decrypt NIST KAT #2
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_decrypt_cfb128:"ffffffff000000000000000000000000":"00000000000000000000000000000000":"9c28524a16a1e1c1452971caa8d13476":"00000000000000000000000000000000"

AES-128-CFB128 Decrypt NIST KAT #3
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_decrypt_cfb128:"ffffffff800000000000000000000000":"00000000000000000000000000000000":"ed62e16363638360fdd6ad62112794f0":"00000000000000000000000000000000"

AES-128-CFB128 Decrypt NIST KAT #4
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_decrypt_cfb128:"3071a2a48fe6cbd04f1a129098e308f8":"00000000000000000000000000000000":"4b98e06d356deb07ebb824e5713f7be3":"00000000000000000000000000000000"

AES-128-CFB128 Decrypt NIST KAT #5
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_decrypt_cfb128:"90f42ec0f68385f2ffc5dfc03a654dce":"00000000000000000000000000000000":"7a20a53d460fc9ce0423a7a0764c6cf2":"00000000000000000000000000000000"

AES-128-CFB128 Decrypt NIST KAT #6
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_decrypt_cfb128:"febd9a24d8b65c1c787d50a4ed3619a9":"00000000000000000000000000000000":"f4a70d8af877f9b02b4c40df57d45b17":"00000000000000000000000000000000"

AES-128-CFB128 Decrypt NIST KAT #7
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_decrypt_cfb128:"00000000000000000000000000000000":"f34481ec3cc627bacd5dc3fb08f273e6":"0336763e966d92595a567cc9ce537f5e":"00000000000000000000000000000000"

AES-128-CFB128 Decrypt NIST KAT #8
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_decrypt_cfb128:"00000000000000000000000000000000":"9798c4640bad75c7c3227db910174e72":"a9a1631bf4996954ebc093957b234589":"00000000000000000000000000000000"

AES-128-CFB128 Decrypt NIST KAT #9
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_decrypt_cfb128:"00000000000000000000000000000000":"96ab5c2ff612d9dfaae8c31f30c42168":"ff4f8391a6a40ca5b25d23bedd44a597":"00000000000000000000000000000000"

AES-128-CFB128 Decrypt NIST KAT #10
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_decrypt_cfb128:"00000000000000000000000000000000":"ffffffffffffffff0000000000000000":"f807c3e7985fe0f5a50e2cdb25c5109e":"00000000000000000000000000000000"

AES-128-CFB128 Decrypt NIST KAT #11
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_decrypt_cfb128:"00000000000000000000000000000000":"ffffffffffffffff8000000000000000":"41f992a856fb278b389a62f5d274d7e9":"00000000000000000000000000000000"

AES-128-CFB128 Decrypt NIST KAT #12
depends_on:MBEDTLS_CIPHER_MODE_CFB
aes_decrypt_cfb128:"00000000000000000000000000000000":"ffffffffffffffffc000000000000000":"10d3ed7a6fe15ab4d91acbc7d0767ab1":"00000000000000000000000000000000"

AES-192-CFB128 Encrypt NIST KAT #1
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"fffffffffffffffffffc0000000000000000000000000000":"00000000000000000000000000000000":"00000000000000000000000000000000":"8dfd999be5d0cfa35732c0ddc88ff5a5"

AES-192-CFB128 Encrypt NIST KAT #2
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"fffffffffffffffffffe0000000000000000000000000000":"00000000000000000000000000000000":"00000000000000000000000000000000":"02647c76a300c3173b841487eb2bae9f"

AES-192-CFB128 Encrypt NIST KAT #3
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"ffffffffffffffffffff0000000000000000000000000000":"00000000000000000000000000000000":"00000000000000000000000000000000":"172df8b02f04b53adab028b4e01acd87"

AES-192-CFB128 Encrypt NIST KAT #4
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"d184c36cf0dddfec39e654195006022237871a47c33d3198":"00000000000000000000000000000000":"00000000000000000000000000000000":"2e19fb60a3e1de0166f483c97824a978"

AES-192-CFB128 Encrypt NIST KAT #5
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"4c6994ffa9dcdc805b60c2c0095334c42d95a8fc0ca5b080":"00000000000000000000000000000000":"00000000000000000000000000000000":"7656709538dd5fec41e0ce6a0f8e207d"

AES-192-CFB128 Encrypt NIST KAT #6
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"c88f5b00a4ef9a6840e2acaf33f00a3bdc4e25895303fa72":"00000000000000000000000000000000":"00000000000000000000000000000000":"a67cf333b314d411d3c0ae6e1cfcd8f5"

AES-192-CFB128 Encrypt NIST KAT #7
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"000000000000000000000000000000000000000000000000":"9c2d8842e5f48f57648205d39a239af1":"00000000000000000000000000000000":"c9b8135ff1b5adc413dfd053b21bd96d"

AES-192-CFB128 Encrypt NIST KAT #8
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"000000000000000000000000000000000000000000000000":"bff52510095f518ecca60af4205444bb":"00000000000000000000000000000000":"4a3650c3371ce2eb35e389a171427440"

AES-192-CFB128 Encrypt NIST KAT #9
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"000000000000000000000000000000000000000000000000":"51719783d3185a535bd75adc65071ce1":"00000000000000000000000000000000":"4f354592ff7c8847d2d0870ca9481b7c"

AES-192-CFB128 Encrypt NIST KAT #10
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"000000000000000000000000000000000000000000000000":"ffffffffffffffe00000000000000000":"00000000000000000000000000000000":"f34e4a6324ea4a5c39a661c8fe5ada8f"

AES-192-CFB128 Encrypt NIST KAT #11
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"000000000000000000000000000000000000000000000000":"fffffffffffffff00000000000000000":"00000000000000000000000000000000":"0882a16f44088d42447a29ac090ec17e"

AES-192-CFB128 Encrypt NIST KAT #12
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"000000000000000000000000000000000000000000000000":"fffffffffffffff80000000000000000":"00000000000000000000000000000000":"3a3c15bfc11a9537c130687004e136ee"

AES-192-CFB128 Decrypt NIST KAT #1
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"ffffffffffffffffffffffffffffffffffffffffffe00000":"00000000000000000000000000000000":"60136703374f64e860b48ce31f930716":"00000000000000000000000000000000"

AES-192-CFB128 Decrypt NIST KAT #2
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"fffffffffffffffffffffffffffffffffffffffffff00000":"00000000000000000000000000000000":"8d63a269b14d506ccc401ab8a9f1b591":"00000000000000000000000000000000"

AES-192-CFB128 Decrypt NIST KAT #3
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"fffffffffffffffffffffffffffffffffffffffffff80000":"00000000000000000000000000000000":"d317f81dc6aa454aee4bd4a5a5cff4bd":"00000000000000000000000000000000"

AES-192-CFB128 Decrypt NIST KAT #4
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"98c6b8e01e379fbd14e61af6af891596583565f2a27d59e9":"00000000000000000000000000000000":"19c80ec4a6deb7e5ed1033dda933498f":"00000000000000000000000000000000"

AES-192-CFB128 Decrypt NIST KAT #5
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"b3ad5cea1dddc214ca969ac35f37dae1a9a9d1528f89bb35":"00000000000000000000000000000000":"3cf5e1d21a17956d1dffad6a7c41c659":"00000000000000000000000000000000"

AES-192-CFB128 Decrypt NIST KAT #6
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"45899367c3132849763073c435a9288a766c8b9ec2308516":"00000000000000000000000000000000":"69fd12e8505f8ded2fdcb197a121b362":"00000000000000000000000000000000"

AES-192-CFB128 Decrypt NIST KAT #7
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"000000000000000000000000000000000000000000000000":"1b077a6af4b7f98229de786d7516b639":"275cfc0413d8ccb70513c3859b1d0f72":"00000000000000000000000000000000"

AES-192-CFB128 Decrypt NIST KAT #8
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"000000000000000000000000000000000000000000000000":"9c2d8842e5f48f57648205d39a239af1":"c9b8135ff1b5adc413dfd053b21bd96d":"00000000000000000000000000000000"

AES-192-CFB128 Decrypt NIST KAT #9
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"000000000000000000000000000000000000000000000000":"bff52510095f518ecca60af4205444bb":"4a3650c3371ce2eb35e389a171427440":"00000000000000000000000000000000"

AES-192-CFB128 Decrypt NIST KAT #10
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"000000000000000000000000000000000000000000000000":"ffffffffffffffffffff000000000000":"54d632d03aba0bd0f91877ebdd4d09cb":"00000000000000000000000000000000"

AES-192-CFB128 Decrypt NIST KAT #11
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"000000000000000000000000000000000000000000000000":"ffffffffffffffffffff800000000000":"d3427be7e4d27cd54f5fe37b03cf0897":"00000000000000000000000000000000"

AES-192-CFB128 Decrypt NIST KAT #12
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"000000000000000000000000000000000000000000000000":"ffffffffffffffffffffc00000000000":"b2099795e88cc158fd75ea133d7e7fbe":"00000000000000000000000000000000"

AES-256-CFB128 Encrypt NIST KAT #1
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"ffffffe000000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"00000000000000000000000000000000":"bbd1097a62433f79449fa97d4ee80dbf"

AES-256-CFB128 Encrypt NIST KAT #2
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"fffffff000000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"00000000000000000000000000000000":"07058e408f5b99b0e0f061a1761b5b3b"

AES-256-CFB128 Encrypt NIST KAT #3
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"fffffff800000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"00000000000000000000000000000000":"5fd1f13fa0f31e37fabde328f894eac2"

AES-256-CFB128 Encrypt NIST KAT #4
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"13428b5e4c005e0636dd338405d173ab135dec2a25c22c5df0722d69dcc43887":"00000000000000000000000000000000":"00000000000000000000000000000000":"649a71545378c783e368c9ade7114f6c"

AES-256-CFB128 Encrypt NIST KAT #5
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"07eb03a08d291d1b07408bf3512ab40c91097ac77461aad4bb859647f74f00ee":"00000000000000000000000000000000":"00000000000000000000000000000000":"47cb030da2ab051dfc6c4bf6910d12bb"

AES-256-CFB128 Encrypt NIST KAT #6
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"90143ae20cd78c5d8ebdd6cb9dc1762427a96c78c639bccc41a61424564eafe1":"00000000000000000000000000000000":"00000000000000000000000000000000":"798c7c005dee432b2c8ea5dfa381ecc3"

AES-256-CFB128 Encrypt NIST KAT #7
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"0000000000000000000000000000000000000000000000000000000000000000":"0b24af36193ce4665f2825d7b4749c98":"00000000000000000000000000000000":"a9ff75bd7cf6613d3731c77c3b6d0c04"

AES-256-CFB128 Encrypt NIST KAT #8
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"0000000000000000000000000000000000000000000000000000000000000000":"761c1fe41a18acf20d241650611d90f1":"00000000000000000000000000000000":"623a52fcea5d443e48d9181ab32c7421"

AES-256-CFB128 Encrypt NIST KAT #9
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"0000000000000000000000000000000000000000000000000000000000000000":"8a560769d605868ad80d819bdba03771":"00000000000000000000000000000000":"38f2c7ae10612415d27ca190d27da8b4"

AES-256-CFB128 Encrypt NIST KAT #10
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"0000000000000000000000000000000000000000000000000000000000000000":"ffffffffffffffffffffffffe0000000":"00000000000000000000000000000000":"2be1fae5048a25582a679ca10905eb80"

AES-256-CFB128 Encrypt NIST KAT #11
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"0000000000000000000000000000000000000000000000000000000000000000":"fffffffffffffffffffffffff0000000":"00000000000000000000000000000000":"da86f292c6f41ea34fb2068df75ecc29"

AES-256-CFB128 Encrypt NIST KAT #12
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb128:"0000000000000000000000000000000000000000000000000000000000000000":"fffffffffffffffffffffffff8000000":"00000000000000000000000000000000":"220df19f85d69b1b562fa69a3c5beca5"

AES-256-CFB128 Decrypt NIST KAT #1
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"ffffffffff800000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"be66cfea2fecd6bf0ec7b4352c99bcaa":"00000000000000000000000000000000"

AES-256-CFB128 Decrypt NIST KAT #2
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"ffffffffffc00000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"df31144f87a2ef523facdcf21a427804":"00000000000000000000000000000000"

AES-256-CFB128 Decrypt NIST KAT #3
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"ffffffffffe00000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"b5bb0f5629fb6aae5e1839a3c3625d63":"00000000000000000000000000000000"

AES-256-CFB128 Decrypt NIST KAT #4
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"1d85a181b54cde51f0e098095b2962fdc93b51fe9b88602b3f54130bf76a5bd9":"00000000000000000000000000000000":"531c2c38344578b84d50b3c917bbb6e1":"00000000000000000000000000000000"

AES-256-CFB128 Decrypt NIST KAT #5
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"dc0eba1f2232a7879ded34ed8428eeb8769b056bbaf8ad77cb65c3541430b4cf":"00000000000000000000000000000000":"fc6aec906323480005c58e7e1ab004ad":"00000000000000000000000000000000"

AES-256-CFB128 Decrypt NIST KAT #6
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"f8be9ba615c5a952cabbca24f68f8593039624d524c816acda2c9183bd917cb9":"00000000000000000000000000000000":"a3944b95ca0b52043584ef02151926a8":"00000000000000000000000000000000"

AES-256-CFB128 Decrypt NIST KAT #7
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"0000000000000000000000000000000000000000000000000000000000000000":"761c1fe41a18acf20d241650611d90f1":"623a52fcea5d443e48d9181ab32c7421":"00000000000000000000000000000000"

AES-256-CFB128 Decrypt NIST KAT #8
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"0000000000000000000000000000000000000000000000000000000000000000":"8a560769d605868ad80d819bdba03771":"38f2c7ae10612415d27ca190d27da8b4":"00000000000000000000000000000000"

AES-256-CFB128 Decrypt NIST KAT #9
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"0000000000000000000000000000000000000000000000000000000000000000":"91fbef2d15a97816060bee1feaa49afe":"1bc704f1bce135ceb810341b216d7abe":"00000000000000000000000000000000"

AES-256-CFB128 Decrypt NIST KAT #10
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"0000000000000000000000000000000000000000000000000000000000000000":"e0000000000000000000000000000000":"9b80eefb7ebe2d2b16247aa0efc72f5d":"00000000000000000000000000000000"

AES-256-CFB128 Decrypt NIST KAT #11
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"0000000000000000000000000000000000000000000000000000000000000000":"f0000000000000000000000000000000":"7f2c5ece07a98d8bee13c51177395ff7":"00000000000000000000000000000000"

AES-256-CFB128 Decrypt NIST KAT #12
depends_on:MBEDTLS_CIPHER_MODE_CFB:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb128:"0000000000000000000000000000000000000000000000000000000000000000":"f8000000000000000000000000000000":"7818d800dcf6f4be1e0e94f403d1e4c2":"00000000000000000000000000000000"

AES-128-CFB8 Encrypt NIST MMT #0
aes_encrypt_cfb8:"c57d699d89df7cfbef71c080a6b10ac3":"fcb2bc4c006b87483978796a2ae2c42e":"61":"24"

AES-128-CFB8 Encrypt NIST MMT #1
aes_encrypt_cfb8:"0d8f3dc3edee60db658bb97faf46fba3":"e481fdc42e606b96a383c0a1a5520ebb":"aacd":"5066"

AES-128-CFB8 Encrypt NIST MMT #2
aes_encrypt_cfb8:"c8fe9bf77b930f46d2078b8c0e657cd4":"f475c64991b20eaee183a22629e21e22":"c90635":"d27691"

AES-128-CFB8 Encrypt NIST MMT #3
aes_encrypt_cfb8:"280cf81af5cc7e7363579c1da03390e6":"5d6cf4722d0e21f1d9ced53a0e36c342":"b2a22ced":"73f3aebf"

AES-128-CFB8 Encrypt NIST MMT #4
aes_encrypt_cfb8:"5d5e7f20e0a66d3e09e0e5a9912f8a46":"052d7ea0ad1f2956a23b27afe1d87b6b":"b84a90fc6d":"1a9a61c307"

AES-128-CFB8 Encrypt NIST MMT #5
aes_encrypt_cfb8:"ec89fb348787cf902ca973c47081438d":"528fe95c711bd13f37bc52cc9e96d45c":"14253472e99d":"cfc247e33a3b"

AES-128-CFB8 Encrypt NIST MMT #6
aes_encrypt_cfb8:"6607987c354809cba818639dcd185147":"552c101a0b7c0ca143af258453937fa3":"9b1a5a1369166e":"b7ab2a4cc71904"

AES-128-CFB8 Encrypt NIST MMT #7
aes_encrypt_cfb8:"c028e6bf2b749ffa86759f2f84e93cb0":"288c752d9faccf367e5d0cca1fa6ec3b":"324015878cdc82bf":"873250152fc6a5bb"

AES-128-CFB8 Encrypt NIST MMT #8
aes_encrypt_cfb8:"d01da95d2c2a61da06ea78cfba59cc30":"f9a393ad90814faf262e3a5b1d97592e":"57c1a30e48166d9640":"e9a8c3b776edd39e3d"

AES-128-CFB8 Encrypt NIST MMT #9
aes_encrypt_cfb8:"3a6f9159263fa6cef2a075caface5817":"0fc23662b7dbf73827f0c7de321ca36e":"87efeb8d559ed3367728":"8e9c50425614d540ce11"

AES-128-CFB8 Decrypt NIST MMT #0
aes_decrypt_cfb8:"03edfe082550bd5ac8ddf64f42a0547f":"52acd8dab62c981da08e51939cc08dab":"21":"09"

AES-128-CFB8 Decrypt NIST MMT #1
aes_decrypt_cfb8:"38cf776750162edc63c3b5dbe311ab9f":"98fbbd288872c40f1926b16ecaec1561":"4878":"eb24"

AES-128-CFB8 Decrypt NIST MMT #2
aes_decrypt_cfb8:"c9053c87c3e56bc5e52bd31f6545f991":"b8f9640d0923da13fe6eb87b01f0cfa0":"aeb6d2":"910949"

AES-128-CFB8 Decrypt NIST MMT #3
aes_decrypt_cfb8:"e96771f5f20a89ee871261d2d18e1e46":"6e86403e33396655907ae06ef192262f":"83cab2f3":"3b7f1f1c"

AES-128-CFB8 Decrypt NIST MMT #4
aes_decrypt_cfb8:"92ad13ecb60bde1bb3b34ce07867672b":"f95a4060b8f80e3f839d4c3ca33dad94":"49f73e652b":"17b9b9e16d"

AES-128-CFB8 Decrypt NIST MMT #5
aes_decrypt_cfb8:"eb57b8dd076e7bbb33d4bfc4d7ecb27e":"51135997a067dcd2e016c57134c5fa52":"b0eacbf2ca46":"ca989fa4e818"

AES-128-CFB8 Decrypt NIST MMT #6
aes_decrypt_cfb8:"70abc48bb1be490183f0fe3df56195ff":"e251f179174b71ee1e488ab3dd200483":"08fbef9b2a369a":"5405da1186b7e0"

AES-128-CFB8 Decrypt NIST MMT #7
aes_decrypt_cfb8:"1273b8e0eee1a1ca827059b4d0a3a55d":"622cab49092d026f554dd98a6441dc26":"b3cb9d8892423aeb":"d497df73afb9787c"

AES-128-CFB8 Decrypt NIST MMT #8
aes_decrypt_cfb8:"49437e06b6faa5f20fd98bf71f8ff554":"63c818e0d3cb5b7054ef3e1e87df0e12":"01992a986279c3685e":"f203bcd402b65919da"

AES-128-CFB8 Decrypt NIST MMT #9
aes_decrypt_cfb8:"6399c1dc068ba3509845628fa9ed1a96":"1157c2766c86b754df485be9dd5851df":"c9c284e9abbfe6fb11fe":"feff4e2e2458addf2a54"

AES-192-CFB8 Encrypt NIST MMT #0
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb8:"32a1b0e3da368db563d7316b9779d3327e53d9a6d287ed97":"3dd0e7e21f09d5842f3a699da9b57346":"54":"6d"

AES-192-CFB8 Encrypt NIST MMT #1
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb8:"a6381dcc18dd85d7729c1dce90743bbe1df580d857f5b9c4":"c0ac501fad7f4a1465daf32e18fc1a4f":"a456":"8fb6"

AES-192-CFB8 Encrypt NIST MMT #2
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb8:"d08dbee4732c7ffc544c1695b201d30e795037325ef0aa18":"a1e39aeeb972a8d70aa0fc7d6fac6eac":"fd115d":"c4c016"

AES-192-CFB8 Encrypt NIST MMT #3
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb8:"277185a4a440869920f523c4d578fc5bedd33aee8d2ebaf7":"67be00572f82aabc13d6e5a2e51d1f08":"88e07061":"8bb630ba"

AES-192-CFB8 Encrypt NIST MMT #4
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb8:"83f70fdce47306fcbb8c21b6a8b3209f7ec185fef4deebd4":"ff73b310cf7e62ce6f501092fa6cc888":"36664e222d":"20855555d1"

AES-192-CFB8 Encrypt NIST MMT #5
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb8:"c5be271a29f4a29e085e8e98196601dcb88ccc03e559a304":"9f51fa2eb8a084718f7240e47d135dce":"b57f12342a62":"73ff9bf3ec4b"

AES-192-CFB8 Encrypt NIST MMT #6
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb8:"9c55322e6d495be01076d4b80371ad1479ae5636ff9861f5":"2b79cfc1ff37254dedf5924a6b61e3e0":"6dcede43c2ee65":"7c897658282220"

AES-192-CFB8 Encrypt NIST MMT #7
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb8:"6e78ccece7d1b2a3c08cf0de738bee33cbbbf78d9bf4922c":"4bbe15b1e94a7b97250a2136d8804e46":"ceda42527871f802":"d92ff89045b1917f"

AES-192-CFB8 Encrypt NIST MMT #8
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb8:"13c98665746f7825b37b404916240adbd1e4364be1d05c63":"0e479fbd5f3961f38b8a26be1f2d65c5":"1b0a63d73464ab3c8a":"5485847e5d3c2e2cc4"

AES-192-CFB8 Encrypt NIST MMT #9
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb8:"537e7bf661fd4024a024613f15b13690f7d0c847c1e18965":"3a81f9d9d3c155b0caad5d73349476fc":"d3d8b9b984adc24237ee":"3879fea72ac99929e53a"

AES-192-CFB8 Decrypt NIST MMT #0
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb8:"7dbdc15ad4034ed828dc862799b7adc9abd68eaf9d526d5d":"4359683af5a3a85c248fb7f5506f317b":"25":"2d"

AES-192-CFB8 Decrypt NIST MMT #1
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb8:"3a2cdf9c9608c1dd6233d03dd855293b0885915114b25279":"e7a28ee34acc52128ddae658ec6398a2":"0678":"7b04"

AES-192-CFB8 Decrypt NIST MMT #2
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb8:"c984b99a6cc5bc88003143cbe4b755e6e30ba94114f7ad1e":"41e3b8fd138f8c358dfeef420302f634":"037cf6":"658d0a"

AES-192-CFB8 Decrypt NIST MMT #3
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb8:"39747da225bdc0c53c3463fd686dbe19d14157535171f91d":"77d3a5ad8bbdb169f8d29e5f21798651":"0fb0cee2":"2d191f2f"

AES-192-CFB8 Decrypt NIST MMT #4
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb8:"4cd13179dfa16d01c6a8633dfc8783e723e72114c9b0d50a":"6657c46c99d642474c330d8016b71dbe":"09d914cf0b":"105a64c872"

AES-192-CFB8 Decrypt NIST MMT #5
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb8:"5dcc9b8d8a456e9917cd8d54d7f7100b34964b4ed2d398a0":"4fa295a8987f1b010ce4e011fbf94156":"288c752d9fac":"98f332d37b78"

AES-192-CFB8 Decrypt NIST MMT #6
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb8:"c8baf0204ef80b8e0125efe43a0bccdfd0f356b62e6c75fe":"e9144bf2cbc5720a1b4cb6f37d11edff":"c9981a34b7aa89":"56bb4c3cae53b3"

AES-192-CFB8 Decrypt NIST MMT #7
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb8:"64e40763f38a63ae378c32052b0ae3aa538bb868a04ac985":"aacf65089e4b285438451ffdcd0f6389":"d8fcf83a88510a0d":"b567411bc61b0a76"

AES-192-CFB8 Decrypt NIST MMT #8
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb8:"7bfdca9605f17253f203efffc92da96fde023007d22cdad0":"45c09e44036070f8a7737a5176b8cf26":"9c195b1944c4af5bfb":"89358df65c3ef14d26"

AES-192-CFB8 Decrypt NIST MMT #9
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb8:"baf08b76317a65c5f07ae6f57eb0e65488659324d29709e3":"0a02846b62abb693ef31d754842eed29":"729c0b6deb75fa6eb5e8":"9895932402393dc33a60"

AES-256-CFB8 Encrypt NIST MMT #0
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb8:"34e8091cee09f1bd3ebf1e8f05f51bfbd4899ef2ae006a3a0f7875052cdd46c8":"43eb4dcc4b04a80216a20e4a09a7abb5":"f9":"28"

AES-256-CFB8 Encrypt NIST MMT #1
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb8:"e04e43173113109e1343393842fe6caef3f8a2e506d7f55f83dcb10444c6ad23":"a38b88a293b077fb5546636aad90d663":"2914":"69a6"

AES-256-CFB8 Encrypt NIST MMT #2
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb8:"064874092f7a13cc4462247ad423d0e96edf42e8b67a5a23b7a0a6477b098e66":"338c552ff1eca14408e05d8cf9f3b31b":"b974fa":"1cff95"

AES-256-CFB8 Encrypt NIST MMT #3
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb8:"56794adb0ef04aeddeabd650de736531d408837954b919002c33edfdff976cc2":"71b5526facea4236d33f1f4107e4b04f":"db774912":"f04d9d4f"

AES-256-CFB8 Encrypt NIST MMT #4
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb8:"dddd7f234e7d0e6ec64560b96430986a856f2ee9805443a7946e31601ef6679d":"e20f39db0025eb24491bd06012887108":"ad1d5311ea":"19cc97a662"

AES-256-CFB8 Encrypt NIST MMT #5
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb8:"ec73a760272c83f91771b3ab7b188715c6d6afb9c554feae83856e966a3863d0":"ae7bfa38fd25778fcf66ce8157f6e42e":"02fe724fbc5d":"b0eca63405f4"

AES-256-CFB8 Encrypt NIST MMT #6
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb8:"a66874ca0b70fb98b37c033ec96413f339adae02acade015b9f016b459db3309":"6ed480d9e4ed031cf66bb1e07f8d5514":"b4777e6bcd9a05":"8c017397ad5bab"

AES-256-CFB8 Encrypt NIST MMT #7
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb8:"a3dbbb775ada92b0b8ed1632444e21c1c86ff3eba8f628307306e766b8c15b5c":"4ec56a8e541f5cfe7b8ab947bfa4fd08":"1d70a5a82badf5ea":"1e22bebebeacd81d"

AES-256-CFB8 Encrypt NIST MMT #8
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb8:"64135e67c0ca1acef3360d930afcd726c5b04861a69c1b6a48bde1daf20f3b1f":"5377a154d5f948189f9aa57b466c16b2":"a36ca5ea382a322eef":"3105016567d3174aed"

AES-256-CFB8 Encrypt NIST MMT #9
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cfb8:"ebbb4566b5e182e0f072466b0b311df38f9175bc0213a5530bce2ec4d74f400d":"0956a48e01002c9e16376d6e308dbad1":"b0fe25ac8d3d28a2f471":"638c6823e7256fb5626e"

AES-256-CFB8 Decrypt NIST MMT #0
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb8:"1687831580cb764321a9d674dbd0a9640f668b0f58ef01b87a710b3095d5f855":"6cd5bec6d6e1fd23afc543b8f80d3f89":"6f":"98"

AES-256-CFB8 Decrypt NIST MMT #1
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb8:"b6b504e8b7065373ea31cd549e52eda7cb96fd1db14eddacbc420085ab48b747":"870ecd45b1241803ddaf8bad15a025d7":"17d4":"3572"

AES-256-CFB8 Decrypt NIST MMT #2
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb8:"6ad3105e15fb5b742bf4fe1eb8e98c6c1ffea653107c84f6b42ed1232a0bbc21":"17534c89c4eae5dea6ea353dde7b1623":"a9841e":"f9411a"

AES-256-CFB8 Decrypt NIST MMT #3
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb8:"758f3fa8b2b289f19fd59e7316be40b904eff7f565caac4570f972360e0da787":"b21335ae980898fa92c4b3069e532973":"84b35e25":"47887872"

AES-256-CFB8 Decrypt NIST MMT #4
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb8:"802e854eb799500975d960a67885820d195e02ab23d51f15e5cdbcee86a1580c":"94478c4e44e2fa8d2e6bc43d384597e6":"d1e96bf1e8":"ed414b5689"

AES-256-CFB8 Decrypt NIST MMT #5
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb8:"3a0c03ca9d1e5d49bb37f9041f88d159c3f1d5ce26c798f59ed54a93f0a0e600":"9aae38ba832e4b093b50444074517d20":"74410ccd12da":"8207eee2a7ab"

AES-256-CFB8 Decrypt NIST MMT #6
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb8:"ee05462128fea75e919f6f436cb198f222847d698a283f5767df682d33d3ce77":"d2ad55e41887075184635112a22fc093":"ff039e89877b44":"aff3aa4c24e353"

AES-256-CFB8 Decrypt NIST MMT #7
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb8:"08abbdcc3eb9c1717db1faa38dcd0893afd5e16e2596747af58f8d61ebedf9cd":"b925c8dc9a9b55a4372ea6d37d21c1eb":"e176ba99ea602fd9":"b7370050288bf600"

AES-256-CFB8 Decrypt NIST MMT #8
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb8:"56d404a893fb3b3f594aab18939230b096646a37a781629fbd9270f3891a5cea":"e5906b36f2d97e6f2db19b6c7a3ce319":"c55a9a917a809a784b":"e44995bbb0fff40fee"

AES-256-CFB8 Decrypt NIST MMT #9
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cfb8:"ec13062551e4d7291e320f565b749eea1809b663b26f2c4d53b52058b833e0ad":"fbfa5a528e20863012790c2abafb5a0c":"2bfc3f0209307140101a":"547bfd642cf6e12ed942"
