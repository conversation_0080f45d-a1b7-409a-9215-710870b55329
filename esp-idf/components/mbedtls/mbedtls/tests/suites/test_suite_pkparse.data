Parse RSA Key #1 (No password when required)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_PEM_PARSE_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_AES_C
pk_parse_keyfile_rsa:"../framework/data_files/test-ca.key":"NULL":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #2 (Correct password)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_PEM_PARSE_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_AES_C
pk_parse_keyfile_rsa:"../framework/data_files/test-ca.key":"PolarSSLTest":0

Parse RSA Key #3 (Wrong password)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_PEM_PARSE_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_AES_C
pk_parse_keyfile_rsa:"../framework/data_files/test-ca.key":"PolarSSLWRONG":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #4 (DES Encrypted)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_DES_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs1_1024_des.pem":"testkey":0

Parse RSA Key #5 (3DES Encrypted)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_DES_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs1_1024_3des.pem":"testkey":0

Parse RSA Key #6 (AES-128 Encrypted)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_AES_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs1_1024_aes128.pem":"testkey":0

Parse RSA Key #7 (AES-192 Encrypted)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_AES_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs1_1024_aes192.pem":"testkey":0

Parse RSA Key #8 (AES-256 Encrypted)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_AES_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs1_1024_aes256.pem":"testkey":0

Parse RSA Key #9 (2048-bit, DES Encrypted)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_DES_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs1_2048_des.pem":"testkey":0

Parse RSA Key #10 (2048-bit, 3DES Encrypted)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_DES_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs1_2048_3des.pem":"testkey":0

Parse RSA Key #11 (2048-bit, AES-128 Encrypted)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_AES_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs1_2048_aes128.pem":"testkey":0

Parse RSA Key #12 (2048-bit, AES-192 Encrypted)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_AES_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs1_2048_aes192.pem":"testkey":0

Parse RSA Key #13 (2048-bit, AES-256 Encrypted)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_AES_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs1_2048_aes256.pem":"testkey":0

Parse RSA Key #14 (4096-bit, DES Encrypted)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_DES_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs1_4096_des.pem":"testkey":0

Parse RSA Key #15 (4096-bit, 3DES Encrypted)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_DES_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs1_4096_3des.pem":"testkey":0

Parse RSA Key #16 (4096-bit, AES-128 Encrypted)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_AES_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs1_4096_aes128.pem":"testkey":0

Parse RSA Key #17 (4096-bit, AES-192 Encrypted)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_AES_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs1_4096_aes192.pem":"testkey":0

Parse RSA Key #18 (4096-bit, AES-256 Encrypted)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_AES_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs1_4096_aes256.pem":"testkey":0

Parse RSA Key #19 (PKCS#8 wrapped)
depends_on:MBEDTLS_MD_CAN_MD5:MBEDTLS_PEM_PARSE_C
pk_parse_keyfile_rsa:"../framework/data_files/format_gen.key":"":0

Parse RSA Key #20 (PKCS#8 encrypted SHA1-3DES)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_1024_3des.pem":"PolarSSLTest":0

Parse RSA Key #20.1 (PKCS#8 encrypted SHA1-3DES, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_1024_3des.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #20.2 (PKCS#8 encrypted SHA1-3DES, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_1024_3des.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #21 (PKCS#8 encrypted SHA1-3DES, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_2048_3des.pem":"PolarSSLTest":0

Parse RSA Key #21.1 (PKCS#8 encrypted SHA1-3DES, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_2048_3des.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #21.2 (PKCS#8 encrypted SHA1-3DES, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_2048_3des.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #22 (PKCS#8 encrypted SHA1-3DES, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_4096_3des.pem":"PolarSSLTest":0

Parse RSA Key #22.1 (PKCS#8 encrypted SHA1-3DES, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_4096_3des.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #22.2 (PKCS#8 encrypted SHA1-3DES, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_4096_3des.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #23 (PKCS#8 encrypted SHA1-3DES DER)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_1024_3des.der":"PolarSSLTest":0

Parse RSA Key #24 (PKCS#8 encrypted SHA1-3DES DER, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_2048_3des.der":"PolarSSLTest":0

Parse RSA Key #25 (PKCS#8 encrypted SHA1-3DES DER, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_4096_3des.der":"PolarSSLTest":0

Parse RSA Key #26 (PKCS#8 encrypted SHA1-2DES)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_1024_2des.pem":"PolarSSLTest":0

Parse RSA Key #26.1 (PKCS#8 encrypted SHA1-2DES, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_1024_2des.pem":"PolarSLTest":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #26.2 (PKCS#8 encrypted SHA1-2DES, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_1024_2des.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #27 (PKCS#8 encrypted SHA1-2DES, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_2048_2des.pem":"PolarSSLTest":0

Parse RSA Key #27.1 (PKCS#8 encrypted SHA1-2DES, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_2048_2des.pem":"PolarSLTest":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #27.2 (PKCS#8 encrypted SHA1-2DES, 2048-bit no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_2048_2des.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #28 (PKCS#8 encrypted SHA1-2DES, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_4096_2des.pem":"PolarSSLTest":0

Parse RSA Key #28.1 (PKCS#8 encrypted SHA1-2DES, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_4096_2des.pem":"PolarSLTest":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #28.2 (PKCS#8 encrypted SHA1-2DES, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_4096_2des.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #29 (PKCS#8 encrypted SHA1-2DES DER)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_1024_2des.der":"PolarSSLTest":0

Parse RSA Key #30 (PKCS#8 encrypted SHA1-2DES DER, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_2048_2des.der":"PolarSSLTest":0

Parse RSA Key #31 (PKCS#8 encrypted SHA1-2DES DER, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbe_sha1_4096_2des.der":"PolarSSLTest":0

Parse RSA Key #38 (PKCS#8 encrypted v2 PBKDF2 3DES)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des.pem":"PolarSSLTest":0

Parse RSA Key #38.1 (PKCS#8 encrypted v2 PBKDF2 3DES, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #38.2 (PKCS#8 encrypted v2 PBKDF2 3DES, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #39 (PKCS#8 encrypted v2 PBKDF2 3DES, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des.pem":"PolarSSLTest":0

Parse RSA Key #39.1 (PKCS#8 encrypted v2 PBKDF2 3DES, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #39.2 (PKCS#8 encrypted v2 PBKDF2 3DES, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #40 (PKCS#8 encrypted v2 PBKDF2 3DES, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des.pem":"PolarSSLTest":0

Parse RSA Key #40.1 (PKCS#8 encrypted v2 PBKDF2 3DES, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #40.2 (PKCS#8 encrypted v2 PBKDF2 3DES, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #41 (PKCS#8 encrypted v2 PBKDF2 3DES DER)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des.der":"PolarSSLTest":0

Parse RSA Key #41.1 (PKCS#8 encrypted v2 PBKDF2 3DES DER, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #41.2 (PKCS#8 encrypted v2 PBKDF2 3DES DER, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #42 (PKCS#8 encrypted v2 PBKDF2 3DES DER, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des.der":"PolarSSLTest":0

Parse RSA Key #42.1 (PKCS#8 encrypted v2 PBKDF2 3DES DER, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #42.2 (PKCS#8 encrypted v2 PBKDF2 3DES DER, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #43 (PKCS#8 encrypted v2 PBKDF2 3DES DER, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des.der":"PolarSSLTest":0

Parse RSA Key #43.1 (PKCS#8 encrypted v2 PBKDF2 3DES DER, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #43.2 (PKCS#8 encrypted v2 PBKDF2 3DES DER, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #44 (PKCS#8 encrypted v2 PBKDF2 DES)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des.pem":"PolarSSLTest":0

Parse RSA Key #44.1 (PKCS#8 encrypted v2 PBKDF2 DES, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #44.2 (PKCS#8 encrypted v2 PBKDF2 DES, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #45 (PKCS#8 encrypted v2 PBKDF2 DES, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des.pem":"PolarSSLTest":0

Parse RSA Key #45.1 (PKCS#8 encrypted v2 PBKDF2 DES, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #45.2 (PKCS#8 encrypted v2 PBKDF2 DES, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #46 (PKCS#8 encrypted v2 PBKDF2 DES, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des.pem":"PolarSSLTest":0

Parse RSA Key #46.1 (PKCS#8 encrypted v2 PBKDF2 DES, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #46.2 (PKCS#8 encrypted v2 PBKDF2 DES, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #47 (PKCS#8 encrypted v2 PBKDF2 DES DER)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des.der":"PolarSSLTest":0

Parse RSA Key #47.1 (PKCS#8 encrypted v2 PBKDF2 DES DER, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #47.2 (PKCS#8 encrypted v2 PBKDF2 DES DER, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #48 (PKCS#8 encrypted v2 PBKDF2 DES DER, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des.der":"PolarSSLTest":0

Parse RSA Key #48.1 (PKCS#8 encrypted v2 PBKDF2 DES DER, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #48.2 (PKCS#8 encrypted v2 PBKDF2 DES DER, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #49 (PKCS#8 encrypted v2 PBKDF2 DES DER, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des.der":"PolarSSLTest":0

Parse RSA Key #49.1 (PKCS#8 encrypted v2 PBKDF2 DES DER, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #49.2 (PKCS#8 encrypted v2 PBKDF2 DES DER, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA1:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #50 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA224)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha224.pem":"PolarSSLTest":0

Parse RSA Key #50.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA224, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha224.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #50.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA224, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha224.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #51 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA224, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha224.pem":"PolarSSLTest":0

Parse RSA Key #51.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA224, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha224.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #51.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA224, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha224.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #52 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA224, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha224.pem":"PolarSSLTest":0

Parse RSA Key #52.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA224, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha224.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #52.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA224, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha224.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #53 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA224 DER)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha224.der":"PolarSSLTest":0

Parse RSA Key #53.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA224 DER, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha224.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #53.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA224 DER, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha224.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #54 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA224 DER, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha224.der":"PolarSSLTest":0

Parse RSA Key #54.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA224 DER, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha224.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #54.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA224 DER, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha224.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #55 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA224 DER, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha224.der":"PolarSSLTest":0

Parse RSA Key #55.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA224 DER, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha224.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #55.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA224 DER, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha224.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #56 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA224)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha224.pem":"PolarSSLTest":0

Parse RSA Key #56.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA224, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha224.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #56.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA224, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha224.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #57 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA224, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha224.pem":"PolarSSLTest":0

Parse RSA Key #57.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA224, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha224.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #57.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA224, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha224.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #58 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA224, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha224.pem":"PolarSSLTest":0

Parse RSA Key #58.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA224, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha224.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #58.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA224, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha224.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #59 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA224 DER)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha224.der":"PolarSSLTest":0

Parse RSA Key #59.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA224 DER, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha224.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #59.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA224 DER, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha224.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #60 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA224 DER, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha224.der":"PolarSSLTest":0

Parse RSA Key #60.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA224 DER, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha224.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #60.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA224 DER, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha224.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #61 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA224 DER, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha224.der":"PolarSSLTest":0

Parse RSA Key #61.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA224 DER, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha224.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #61.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA224 DER, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA224:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha224.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #62 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA256)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha256.pem":"PolarSSLTest":0

Parse RSA Key #62.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA256, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha256.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #62.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA256, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha256.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #63 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA256, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha256.pem":"PolarSSLTest":0

Parse RSA Key #63.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA256, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha256.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #63.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA256, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha256.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #64 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA256, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha256.pem":"PolarSSLTest":0

Parse RSA Key #64.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA256, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha256.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #64.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA256, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha256.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #65 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA256 DER)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha256.der":"PolarSSLTest":0

Parse RSA Key #65.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA256 DER, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha256.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #65.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA256 DER, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha256.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #66 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA256 DER, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha256.der":"PolarSSLTest":0

Parse RSA Key #66.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA256 DER, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha256.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #66.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA256 DER, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha256.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #67 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA256 DER, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha256.der":"PolarSSLTest":0

Parse RSA Key #68.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA256 DER, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha256.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #68.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA256 DER, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha256.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #69 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA256)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha256.pem":"PolarSSLTest":0

Parse RSA Key #69.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA256, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha256.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #69.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA256, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha256.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #70 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA256, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha256.pem":"PolarSSLTest":0

Parse RSA Key #70.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA256, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha256.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #70.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA256, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha256.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #71 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA256, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha256.pem":"PolarSSLTest":0

Parse RSA Key #71.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA256, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha256.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #71.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA256, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha256.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #72 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA256 DER)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha256.der":"PolarSSLTest":0

Parse RSA Key #72.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA256 DER, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha256.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #72.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA256 DER, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha256.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #73 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA256 DER, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha256.der":"PolarSSLTest":0

Parse RSA Key #73.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA256 DER, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha256.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #73.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA256 DER, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha256.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #74 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA256 DER, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha256.der":"PolarSSLTest":0

Parse RSA Key #74.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA256 DER, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha256.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #74.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA256 DER, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA256:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha256.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #75 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA384)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha384.pem":"PolarSSLTest":0

Parse RSA Key #75.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA384, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha384.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #75.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA384, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha384.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #76 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA384, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha384.pem":"PolarSSLTest":0

Parse RSA Key #76.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA384, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha384.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #76.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA384, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha384.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #77 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA384, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha384.pem":"PolarSSLTest":0

Parse RSA Key #77.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA384, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha384.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #77.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA384, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha384.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #78 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA384 DER)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha384.der":"PolarSSLTest":0

Parse RSA Key #78.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA384 DER, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha384.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #78.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA384 DER, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha384.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #79 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA384 DER, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha384.der":"PolarSSLTest":0

Parse RSA Key #79.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA384 DER, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha384.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #79.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA384 DER, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha384.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #80 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA384 DER, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha384.der":"PolarSSLTest":0

Parse RSA Key #80.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA384 DER, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha384.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #80.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA384 DER, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha384.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #81 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA384)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha384.pem":"PolarSSLTest":0

Parse RSA Key #81.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA384, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha384.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #81.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA384, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha384.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #82 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA384, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha384.pem":"PolarSSLTest":0

Parse RSA Key #82.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA384, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha384.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #82.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA384, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha384.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #83 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA384, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha384.pem":"PolarSSLTest":0

Parse RSA Key #83.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA384, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha384.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #83.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA384, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha384.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #84 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA384 DER)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha384.der":"PolarSSLTest":0

Parse RSA Key #84.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA384 DER, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha384.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #85.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA384 DER, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha384.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #86 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA384 DER, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha384.der":"PolarSSLTest":0

Parse RSA Key #86.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA384 DER, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha384.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #86.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA384 DER, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha384.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #87 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA384 DER, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha384.der":"PolarSSLTest":0

Parse RSA Key #87.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA384 DER, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha384.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #87.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA384 DER, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha384.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #88 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA512)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha512.pem":"PolarSSLTest":0

Parse RSA Key #88.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA512, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha512.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #88.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA512, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha512.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #89 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA512, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha512.pem":"PolarSSLTest":0

Parse RSA Key #89.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA512, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha512.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #89.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA512, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha512.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #90 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA512, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha512.pem":"PolarSSLTest":0

Parse RSA Key #90.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA512, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha512.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #90.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA512, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha512.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #91 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA512 DER)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha512.der":"PolarSSLTest":0

Parse RSA Key #91.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA512 DER, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha512.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #91.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA512 DER, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_3des_sha512.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #92 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA512 DER, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha512.der":"PolarSSLTest":0

Parse RSA Key #92.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA512 DER, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha512.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #92.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA512 DER, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_3des_sha512.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #93 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA512 DER, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha512.der":"PolarSSLTest":0

Parse RSA Key #93.1 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA512 DER, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha512.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #93.2 (PKCS#8 encrypted v2 PBKDF2 3DES hmacWithSHA512 DER, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_3des_sha512.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #94 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA512)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha512.pem":"PolarSSLTest":0

Parse RSA Key #94.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA512, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha512.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #94.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA512, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha512.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #95 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA512, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha512.pem":"PolarSSLTest":0

Parse RSA Key #95.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA512, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha512.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #95.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA512, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha512.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #96 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA512, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha512.pem":"PolarSSLTest":0

Parse RSA Key #96.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA512, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha512.pem":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #96.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA512, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha512.pem":"":MBEDTLS_ERR_PK_PASSWORD_REQUIRED

Parse RSA Key #97 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA512 DER)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha512.der":"PolarSSLTest":0

Parse RSA Key #97.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA512 DER, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha512.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #97.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA512 DER, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_1024_des_sha512.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #98 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA512 DER, 2048-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha512.der":"PolarSSLTest":0

Parse RSA Key #98.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA512 DER, 2048-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha512.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #98.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA512 DER, 2048-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_des_sha512.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #99 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA512 DER, 4096-bit)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha512.der":"PolarSSLTest":0

Parse RSA Key #99.1 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA512 DER, 4096-bit, wrong PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha512.der":"PolarSSLTes":MBEDTLS_ERR_PK_PASSWORD_MISMATCH

Parse RSA Key #99.2 (PKCS#8 encrypted v2 PBKDF2 DES hmacWithSHA512 DER, 4096-bit, no PW)
depends_on:MBEDTLS_DES_C:MBEDTLS_MD_CAN_SHA512:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_TEST_PK_ALLOW_RSA_KEY_PAIR_4096
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_4096_des_sha512.der":"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Parse RSA Key #99.3 (PKCS#8 encrypted v2 PBKDF2 AES-128-CBC hmacWithSHA384, 2048-bit)
depends_on:MBEDTLS_AES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_aes128cbc_sha384.pem":"PolarSSLTest":0

Parse RSA Key #99.4 (PKCS#8 encrypted v2 PBKDF2 AES-192-CBC hmacWithSHA384, 2048-bit)
depends_on:MBEDTLS_AES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_aes192cbc_sha384.pem":"PolarSSLTest":0

Parse RSA Key #99.5 (PKCS#8 encrypted v2 PBKDF2 AES-256-CBC hmacWithSHA384, 2048-bit)
depends_on:MBEDTLS_AES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PEM_PARSE_C:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_aes256cbc_sha384.pem":"PolarSSLTest":0

Parse RSA Key #99.6 (PKCS#8 encrypted v2 PBKDF2 AES-128-CBC hmacWithSHA384 DER, 2048-bit)
depends_on:MBEDTLS_AES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_aes128cbc_sha384.der":"PolarSSLTest":0

Parse RSA Key #99.7 (PKCS#8 encrypted v2 PBKDF2 AES-192-CBC hmacWithSHA384 DER, 2048-bit)
depends_on:MBEDTLS_AES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_aes192cbc_sha384.der":"PolarSSLTest":0

Parse RSA Key #99.8 (PKCS#8 encrypted v2 PBKDF2 AES-256-CBC hmacWithSHA384 DER, 2048-bit)
depends_on:MBEDTLS_AES_C:MBEDTLS_MD_CAN_SHA384:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs8_pbes2_pbkdf2_2048_aes256cbc_sha384.der":"PolarSSLTest":0

# Test keys with non-word-aligned sizes.
# We use sizes that are large enough to exercise PKCS#1 v1.5 signature with
# the largest supported hashes (SHA-512 and SHA3-512.)
Parse RSA Key #100 (768-bit)
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs1_768_clear.der":"":0

Parse RSA Key #100 (769-bit)
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs1_769_clear.der":"":0

Parse RSA Key #100 (770-bit)
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs1_770_clear.der":"":0

Parse RSA Key #100 (776-bit)
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs1_776_clear.der":"":0

Parse RSA Key #100 (784-bit)
pk_parse_keyfile_rsa:"../framework/data_files/rsa_pkcs1_784_clear.der":"":0

Parse Public RSA Key #1 (PKCS#8 wrapped)
depends_on:MBEDTLS_PEM_PARSE_C
pk_parse_public_keyfile_rsa:"../framework/data_files/rsa_pkcs8_2048_public.pem":0

Parse Public RSA Key #1 (PKCS#8 wrapped, DER)
pk_parse_public_keyfile_rsa:"../framework/data_files/rsa_pkcs8_2048_public.der":0

Parse Public RSA Key #3 (PKCS#1 wrapped)
depends_on:MBEDTLS_PEM_PARSE_C
pk_parse_public_keyfile_rsa:"../framework/data_files/rsa_pkcs1_2048_public.pem":0

Parse Public RSA Key #4 (PKCS#1 wrapped, DER)
pk_parse_public_keyfile_rsa:"../framework/data_files/rsa_pkcs1_2048_public.der":0

Parse Public EC Key #1 (RFC 5480, DER)
depends_on:MBEDTLS_ECP_HAVE_SECP192R1
pk_parse_public_keyfile_ec:"../framework/data_files/ec_pub.der":0

Parse Public EC Key #2 (RFC 5480, PEM)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_SECP192R1
pk_parse_public_keyfile_ec:"../framework/data_files/ec_pub.pem":0

Parse Public EC Key #2a (RFC 5480, PEM, secp192r1, compressed)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_PARSE_EC_COMPRESSED:MBEDTLS_ECP_DP_SECP192R1_ENABLED
pk_parse_public_keyfile_ec:"../framework/data_files/ec_pub.comp.pem":0

Parse Public EC Key #3 (RFC 5480, secp224r1)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_SECP224R1
pk_parse_public_keyfile_ec:"../framework/data_files/ec_224_pub.pem":0

# Compressed points parsing does not support MBEDTLS_ECP_DP_SECP224R1 and
# MBEDTLS_ECP_DP_SECP224K1. Therefore a failure is expected in this case
Parse Public EC Key #3a (RFC 5480, secp224r1, compressed)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_PARSE_EC_COMPRESSED:MBEDTLS_ECP_DP_SECP224R1_ENABLED
pk_parse_public_keyfile_ec:"../framework/data_files/ec_224_pub.comp.pem":MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE

Parse Public EC Key #4 (RFC 5480, secp256r1)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_SECP256R1
pk_parse_public_keyfile_ec:"../framework/data_files/ec_256_pub.pem":0

Parse Public EC Key #4a (RFC 5480, secp256r1, compressed)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_PARSE_EC_COMPRESSED:MBEDTLS_ECP_DP_SECP256R1_ENABLED
pk_parse_public_keyfile_ec:"../framework/data_files/ec_256_pub.comp.pem":0

Parse Public EC Key #5 (RFC 5480, secp384r1)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_SECP384R1
pk_parse_public_keyfile_ec:"../framework/data_files/ec_384_pub.pem":0

Parse Public EC Key #5a (RFC 5480, secp384r1, compressed)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_PARSE_EC_COMPRESSED:MBEDTLS_ECP_DP_SECP384R1_ENABLED
pk_parse_public_keyfile_ec:"../framework/data_files/ec_384_pub.comp.pem":0

Parse Public EC Key #6 (RFC 5480, secp521r1)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_SECP521R1
pk_parse_public_keyfile_ec:"../framework/data_files/ec_521_pub.pem":0

Parse Public EC Key #6a (RFC 5480, secp521r1, compressed)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_PARSE_EC_COMPRESSED:MBEDTLS_ECP_DP_SECP521R1_ENABLED
pk_parse_public_keyfile_ec:"../framework/data_files/ec_521_pub.comp.pem":0

Parse Public EC Key #7 (RFC 5480, brainpoolP256r1)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_BP256R1
pk_parse_public_keyfile_ec:"../framework/data_files/ec_bp256_pub.pem":0

Parse Public EC Key #7a (RFC 5480, brainpoolP256r1, compressed)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_PARSE_EC_COMPRESSED:MBEDTLS_ECP_DP_BP256R1_ENABLED
pk_parse_public_keyfile_ec:"../framework/data_files/ec_bp256_pub.comp.pem":0

Parse Public EC Key #8 (RFC 5480, brainpoolP384r1)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_BP384R1
pk_parse_public_keyfile_ec:"../framework/data_files/ec_bp384_pub.pem":0

Parse Public EC Key #8a (RFC 5480, brainpoolP384r1, compressed)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_PARSE_EC_COMPRESSED:MBEDTLS_ECP_DP_BP384R1_ENABLED
pk_parse_public_keyfile_ec:"../framework/data_files/ec_bp384_pub.comp.pem":0

Parse Public EC Key #9 (RFC 5480, brainpoolP512r1)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_BP512R1
pk_parse_public_keyfile_ec:"../framework/data_files/ec_bp512_pub.pem":0

Parse Public EC Key #9a (RFC 5480, brainpoolP512r1, compressed)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_PARSE_EC_COMPRESSED:MBEDTLS_ECP_DP_BP512R1_ENABLED
pk_parse_public_keyfile_ec:"../framework/data_files/ec_bp512_pub.comp.pem":0

Parse Public EC Key #10 (RFC 8410, DER, X25519)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_CURVE25519
pk_parse_public_keyfile_ec:"../framework/data_files/ec_x25519_pub.der":0

Parse Public EC Key #11 (RFC 8410, DER, X448)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_CURVE448
pk_parse_public_keyfile_ec:"../framework/data_files/ec_x448_pub.der":0

Parse Public EC Key #12 (RFC 8410, PEM, X25519)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_CURVE25519
pk_parse_public_keyfile_ec:"../framework/data_files/ec_x25519_pub.pem":0

Parse Public EC Key #13 (RFC 8410, PEM, X448)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_CURVE448
pk_parse_public_keyfile_ec:"../framework/data_files/ec_x448_pub.pem":0

Parse EC Key #1 (SEC1 DER)
depends_on:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP192R1
pk_parse_keyfile_ec:"../framework/data_files/ec_prv.sec1.der":"NULL":0

Parse EC Key #2 (SEC1 PEM)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_SECP192R1
pk_parse_keyfile_ec:"../framework/data_files/ec_prv.sec1.pem":"NULL":0

Parse EC Key #2a (SEC1 PEM, secp192r1, compressed)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_PARSE_EC_COMPRESSED:MBEDTLS_ECP_DP_SECP192R1_ENABLED
pk_parse_keyfile_ec:"../framework/data_files/ec_prv.sec1.comp.pem":"NULL":0

Parse EC Key #3 (SEC1 PEM encrypted)
depends_on:MBEDTLS_DES_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_SECP192R1:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_MD_CAN_MD5
pk_parse_keyfile_ec:"../framework/data_files/ec_prv.sec1.pw.pem":"polar":0

Parse EC Key #4 (PKCS8 DER)
depends_on:MBEDTLS_ECP_HAVE_SECP192R1
pk_parse_keyfile_ec:"../framework/data_files/ec_prv.pk8.der":"NULL":0

Parse EC Key #4a (PKCS8 DER, no public key)
depends_on:MBEDTLS_ECP_HAVE_SECP256R1
pk_parse_keyfile_ec:"../framework/data_files/ec_prv.pk8nopub.der":"NULL":0

Parse EC Key #4b (PKCS8 DER, no public key, with parameters)
depends_on:MBEDTLS_ECP_HAVE_SECP256R1
pk_parse_keyfile_ec:"../framework/data_files/ec_prv.pk8nopubparam.der":"NULL":0

Parse EC Key #4c (PKCS8 DER, with parameters)
depends_on:MBEDTLS_ECP_HAVE_SECP256R1
pk_parse_keyfile_ec:"../framework/data_files/ec_prv.pk8param.der":"NULL":0

Parse EC Key #5 (PKCS8 PEM)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_SECP192R1
pk_parse_keyfile_ec:"../framework/data_files/ec_prv.pk8.pem":"NULL":0

Parse EC Key #5a (PKCS8 PEM, no public key)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_SECP256R1
pk_parse_keyfile_ec:"../framework/data_files/ec_prv.pk8nopub.pem":"NULL":0

Parse EC Key #5b (PKCS8 PEM, no public key, with parameters)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_SECP256R1
pk_parse_keyfile_ec:"../framework/data_files/ec_prv.pk8nopubparam.pem":"NULL":0

Parse EC Key #5c (PKCS8 PEM, with parameters)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_SECP256R1
pk_parse_keyfile_ec:"../framework/data_files/ec_prv.pk8param.pem":"NULL":0

Parse EC Key #8 (SEC1 PEM, secp224r1)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_SECP224R1
pk_parse_keyfile_ec:"../framework/data_files/ec_224_prv.pem":"NULL":0

Parse EC Key #8a (SEC1 PEM, secp224r1, compressed)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_PARSE_EC_COMPRESSED:MBEDTLS_ECP_DP_SECP224R1_ENABLED
pk_parse_keyfile_ec:"../framework/data_files/ec_224_prv.comp.pem":"NULL":0

Parse EC Key #9 (SEC1 PEM, secp256r1)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_SECP256R1
pk_parse_keyfile_ec:"../framework/data_files/ec_256_prv.pem":"NULL":0

Parse EC Key #9a (SEC1 PEM, secp256r1, compressed)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_PARSE_EC_COMPRESSED:MBEDTLS_ECP_DP_SECP256R1_ENABLED
pk_parse_keyfile_ec:"../framework/data_files/ec_256_prv.comp.pem":"NULL":0

Parse EC Key #10 (SEC1 PEM, secp384r1)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_SECP384R1
pk_parse_keyfile_ec:"../framework/data_files/ec_384_prv.pem":"NULL":0

Parse EC Key #10a (SEC1 PEM, secp384r1, compressed)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_PARSE_EC_COMPRESSED:MBEDTLS_ECP_DP_SECP384R1_ENABLED
pk_parse_keyfile_ec:"../framework/data_files/ec_384_prv.comp.pem":"NULL":0

Parse EC Key #11 (SEC1 PEM, secp521r1)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_SECP521R1
pk_parse_keyfile_ec:"../framework/data_files/ec_521_prv.pem":"NULL":0

Parse EC Key #11a (SEC1 PEM, secp521r1, compressed)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_PARSE_EC_COMPRESSED:MBEDTLS_ECP_DP_SECP521R1_ENABLED
pk_parse_keyfile_ec:"../framework/data_files/ec_521_prv.comp.pem":"NULL":0

Parse EC Key #12 (SEC1 PEM, bp256r1)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_BP256R1
pk_parse_keyfile_ec:"../framework/data_files/ec_bp256_prv.pem":"NULL":0

Parse EC Key #12a (SEC1 PEM, bp256r1, compressed)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_PARSE_EC_COMPRESSED:MBEDTLS_ECP_DP_BP256R1_ENABLED
pk_parse_keyfile_ec:"../framework/data_files/ec_bp256_prv.comp.pem":"NULL":0

Parse EC Key #13 (SEC1 PEM, bp384r1)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_BP384R1
pk_parse_keyfile_ec:"../framework/data_files/ec_bp384_prv.pem":"NULL":0

Parse EC Key #13a (SEC1 PEM, bp384r1, compressed)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_PARSE_EC_COMPRESSED:MBEDTLS_ECP_DP_BP384R1_ENABLED
pk_parse_keyfile_ec:"../framework/data_files/ec_bp384_prv.comp.pem":"NULL":0

Parse EC Key #14 (SEC1 PEM, bp512r1)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_BP512R1
pk_parse_keyfile_ec:"../framework/data_files/ec_bp512_prv.pem":"NULL":0

Parse EC Key #14a (SEC1 PEM, bp512r1, compressed)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_PARSE_EC_COMPRESSED:MBEDTLS_ECP_DP_BP512R1_ENABLED
pk_parse_keyfile_ec:"../framework/data_files/ec_bp512_prv.comp.pem":"NULL":0

Parse EC Key #15 (SEC1 DER, secp256k1, SpecifiedECDomain)
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED:MBEDTLS_PK_PARSE_EC_EXTENDED
pk_parse_keyfile_ec:"../framework/data_files/ec_prv.specdom.der":"NULL":0

Parse EC Key #16 (RFC 8410, DER, X25519)
depends_on:MBEDTLS_ECP_HAVE_CURVE25519
pk_parse_keyfile_ec:"../framework/data_files/ec_x25519_prv.der":"NULL":0

Parse EC Key #17 (RFC 8410, DER, X448)
depends_on:MBEDTLS_ECP_HAVE_CURVE448
pk_parse_keyfile_ec:"../framework/data_files/ec_x448_prv.der":"NULL":0

Parse EC Key #18 (RFC 8410, PEM, X25519)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_CURVE25519
pk_parse_keyfile_ec:"../framework/data_files/ec_x25519_prv.pem":"NULL":0

Parse EC Key #19 (RFC 8410, PEM, X448)
depends_on:MBEDTLS_PEM_PARSE_C:MBEDTLS_ECP_HAVE_CURVE448
pk_parse_keyfile_ec:"../framework/data_files/ec_x448_prv.pem":"NULL":0

Key ASN1 (No data)
pk_parse_key:"":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Key ASN1 (First tag not Sequence)
pk_parse_key:"020100":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Key ASN1 (ECPrivateKey, empty parameters)
depends_on:MBEDTLS_PK_HAVE_ECC_KEYS
pk_parse_key:"30070201010400a000":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Key ASN1 (OneAsymmetricKey X25519, with invalid optional AlgorithIdentifier parameters)
depends_on:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_CURVE25519
pk_parse_key:"3030020100300706032b656e050004220420b06d829655543a51cba36e53522bc0acfd60af59466555fb3e1e796872ab1a59":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Key ASN1 (OneAsymmetricKey X25519, with NULL private key)
depends_on:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_CURVE25519
pk_parse_key:"300e020100300506032b656e04020500":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Key ASN1 (OneAsymmetricKey with invalid AlgorithIdentifier)
pk_parse_key:"3013020100300a06082b0601040181fd5904020500":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Key ASN1 (OneAsymmetricKey X25519, with unsupported attributes)
depends_on:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_CURVE25519
pk_parse_key:"304f020100300506032b656e04220420b06d829655543a51cba36e53522bc0acfd60af59466555fb3e1e796872ab1a59a01f301d060a2a864886f70d01090914310f0c0d437572646c6520436861697273":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Key ASN1 (OneAsymmetricKey X25519, unsupported version 2 with public key)
depends_on:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_CURVE25519
pk_parse_key:"3051020101300506032b656e04220420b06d829655543a51cba36e53522bc0acfd60af59466555fb3e1e796872ab1a598121009bc3b0e93d8233fe6a8ba6138948cc12a91362d5c2ed81584db05ab5419c9d11":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Key ASN1 (OneAsymmetricKey X25519, unsupported version 2 with public key and unsupported attributes)
depends_on:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_CURVE25519
pk_parse_key:"3072020101300506032b656e04220420b06d829655543a51cba36e53522bc0acfd60af59466555fb3e1e796872ab1a59a01f301d060a2a864886f70d01090914310f0c0d437572646c65204368616972738121009bc3b0e93d8233fe6a8ba6138948cc12a91362d5c2ed81584db05ab5419c9d11":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT

Key ASN1 (Encrypted key PKCS5, trailing garbage data)
depends_on:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_CURVE25519:MBEDTLS_MD_CAN_SHA1:MBEDTLS_CIPHER_C:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_PKCS5_C:MBEDTLS_CIPHER_C
pk_parse_key_encrypted:"307C304006092A864886F70D01050D3033301B06092A864886F70D01050C300E04082ED7F24A1D516DD702020800301406082A864886F70D030704088A4FCC9DCC3949100438AD100BAC552FD0AE70BECAFA60F5E519B6180C77E8DB0B9ECC6F23FEDD30AB9BDCA2AF9F97BC470FC3A82DCA2364E22642DE0AF9275A82CB":"AAAAAAAAAAAAAAAAAA":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

Key ASN1 (Encrypted key PKCS12, trailing garbage data)
depends_on:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_CURVE25519:MBEDTLS_MD_CAN_SHA1:MBEDTLS_CIPHER_C:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_PKCS12_C:MBEDTLS_CIPHER_C
pk_parse_key_encrypted:"3058301C060A2A864886F70D010C0103300E0409CCCCCCCCCCCCCCCCCC02010A04380A8CAF39C4FA001884D0583B323C5E70942444FBE1F650B92F8ADF4AD7BD5049B4748F53A2531139EBF253FE01E8FC925C82C759C944B4D0":"AAAAAAAAAAAAAAAAAA":MBEDTLS_ERR_PK_KEY_INVALID_FORMAT + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

# From RFC8410 Appendix A but made into version 0
OneAsymmetricKey X25519, doesn't match masking requirements #1
depends_on:MBEDTLS_ECP_HAVE_CURVE25519
pk_parse_fix_montgomery:"302e020100300506032b656e04220420f8ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff3f":"302e020100300506032b656e04220420f8ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff7f"

# Full inverse of the expected x25519 pattern:
# - 3 LSb to 1
# - 1st MSb to 1
# - 2nd MSb to 0
# Note: Montgomery keys are written in Little endian format.
OneAsymmetricKey X25519, doesn't match masking requirements #2
depends_on:MBEDTLS_ECP_HAVE_CURVE25519
pk_parse_fix_montgomery:"302e020100300506032b656e04220420ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffbf":"302e020100300506032b656e04220420f8ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff7f"

# Full inverse of the expected x448 pattern:
# - 2 LSb to 1
# - MSb to 0
# Note: Montgomery keys are written in Little endian format.
OneAsymmetricKey X448, doesn't match masking requirements #3
depends_on:MBEDTLS_ECP_HAVE_CURVE448
pk_parse_fix_montgomery:"3046020100300506032b656f043a0438ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff7f":"3046020100300506032b656f043a0438fcffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"
