/* BEGIN_HEADER */
#include "mbedtls/rsa.h"
/* END_HEADER */

/* BEGIN_DEPENDENCIES
 * depends_on:MBEDTLS_PKCS1_V21:MBEDTLS_RSA_C
 * END_DEPENDENCIES
 */

/* BEGIN_CASE */
void pkcs1_rsaes_oaep_encrypt(int mod, data_t *input_N, data_t *input_E,
                              int hash, data_t *message_str, data_t *rnd_buf,
                              data_t *result_str, int result)
{
    unsigned char output[256];
    mbedtls_rsa_context ctx;
    mbedtls_rsa_init(&ctx);
    mbedtls_test_rnd_buf_info info;
    mbedtls_mpi N, E;
    mbedtls_mpi_init(&N); mbedtls_mpi_init(&E);

    info.fallback_f_rng = mbedtls_test_rnd_std_rand;
    info.fallback_p_rng = NULL;
    info.buf = rnd_buf->x;
    info.length = rnd_buf->len;

    MD_PSA_INIT();

    TEST_ASSERT(mbedtls_rsa_set_padding(&ctx,
                                        MBEDTLS_RSA_PKCS_V21, hash) == 0);
    memset(output, 0x00, sizeof(output));

    TEST_EQUAL(mbedtls_rsa_get_padding_mode(&ctx), MBEDTLS_RSA_PKCS_V21);
    TEST_EQUAL(mbedtls_rsa_get_md_alg(&ctx), hash);

    TEST_ASSERT(mbedtls_mpi_read_binary(&N, input_N->x, input_N->len) == 0);
    TEST_ASSERT(mbedtls_mpi_read_binary(&E, input_E->x, input_E->len) == 0);
    TEST_ASSERT(mbedtls_rsa_import(&ctx, &N, NULL, NULL, NULL, &E) == 0);
    TEST_ASSERT(mbedtls_rsa_get_len(&ctx) == (size_t) ((mod + 7) / 8));
    TEST_ASSERT(mbedtls_rsa_check_pubkey(&ctx) == 0);

    if (message_str->len == 0) {
        message_str->x = NULL;
    }
    TEST_ASSERT(mbedtls_rsa_pkcs1_encrypt(&ctx,
                                          &mbedtls_test_rnd_buffer_rand,
                                          &info, message_str->len,
                                          message_str->x,
                                          output) == result);
    if (result == 0) {
        TEST_MEMORY_COMPARE(output, ctx.len, result_str->x, result_str->len);
    }

exit:
    mbedtls_mpi_free(&N); mbedtls_mpi_free(&E);
    mbedtls_rsa_free(&ctx);
    MD_PSA_DONE();
}
/* END_CASE */

/* BEGIN_CASE */
void pkcs1_rsaes_oaep_decrypt(int mod, data_t *input_P, data_t *input_Q,
                              data_t *input_N, data_t *input_E, int hash,
                              data_t *result_str, char *seed, data_t *message_str,
                              int result)
{
    unsigned char output[64];
    mbedtls_rsa_context ctx;
    mbedtls_rsa_init(&ctx);
    size_t output_len;
    mbedtls_test_rnd_pseudo_info rnd_info;
    mbedtls_mpi N, P, Q, E;
    mbedtls_mpi_init(&N); mbedtls_mpi_init(&P);
    mbedtls_mpi_init(&Q); mbedtls_mpi_init(&E);
    ((void) seed);

    MD_PSA_INIT();

    TEST_ASSERT(mbedtls_rsa_set_padding(&ctx,
                                        MBEDTLS_RSA_PKCS_V21, hash) == 0);

    TEST_EQUAL(mbedtls_rsa_get_padding_mode(&ctx), MBEDTLS_RSA_PKCS_V21);
    TEST_EQUAL(mbedtls_rsa_get_md_alg(&ctx), hash);

    memset(output, 0x00, sizeof(output));
    memset(&rnd_info, 0, sizeof(mbedtls_test_rnd_pseudo_info));

    TEST_ASSERT(mbedtls_mpi_read_binary(&P, input_P->x, input_P->len) == 0);
    TEST_ASSERT(mbedtls_mpi_read_binary(&Q, input_Q->x, input_Q->len) == 0);
    TEST_ASSERT(mbedtls_mpi_read_binary(&N, input_N->x, input_N->len) == 0);
    TEST_ASSERT(mbedtls_mpi_read_binary(&E, input_E->x, input_E->len) == 0);

    TEST_ASSERT(mbedtls_rsa_import(&ctx, &N, &P, &Q, NULL, &E) == 0);
    TEST_ASSERT(mbedtls_rsa_get_len(&ctx) == (size_t) ((mod + 7) / 8));
    TEST_ASSERT(mbedtls_rsa_complete(&ctx) == 0);
    TEST_ASSERT(mbedtls_rsa_check_privkey(&ctx) == 0);

    if (result_str->len == 0) {
        TEST_ASSERT(mbedtls_rsa_pkcs1_decrypt(&ctx,
                                              &mbedtls_test_rnd_pseudo_rand,
                                              &rnd_info,
                                              &output_len, message_str->x,
                                              NULL, 0) == result);
    } else {
        TEST_ASSERT(mbedtls_rsa_pkcs1_decrypt(&ctx,
                                              &mbedtls_test_rnd_pseudo_rand,
                                              &rnd_info,
                                              &output_len, message_str->x,
                                              output,
                                              sizeof(output)) == result);
        if (result == 0) {
            TEST_MEMORY_COMPARE(output, output_len, result_str->x, result_str->len);
        }
    }

exit:
    mbedtls_mpi_free(&N); mbedtls_mpi_free(&P);
    mbedtls_mpi_free(&Q); mbedtls_mpi_free(&E);
    mbedtls_rsa_free(&ctx);
    MD_PSA_DONE();
}
/* END_CASE */

/* BEGIN_CASE */
void pkcs1_rsassa_pss_sign(int mod, data_t *input_P, data_t *input_Q,
                           data_t *input_N, data_t *input_E, int digest,
                           int hash, data_t *hash_digest, data_t *rnd_buf,
                           data_t *result_str, int fixed_salt_length,
                           int result)
{
    unsigned char output[512];
    mbedtls_rsa_context ctx;
    mbedtls_rsa_init(&ctx);
    mbedtls_test_rnd_buf_info info;
    mbedtls_mpi N, P, Q, E;
    mbedtls_mpi_init(&N); mbedtls_mpi_init(&P);
    mbedtls_mpi_init(&Q); mbedtls_mpi_init(&E);

    info.fallback_f_rng = mbedtls_test_rnd_std_rand;
    info.fallback_p_rng = NULL;
    info.buf = rnd_buf->x;
    info.length = rnd_buf->len;

    MD_PSA_INIT();

    TEST_ASSERT(mbedtls_rsa_set_padding(&ctx,
                                        MBEDTLS_RSA_PKCS_V21, hash) == 0);

    TEST_EQUAL(mbedtls_rsa_get_padding_mode(&ctx), MBEDTLS_RSA_PKCS_V21);
    TEST_EQUAL(mbedtls_rsa_get_md_alg(&ctx), hash);

    memset(output, 0x00, sizeof(output));

    TEST_ASSERT(mbedtls_mpi_read_binary(&P, input_P->x, input_P->len) == 0);
    TEST_ASSERT(mbedtls_mpi_read_binary(&Q, input_Q->x, input_Q->len) == 0);
    TEST_ASSERT(mbedtls_mpi_read_binary(&N, input_N->x, input_N->len) == 0);
    TEST_ASSERT(mbedtls_mpi_read_binary(&E, input_E->x, input_E->len) == 0);

    TEST_ASSERT(mbedtls_rsa_import(&ctx, &N, &P, &Q, NULL, &E) == 0);
    TEST_ASSERT(mbedtls_rsa_get_len(&ctx) == (size_t) ((mod + 7) / 8));
    TEST_ASSERT(mbedtls_rsa_complete(&ctx) == 0);
    TEST_ASSERT(mbedtls_rsa_check_privkey(&ctx) == 0);

    if (fixed_salt_length == MBEDTLS_RSA_SALT_LEN_ANY) {
        TEST_ASSERT(mbedtls_rsa_pkcs1_sign(
                        &ctx, &mbedtls_test_rnd_buffer_rand, &info,
                        digest, hash_digest->len, hash_digest->x, output) == result);
        if (result == 0) {
            TEST_MEMORY_COMPARE(output, ctx.len, result_str->x, result_str->len);
        }

        info.buf = rnd_buf->x;
        info.length = rnd_buf->len;
    }

    TEST_ASSERT(mbedtls_rsa_rsassa_pss_sign_ext(
                    &ctx, &mbedtls_test_rnd_buffer_rand, &info,
                    digest, hash_digest->len, hash_digest->x,
                    fixed_salt_length, output) == result);
    if (result == 0) {
        TEST_MEMORY_COMPARE(output, ctx.len, result_str->x, result_str->len);
    }

exit:
    mbedtls_mpi_free(&N); mbedtls_mpi_free(&P);
    mbedtls_mpi_free(&Q); mbedtls_mpi_free(&E);
    mbedtls_rsa_free(&ctx);
    MD_PSA_DONE();
}
/* END_CASE */

/* BEGIN_CASE */
void pkcs1_rsassa_pss_verify(int mod, data_t *input_N, data_t *input_E,
                             int digest, int hash, data_t *hash_digest,
                             char *salt, data_t *result_str, int result)
{
    mbedtls_rsa_context ctx;
    mbedtls_rsa_init(&ctx);
    mbedtls_mpi N, E;
    mbedtls_mpi_init(&N); mbedtls_mpi_init(&E);
    ((void) salt);

    MD_PSA_INIT();

    TEST_ASSERT(mbedtls_rsa_set_padding(&ctx,
                                        MBEDTLS_RSA_PKCS_V21, hash) == 0);

    TEST_EQUAL(mbedtls_rsa_get_padding_mode(&ctx), MBEDTLS_RSA_PKCS_V21);
    TEST_EQUAL(mbedtls_rsa_get_md_alg(&ctx), hash);

    TEST_ASSERT(mbedtls_mpi_read_binary(&N, input_N->x, input_N->len) == 0);
    TEST_ASSERT(mbedtls_mpi_read_binary(&E, input_E->x, input_E->len) == 0);

    TEST_ASSERT(mbedtls_rsa_import(&ctx, &N, NULL, NULL, NULL, &E) == 0);
    TEST_ASSERT(mbedtls_rsa_get_len(&ctx) == (size_t) ((mod + 7) / 8));
    TEST_ASSERT(mbedtls_rsa_check_pubkey(&ctx) == 0);


    TEST_ASSERT(mbedtls_rsa_pkcs1_verify(&ctx, digest, hash_digest->len, hash_digest->x,
                                         result_str->x) == result);

exit:
    mbedtls_mpi_free(&N); mbedtls_mpi_free(&E);
    mbedtls_rsa_free(&ctx);
    MD_PSA_DONE();
}
/* END_CASE */

/* BEGIN_CASE */
void pkcs1_rsassa_pss_verify_ext(int mod, data_t *input_N, data_t *input_E,
                                 int msg_digest_id, int ctx_hash,
                                 int mgf_hash, int salt_len,
                                 data_t *hash_digest,
                                 data_t *result_str, int result_simple,
                                 int result_full)
{
    mbedtls_rsa_context ctx;
    mbedtls_rsa_init(&ctx);
    mbedtls_mpi N, E;
    mbedtls_mpi_init(&N); mbedtls_mpi_init(&E);

    MD_PSA_INIT();

    TEST_ASSERT(mbedtls_rsa_set_padding(&ctx,
                                        MBEDTLS_RSA_PKCS_V21, ctx_hash) == 0);

    TEST_EQUAL(mbedtls_rsa_get_padding_mode(&ctx), MBEDTLS_RSA_PKCS_V21);
    TEST_EQUAL(mbedtls_rsa_get_md_alg(&ctx), ctx_hash);

    TEST_ASSERT(mbedtls_mpi_read_binary(&N, input_N->x, input_N->len) == 0);
    TEST_ASSERT(mbedtls_mpi_read_binary(&E, input_E->x, input_E->len) == 0);

    TEST_ASSERT(mbedtls_rsa_import(&ctx, &N, NULL, NULL, NULL, &E) == 0);
    TEST_ASSERT(mbedtls_rsa_get_len(&ctx) == (size_t) ((mod + 7) / 8));
    TEST_ASSERT(mbedtls_rsa_check_pubkey(&ctx) == 0);


    TEST_ASSERT(mbedtls_rsa_pkcs1_verify(&ctx, msg_digest_id,
                                         hash_digest->len, hash_digest->x,
                                         result_str->x) == result_simple);

    TEST_ASSERT(mbedtls_rsa_rsassa_pss_verify_ext(&ctx, msg_digest_id, hash_digest->len,
                                                  hash_digest->x, mgf_hash, salt_len,
                                                  result_str->x) == result_full);

exit:
    mbedtls_mpi_free(&N); mbedtls_mpi_free(&E);
    mbedtls_rsa_free(&ctx);
    MD_PSA_DONE();
}
/* END_CASE */
