Camellia - Invalid parameters
camellia_invalid_param:

Camellia-128-ECB Encrypt RFC3713 #1
camellia_encrypt_ecb:"0123456789abcdeffedcba9876543210":"0123456789abcdeffedcba9876543210":"67673138549669730857065648eabe43":0

Camellia-192-ECB Encrypt RFC3713 #1
camellia_encrypt_ecb:"0123456789abcdeffedcba98765432100011223344556677":"0123456789abcdeffedcba9876543210":"b4993401b3e996f84ee5cee7d79b09b9":0

Camellia-256-ECB Encrypt RFC3713 #1
camellia_encrypt_ecb:"0123456789abcdeffedcba987654321000112233445566778899aabbccddeeff":"0123456789abc<PERSON>ffedcba9876543210":"9acc237dff16d76c20ef7c919e3a7509":0

Camellia-128-ECB Encrypt Perl EVP #1 [#1]
camellia_encrypt_ecb:"000102030405060708090A0B0C0D0E0F":"00112233445566778899AABBCCDDEEFF":"77CF412067AF8270613529149919546F":0

Camellia-192-ECB Encrypt Perl EVP #1 [#1]
camellia_encrypt_ecb:"000102030405060708090A0B0C0D0E0F1011121314151617":"00112233445566778899AABBCCDDEEFF":"B22F3C36B72D31329EEE8ADDC2906C68":0

Camellia-256-ECB Encrypt Perl EVP #1 [#1]
camellia_encrypt_ecb:"000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F":"00112233445566778899AABBCCDDEEFF":"2EDF1F3418D53B88841FC8985FB1ECF2":0

Camellia-128-ECB Encrypt Perl EVP #1 [#2]
camellia_encrypt_ecb:"2B7E151628AED2A6ABF7158809CF4F3C":"6BC1BEE22E409F96E93D7E117393172A":"432FC5DCD628115B7C388D770B270C96":0

Camellia-128-ECB Encrypt Perl EVP #2
camellia_encrypt_ecb:"2B7E151628AED2A6ABF7158809CF4F3C":"AE2D8A571E03AC9C9EB76FAC45AF8E51":"0BE1F14023782A22E8384C5ABB7FAB2B":0

Camellia-128-ECB Encrypt Perl EVP #3
camellia_encrypt_ecb:"2B7E151628AED2A6ABF7158809CF4F3C":"30C81C46A35CE411E5FBC1191A0A52EF":"A0A1ABCD1893AB6FE0FE5B65DF5F8636":0

Camellia-128-ECB Encrypt Perl EVP #4
camellia_encrypt_ecb:"2B7E151628AED2A6ABF7158809CF4F3C":"F69F2445DF4F9B17AD2B417BE66C3710":"E61925E0D5DFAA9BB29F815B3076E51A":0

Camellia-192-ECB Encrypt Perl EVP #1 [#2]
camellia_encrypt_ecb:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"6BC1BEE22E409F96E93D7E117393172A":"CCCC6C4E138B45848514D48D0D3439D3":0

Camellia-192-ECB Encrypt Perl EVP #2
camellia_encrypt_ecb:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"AE2D8A571E03AC9C9EB76FAC45AF8E51":"5713C62C14B2EC0F8393B6AFD6F5785A":0

Camellia-192-ECB Encrypt Perl EVP #3
camellia_encrypt_ecb:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"30C81C46A35CE411E5FBC1191A0A52EF":"B40ED2B60EB54D09D030CF511FEEF366":0

Camellia-192-ECB Encrypt Perl EVP #4
camellia_encrypt_ecb:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"F69F2445DF4F9B17AD2B417BE66C3710":"909DBD95799096748CB27357E73E1D26":0

Camellia-256-ECB Encrypt Perl EVP #1 [#2]
camellia_encrypt_ecb:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"6BC1BEE22E409F96E93D7E117393172A":"BEFD219B112FA00098919CD101C9CCFA":0

Camellia-256-ECB Encrypt Perl EVP #2
camellia_encrypt_ecb:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"AE2D8A571E03AC9C9EB76FAC45AF8E51":"C91D3A8F1AEA08A9386CF4B66C0169EA":0

Camellia-256-ECB Encrypt Perl EVP #3
camellia_encrypt_ecb:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"30C81C46A35CE411E5FBC1191A0A52EF":"A623D711DC5F25A51BB8A80D56397D28":0

Camellia-256-ECB Encrypt Perl EVP #4
camellia_encrypt_ecb:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"F69F2445DF4F9B17AD2B417BE66C3710":"7960109FB6DC42947FCFE59EA3C5EB6B":0

Camellia-128-CBC Encrypt Perl EVP #1
camellia_encrypt_cbc:"2B7E151628AED2A6ABF7158809CF4F3C":"000102030405060708090A0B0C0D0E0F":"6BC1BEE22E409F96E93D7E117393172A":"1607CF494B36BBF00DAEB0B503C831AB":0

Camellia-128-CBC Encrypt Perl EVP #2
camellia_encrypt_cbc:"2B7E151628AED2A6ABF7158809CF4F3C":"1607CF494B36BBF00DAEB0B503C831AB":"AE2D8A571E03AC9C9EB76FAC45AF8E51":"A2F2CF671629EF7840C5A5DFB5074887":0

Camellia-128-CBC Encrypt Perl EVP #3
camellia_encrypt_cbc:"2B7E151628AED2A6ABF7158809CF4F3C":"A2F2CF671629EF7840C5A5DFB5074887":"30C81C46A35CE411E5FBC1191A0A52EF":"0F06165008CF8B8B5A63586362543E54":0

Camellia-128-CBC Encrypt Perl EVP #4
camellia_encrypt_cbc:"2B7E151628AED2A6ABF7158809CF4F3C":"36A84CDAFD5F9A85ADA0F0A993D6D577":"F69F2445DF4F9B17AD2B417BE66C3710":"74C64268CDB8B8FAF5B34E8AF3732980":0

Camellia-192-CBC Encrypt Perl EVP #1
camellia_encrypt_cbc:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"000102030405060708090A0B0C0D0E0F":"6BC1BEE22E409F96E93D7E117393172A":"2A4830AB5AC4A1A2405955FD2195CF93":0

Camellia-192-CBC Encrypt Perl EVP #2
camellia_encrypt_cbc:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"2A4830AB5AC4A1A2405955FD2195CF93":"AE2D8A571E03AC9C9EB76FAC45AF8E51":"5D5A869BD14CE54264F892A6DD2EC3D5":0

Camellia-192-CBC Encrypt Perl EVP #3
camellia_encrypt_cbc:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"5D5A869BD14CE54264F892A6DD2EC3D5":"30C81C46A35CE411E5FBC1191A0A52EF":"37D359C3349836D884E310ADDF68C449":0

Camellia-192-CBC Encrypt Perl EVP #4
camellia_encrypt_cbc:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"37D359C3349836D884E310ADDF68C449":"F69F2445DF4F9B17AD2B417BE66C3710":"01FAAA930B4AB9916E9668E1428C6B08":0

Camellia-256-CBC Encrypt Perl EVP #1
camellia_encrypt_cbc:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"000102030405060708090A0B0C0D0E0F":"6BC1BEE22E409F96E93D7E117393172A":"E6CFA35FC02B134A4D2C0B6737AC3EDA":0

Camellia-256-CBC Encrypt Perl EVP #2
camellia_encrypt_cbc:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"E6CFA35FC02B134A4D2C0B6737AC3EDA":"AE2D8A571E03AC9C9EB76FAC45AF8E51":"36CBEB73BD504B4070B1B7DE2B21EB50":0

Camellia-256-CBC Encrypt Perl EVP #3
camellia_encrypt_cbc:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"36CBEB73BD504B4070B1B7DE2B21EB50":"30C81C46A35CE411E5FBC1191A0A52EF":"E31A6055297D96CA3330CDF1B1860A83":0

Camellia-256-CBC Encrypt Perl EVP #4
camellia_encrypt_cbc:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"E31A6055297D96CA3330CDF1B1860A83":"F69F2445DF4F9B17AD2B417BE66C3710":"5D563F6D1CCCF236051C0C5C1C58F28F":0

Camellia-128-CFB128 Encrypt Perl EVP #1
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_encrypt_cfb128:"2B7E151628AED2A6ABF7158809CF4F3C":"000102030405060708090A0B0C0D0E0F":"6BC1BEE22E409F96E93D7E117393172A":"14F7646187817EB586599146B82BD719"

Camellia-128-CFB128 Encrypt Perl EVP #2
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_encrypt_cfb128:"2B7E151628AED2A6ABF7158809CF4F3C":"14F7646187817EB586599146B82BD719":"AE2D8A571E03AC9C9EB76FAC45AF8E51":"A53D28BB82DF741103EA4F921A44880B"

Camellia-128-CFB128 Encrypt Perl EVP #3
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_encrypt_cfb128:"2B7E151628AED2A6ABF7158809CF4F3C":"A53D28BB82DF741103EA4F921A44880B":"30C81C46A35CE411E5FBC1191A0A52EF":"9C2157A664626D1DEF9EA420FDE69B96"

Camellia-128-CFB128 Encrypt Perl EVP #4
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_encrypt_cfb128:"2B7E151628AED2A6ABF7158809CF4F3C":"9C2157A664626D1DEF9EA420FDE69B96":"F69F2445DF4F9B17AD2B417BE66C3710":"742A25F0542340C7BAEF24CA8482BB09"

Camellia-128-CFB128 Decrypt Perl EVP #1
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_decrypt_cfb128:"2B7E151628AED2A6ABF7158809CF4F3C":"000102030405060708090A0B0C0D0E0F":"6BC1BEE22E409F96E93D7E117393172A":"14F7646187817EB586599146B82BD719"

Camellia-128-CFB128 Decrypt Perl EVP #2
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_decrypt_cfb128:"2B7E151628AED2A6ABF7158809CF4F3C":"14F7646187817EB586599146B82BD719":"AE2D8A571E03AC9C9EB76FAC45AF8E51":"A53D28BB82DF741103EA4F921A44880B"

Camellia-128-CFB128 Decrypt Perl EVP #3
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_decrypt_cfb128:"2B7E151628AED2A6ABF7158809CF4F3C":"A53D28BB82DF741103EA4F921A44880B":"30C81C46A35CE411E5FBC1191A0A52EF":"9C2157A664626D1DEF9EA420FDE69B96"

Camellia-128-CFB128 Decrypt Perl EVP #4
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_decrypt_cfb128:"2B7E151628AED2A6ABF7158809CF4F3C":"9C2157A664626D1DEF9EA420FDE69B96":"F69F2445DF4F9B17AD2B417BE66C3710":"742A25F0542340C7BAEF24CA8482BB09"

Camellia-192-CFB128 Encrypt Perl EVP #1
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_encrypt_cfb128:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"000102030405060708090A0B0C0D0E0F":"6BC1BEE22E409F96E93D7E117393172A":"C832BB9780677DAA82D9B6860DCD565E"

Camellia-192-CFB128 Encrypt Perl EVP #2
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_encrypt_cfb128:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"C832BB9780677DAA82D9B6860DCD565E":"AE2D8A571E03AC9C9EB76FAC45AF8E51":"86F8491627906D780C7A6D46EA331F98"

Camellia-192-CFB128 Encrypt Perl EVP #3
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_encrypt_cfb128:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"86F8491627906D780C7A6D46EA331F98":"30C81C46A35CE411E5FBC1191A0A52EF":"69511CCE594CF710CB98BB63D7221F01"

Camellia-192-CFB128 Encrypt Perl EVP #4
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_encrypt_cfb128:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"69511CCE594CF710CB98BB63D7221F01":"F69F2445DF4F9B17AD2B417BE66C3710":"D5B5378A3ABED55803F25565D8907B84"

Camellia-192-CFB128 Decrypt Perl EVP #1
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_decrypt_cfb128:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"000102030405060708090A0B0C0D0E0F":"6BC1BEE22E409F96E93D7E117393172A":"C832BB9780677DAA82D9B6860DCD565E"

Camellia-192-CFB128 Decrypt Perl EVP #2
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_decrypt_cfb128:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"C832BB9780677DAA82D9B6860DCD565E":"AE2D8A571E03AC9C9EB76FAC45AF8E51":"86F8491627906D780C7A6D46EA331F98"

Camellia-192-CFB128 Decrypt Perl EVP #3
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_decrypt_cfb128:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"86F8491627906D780C7A6D46EA331F98":"30C81C46A35CE411E5FBC1191A0A52EF":"69511CCE594CF710CB98BB63D7221F01"

Camellia-192-CFB128 Decrypt Perl EVP #4
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_decrypt_cfb128:"8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B":"69511CCE594CF710CB98BB63D7221F01":"F69F2445DF4F9B17AD2B417BE66C3710":"D5B5378A3ABED55803F25565D8907B84"

Camellia-256-CFB128 Encrypt Perl EVP #1
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_encrypt_cfb128:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"000102030405060708090A0B0C0D0E0F":"6BC1BEE22E409F96E93D7E117393172A":"CF6107BB0CEA7D7FB1BD31F5E7B06C93"

Camellia-256-CFB128 Encrypt Perl EVP #2
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_encrypt_cfb128:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"CF6107BB0CEA7D7FB1BD31F5E7B06C93":"AE2D8A571E03AC9C9EB76FAC45AF8E51":"89BEDB4CCDD864EA11BA4CBE849B5E2B"

Camellia-256-CFB128 Encrypt Perl EVP #3
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_encrypt_cfb128:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"89BEDB4CCDD864EA11BA4CBE849B5E2B":"30C81C46A35CE411E5FBC1191A0A52EF":"555FC3F34BDD2D54C62D9E3BF338C1C4"

Camellia-256-CFB128 Encrypt Perl EVP #4
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_encrypt_cfb128:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"555FC3F34BDD2D54C62D9E3BF338C1C4":"F69F2445DF4F9B17AD2B417BE66C3710":"5953ADCE14DB8C7F39F1BD39F359BFFA"

Camellia-256-CFB128 Decrypt Perl EVP #1
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_decrypt_cfb128:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"000102030405060708090A0B0C0D0E0F":"6BC1BEE22E409F96E93D7E117393172A":"CF6107BB0CEA7D7FB1BD31F5E7B06C93"

Camellia-256-CFB128 Decrypt Perl EVP #2
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_decrypt_cfb128:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"CF6107BB0CEA7D7FB1BD31F5E7B06C93":"AE2D8A571E03AC9C9EB76FAC45AF8E51":"89BEDB4CCDD864EA11BA4CBE849B5E2B"

Camellia-256-CFB128 Decrypt Perl EVP #3
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_decrypt_cfb128:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"89BEDB4CCDD864EA11BA4CBE849B5E2B":"30C81C46A35CE411E5FBC1191A0A52EF":"555FC3F34BDD2D54C62D9E3BF338C1C4"

Camellia-256-CFB128 Decrypt Perl EVP #4
depends_on:MBEDTLS_CIPHER_MODE_CFB
camellia_decrypt_cfb128:"603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4":"555FC3F34BDD2D54C62D9E3BF338C1C4":"F69F2445DF4F9B17AD2B417BE66C3710":"5953ADCE14DB8C7F39F1BD39F359BFFA"

Camellia-ECB Encrypt (Invalid key length)
camellia_encrypt_ecb:"0123456789abcdeffedcba98765432":"0123456789abcdeffedcba9876543210":"67673138549669730857065648eabe43":MBEDTLS_ERR_CAMELLIA_BAD_INPUT_DATA

Camellia-ECB Decrypt (Invalid key length)
camellia_decrypt_ecb:"0123456789abcdeffedcba98765432":"0123456789abcdeffedcba9876543210":"67673138549669730857065648eabe43":MBEDTLS_ERR_CAMELLIA_BAD_INPUT_DATA

Camellia-256-CBC Encrypt (Invalid input length)
camellia_encrypt_cbc:"0000000000000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"ffffffffffffffe000000000000000":"":MBEDTLS_ERR_CAMELLIA_INVALID_INPUT_LENGTH

Camellia-256-CBC Decrypt (Invalid input length)
camellia_decrypt_cbc:"0000000000000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"623a52fcea5d443e48d9181ab32c74":"":MBEDTLS_ERR_CAMELLIA_INVALID_INPUT_LENGTH

Camellia Selftest
depends_on:MBEDTLS_SELF_TEST
camellia_selftest:
