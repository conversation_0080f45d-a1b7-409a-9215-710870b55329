# mbedtls_ecdsa_der_to_raw() doesn't accept a null output buffer,
# even with otherwise invalid paramters,
# so we pass it a (non-null) buffer of length 1.
ECDSA Raw -> DER, 0bit
ecdsa_raw_to_der:0:"":"00":MBEDTLS_ERR_ASN1_INVALID_DATA

ECDSA DER -> Raw, 0bit
ecdsa_der_to_raw:0:"":"":MBEDTLS_ERR_ASN1_INVALID_DATA

ECDSA Raw -> DER, 256bit, Success
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_raw_to_der:256:"11111111111111111111111111111111111111111111111111111111111111112222222222222222222222222222222222222222222222222222222222222222":"30440220111111111111111111111111111111111111111111111111111111111111111102202222222222222222222222222222222222222222222222222222222222222222":0

ECDSA Raw -> DER, 256bit, DER buffer too small
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_raw_to_der:256:"11111111111111111111111111111111111111111111111111111111111111112222222222222222222222222222222222222222222222222222222222222222":"304402201111111111111111111111111111111111111111111111111111111111111111022022222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_BUF_TOO_SMALL

# Check coordinates one byte larger than the largest supported curve.
# If we add an even larger curve, this test case will fail in the full
# configuration because mbedtls_ecdsa_raw_to_der() will return 0, and we'll
# need to use larger data for this test case.
ECDSA Raw -> DER, very large input (536-bit)
ecdsa_raw_to_der:536:"1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111122222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222":"30818a024311111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111024322222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_BUF_TOO_SMALL

ECDSA Raw -> DER, very large input (1016-bit)
ecdsa_raw_to_der:1016:"1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111122222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222":"30820102027f11111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111027f22222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_BUF_TOO_SMALL

ECDSA Raw -> DER, 256bit, Null r
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_raw_to_der:256:"00000000000000000000000000000000000000000000000000000000000000002222222222222222222222222222222222222222222222222222222222222222":"30440220111111111111111111111111111111111111111111111111111111111111111102202222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_INVALID_DATA

ECDSA Raw -> DER, 256bit, Null s
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_raw_to_der:256:"11111111111111111111111111111111111111111111111111111111111111110000000000000000000000000000000000000000000000000000000000000000":"30440220111111111111111111111111111111111111111111111111111111111111111102202222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_INVALID_DATA

ECDSA Raw -> DER, 256bit, r with MSb set
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_raw_to_der:256:"91111111111111111111111111111111111111111111111111111111111111112222222222222222222222222222222222222222222222222222222222222222":"3045022100911111111111111111111111111111111111111111111111111111111111111102202222222222222222222222222222222222222222222222222222222222222222":0

ECDSA Raw -> DER, 256bit, s with MSb set
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_raw_to_der:256:"1111111111111111111111111111111111111111111111111111111111111111A222222222222222222222222222222222222222222222222222222222222222":"304502201111111111111111111111111111111111111111111111111111111111111111022100A222222222222222222222222222222222222222222222222222222222222222":0

ECDSA Raw -> DER, 256bit, both r and s with MSb set
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_raw_to_der:256:"A111111111111111111111111111111111111111111111111111111111111111A222222222222222222222222222222222222222222222222222222222222222":"3046022100A111111111111111111111111111111111111111111111111111111111111111022100A222222222222222222222222222222222222222222222222222222222222222":0

ECDSA Raw -> DER, 256bit, r and s only 1 byte of data
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_raw_to_der:256:"00000000000000000000000000000000000000000000000000000000000000110000000000000000000000000000000000000000000000000000000000000022":"3006020111020122":0

ECDSA Raw -> DER, 256bit, r and s only 1 byte of data with MSb set
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_raw_to_der:256:"000000000000000000000000000000000000000000000000000000000000009100000000000000000000000000000000000000000000000000000000000000A2":"300802020091020200A2":0

ECDSA Raw -> DER, 256bit, Invalid raw signature (r 1 byte shorter)
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_raw_to_der:256:"111111111111111111111111111111111111111111111111111111111111112222222222222222222222222222222222222222222222222222222222222222":"30440220111111111111111111111111111111111111111111111111111111111111111102202222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_INVALID_DATA

ECDSA Raw -> DER, 256bit, Invalid raw signature (r and s 1 byte shorter)
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_raw_to_der:256:"1111111111111111111111111111111111111111111111111111111111111122222222222222222222222222222222222222222222222222222222222222":"30440220111111111111111111111111111111111111111111111111111111111111111102202222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_INVALID_DATA

ECDSA Raw -> DER, 256bit, Invalid raw signature (r 1 byte longer)
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_raw_to_der:256:"1111111111111111111111111111111111111111111111111111111111111111112222222222222222222222222222222222222222222222222222222222222222":"30440220111111111111111111111111111111111111111111111111111111111111111102202222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_INVALID_DATA

ECDSA Raw -> DER, 256bit, Invalid raw signature (r and s 1 byte longer)
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_raw_to_der:256:"111111111111111111111111111111111111111111111111111111111111111111222222222222222222222222222222222222222222222222222222222222222222":"30440220111111111111111111111111111111111111111111111111111111111111111102202222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_INVALID_DATA

ECDSA DER -> Raw, 256bit, Success
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_der_to_raw:256:"30440220111111111111111111111111111111111111111111111111111111111111111102202222222222222222222222222222222222222222222222222222222222222222":"11111111111111111111111111111111111111111111111111111111111111112222222222222222222222222222222222222222222222222222222222222222":0

ECDSA DER -> Raw, 256bit, Raw buffer too small
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_der_to_raw:256:"30440220111111111111111111111111111111111111111111111111111111111111111102202222222222222222222222222222222222222222222222222222222222222222":"111111111111111111111111111111111111111111111111111111111111111122222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_BUF_TOO_SMALL

# Check coordinates one byte larger than the largest supported curve.
# If we add an even larger curve, this test case will fail in the full
# configuration because mbedtls_ecdsa_der_to_raw() will return 0, and we'll
# need to use larger data for this test case.
ECDSA DER -> Raw, very large input (536-bit)
ecdsa_der_to_raw:536:"30818a024311111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111024322222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222":"1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111122222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_BUF_TOO_SMALL

ECDSA DER -> Raw, very large input (1016-bit)
ecdsa_der_to_raw:1016:"30820102027f11111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111027f22222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222":"1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111122222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_BUF_TOO_SMALL

ECDSA DER -> Raw, 256bit, Wrong sequence tag
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_der_to_raw:256:"40440220111111111111111111111111111111111111111111111111111111111111111102202222222222222222222222222222222222222222222222222222222222222222":"11111111111111111111111111111111111111111111111111111111111111112222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

ECDSA DER -> Raw, 256bit, Invalid sequence length
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_der_to_raw:256:"30ff0220111111111111111111111111111111111111111111111111111111111111111102202222222222222222222222222222222222222222222222222222222222222222":"11111111111111111111111111111111111111111111111111111111111111112222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_INVALID_LENGTH

ECDSA DER -> Raw, 256bit, Wrong integer tag
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_der_to_raw:256:"30440120111111111111111111111111111111111111111111111111111111111111111102202222222222222222222222222222222222222222222222222222222222222222":"11111111111111111111111111111111111111111111111111111111111111112222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

ECDSA DER -> Raw, 256bit, Wrong r integer length (1 byte smaller than the actual size)
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_der_to_raw:256:"30440219111111111111111111111111111111111111111111111111111111111111111102202222222222222222222222222222222222222222222222222222222222222222":"11111111111111111111111111111111111111111111111111111111111111112222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_UNEXPECTED_TAG

ECDSA DER -> Raw, 256bit, Wrong r integer length (1 byte larger than the actual size)
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_der_to_raw:256:"30440221111111111111111111111111111111111111111111111111111111111111111102202222222222222222222222222222222222222222222222222222222222222222":"11111111111111111111111111111111111111111111111111111111111111112222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_INVALID_DATA

ECDSA DER -> Raw, 256bit, Wrong s integer length (1 byte smaller than the actual size)
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_der_to_raw:256:"30440220111111111111111111111111111111111111111111111111111111111111111102192222222222222222222222222222222222222222222222222222222222222222":"11111111111111111111111111111111111111111111111111111111111111112222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_LENGTH_MISMATCH

ECDSA DER -> Raw, 256bit, Wrong s integer length (1 byte larger than the actual size)
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_der_to_raw:256:"30440220111111111111111111111111111111111111111111111111111111111111111102212222222222222222222222222222222222222222222222222222222222222222":"11111111111111111111111111111111111111111111111111111111111111112222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_OUT_OF_DATA

ECDSA DER -> Raw, 256bit, r size 1 byte larger than allowed for output raw coordinate
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_der_to_raw:256:"3045022111111111111111111111111111111111111111111111111111111111111111111102202222222222222222222222222222222222222222222222222222222222222222":"11111111111111111111111111111111111111111111111111111111111111112222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_INVALID_DATA

ECDSA DER -> Raw, 256bit, r with MSb set
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_der_to_raw:256:"3045022100911111111111111111111111111111111111111111111111111111111111111102202222222222222222222222222222222222222222222222222222222222222222":"91111111111111111111111111111111111111111111111111111111111111112222222222222222222222222222222222222222222222222222222222222222":0

ECDSA DER -> Raw, 256bit, Invalid r all zeros
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_der_to_raw:256:"30440220000000000000000000000000000000000000000000000000000000000000000002202222222222222222222222222222222222222222222222222222222222222222":"00000000000000000000000000000000000000000000000000000000000000002222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_INVALID_DATA

ECDSA DER -> Raw, 256bit, Invalid s all zeros
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_der_to_raw:256:"30440220111111111111111111111111111111111111111111111111111111111111111102200000000000000000000000000000000000000000000000000000000000000000":"11111111111111111111111111111111111111111111111111111111111111110000000000000000000000000000000000000000000000000000000000000000":MBEDTLS_ERR_ASN1_INVALID_DATA

ECDSA DER -> Raw, 256bit, Valid r only 1 zero byte
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_der_to_raw:256:"302502010002202222222222222222222222222222222222222222222222222222222222222222":"00000000000000000000000000000000000000000000000000000000000000002222222222222222222222222222222222222222222222222222222222222222":0

ECDSA DER -> Raw, 256bit, Valid s only 1 zero byte
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_der_to_raw:256:"302502201111111111111111111111111111111111111111111111111111111111111111020100":"11111111111111111111111111111111111111111111111111111111111111110000000000000000000000000000000000000000000000000000000000000000":0

ECDSA DER -> Raw, 256bit, Invalid 0-length r
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_der_to_raw:256:"3024020002202222222222222222222222222222222222222222222222222222222222222222":"00000000000000000000000000000000000000000000000000000000000000002222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_INVALID_DATA

ECDSA DER -> Raw, 256bit, Invalid 0-length s
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_der_to_raw:256:"3024022011111111111111111111111111111111111111111111111111111111111111110200":"11111111111111111111111111111111111111111111111111111111111111110000000000000000000000000000000000000000000000000000000000000000":MBEDTLS_ERR_ASN1_INVALID_DATA

ECDSA DER -> Raw, 256bit, Invalid r 2 leading zeros
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_der_to_raw:256:"3027020300000102202222222222222222222222222222222222222222222222222222222222222222":"00000000000000000000000000000000000000000000000000000000000000002222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_INVALID_DATA

ECDSA DER -> Raw, 256bit, Invalid s 2 leading zeros
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_der_to_raw:256:"3027022011111111111111111111111111111111111111111111111111111111111111110203000001":"11111111111111111111111111111111111111111111111111111111111111110000000000000000000000000000000000000000000000000000000000000000":MBEDTLS_ERR_ASN1_INVALID_DATA

ECDSA DER -> Raw, 256bit, Invalid r: MSb set without leading zero
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_der_to_raw:256:"30440220911111111111111111111111111111111111111111111111111111111111111102202222222222222222222222222222222222222222222222222222222222222222":"11111111111111111111111111111111111111111111111111111111111111112222222222222222222222222222222222222222222222222222222222222222":MBEDTLS_ERR_ASN1_INVALID_DATA

# 512/521 bit sizes are useful to test sequence's length encoded with 2 bytes.
ECDSA Raw -> DER, 512bit, Success
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 512
ecdsa_raw_to_der:512:"1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111122222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222":"308184024011111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111024022222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222":0

# 512/521 bit sizes are useful to test sequence's length encoded with 2 bytes.
ECDSA DER -> Raw, 512bit, Success
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 512
ecdsa_der_to_raw:512:"308184024011111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111024022222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222":"1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111122222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222":0

# 512/521 bit sizes are useful to test sequence's length encoded with 2 bytes.
ECDSA Raw -> DER, 521bit, Success
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 521
ecdsa_raw_to_der:521:"011111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111012222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222":"30818802420111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111110242012222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222":0

# 512/521 bit sizes are useful to test sequence's length encoded with 2 bytes.
ECDSA DER -> Raw, 521bit, Success
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 521
ecdsa_der_to_raw:521:"30818802420111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111110242012222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222":"011111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111012222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222":0

ECDSA Raw -> DER, 256bit, Incremental DER buffer sizes
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_raw_to_der_incremental:256:"91111111111111111111111111111111111111111111111111111111111111112222222222222222222222222222222222222222222222222222222222222222":"3045022100911111111111111111111111111111111111111111111111111111111111111102202222222222222222222222222222222222222222222222222222222222222222"

ECDSA Raw -> DER, 512bit, Incremental DER buffer sizes
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 512
ecdsa_raw_to_der_incremental:512:"9111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111122222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222":"30818502410091111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111024022222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222"

ECDSA Raw -> DER, 521bit, Incremental DER buffer sizes
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 521
ecdsa_raw_to_der_incremental:521:"011111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111012222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222":"30818802420111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111110242012222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222"

ECDSA Raw -> DER, 256bit, DER buffer of minimal length (1 byte per integer)
depends_on:PSA_VENDOR_ECC_MAX_CURVE_BITS >= 256
ecdsa_raw_to_der_incremental:256:"00000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000002":"3006020101020102"
