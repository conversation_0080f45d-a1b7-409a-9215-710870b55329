ECP invalid params
ecp_invalid_param:

ECP curve info #1
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
mbedtls_ecp_curve_info:MBEDTLS_ECP_DP_BP512R1:28:512:"brainpoolP512r1"

ECP curve info #2
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
mbedtls_ecp_curve_info:MBEDTLS_ECP_DP_BP384R1:27:384:"brainpoolP384r1"

ECP curve info #3
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
mbedtls_ecp_curve_info:MBEDTLS_ECP_DP_BP256R1:26:256:"brainpoolP256r1"

ECP curve info #4
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
mbedtls_ecp_curve_info:MBEDTLS_ECP_DP_SECP521R1:25:521:"secp521r1"

ECP curve info #5
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
mbedtls_ecp_curve_info:MBEDTLS_ECP_DP_SECP384R1:24:384:"secp384r1"

ECP curve info #6
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
mbedtls_ecp_curve_info:MBEDTLS_ECP_DP_SECP256R1:23:256:"secp256r1"

ECP curve info #7
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
mbedtls_ecp_curve_info:MBEDTLS_ECP_DP_SECP224R1:21:224:"secp224r1"

ECP curve info #8
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
mbedtls_ecp_curve_info:MBEDTLS_ECP_DP_SECP192R1:19:192:"secp192r1"

ECP check pubkey Curve25519 #1 (biggest)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE25519:"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF":"0":"1":0

ECP check pubkey Curve25519 #2 (too big)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE25519:"010000000000000000000000000000000000000000000000000000000000000000":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check pubkey Curve25519 #3 (DoS big)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE25519:"0100000000000000000000000000000000000000000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check pubkey Curve25519 y ignored
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE25519:"2":"-1":"1":0

ECP check pubkey Curve25519 z is not 1
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE25519:"2":"0":"2":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check pubkey Curve25519 x negative
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE25519:"-2":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

# see https://cr.yp.to/ecdh.html#validate
ECP check pubkey Curve25519 low-order point #1
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE25519:"0":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

# see https://cr.yp.to/ecdh.html#validate
ECP check pubkey Curve25519 low-order point #2
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE25519:"1":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

# see https://cr.yp.to/ecdh.html#validate
ECP check pubkey Curve25519 low-order point #3 (let's call this u)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE25519:"b8495f16056286fdb1329ceb8d09da6ac49ff1fae35616aeb8413b7c7aebe0":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

# see https://cr.yp.to/ecdh.html#validate
ECP check pubkey Curve25519 low-order point #4 (let's call this v)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE25519:"57119fd0dd4e22d8868e1c58c45c44045bef839c55b1d0b1248c50a3bc959c5f":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

# see https://cr.yp.to/ecdh.html#validate
ECP check pubkey Curve25519 low-order point #5 p-1
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE25519:"7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffec":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

# see https://cr.yp.to/ecdh.html#validate
ECP check pubkey Curve25519 low-order point #6 p
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE25519:"7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffed":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

# see https://cr.yp.to/ecdh.html#validate
ECP check pubkey Curve25519 low-order point #7 p+1
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE25519:"7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffee":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

# see https://cr.yp.to/ecdh.html#validate
ECP check pubkey Curve25519 low-order point #8 p+u
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE25519:"80b8495f16056286fdb1329ceb8d09da6ac49ff1fae35616aeb8413b7c7aebcd":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

# see https://cr.yp.to/ecdh.html#validate
ECP check pubkey Curve25519 low-order point #9 p+v
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE25519:"d7119fd0dd4e22d8868e1c58c45c44045bef839c55b1d0b1248c50a3bc959c4c":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

# see https://cr.yp.to/ecdh.html#validate
ECP check pubkey Curve25519 low-order point #10 2p-1
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE25519:"ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd9":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

# see https://cr.yp.to/ecdh.html#validate
ECP check pubkey Curve25519 low-order point #11 2p
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE25519:"ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffda":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

# see https://cr.yp.to/ecdh.html#validate
ECP check pubkey Curve25519 low-order point #12 2p+1
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE25519:"ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdb":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check pubkey Curve448 #1 (biggest)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE448:"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF":"0":"1":0

ECP check pubkey Curve448 #2 (too big)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE448:"01FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check pubkey Curve448 #3 (DoS big)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE448:"0100000000000000000000000000000000000000000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check pubkey Curve448 y ignored
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE448:"2":"-1":"1":0

ECP check pubkey Curve448 z is not 1
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE448:"2":"0":"2":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check pubkey Curve448 x negative
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE448:"-2":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check pubkey Curve448 low-order point #1
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE448:"0":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check pubkey Curve448 low-order point #2
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE448:"1":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check pubkey Curve448 low-order point #3 p-1
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE448:"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check pubkey Curve448 low-order point #4 p
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE448:"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check pubkey Curve448 low-order point #5 p+1
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_CURVE448:"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000000000000000000000000000000000000000000000000000":"0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check pubkey Koblitz #1 (point not on curve)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_SECP224K1:"E2000000000000BB3A13D43B323337383935321F0603551D":"100101FF040830060101FF02010A30220603551D0E041B04636FC0C0":"1":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check pubkey Koblitz #2 (coordinate not affine)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_check_pub:MBEDTLS_ECP_DP_SECP224K1:"E2000000000000BB3A13D43B323337383935321F0603551D":"100101FF040830060101FF02010A30220603551D0E041B04636FC0C0":"101":MBEDTLS_ERR_ECP_INVALID_KEY

ECP write binary #1 (zero, uncompressed, buffer just fits)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_write_binary:MBEDTLS_ECP_DP_SECP192R1:"01":"01":"00":MBEDTLS_ECP_PF_UNCOMPRESSED:"00":1:0

ECP write binary #2 (zero, buffer too small)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_write_binary:MBEDTLS_ECP_DP_SECP192R1:"01":"01":"00":MBEDTLS_ECP_PF_UNCOMPRESSED:"00":0:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write binary #3 (non-zero, uncompressed, buffer just fits)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_write_binary:MBEDTLS_ECP_DP_SECP192R1:"48d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc9933":"6ceed4d7cba482e288669ee1b6415626d6f34d28501e060c":"01":MBEDTLS_ECP_PF_UNCOMPRESSED:"0448d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc99336ceed4d7cba482e288669ee1b6415626d6f34d28501e060c":49:0

ECP write binary #4 (non-zero, uncompressed, buffer too small)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_write_binary:MBEDTLS_ECP_DP_SECP192R1:"48d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc9933":"6ceed4d7cba482e288669ee1b6415626d6f34d28501e060c":"01":MBEDTLS_ECP_PF_UNCOMPRESSED:"0448d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc99336ceed4d7cba482e288669ee1b6415626d6f34d28501e060c":48:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write binary #5 (zero, compressed, buffer just fits)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_write_binary:MBEDTLS_ECP_DP_SECP192R1:"01":"01":"00":MBEDTLS_ECP_PF_COMPRESSED:"00":1:0

ECP write binary #6 (zero, buffer too small)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_write_binary:MBEDTLS_ECP_DP_SECP192R1:"01":"01":"00":MBEDTLS_ECP_PF_COMPRESSED:"00":0:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write binary #7 (even, compressed, buffer just fits)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_write_binary:MBEDTLS_ECP_DP_SECP192R1:"48d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc9933":"6ceed4d7cba482e288669ee1b6415626d6f34d28501e060c":"01":MBEDTLS_ECP_PF_COMPRESSED:"0248d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc9933":25:0

ECP write binary #8 (even, compressed, buffer too small)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_write_binary:MBEDTLS_ECP_DP_SECP192R1:"48d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc9933":"6ceed4d7cba482e288669ee1b6415626d6f34d28501e060c":"01":MBEDTLS_ECP_PF_COMPRESSED:"0248d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc9933":24:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write binary #9 (odd, compressed, buffer just fits)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_write_binary:MBEDTLS_ECP_DP_SECP192R1:"48d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc9933":"93112b28345b7d1d7799611e49bea9d8290cb2d7afe1f9f3":"01":MBEDTLS_ECP_PF_COMPRESSED:"0348d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc9933":25:0

ECP write binary #10 (Montgomery curve25519, buffer just fits)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_write_binary:MBEDTLS_ECP_DP_CURVE25519:"11223344556677889900aabbccddeeff11223344556677889900aabbccddeeff":"0":"1":MBEDTLS_ECP_PF_COMPRESSED:"ffeeddccbbaa00998877665544332211ffeeddccbbaa00998877665544332211":32:0

ECP write binary #11 (Montgomery curve25519, buffer too small)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_write_binary:MBEDTLS_ECP_DP_CURVE25519:"11223344556677889900aabbccddeeff11223344556677889900aabbccddeeff":"0":"1":MBEDTLS_ECP_PF_COMPRESSED:"ffeeddccbbaa00998877665544332211ffeeddccbbaa00998877665544332211":31:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write binary #12 (Montgomery curve448, buffer just fits)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_write_binary:MBEDTLS_ECP_DP_CURVE448:"3eb7a829b0cd20f5bcfc0b599b6feccf6da4627107bdb0d4f345b43027d8b972fc3e34fb4232a13ca706dcb57aec3dae07bdc1c67bf33609":"0":"1":MBEDTLS_ECP_PF_COMPRESSED:"0936f37bc6c1bd07ae3dec7ab5dc06a73ca13242fb343efc72b9d82730b445f3d4b0bd077162a46dcfec6f9b590bfcbcf520cdb029a8b73e":56:0

ECP write binary #13 (Montgomery curve448, buffer too small)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_write_binary:MBEDTLS_ECP_DP_CURVE448:"3eb7a829b0cd20f5bcfc0b599b6feccf6da4627107bdb0d4f345b43027d8b972fc3e34fb4232a13ca706dcb57aec3dae07bdc1c67bf33609":"0":"1":MBEDTLS_ECP_PF_COMPRESSED:"0936f37bc6c1bd07ae3dec7ab5dc06a73ca13242fb343efc72b9d82730b445f3d4b0bd077162a46dcfec6f9b590bfcbcf520cdb029a8b73e":55:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP read binary #1 (zero, invalid ilen)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_SECP192R1:"0000":"01":"01":"00":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP read binary #2 (zero, invalid first byte)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_SECP192R1:"01":"01":"01":"00":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP read binary #3 (zero, OK)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_SECP192R1:"00":"01":"01":"00":0

ECP read binary #4 (non-zero, invalid ilen, too short)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_SECP192R1:"04001122":"01":"01":"00":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP read binary #4a (non-zero, invalid ilen, too short)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_SECP192R1:"03001122":"01":"01":"00":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP read binary #4b (non-zero, invalid ilen, too short)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_SECP192R1:"02001122":"01":"01":"00":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP read binary #4c (non-zero, invalid ilen, too long)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_SECP192R1:"040011223344556677889900112233445566778899001122334455":"01":"01":"00":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP read binary #4d (non-zero, invalid ilen, too long)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_SECP192R1:"030011223344556677889900112233445566778899001122334455":"01":"01":"00":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP read binary #4e (non-zero, invalid ilen, too long)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_SECP192R1:"020011223344556677889900112233445566778899001122334455":"01":"01":"00":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP read binary #5 (non-zero, invalid first byte)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_SECP192R1:"0548d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc99336ceed4d7cba482e288669ee1b6415626d6f34d28501e060c":"48d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc9933":"6ceed4d7cba482e288669ee1b6415626d6f34d28501e060c":"01":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP read binary #5a (non-zero, compressed format, invalid first byte)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_SECP192R1:"0548d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc9933":"48d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc9933":"6ceed4d7cba482e288669ee1b6415626d6f34d28501e060c":"01":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP read binary #5b (non-zero, compressed format, parity 0, OK)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_SECP192R1:"0248d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc9933":"48d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc9933":"6ceed4d7cba482e288669ee1b6415626d6f34d28501e060c":"01":0

ECP read binary #5c (non-zero, compressed format, parity 1, OK)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_SECP192R1:"0348d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc9933":"48d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc9933":"93112b28345b7d1d7799611e49bea9d8290cb2d7afe1f9f3":"01":0

ECP read binary #6 (non-zero, OK)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_SECP192R1:"0448d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc99336ceed4d7cba482e288669ee1b6415626d6f34d28501e060c":"48d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc9933":"6ceed4d7cba482e288669ee1b6415626d6f34d28501e060c":"01":0

ECP read binary #7 (Curve25519, OK)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_CURVE25519:"8520f0098930a754748b7ddcb43ef75a0dbf3a0d26381af4eba4a98eaa9b4e6a":"6a4e9baa8ea9a4ebf41a38260d3abf0d5af73eb4dc7d8b7454a7308909f02085":"0":"1":0

ECP read binary #8 (Curve25519, masked first bit)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_CURVE25519:"8520f0098930a754748b7ddcb43ef75a0dbf3a0d26381af4eba4a98eaa9b4efa":"7a4e9baa8ea9a4ebf41a38260d3abf0d5af73eb4dc7d8b7454a7308909f02085":"0":"1":0

ECP read binary #9 (Curve25519, too short)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_CURVE25519:"20f0098930a754748b7ddcb43ef75a0dbf3a0d26381af4eba4a98eaa9b4e6a":"6a4e9baa8ea9a4ebf41a38260d3abf0d5af73eb4dc7d8b7454a7308909f020":"0":"1":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP read binary #10 (Curve25519, non-canonical)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_CURVE25519:"ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff7f":"7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff":"0":"1":0

ECP read binary #11 (Curve25519, masked non-canonical)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_CURVE25519:"ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff":"7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff":"0":"1":0

ECP read binary #12 (Curve25519, too long)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_CURVE25519:"8520f0098930a754748b7ddcb43ef75a0dbf3a0d26381af4eba4a98eaa9b4e6a00":"6a4e9baa8ea9a4ebf41a38260d3abf0d5af73eb4dc7d8b7454a7308909f02085":"0":"1":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP read binary #13 (Curve448, OK)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_CURVE448:"9a8f4925d1519f5775cf46b04b5800d4ee9ee8bae8bc5565d498c28dd9c9baf574a9419744897391006382a6f127ab1d9ac2d8c0a598726b":"6b7298a5c0d8c29a1dab27f1a6826300917389449741a974f5bac9d98dc298d46555bce8bae89eeed400584bb046cf75579f51d125498f9a":"0":"1":0

ECP read binary #14 (Curve448, too long)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_CURVE448:"9a8f4925d1519f5775cf46b04b5800d4ee9ee8bae8bc5565d498c28dd9c9baf574a9419744897391006382a6f127ab1d9ac2d8c0a598726b00":"6b7298a5c0d8c29a1dab27f1a6826300917389449741a974f5bac9d98dc298d46555bce8bae89eeed400584bb046cf75579f51d125498f9a":"0":"1":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP read binary #15 (Curve448, too short)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_CURVE448:"8f4925d1519f5775cf46b04b5800d4ee9ee8bae8bc5565d498c28dd9c9baf574a9419744897391006382a6f127ab1d9ac2d8c0a598726b":"6b7298a5c0d8c29a1dab27f1a6826300917389449741a974f5bac9d98dc298d46555bce8bae89eeed400584bb046cf75579f51d125498f9a":"0":"1":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP read binary #16 (Curve448, non-canonical)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_CURVE448:"ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff":"ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff":"0":"1":0

ECP read binary #17 (non-zero, compressed format, p != 3 mod 4, secp224r1)
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_SECP224R1:"0200000000000000000000000000000000000000000000000000000000":"01":"01":"01":MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE

ECP read binary #17a (non-zero, compressed format, p != 3 mod 4, secp224k1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_read_binary:MBEDTLS_ECP_DP_SECP224K1:"0200000000000000000000000000000000000000000000000000000000":"01":"01":"01":MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE

ECP tls read point #1 (zero, invalid length byte)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
mbedtls_ecp_tls_read_point:MBEDTLS_ECP_DP_SECP192R1:"0200":"01":"01":"00":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP tls read point #2 (zero, OK)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
mbedtls_ecp_tls_read_point:MBEDTLS_ECP_DP_SECP192R1:"0100":"01":"01":"00":0

ECP tls read point #3 (non-zero, invalid length byte)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
mbedtls_ecp_tls_read_point:MBEDTLS_ECP_DP_SECP192R1:"300448d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc99336ceed4d7cba482e288669ee1b6415626d6f34d28501e060c":"48d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc9933":"6ceed4d7cba482e288669ee1b6415626d6f34d28501e060c":"01":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP tls read point #4 (non-zero, OK)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
mbedtls_ecp_tls_read_point:MBEDTLS_ECP_DP_SECP192R1:"310448d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc99336ceed4d7cba482e288669ee1b6415626d6f34d28501e060c":"48d8082a3a1e3112bc03a8ef2f6d40d0a77a6f8e00cc9933":"6ceed4d7cba482e288669ee1b6415626d6f34d28501e060c":"01":0

ECP tls write-read point #1
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_tls_write_read_point:MBEDTLS_ECP_DP_SECP192R1

ECP tls write-read point #2
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_tls_write_read_point:MBEDTLS_ECP_DP_SECP521R1

Check ECP group metadata #1 secp192k1 (SEC 2)
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED
mbedtls_ecp_group_metadata:MBEDTLS_ECP_DP_SECP192K1:192:MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS:"fffffffffffffffffffffffffffffffffffffffeffffee37":"000000000000000000000000000000000000000000000000":"000000000000000000000000000000000000000000000003":"db4ff10ec057e9ae26b07d0280b7f4341da5d1b1eae06c7d":"9b2f2f6d9c5628a7844163d015be86344082aa88d95e2f9d":"fffffffffffffffffffffffe26f2fc170f69466a74defd8d":18

Check ECP group metadata #2 secp192r1 (SEC 2)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
mbedtls_ecp_group_metadata:MBEDTLS_ECP_DP_SECP192R1:192:MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS:"fffffffffffffffffffffffffffffffeffffffffffffffff":"":"64210519e59c80e70fa7e9ab72243049feb8deecc146b9b1":"188da80eb03090f67cbf20eb43a18800f4ff0afd82ff1012":"07192b95ffc8da78631011ed6b24cdd573f977a11e794811":"ffffffffffffffffffffffff99def836146bc9b1b4d22831":19

Check ECP group metadata #3 secp224k1 (SEC 2)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
mbedtls_ecp_group_metadata:MBEDTLS_ECP_DP_SECP224K1:224:MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS:"fffffffffffffffffffffffffffffffffffffffffffffffeffffe56d":"00000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000000000000000000000000005":"a1455b334df099df30fc28a169a467e9e47075a90f7e650eb6b7a45c":"7e089fed7fba344282cafbd6f7e319f7c0b0bd59e2ca4bdb556d61a5":"010000000000000000000000000001dce8d2ec6184caf0a971769fb1f7":20

Check ECP group metadata #4 secp224r1 (SEC 2)
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
mbedtls_ecp_group_metadata:MBEDTLS_ECP_DP_SECP224R1:224:MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS:"ffffffffffffffffffffffffffffffff000000000000000000000001":"":"b4050a850c04b3abf54132565044b0b7d7bfd8ba270b39432355ffb4":"b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21":"bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34":"ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d":21

Check ECP group metadata #5 secp256k1 (SEC 2)
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
mbedtls_ecp_group_metadata:MBEDTLS_ECP_DP_SECP256K1:256:MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS:"fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f":"0000000000000000000000000000000000000000000000000000000000000000":"0000000000000000000000000000000000000000000000000000000000000007":"79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798":"483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8":"fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141":22

Check ECP group metadata #6 secp256r1 (SEC 2)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
mbedtls_ecp_group_metadata:MBEDTLS_ECP_DP_SECP256R1:256:MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS:"ffffffff00000001000000000000000000000000ffffffffffffffffffffffff":"":"5ac635d8aa3a93e7b3ebbd55769886bc651d06b0cc53b0f63bce3c3e27d2604b":"6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296":"4fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5":"ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551":23

Check ECP group metadata #7 secp384r1 (SEC 2)
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
mbedtls_ecp_group_metadata:MBEDTLS_ECP_DP_SECP384R1:384:MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS:"fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff":"":"b3312fa7e23ee7e4988e056be3f82d19181d9c6efe8141120314088f5013875ac656398d8a2ed19d2a85c8edd3ec2aef":"aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7":"3617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f":"ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973":24

Check ECP group metadata #8 secp521r1 (SEC 2)
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
mbedtls_ecp_group_metadata:MBEDTLS_ECP_DP_SECP521R1:521:MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS:"01ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff":"":"0051953eb9618e1c9a1f929a21a0b68540eea2da725b99b315f3b8b489918ef109e156193951ec7e937b1652c0bd3bb1bf073573df883d2c34f1ef451fd46b503f00":"00c6858e06b70404e9cd9e3ecb662395b4429c648139053fb521f828af606b4d3dbaa14b5e77efe75928fe1dc127a2ffa8de3348b3c1856a429bf97e7e31c2e5bd66":"011839296a789a3bc0045c8a5fb42c7d1bd998f54449579b446817afbd17273e662c97ee72995ef42640c550b9013fad0761353c7086a272c24088be94769fd16650":"01fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409":25

Check ECP group metadata #9 bp256r1 (RFC 5639)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
mbedtls_ecp_group_metadata:MBEDTLS_ECP_DP_BP256R1:256:MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS:"a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377":"7d5a0975fc2c3057eef67530417affe7fb8055c126dc5c6ce94a4b44f330b5d9":"26dc5c6ce94a4b44f330b5d9bbd77cbf958416295cf7e1ce6bccdc18ff8c07b6":"8bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262":"547ef835c3dac4fd97f8461a14611dc9c27745132ded8e545c1d54c72f046997":"a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7":26

Check ECP group metadata #10 bp384r1 (RFC 5639)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
mbedtls_ecp_group_metadata:MBEDTLS_ECP_DP_BP384R1:384:MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS:"8cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec53":"7bc382c63d8c150c3c72080ace05afa0c2bea28e4fb22787139165efba91f90f8aa5814a503ad4eb04a8c7dd22ce2826":"04a8c7dd22ce28268b39b55416f0447c2fb77de107dcd2a62e880ea53eeb62d57cb4390295dbc9943ab78696fa504c11":"1d1c64f068cf45ffa2a63a81b7c13f6b8847a3e77ef14fe3db7fcafe0cbd10e8e826e03436d646aaef87b2e247d4af1e":"8abe1d7520f9c2a45cb1eb8e95cfd55262b70b29feec5864e19c054ff99129280e4646217791811142820341263c5315":"8cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046565":27

Check ECP group metadata #11 bp512r1 (RFC 5639)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
mbedtls_ecp_group_metadata:MBEDTLS_ECP_DP_BP512R1:512:MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS:"aadd9db8dbe9c48b3fd4e6ae33c9fc07cb308db3b3c9d20ed6639cca703308717d4d9b009bc66842aecda12ae6a380e62881ff2f2d82c68528aa6056583a48f3":"7830a3318b603b89e2327145ac234cc594cbdd8d3df91610a83441caea9863bc2ded5d5aa8253aa10a2ef1c98b9ac8b57f1117a72bf2c7b9e7c1ac4d77fc94ca":"3df91610a83441caea9863bc2ded5d5aa8253aa10a2ef1c98b9ac8b57f1117a72bf2c7b9e7c1ac4d77fc94cadc083e67984050b75ebae5dd2809bd638016f723":"81aee4bdd82ed9645a21322e9c4c6a9385ed9f70b5d916c1b43b62eef4d0098eff3b1f78e2d0d48d50d1687b93b97d5f7c6d5047406a5e688b352209bcb9f822":"7dde385d566332ecc0eabfa9cf7822fdf209f70024a57b1aa000c55b881f8111b2dcde494a5f485e5bca4bd88a2763aed1ca2b2fa8f0540678cd1e0f3ad80892":"aadd9db8dbe9c48b3fd4e6ae33c9fc07cb308db3b3c9d20ed6639cca70330870553e5c414ca92619418661197fac10471db1d381085ddaddb58796829ca90069":28

Check ECP group metadata #12 curve25519 (RFC 7748)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_group_metadata:MBEDTLS_ECP_DP_CURVE25519:256:MBEDTLS_ECP_TYPE_MONTGOMERY:"7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffed":"76d06":"":"9":"":"1000000000000000000000000000000014def9dea2f79cd65812631a5cf5d3ed":29

Check ECP group metadata #13 curve448 (RFC 7748)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
mbedtls_ecp_group_metadata:MBEDTLS_ECP_DP_CURVE448:448:MBEDTLS_ECP_TYPE_MONTGOMERY:"fffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffffffffffffffffffffffffffffffffffffffffffffffffffff":"262a6":"":"5":"":"3fffffffffffffffffffffffffffffffffffffffffffffffffffffff7cca23e9c44edb49aed63690216cc2728dc58f552378c292ab5844f3":30

ECP tls read group #1 (record too short)
mbedtls_ecp_tls_read_group:"0313":MBEDTLS_ERR_ECP_BAD_INPUT_DATA:0:0

ECP tls read group #2 (bad curve_type)
mbedtls_ecp_tls_read_group:"010013":MBEDTLS_ERR_ECP_BAD_INPUT_DATA:0:0

ECP tls read group #3 (unknown curve)
mbedtls_ecp_tls_read_group:"030010":MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE:0:0

ECP tls read group #4 (OK, buffer just fits)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
mbedtls_ecp_tls_read_group:"030017":0:256:3

ECP tls read group #5 (OK, buffer continues)
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
mbedtls_ecp_tls_read_group:"0300180000":0:384:3

ECP tls write-read group #1
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_tls_write_read_group:MBEDTLS_ECP_DP_SECP192R1

ECP tls write-read group #2
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_tls_write_read_group:MBEDTLS_ECP_DP_SECP521R1

ECP check privkey #1 (short weierstrass, too small)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
mbedtls_ecp_check_privkey:MBEDTLS_ECP_DP_SECP192R1:"00":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check privkey #2 (short weierstrass, smallest)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
mbedtls_ecp_check_privkey:MBEDTLS_ECP_DP_SECP192R1:"01":0

ECP check privkey #3 (short weierstrass, biggest)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
mbedtls_ecp_check_privkey:MBEDTLS_ECP_DP_SECP192R1:"FFFFFFFFFFFFFFFFFFFFFFFF99DEF836146BC9B1B4D22830":0

ECP check privkey #4 (short weierstrass, too big)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
mbedtls_ecp_check_privkey:MBEDTLS_ECP_DP_SECP192R1:"FFFFFFFFFFFFFFFFFFFFFFFF99DEF836146BC9B1B4D22831":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check privkey #5 (montgomery curve25519, too big)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_check_privkey:MBEDTLS_ECP_DP_CURVE25519:"C000000000000000000000000000000000000000000000000000000000000000":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check privkey #6 (montgomery curve25519, not big enough)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_check_privkey:MBEDTLS_ECP_DP_CURVE25519:"3FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF0":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check privkey #7 (montgomery curve25519, msb OK)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_check_privkey:MBEDTLS_ECP_DP_CURVE25519:"4000000000000000000000000000000000000000000000000000000000000000":0

ECP check privkey #8 (montgomery curve25519, msb not OK)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_check_privkey:MBEDTLS_ECP_DP_CURVE25519:"0FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check privkey #9 (montgomery curve25519, bit 0 set)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_check_privkey:MBEDTLS_ECP_DP_CURVE25519:"4000000000000000000000000000000000000000000000000000000000000001":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check privkey #10 (montgomery curve25519, bit 1 set)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_check_privkey:MBEDTLS_ECP_DP_CURVE25519:"4000000000000000000000000000000000000000000000000000000000000002":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check privkey #11 (montgomery curve25519, bit 2 set)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_check_privkey:MBEDTLS_ECP_DP_CURVE25519:"4000000000000000000000000000000000000000000000000000000000000004":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check privkey #12 (montgomery curve25519, OK)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_check_privkey:MBEDTLS_ECP_DP_CURVE25519:"7FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8":0

ECP check privkey #13 (montgomery curve448, too big)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
mbedtls_ecp_check_privkey:MBEDTLS_ECP_DP_CURVE448:"800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check privkey #14 (montgomery curve448, not big enough)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
mbedtls_ecp_check_privkey:MBEDTLS_ECP_DP_CURVE448:"80000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check privkey #15 (montgomery curve448, msb OK)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
mbedtls_ecp_check_privkey:MBEDTLS_ECP_DP_CURVE448:"8000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000":0

ECP check privkey #15 (montgomery curve448, msb not OK)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
mbedtls_ecp_check_privkey:MBEDTLS_ECP_DP_CURVE448:"0FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check privkey #17 (montgomery curve448, bit 0 set)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
mbedtls_ecp_check_privkey:MBEDTLS_ECP_DP_CURVE448:"8000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check privkey #18 (montgomery curve448, bit 1 set)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
mbedtls_ecp_check_privkey:MBEDTLS_ECP_DP_CURVE448:"8000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002":MBEDTLS_ERR_ECP_INVALID_KEY

ECP check privkey #19 (montgomery curve448, OK)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
mbedtls_ecp_check_privkey:MBEDTLS_ECP_DP_CURVE448:"8FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC":0

ECP check public-private #1 (OK)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
mbedtls_ecp_check_pub_priv:MBEDTLS_ECP_DP_SECP256R1:"37cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f76822596292":"4ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edff":MBEDTLS_ECP_DP_SECP256R1:"00f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":"37cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f76822596292":"4ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edff":0

ECP check public-private #2 (group none)
mbedtls_ecp_check_pub_priv:MBEDTLS_ECP_DP_NONE:"37cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f76822596292":"4ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edff":MBEDTLS_ECP_DP_NONE:"00f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":"37cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f76822596292":"4ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edff":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP check public-private #3 (group mismatch)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_ECP_DP_SECP384R1_ENABLED
mbedtls_ecp_check_pub_priv:MBEDTLS_ECP_DP_SECP384R1:"37cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f76822596292":"4ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edff":MBEDTLS_ECP_DP_SECP256R1:"00f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":"37cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f76822596292":"4ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edff":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP check public-private #4 (Qx mismatch)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
mbedtls_ecp_check_pub_priv:MBEDTLS_ECP_DP_SECP256R1:"37cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f76822596293":"4ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edff":MBEDTLS_ECP_DP_SECP256R1:"00f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":"37cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f76822596292":"4ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edff":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP check public-private #5 (Qy mismatch)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
mbedtls_ecp_check_pub_priv:MBEDTLS_ECP_DP_SECP256R1:"37cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f76822596292":"4ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edfe":MBEDTLS_ECP_DP_SECP256R1:"00f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":"37cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f76822596292":"4ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edff":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP check public-private #6 (wrong Qx)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
mbedtls_ecp_check_pub_priv:MBEDTLS_ECP_DP_SECP256R1:"37cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f76822596293":"4ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edff":MBEDTLS_ECP_DP_SECP256R1:"00f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":"37cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f76822596293":"4ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edff":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP check public-private #7 (wrong Qy)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
mbedtls_ecp_check_pub_priv:MBEDTLS_ECP_DP_SECP256R1:"37cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f76822596292":"4ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edfe":MBEDTLS_ECP_DP_SECP256R1:"00f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":"37cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f76822596292":"4ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edfe":MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP calculate public: secp256r1, good
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_calc_public:MBEDTLS_ECP_DP_SECP256R1:"00f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":0:"0437cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f768225962924ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edff"

ECP calculate public: secp256r1, private value out of range
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_calc_public:MBEDTLS_ECP_DP_SECP256R1:"ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff":MBEDTLS_ERR_ECP_INVALID_KEY:""

# Alice's private key from rfc 7748, masked and adjusted for endianness
# because the test function wants the little-endian representation.
ECP calculate public: Curve25519, good
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_calc_public:MBEDTLS_ECP_DP_CURVE25519:"6a2cb91da5fb77b12a99c0eb872f4cdf4566b25172c1163c7da518730a6d0770":0:"8520f0098930a754748b7ddcb43ef75a0dbf3a0d26381af4eba4a98eaa9b4e6a"

ECP calculate public: Curve25519, private value not masked
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_calc_public:MBEDTLS_ECP_DP_CURVE25519:"2a2cb91da5fb77b12a99c0eb872f4cdf4566b25172c1163c7da518730a6d0770":MBEDTLS_ERR_ECP_INVALID_KEY:"8520f0098930a754748b7ddcb43ef75a0dbf3a0d26381af4eba4a98eaa9b4e6a"

ECP gen keypair [#1]
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
mbedtls_ecp_gen_keypair:MBEDTLS_ECP_DP_SECP192R1

ECP gen keypair [#2]
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_gen_keypair:MBEDTLS_ECP_DP_CURVE25519

ECP gen keypair [#3]
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
mbedtls_ecp_gen_keypair:MBEDTLS_ECP_DP_CURVE448

ECP gen keypair wrapper
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
mbedtls_ecp_gen_key:MBEDTLS_ECP_DP_SECP192R1

ECP generate Montgomery key: Curve25519, random in range
genkey_mx_known_answer:254:"9e020406080a0c0e10121416181a1c1e20222426282a2c2e30323436383a3df0":"4f0102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1ef8"

ECP generate Montgomery key: Curve25519, clear higher bit
genkey_mx_known_answer:254:"ff0102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1ef8":"7f808101820283038404850586068707880889098a0a8b0b8c0c8d0d8e0e8f78"

ECP generate Montgomery key: Curve25519, clear low bits
genkey_mx_known_answer:254:"9e020406080a0c0e10121416181a1c1e20222426282a2c2e30323436383a3dff":"4f0102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1ef8"

ECP generate Montgomery key: Curve25519, random = all-bits-zero
genkey_mx_known_answer:254:"0000000000000000000000000000000000000000000000000000000000000000":"4000000000000000000000000000000000000000000000000000000000000000"

ECP generate Montgomery key: Curve25519, random = all-bits-one
genkey_mx_known_answer:254:"ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff":"7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff8"

ECP generate Montgomery key: Curve25519, not enough entropy
genkey_mx_known_answer:254:"4f0102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e":""

ECP generate Montgomery key: Curve448, random in range
genkey_mx_known_answer:447:"cf0102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f30313233343536fc":"cf0102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f30313233343536fc"

ECP generate Montgomery key: Curve448, set high bit
genkey_mx_known_answer:447:"0f0102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f30313233343536fc":"8f0102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f30313233343536fc"

ECP generate Montgomery key: Curve448, clear low bits
genkey_mx_known_answer:447:"cf0102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f30313233343536ff":"cf0102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f30313233343536fc"

ECP generate Montgomery key: Curve448, random = all-bits-zero
genkey_mx_known_answer:447:"0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000":"8000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"

ECP generate Montgomery key: Curve448, random = all-bits-one
genkey_mx_known_answer:447:"ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff":"fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc"

ECP generate Montgomery key: Curve448, not enough entropy
genkey_mx_known_answer:447:"4f0102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f30313233343536":""

ECP set public key: invalid group (0)
ecp_set_public_key_group_check:MBEDTLS_ECP_DP_NONE:MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE

ECP set public key: valid group (secp256r1)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_set_public_key_group_check:MBEDTLS_ECP_DP_SECP256R1:0

ECP set public key: group not supported (secp256r1)
depends_on:!MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_set_public_key_group_check:MBEDTLS_ECP_DP_SECP256R1:MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE

ECP set public key: bad group (not in enum)
ecp_set_public_key_group_check:MBEDTLS_ECP_DP_MAX:MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE

ECP set public key: good, secp256r1
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_set_public_key_good:MBEDTLS_ECP_DP_SECP256R1:"04e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e0e1ff20e1ffe120e1e1e173287170a761308491683e345cacaebb500c96e1a7bbd37772968b2c951f0579"

ECP set public key: good, Curve25519
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_set_public_key_good:MBEDTLS_ECP_DP_CURVE25519:"8520f0098930a754748b7ddcb43ef75a0dbf3a0d26381af4eba4a98eaa9b4e6a"

ECP set public key after private: good, secp256r1
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_set_public_key_after_private:MBEDTLS_ECP_DP_SECP256R1:"70726976617465206b6579":MBEDTLS_ECP_DP_SECP256R1:"04e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e0e1ff20e1ffe120e1e1e173287170a761308491683e345cacaebb500c96e1a7bbd37772968b2c951f0579"

ECP set public key after private: good, Curve25519
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_set_public_key_after_private:MBEDTLS_ECP_DP_CURVE25519:"70076d0a7318a57d3c16c17251b26645df4c2f87ebc0992ab177fba51db92c6a":MBEDTLS_ECP_DP_CURVE25519:"8520f0098930a754748b7ddcb43ef75a0dbf3a0d26381af4eba4a98eaa9b4e6a"

ECP set public key after private: secp256r1 then secp256k1
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecp_set_public_key_after_private:MBEDTLS_ECP_DP_SECP256R1:"70726976617465206b6579":MBEDTLS_ECP_DP_SECP256K1:"04e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e0e1ff20e1ffe120e1e1e173287170a761308491683e345cacaebb500c96e1a7bbd37772968b2c951f0579"

ECP set public key after private: secp256r1 then secp384r1
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_set_public_key_after_private:MBEDTLS_ECP_DP_SECP256R1:"70726976617465206b6579":MBEDTLS_ECP_DP_SECP384R1:"04aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaae1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e0e1ff20e1ffe120e1e1e173287170a761308491683e345cacaebb500c96e1a7bbd37772968b2c951f0579bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb"

ECP set public key after private: secp384r1 then secp256r1
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_set_public_key_after_private:MBEDTLS_ECP_DP_SECP384R1:"70726976617465206b6579":MBEDTLS_ECP_DP_SECP256R1:"04e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e0e1ff20e1ffe120e1e1e173287170a761308491683e345cacaebb500c96e1a7bbd37772968b2c951f0579"

ECP read key #1 (short weierstrass, too small)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_SECP192R1:"00":MBEDTLS_ERR_ECP_INVALID_KEY:0

ECP read key #2 (short weierstrass, smallest)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_SECP192R1:"01":0:1

ECP read key #3 (short weierstrass, biggest)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_SECP192R1:"FFFFFFFFFFFFFFFFFFFFFFFF99DEF836146BC9B1B4D22830":0:1

ECP read key #4 (short weierstrass, too big)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_SECP192R1:"FFFFFFFFFFFFFFFFFFFFFFFF99DEF836146BC9B1B4D22831":MBEDTLS_ERR_ECP_INVALID_KEY:0

ECP read key #5 (Curve25519, most significant bit set)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_CURVE25519:"00000000000000000000000000000000000000000000000000000000000000C0":0:0

ECP read key #6 (Curve25519, second most significant bit unset)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_CURVE25519:"F0FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF3F":0:0

ECP read key #7 (Curve25519, msb OK)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_CURVE25519:"0000000000000000000000000000000000000000000000000000000000000040":0:1

ECP read key #8 (Curve25519, bit 0 set)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_CURVE25519:"0100000000000000000000000000000000000000000000000000000000000040":0:0

ECP read key #9 (Curve25519, bit 1 set)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_CURVE25519:"0200000000000000000000000000000000000000000000000000000000000040":0:0

ECP read key #10 (Curve25519, bit 2 set)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_CURVE25519:"0400000000000000000000000000000000000000000000000000000000000040":0:0

ECP read key #11 (Curve25519, OK)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_CURVE25519:"F8FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF7F":0:1

ECP read key #12 (Curve25519, too long)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_CURVE25519:"00000000000000000000000000000000000000000000000000000000000000000C":MBEDTLS_ERR_ECP_INVALID_KEY:0

ECP read key #13 (Curve25519, not long enough)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_CURVE25519:"F0FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF3F":MBEDTLS_ERR_ECP_INVALID_KEY:0

ECP read key #14 (Curve448, most significant bit unset)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_CURVE448:"FCFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF7F":0:0

ECP read key #15 (Curve448, msb OK)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_CURVE448:"0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000080":0:1

ECP read key #16 (Curve448, bit 0 set)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_CURVE448:"0100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000080":0:0

ECP read key #17 (Curve448, bit 1 set)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_CURVE448:"0200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000080":0:0

ECP read key #18 (Curve448, OK)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_CURVE448:"FCFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8F":0:1

ECP read key #19 (Curve448, too long)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_CURVE448:"FCFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8F":MBEDTLS_ERR_ECP_INVALID_KEY:0

ECP read key #20 (Curve448, not long enough)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_CURVE448:"FCFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8F":MBEDTLS_ERR_ECP_INVALID_KEY:0

ECP read key #21 (Curve448, not supported)
depends_on:!MBEDTLS_ECP_DP_CURVE448_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_CURVE448:"FCFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF":MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE:0

ECP read key #22 (Curve25519, not supported)
depends_on:!MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_CURVE25519:"F8FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF7F":MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE:0

ECP read key #23 (invalid curve)
mbedtls_ecp_read_key:INT_MAX:"F8FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF7F":MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE:0

ECP read key #24 (Curve25519 RFC, OK)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
mbedtls_ecp_read_key:MBEDTLS_ECP_DP_CURVE25519:"70076d0a7318a57d3c16c17251b26645df4c2f87ebc0992ab177fba51db92c6a":0:1

ECP write key: secp256r1, nominal
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_SECP256R1:"f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":32:0

ECP write key: secp256r1, output longer by 1
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_SECP256R1:"f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":33:0

ECP write key: secp256r1, output longer by 32
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_SECP256R1:"f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":64:0

ECP write key: secp256r1, output longer by 33
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_SECP256R1:"f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":65:0

ECP write key: secp256r1, output short by 1
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_SECP256R1:"f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":31:MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL

ECP write key: secp256r1, output_size=1
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_SECP256R1:"f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":1:MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL

ECP write key: secp256r1, output_size=0
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_SECP256R1:"f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":0:MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL

ECP write key: secp256r1, top byte = 0, output_size=32
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_SECP256R1:"00ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff":32:0

ECP write key: secp256r1, top byte = 0, output_size=31 (fits)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_SECP256R1:"00ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff":31:0

ECP write key: secp256r1, top byte = 0, output_size=30 (too small)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_SECP256R1:"00ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff":30:MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL

ECP write key: secp256r1, mostly-0 key, output_size=32
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_SECP256R1:"0000000000000000000000000000000000000000000000000000000000000001":32:0

ECP write key: secp256r1, mostly-0 key, output_size=31 (fits)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_SECP256R1:"0000000000000000000000000000000000000000000000000000000000000001":31:0

ECP write key: secp256r1, mostly-0 key, output_size=1 (fits)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_SECP256R1:"0000000000000000000000000000000000000000000000000000000000000001":1:0

ECP write key: secp384r1, nominal
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_SECP384R1:"d27335ea71664af244dd14e9fd1260715dfd8a7965571c48d709ee7a7962a156d706a90cbcb5df2986f05feadb9376f1":48:0

ECP write key: secp384r1, output longer by 1
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_SECP384R1:"d27335ea71664af244dd14e9fd1260715dfd8a7965571c48d709ee7a7962a156d706a90cbcb5df2986f05feadb9376f1":49:0

ECP write key: secp384r1, output longer by 48
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_SECP384R1:"d27335ea71664af244dd14e9fd1260715dfd8a7965571c48d709ee7a7962a156d706a90cbcb5df2986f05feadb9376f1":96:0

ECP write key: secp384r1, output longer by 49
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_SECP384R1:"d27335ea71664af244dd14e9fd1260715dfd8a7965571c48d709ee7a7962a156d706a90cbcb5df2986f05feadb9376f1":97:0

ECP write key: secp384r1, output short by 1
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_SECP384R1:"d27335ea71664af244dd14e9fd1260715dfd8a7965571c48d709ee7a7962a156d706a90cbcb5df2986f05feadb9376f1":47:MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL

ECP write key: secp384r1, output_size=1
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_SECP384R1:"d27335ea71664af244dd14e9fd1260715dfd8a7965571c48d709ee7a7962a156d706a90cbcb5df2986f05feadb9376f1":1:MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL

ECP write key: secp384r1, output_size=0
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_SECP384R1:"d27335ea71664af244dd14e9fd1260715dfd8a7965571c48d709ee7a7962a156d706a90cbcb5df2986f05feadb9376f1":0:MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL

ECP write key: Curve25519, nominal
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_CURVE25519:"a046e36bf0527c9d3b16154b82465edd62144c0ac1fc5a18506a2244ba449a44":32:0

ECP write key: Curve25519, output longer by 1
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_CURVE25519:"a046e36bf0527c9d3b16154b82465edd62144c0ac1fc5a18506a2244ba449a44":33:0

ECP write key: Curve25519, output longer by 32
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_CURVE25519:"a046e36bf0527c9d3b16154b82465edd62144c0ac1fc5a18506a2244ba449a44":64:0

ECP write key: Curve25519, output longer by 33
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_CURVE25519:"a046e36bf0527c9d3b16154b82465edd62144c0ac1fc5a18506a2244ba449a44":65:0

ECP write key: Curve25519, output short by 1
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_CURVE25519:"a046e36bf0527c9d3b16154b82465edd62144c0ac1fc5a18506a2244ba449a44":31:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write key: Curve25519, output_size=1
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_CURVE25519:"a046e36bf0527c9d3b16154b82465edd62144c0ac1fc5a18506a2244ba449a44":1:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write key: Curve25519, output_size=0
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_CURVE25519:"a046e36bf0527c9d3b16154b82465edd62144c0ac1fc5a18506a2244ba449a44":0:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write key: Curve25519, mostly-0 key, output_size=32
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_CURVE25519:"0000000000000000000000000000000000000000000000000000000000000040":32:0

ECP write key: Curve25519, mostly-0 key, output_size=31
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_CURVE25519:"0000000000000000000000000000000000000000000000000000000000000040":31:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write key: Curve448, nominal
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_CURVE448:"3c262fddf9ec8e88495266fea19a34d28882acef045104d0d1aae121700a779c984c24f8cdd78fbff44943eba368f54b29259a4f1c600ad3":56:0

ECP write key: Curve448, output longer by 1
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_CURVE448:"3c262fddf9ec8e88495266fea19a34d28882acef045104d0d1aae121700a779c984c24f8cdd78fbff44943eba368f54b29259a4f1c600ad3":57:0

ECP write key: Curve448, output longer by 32
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_CURVE448:"3c262fddf9ec8e88495266fea19a34d28882acef045104d0d1aae121700a779c984c24f8cdd78fbff44943eba368f54b29259a4f1c600ad3":112:0

ECP write key: Curve448, output longer by 33
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_CURVE448:"3c262fddf9ec8e88495266fea19a34d28882acef045104d0d1aae121700a779c984c24f8cdd78fbff44943eba368f54b29259a4f1c600ad3":113:0

ECP write key: Curve448, output short by 1
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_CURVE448:"3c262fddf9ec8e88495266fea19a34d28882acef045104d0d1aae121700a779c984c24f8cdd78fbff44943eba368f54b29259a4f1c600ad3":55:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write key: Curve448, output_size=1
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_CURVE448:"3c262fddf9ec8e88495266fea19a34d28882acef045104d0d1aae121700a779c984c24f8cdd78fbff44943eba368f54b29259a4f1c600ad3":1:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write key: Curve448, output_size=0
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_CURVE448:"3c262fddf9ec8e88495266fea19a34d28882acef045104d0d1aae121700a779c984c24f8cdd78fbff44943eba368f54b29259a4f1c600ad3":0:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write key: Curve448, mostly-0 key, output_size=56
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_CURVE448:"0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000080":56:0

ECP write key: Curve448, mostly-0 key, output_size=55
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_write_key:MBEDTLS_ECP_DP_CURVE448:"0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000080":55:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write key ext: secp256r1, nominal
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_SECP256R1:"f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":32:0

ECP write key ext: secp256r1, output longer by 1
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_SECP256R1:"f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":33:0

ECP write key ext: secp256r1, output short by 1
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_SECP256R1:"f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":31:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write key ext: secp256r1, output_size=0
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_SECP256R1:"f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":0:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write key ext: secp256r1, top byte = 0, output_size=32
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_SECP256R1:"00ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff":32:0

ECP write key ext: secp256r1, top byte = 0, output_size=31
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_SECP256R1:"00ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff":31:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write key ext: secp256r1, top byte = 0, output_size=30
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_SECP256R1:"00ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff":30:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write key ext: secp256r1, mostly-0 key, output_size=32
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_SECP256R1:"0000000000000000000000000000000000000000000000000000000000000001":32:0

ECP write key ext: secp256r1, mostly-0 key, output_size=1
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_SECP256R1:"0000000000000000000000000000000000000000000000000000000000000001":1:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write key ext: secp256r1, private key not set
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_SECP256R1:"":32:MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP write key ext: secp384r1, nominal
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_SECP384R1:"d27335ea71664af244dd14e9fd1260715dfd8a7965571c48d709ee7a7962a156d706a90cbcb5df2986f05feadb9376f1":48:0

ECP write key ext: secp384r1, output longer by 1
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_SECP384R1:"d27335ea71664af244dd14e9fd1260715dfd8a7965571c48d709ee7a7962a156d706a90cbcb5df2986f05feadb9376f1":49:0

ECP write key ext: secp384r1, output short by 1
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_SECP384R1:"d27335ea71664af244dd14e9fd1260715dfd8a7965571c48d709ee7a7962a156d706a90cbcb5df2986f05feadb9376f1":47:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write key ext: Curve25519, nominal
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_CURVE25519:"a046e36bf0527c9d3b16154b82465edd62144c0ac1fc5a18506a2244ba449a44":32:0

ECP write key ext: Curve25519, output longer by 1
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_CURVE25519:"a046e36bf0527c9d3b16154b82465edd62144c0ac1fc5a18506a2244ba449a44":33:0

ECP write key ext: Curve25519, output short by 1
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_CURVE25519:"a046e36bf0527c9d3b16154b82465edd62144c0ac1fc5a18506a2244ba449a44":31:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write key ext: Curve25519, output_size=0
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_CURVE25519:"a046e36bf0527c9d3b16154b82465edd62144c0ac1fc5a18506a2244ba449a44":0:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write key ext: Curve25519, mostly-0 key, output_size=32
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_CURVE25519:"0000000000000000000000000000000000000000000000000000000000000040":32:0

ECP write key ext: Curve25519, mostly-0 key, output_size=31
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_CURVE25519:"0000000000000000000000000000000000000000000000000000000000000040":31:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write key ext: Curve25519, private key not set
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_CURVE25519:"":32:MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP write key ext: Curve448, nominal
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_CURVE448:"3c262fddf9ec8e88495266fea19a34d28882acef045104d0d1aae121700a779c984c24f8cdd78fbff44943eba368f54b29259a4f1c600ad3":56:0

ECP write key ext: Curve448, output longer by 1
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_CURVE448:"3c262fddf9ec8e88495266fea19a34d28882acef045104d0d1aae121700a779c984c24f8cdd78fbff44943eba368f54b29259a4f1c600ad3":57:0

ECP write key ext: Curve448, output short by 1
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_CURVE448:"3c262fddf9ec8e88495266fea19a34d28882acef045104d0d1aae121700a779c984c24f8cdd78fbff44943eba368f54b29259a4f1c600ad3":55:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write key ext: Curve448, mostly-0 key, output_size=56
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_CURVE448:"0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000080":56:0

ECP write key ext: Curve448, mostly-0 key, output_size=55
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_write_key_ext:MBEDTLS_ECP_DP_CURVE448:"0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000080":55:MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL

ECP write key ext: group not set
ecp_write_key_ext:MBEDTLS_ECP_DP_NONE:"":32:MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ECP mod p192 small (more than 192 bits, less limbs than 2 * 192 bits)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED:MBEDTLS_ECP_NIST_OPTIM
ecp_fast_mod:MBEDTLS_ECP_DP_SECP192R1:"0100000000000103010000000000010201000000000001010100000000000100"

ECP mod p192 readable
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED:MBEDTLS_ECP_NIST_OPTIM
ecp_fast_mod:MBEDTLS_ECP_DP_SECP192R1:"010000000000010501000000000001040100000000000103010000000000010201000000000001010100000000000100"

ECP mod p192 readable with carry
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED:MBEDTLS_ECP_NIST_OPTIM
ecp_fast_mod:MBEDTLS_ECP_DP_SECP192R1:"FF00000000010500FF00000000010400FF00000000010300FF00000000010200FF00000000010100FF00000000010000"

ECP mod p192 random
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED:MBEDTLS_ECP_NIST_OPTIM
ecp_fast_mod:MBEDTLS_ECP_DP_SECP192R1:"36CF96B45D706A0954D89E52CE5F38517A2270E0175849B6F3740151D238CCABEF921437E475881D83BB69E4AA258EBD"

ECP mod p192 (from a past failure case)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED:MBEDTLS_ECP_NIST_OPTIM
ecp_fast_mod:MBEDTLS_ECP_DP_SECP192R1:"1AC2D6F96A2A425E9DD1776DD8368D4BBC86BF4964E79FEA713583BF948BBEFF0939F96FB19EC48C585BDA6A2D35C750"

ECP mod p224 readable without carry
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED:MBEDTLS_ECP_NIST_OPTIM
ecp_fast_mod:MBEDTLS_ECP_DP_SECP224R1:"0000000D0000000C0000000B0000000A0000000900000008000000070000FF060000FF050000FF040000FF03000FF0020000FF010000FF00"

ECP mod p224 readable with negative carry
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED:MBEDTLS_ECP_NIST_OPTIM
ecp_fast_mod:MBEDTLS_ECP_DP_SECP224R1:"0000000D0000000C0000000B0000000A00000009000000080000000700000006000000050000000400000003000000020000000100000000"

ECP mod p224 readable with positive carry
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED:MBEDTLS_ECP_NIST_OPTIM
ecp_fast_mod:MBEDTLS_ECP_DP_SECP224R1:"0000000D0000000C0000000BFFFFFF0AFFFFFF09FFFFFF08FFFFFF070000FF060000FF050000FF040000FF03000FF0020000FF010000FF00"

ECP mod p224 readable with final negative carry
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED:MBEDTLS_ECP_NIST_OPTIM
ecp_fast_mod:MBEDTLS_ECP_DP_SECP224R1:"FF00000D0000000C0000000B0000000A00000009000000080000000700000006000000050000000400000003000000020000000100000000"

ECP mod p521 very small
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED:MBEDTLS_ECP_NIST_OPTIM
ecp_fast_mod:MBEDTLS_ECP_DP_SECP521R1:"01"

ECP mod p521 small (522 bits)
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED:MBEDTLS_ECP_NIST_OPTIM
ecp_fast_mod:MBEDTLS_ECP_DP_SECP521R1:"030000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"

ECP mod p521 readable
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED:MBEDTLS_ECP_NIST_OPTIM
ecp_fast_mod:MBEDTLS_ECP_DP_SECP521R1:"03FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"

ECP mod p521 readable with carry
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED:MBEDTLS_ECP_NIST_OPTIM
ecp_fast_mod:MBEDTLS_ECP_DP_SECP521R1:"03FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001"

ECP test vectors secp192r1 rfc 5114
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_test_vect:MBEDTLS_ECP_DP_SECP192R1:"323FA3169D8E9C6593F59476BC142000AB5BE0E249C43426":"CD46489ECFD6C105E7B3D32566E2B122E249ABAADD870612":"68887B4877DF51DD4DC3D6FD11F0A26F8FD3844317916E9A":"631F95BB4A67632C9C476EEE9AB695AB240A0499307FCF62":"519A121680E0045466BA21DF2EEE47F5973B500577EF13D5":"FF613AB4D64CEE3A20875BDB10F953F6B30CA072C60AA57F":"AD420182633F8526BFE954ACDA376F05E5FF4F837F54FEBE":"4371545ED772A59741D0EDA32C671112B7FDDD51461FCF32"

ECP test vectors secp224r1 rfc 5114
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecp_test_vect:MBEDTLS_ECP_DP_SECP224R1:"B558EB6C288DA707BBB4F8FBAE2AB9E9CB62E3BC5C7573E22E26D37F":"49DFEF309F81488C304CFF5AB3EE5A2154367DC7833150E0A51F3EEB":"4F2B5EE45762C4F654C1A0C67F54CF88B016B51BCE3D7C228D57ADB4":"AC3B1ADD3D9770E6F6A708EE9F3B8E0AB3B480E9F27F85C88B5E6D18":"6B3AC96A8D0CDE6A5599BE8032EDF10C162D0A8AD219506DCD42A207":"D491BE99C213A7D1CA3706DEBFE305F361AFCBB33E2609C8B1618AD5":"52272F50F46F4EDC9151569092F46DF2D96ECC3B6DC1714A4EA949FA":"5F30C6AA36DDC403C0ACB712BB88F1763C3046F6D919BD9C524322BF"

ECP test vectors secp256r1 rfc 5114
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_test_vect:MBEDTLS_ECP_DP_SECP256R1:"814264145F2F56F2E96A8E337A1284993FAF432A5ABCE59E867B7291D507A3AF":"2AF502F3BE8952F2C9B5A8D4160D09E97165BE50BC42AE4A5E8D3B4BA83AEB15":"EB0FAF4CA986C4D38681A0F9872D79D56795BD4BFF6E6DE3C0F5015ECE5EFD85":"2CE1788EC197E096DB95A200CC0AB26A19CE6BCCAD562B8EEE1B593761CF7F41":"B120DE4AA36492795346E8DE6C2C8646AE06AAEA279FA775B3AB0715F6CE51B0":"9F1B7EECE20D7B5ED8EC685FA3F071D83727027092A8411385C34DDE5708B2B6":"DD0F5396219D1EA393310412D19A08F1F5811E9DC8EC8EEA7F80D21C820C2788":"0357DCCD4C804D0D8D33AA42B848834AA5605F9AB0D37239A115BBB647936F50"

ECP test vectors secp384r1 rfc 5114
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_test_vect:MBEDTLS_ECP_DP_SECP384R1:"D27335EA71664AF244DD14E9FD1260715DFD8A7965571C48D709EE7A7962A156D706A90CBCB5DF2986F05FEADB9376F1":"793148F1787634D5DA4C6D9074417D05E057AB62F82054D10EE6B0403D6279547E6A8EA9D1FD77427D016FE27A8B8C66":"C6C41294331D23E6F480F4FB4CD40504C947392E94F4C3F06B8F398BB29E42368F7A685923DE3B67BACED214A1A1D128":"52D1791FDB4B70F89C0F00D456C2F7023B6125262C36A7DF1F80231121CCE3D39BE52E00C194A4132C4A6C768BCD94D2":"5CD42AB9C41B5347F74B8D4EFB708B3D5B36DB65915359B44ABC17647B6B9999789D72A84865AE2F223F12B5A1ABC120":"E171458FEAA939AAA3A8BFAC46B404BD8F6D5B348C0FA4D80CECA16356CA933240BDE8723415A8ECE035B0EDF36755DE":"5EA1FC4AF7256D2055981B110575E0A8CAE53160137D904C59D926EB1B8456E427AA8A4540884C37DE159A58028ABC0E":"0CC59E4B046414A81C8A3BDFDCA92526C48769DD8D3127CAA99B3632D1913942DE362EAFAA962379374D9F3F066841CA"

ECP test vectors secp521r1 rfc 5114
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_test_vect:MBEDTLS_ECP_DP_SECP521R1:"0113F82DA825735E3D97276683B2B74277BAD27335EA71664AF2430CC4F33459B9669EE78B3FFB9B8683015D344DCBFEF6FB9AF4C6C470BE254516CD3C1A1FB47362":"01EBB34DD75721ABF8ADC9DBED17889CBB9765D90A7C60F2CEF007BB0F2B26E14881FD4442E689D61CB2DD046EE30E3FFD20F9A45BBDF6413D583A2DBF59924FD35C":"00F6B632D194C0388E22D8437E558C552AE195ADFD153F92D74908351B2F8C4EDA94EDB0916D1B53C020B5EECAED1A5FC38A233E4830587BB2EE3489B3B42A5A86A4":"00CEE3480D8645A17D249F2776D28BAE616952D1791FDB4B70F7C3378732AA1B22928448BCD1DC2496D435B01048066EBE4F72903C361B1A9DC1193DC2C9D0891B96":"010EBFAFC6E85E08D24BFFFCC1A4511DB0E634BEEB1B6DEC8C5939AE44766201AF6200430BA97C8AC6A0E9F08B33CE7E9FEEB5BA4EE5E0D81510C24295B8A08D0235":"00A4A6EC300DF9E257B0372B5E7ABFEF093436719A77887EBB0B18CF8099B9F4212B6E30A1419C18E029D36863CC9D448F4DBA4D2A0E60711BE572915FBD4FEF2695":"00CDEA89621CFA46B132F9E4CFE2261CDE2D4368EB5656634C7CC98C7A00CDE54ED1866A0DD3E6126C9D2F845DAFF82CEB1DA08F5D87521BB0EBECA77911169C20CC":"00F9A71641029B7FC1A808AD07CD4861E868614B865AFBECAB1F2BD4D8B55EBCB5E3A53143CEB2C511B1AE0AF5AC827F60F2FD872565AC5CA0A164038FE980A7E4BD"

ECP test vectors brainpoolP256r1 rfc 7027
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_test_vect:MBEDTLS_ECP_DP_BP256R1:"81DB1EE100150FF2EA338D708271BE38300CB54241D79950F77B063039804F1D":"44106E913F92BC02A1705D9953A8414DB95E1AAA49E81D9E85F929A8E3100BE5":"8AB4846F11CACCB73CE49CBDD120F5A900A69FD32C272223F789EF10EB089BDC":"55E40BC41E37E3E2AD25C3C6654511FFA8474A91A0032087593852D3E7D76BD3":"8D2D688C6CF93E1160AD04CC4429117DC2C41825E1E9FCA0ADDD34E6F1B39F7B":"990C57520812BE512641E47034832106BC7D3E8DD0E4C7F1136D7006547CEC6A":"89AFC39D41D3B327814B80940B042590F96556EC91E6AE7939BCE31F3A18BF2B":"49C27868F4ECA2179BFD7D59B1E3BF34C1DBDE61AE12931648F43E59632504DE"

ECP test vectors brainpoolP384r1 rfc 7027
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_test_vect:MBEDTLS_ECP_DP_BP384R1:"1E20F5E048A5886F1F157C74E91BDE2B98C8B52D58E5003D57053FC4B0BD65D6F15EB5D1EE1610DF870795143627D042":"68B665DD91C195800650CDD363C625F4E742E8134667B767B1B476793588F885AB698C852D4A6E77A252D6380FCAF068":"55BC91A39C9EC01DEE36017B7D673A931236D2F1F5C83942D049E3FA20607493E0D038FF2FD30C2AB67D15C85F7FAA59":"032640BC6003C59260F7250C3DB58CE647F98E1260ACCE4ACDA3DD869F74E01F8BA5E0324309DB6A9831497ABAC96670":"4D44326F269A597A5B58BBA565DA5556ED7FD9A8A9EB76C25F46DB69D19DC8CE6AD18E404B15738B2086DF37E71D1EB4":"62D692136DE56CBE93BF5FA3188EF58BC8A3A0EC6C1E151A21038A42E9185329B5B275903D192F8D4E1F32FE9CC78C48":"0BD9D3A7EA0B3D519D09D8E48D0785FB744A6B355E6304BC51C229FBBCE239BBADF6403715C35D4FB2A5444F575D4F42":"0DF213417EBE4D8E40A5F76F66C56470C489A3478D146DECF6DF0D94BAE9E598157290F8756066975F1DB34B2324B7BD"

ECP test vectors brainpoolP512r1 rfc 7027
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_test_vect:MBEDTLS_ECP_DP_BP512R1:"16302FF0DBBB5A8D733DAB7141C1B45ACBC8715939677F6A56850A38BD87BD59B09E80279609FF333EB9D4C061231FB26F92EEB04982A5F1D1764CAD57665422":"0A420517E406AAC0ACDCE90FCD71487718D3B953EFD7FBEC5F7F27E28C6149999397E91E029E06457DB2D3E640668B392C2A7E737A7F0BF04436D11640FD09FD":"72E6882E8DB28AAD36237CD25D580DB23783961C8DC52DFA2EC138AD472A0FCEF3887CF62B623B2A87DE5C588301EA3E5FC269B373B60724F5E82A6AD147FDE7":"230E18E1BCC88A362FA54E4EA3902009292F7F8033624FD471B5D8ACE49D12CFABBC19963DAB8E2F1EBA00BFFB29E4D72D13F2224562F405CB80503666B25429":"9D45F66DE5D67E2E6DB6E93A59CE0BB48106097FF78A081DE781CDB31FCE8CCBAAEA8DD4320C4119F1E9CD437A2EAB3731FA9668AB268D871DEDA55A5473199F":"2FDC313095BCDD5FB3A91636F07A959C8E86B5636A1E930E8396049CB481961D365CC11453A06C719835475B12CB52FC3C383BCE35E27EF194512B71876285FA":"A7927098655F1F9976FA50A9D566865DC530331846381C87256BAF3226244B76D36403C024D7BBF0AA0803EAFF405D3D24F11A9B5C0BEF679FE1454B21C4CD1F":"7DB71C3DEF63212841C463E881BDCF055523BD368240E6C3143BD8DEF8B3B3223B95E0F53082FF5E412F4222537A43DF1C6D25729DDB51620A832BE6A26680A2"

ECP test vectors Curve25519
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_test_vec_x:MBEDTLS_ECP_DP_CURVE25519:"5AC99F33632E5A768DE7E81BF854C27C46E3FBF2ABBACD29EC4AFF517369C660":"057E23EA9F1CBE8A27168F6E696A791DE61DD3AF7ACD4EEACC6E7BA514FDA863":"47DC3D214174820E1154B49BC6CDB2ABD45EE95817055D255AA35831B70D3260":"6EB89DA91989AE37C7EAC7618D9E5C4951DBA1D73C285AE1CD26A855020EEF04":"61450CD98E36016B58776A897A9F0AEF738B99F09468B8D6B8511184D53494AB"

ECP point multiplication Curve25519 (normalized) #1
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_test_mul:MBEDTLS_ECP_DP_CURVE25519:"5AC99F33632E5A768DE7E81BF854C27C46E3FBF2ABBACD29EC4AFF517369C660":"09":"00":"01":"057E23EA9F1CBE8A27168F6E696A791DE61DD3AF7ACD4EEACC6E7BA514FDA863":"00":"01":0

ECP point multiplication Curve25519 (not normalized) #2
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_test_mul:MBEDTLS_ECP_DP_CURVE25519:"5AC99F33632E5A768DE7E81BF854C27C46E3FBF2ABBACD29EC4AFF517369C660":"1B":"00":"03":"057E23EA9F1CBE8A27168F6E696A791DE61DD3AF7ACD4EEACC6E7BA514FDA863":"00":"01":MBEDTLS_ERR_ECP_INVALID_KEY

ECP point multiplication Curve25519 (element of order 2: origin) #3
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_test_mul:MBEDTLS_ECP_DP_CURVE25519:"5AC99F33632E5A768DE7E81BF854C27C46E3FBF2ABBACD29EC4AFF517369C660":"00":"00":"01":"00":"01":"00":MBEDTLS_ERR_ECP_INVALID_KEY

ECP point multiplication Curve25519 (element of order 4: 1) #4
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_test_mul:MBEDTLS_ECP_DP_CURVE25519:"5AC99F33632E5A768DE7E81BF854C27C46E3FBF2ABBACD29EC4AFF517369C660":"01":"00":"01":"00":"01":"00":MBEDTLS_ERR_ECP_INVALID_KEY

ECP point multiplication Curve25519 (element of order 8) #5
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_test_mul:MBEDTLS_ECP_DP_CURVE25519:"5AC99F33632E5A768DE7E81BF854C27C46E3FBF2ABBACD29EC4AFF517369C660":"B8495F16056286FDB1329CEB8D09DA6AC49FF1FAE35616AEB8413B7C7AEBE0":"00":"01":"00":"01":"00":MBEDTLS_ERR_ECP_INVALID_KEY

ECP point multiplication rng fail secp256r1
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_test_mul_rng:MBEDTLS_ECP_DP_SECP256R1:"814264145F2F56F2E96A8E337A1284993FAF432A5ABCE59E867B7291D507A3AF"

ECP point multiplication rng fail Curve25519
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_test_mul_rng:MBEDTLS_ECP_DP_CURVE25519:"5AC99F33632E5A768DE7E81BF854C27C46E3FBF2ABBACD29EC4AFF517369C660"

ECP point muladd secp256r1 #1
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_muladd:MBEDTLS_ECP_DP_SECP256R1:"01":"04e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e0e1ff20e1ffe120e1e1e173287170a761308491683e345cacaebb500c96e1a7bbd37772968b2c951f0579":"01":"04e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1ffffffff20e120e1e1e1e13a4e135157317b79d4ecf329fed4f9eb00dc67dbddae33faca8b6d8a0255b5ce":"04fab65e09aa5dd948320f86246be1d3fc571e7f799d9005170ed5cc868b67598431a668f96aa9fd0b0eb15f0edf4c7fe1be2885eadcb57e3db4fdd093585d3fa6"

ECP point muladd secp256r1 #2
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_muladd:MBEDTLS_ECP_DP_SECP256R1:"01":"04e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1ffffffff20e120e1e1e1e13a4e135157317b79d4ecf329fed4f9eb00dc67dbddae33faca8b6d8a0255b5ce":"01":"04e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e0e1ff20e1ffe120e1e1e173287170a761308491683e345cacaebb500c96e1a7bbd37772968b2c951f0579":"04fab65e09aa5dd948320f86246be1d3fc571e7f799d9005170ed5cc868b67598431a668f96aa9fd0b0eb15f0edf4c7fe1be2885eadcb57e3db4fdd093585d3fa6"

ECP point set zero
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_set_zero:MBEDTLS_ECP_DP_SECP256R1:"04e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e1e0e1ff20e1ffe120e1e1e173287170a761308491683e345cacaebb500c96e1a7bbd37772968b2c951f0579"

ECP test vectors Curve448 (RFC 7748 6.2, after decodeUCoordinate)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_test_vec_x:MBEDTLS_ECP_DP_CURVE448:"eb7298a5c0d8c29a1dab27f1a6826300917389449741a974f5bac9d98dc298d46555bce8bae89eeed400584bb046cf75579f51d125498f98":"a01fc432e5807f17530d1288da125b0cd453d941726436c8bbd9c5222c3da7fa639ce03db8d23b274a0721a1aed5227de6e3b731ccf7089b":"ad997351b6106f36b0d1091b929c4c37213e0d2b97e85ebb20c127691d0dad8f1d8175b0723745e639a3cb7044290b99e0e2a0c27a6a301c":"0936f37bc6c1bd07ae3dec7ab5dc06a73ca13242fb343efc72b9d82730b445f3d4b0bd077162a46dcfec6f9b590bfcbcf520cdb029a8b73e":"9d874a5137509a449ad5853040241c5236395435c36424fd560b0cb62b281d285275a740ce32a22dd1740f4aa9161cec95ccc61a18f4ff07"

ECP test vectors secp192k1
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED
ecp_test_vect:MBEDTLS_ECP_DP_SECP192K1:"D1E13A359F6E0F0698791938E6D60246030AE4B0D8D4E9DE":"281BCA982F187ED30AD5E088461EBE0A5FADBB682546DF79":"3F68A8E9441FB93A4DD48CB70B504FCC9AA01902EF5BE0F3":"BE97C5D2A1A94D081E3FACE53E65A27108B7467BDF58DE43":"5EB35E922CD693F7947124F5920022C4891C04F6A8B8DCB2":"60ECF73D0FC43E0C42E8E155FFE39F9F0B531F87B34B6C3C":"372F5C5D0E18313C82AEF940EC3AFEE26087A46F1EBAE923":"D5A9F9182EC09CEAEA5F57EA10225EC77FA44174511985FD"

ECP test vectors secp224k1
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_test_vect:MBEDTLS_ECP_DP_SECP224K1:"8EAD9B2819A3C2746B3EDC1E0D30F23271CDAC048C0615C961B1A9D3":"DEE0A75EF26CF8F501DB80807A3A0908E5CF01852709C1D35B31428B":"276D2B817918F7CD1DA5CCA081EC4B62CD255E0ACDC9F85FA8C52CAC":"AB7E70AEDA68A174ECC1F3800561B2D4FABE97C5D2A1A94D081E3FAC":"D2E94B00FD30201C40EDF73B137427916687AEA1935B277A5960DD1C":"DE728A614B17D91EB3CB2C17DA195562B6281585986332B3E12DA0ED":"B66B673D29038A3487A2D9C10CDCE67646F7C39C984EBE9E8795AD3C":"928C6147AF5EE4B54FA6ECF77B70CA3FEE5F4182DB057878F129DF":

ECP test vectors secp256k1
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecp_test_vect:MBEDTLS_ECP_DP_SECP256K1:"923C6D4756CD940CD1E13A359F6E0F0698791938E6D60246030AE4B0D8D4E9DE":"20A865B295E93C5B090F324B84D7AC7526AA1CFE86DD80E792CECCD16B657D55":"38AC87141A4854A8DFD87333E107B61692323721FE2EAD6E52206FE471A4771B":"4F5036A8ED5809AB7E70AEDA68A174ECC1F3800561B2D4FABE97C5D2A1A94D08":"029F5D2CC5A2C7E538FBA321439B4EC8DD79B7FEB9C0A8A5114EEA39856E22E8":"165171AFC3411A427F24FDDE1192A551C90983EB421BC982AB4CF4E21F18F04B":"E4B5B537D3ACEA7624F2E9C185BFFD80BC7035E515F33E0D4CFAE747FD20038E":"2BC685B7DCDBC694F5E036C4EAE9BFB489D7BF8940C4681F734B71D68501514C"

ECP selftest
ecp_selftest:

ECP restartable mul secp256r1 max_ops=0 (disabled)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_test_vect_restart:MBEDTLS_ECP_DP_SECP256R1:"814264145F2F56F2E96A8E337A1284993FAF432A5ABCE59E867B7291D507A3AF":"2AF502F3BE8952F2C9B5A8D4160D09E97165BE50BC42AE4A5E8D3B4BA83AEB15":"EB0FAF4CA986C4D38681A0F9872D79D56795BD4BFF6E6DE3C0F5015ECE5EFD85":"2CE1788EC197E096DB95A200CC0AB26A19CE6BCCAD562B8EEE1B593761CF7F41":"DD0F5396219D1EA393310412D19A08F1F5811E9DC8EC8EEA7F80D21C820C2788":"0357DCCD4C804D0D8D33AA42B848834AA5605F9AB0D37239A115BBB647936F50":0:0:0

ECP restartable mul secp256r1 max_ops=1
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_test_vect_restart:MBEDTLS_ECP_DP_SECP256R1:"814264145F2F56F2E96A8E337A1284993FAF432A5ABCE59E867B7291D507A3AF":"2AF502F3BE8952F2C9B5A8D4160D09E97165BE50BC42AE4A5E8D3B4BA83AEB15":"EB0FAF4CA986C4D38681A0F9872D79D56795BD4BFF6E6DE3C0F5015ECE5EFD85":"2CE1788EC197E096DB95A200CC0AB26A19CE6BCCAD562B8EEE1B593761CF7F41":"DD0F5396219D1EA393310412D19A08F1F5811E9DC8EC8EEA7F80D21C820C2788":"0357DCCD4C804D0D8D33AA42B848834AA5605F9AB0D37239A115BBB647936F50":1:1:5000

ECP restartable mul secp256r1 max_ops=10000
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_test_vect_restart:MBEDTLS_ECP_DP_SECP256R1:"814264145F2F56F2E96A8E337A1284993FAF432A5ABCE59E867B7291D507A3AF":"2AF502F3BE8952F2C9B5A8D4160D09E97165BE50BC42AE4A5E8D3B4BA83AEB15":"EB0FAF4CA986C4D38681A0F9872D79D56795BD4BFF6E6DE3C0F5015ECE5EFD85":"2CE1788EC197E096DB95A200CC0AB26A19CE6BCCAD562B8EEE1B593761CF7F41":"DD0F5396219D1EA393310412D19A08F1F5811E9DC8EC8EEA7F80D21C820C2788":"0357DCCD4C804D0D8D33AA42B848834AA5605F9AB0D37239A115BBB647936F50":10000:0:0

ECP restartable mul secp256r1 max_ops=250
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_test_vect_restart:MBEDTLS_ECP_DP_SECP256R1:"814264145F2F56F2E96A8E337A1284993FAF432A5ABCE59E867B7291D507A3AF":"2AF502F3BE8952F2C9B5A8D4160D09E97165BE50BC42AE4A5E8D3B4BA83AEB15":"EB0FAF4CA986C4D38681A0F9872D79D56795BD4BFF6E6DE3C0F5015ECE5EFD85":"2CE1788EC197E096DB95A200CC0AB26A19CE6BCCAD562B8EEE1B593761CF7F41":"DD0F5396219D1EA393310412D19A08F1F5811E9DC8EC8EEA7F80D21C820C2788":"0357DCCD4C804D0D8D33AA42B848834AA5605F9AB0D37239A115BBB647936F50":250:2:32

ECP restartable muladd secp256r1 max_ops=0 (disabled)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_muladd_restart:MBEDTLS_ECP_DP_SECP256R1:"CB28E0999B9C7715FD0A80D8E47A77079716CBBF917DD72E97566EA1C066957C":"2B57C0235FB7489768D058FF4911C20FDBE71E3699D91339AFBB903EE17255DC":"C3875E57C85038A0D60370A87505200DC8317C8C534948BEA6559C7C18E6D4CE":"3B4E49C4FDBFC006FF993C81A50EAE221149076D6EC09DDD9FB3B787F85B6483":"2442A5CC0ECD015FA3CA31DC8E2BBC70BF42D60CBCA20085E0822CB04235E970":"6FC98BD7E50211A4A27102FA3549DF79EBCB4BF246B80945CDDFE7D509BBFD7D":0:0:0

ECP restartable muladd secp256r1 max_ops=1
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_muladd_restart:MBEDTLS_ECP_DP_SECP256R1:"CB28E0999B9C7715FD0A80D8E47A77079716CBBF917DD72E97566EA1C066957C":"2B57C0235FB7489768D058FF4911C20FDBE71E3699D91339AFBB903EE17255DC":"C3875E57C85038A0D60370A87505200DC8317C8C534948BEA6559C7C18E6D4CE":"3B4E49C4FDBFC006FF993C81A50EAE221149076D6EC09DDD9FB3B787F85B6483":"2442A5CC0ECD015FA3CA31DC8E2BBC70BF42D60CBCA20085E0822CB04235E970":"6FC98BD7E50211A4A27102FA3549DF79EBCB4BF246B80945CDDFE7D509BBFD7D":1:1:10000

ECP restartable muladd secp256r1 max_ops=10000
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_muladd_restart:MBEDTLS_ECP_DP_SECP256R1:"CB28E0999B9C7715FD0A80D8E47A77079716CBBF917DD72E97566EA1C066957C":"2B57C0235FB7489768D058FF4911C20FDBE71E3699D91339AFBB903EE17255DC":"C3875E57C85038A0D60370A87505200DC8317C8C534948BEA6559C7C18E6D4CE":"3B4E49C4FDBFC006FF993C81A50EAE221149076D6EC09DDD9FB3B787F85B6483":"2442A5CC0ECD015FA3CA31DC8E2BBC70BF42D60CBCA20085E0822CB04235E970":"6FC98BD7E50211A4A27102FA3549DF79EBCB4BF246B80945CDDFE7D509BBFD7D":10000:0:0

ECP restartable muladd secp256r1 max_ops=250
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_muladd_restart:MBEDTLS_ECP_DP_SECP256R1:"CB28E0999B9C7715FD0A80D8E47A77079716CBBF917DD72E97566EA1C066957C":"2B57C0235FB7489768D058FF4911C20FDBE71E3699D91339AFBB903EE17255DC":"C3875E57C85038A0D60370A87505200DC8317C8C534948BEA6559C7C18E6D4CE":"3B4E49C4FDBFC006FF993C81A50EAE221149076D6EC09DDD9FB3B787F85B6483":"2442A5CC0ECD015FA3CA31DC8E2BBC70BF42D60CBCA20085E0822CB04235E970":"6FC98BD7E50211A4A27102FA3549DF79EBCB4BF246B80945CDDFE7D509BBFD7D":250:4:64

ECP export key parameters #1 (OK)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_export:MBEDTLS_ECP_DP_SECP256R1:"37cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f76822596292":"4ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edff":"00f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":0:0

ECP export key parameters #2 (invalid group)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_export:MBEDTLS_ECP_DP_SECP256R1:"37cc56d976091e5a723ec7592dff206eee7cf9069174d0ad14b5f76822596292":"4ee500d82311ffea2fd2345d5d16bd8a88c26b770d55cd8a2a0efa01c8b4edff":"00f12a1320760270a83cbffd53f6031ef76a5d86c8a204f2c30ca9ebf51f0f0ea7":MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE:1

ECP check order for SECP192R1
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_check_order:MBEDTLS_ECP_DP_SECP192R1:"FFFFFFFFFFFFFFFFFFFFFFFF99DEF836146BC9B1B4D22831"

ECP check order for SECP224R1
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecp_check_order:MBEDTLS_ECP_DP_SECP224R1:"FFFFFFFFFFFFFFFFFFFFFFFFFFFF16A2E0B8F03E13DD29455C5C2A3D"

ECP check order for SECP256R1
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_check_order:MBEDTLS_ECP_DP_SECP256R1:"FFFFFFFF00000000FFFFFFFFFFFFFFFFBCE6FAADA7179E84F3B9CAC2FC632551"

ECP check order for SECP384R1
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_check_order:MBEDTLS_ECP_DP_SECP384R1:"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC7634D81F4372DDF581A0DB248B0A77AECEC196ACCC52973"

ECP check order for SECP521R1
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_check_order:MBEDTLS_ECP_DP_SECP521R1:"01FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA51868783BF2F966B7FCC0148F709A5D03BB5C9B8899C47AEBB6FB71E91386409"

ECP check order for BP256R1
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_check_order:MBEDTLS_ECP_DP_BP256R1:"A9FB57DBA1EEA9BC3E660A909D838D718C397AA3B561A6F7901E0E82974856A7"

ECP check order for BP384R1
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_check_order:MBEDTLS_ECP_DP_BP384R1:"8CB91E82A3386D280F5D6F7E50E641DF152F7109ED5456B31F166E6CAC0425A7CF3AB6AF6B7FC3103B883202E9046565"

ECP check order for BP512R1
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_check_order:MBEDTLS_ECP_DP_BP512R1:"AADD9DB8DBE9C48B3FD4E6AE33C9FC07CB308DB3B3C9D20ED6639CCA70330870553E5C414CA92619418661197FAC10471DB1D381085DDADDB58796829CA90069"

ECP check order for CURVE25519
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_check_order:MBEDTLS_ECP_DP_CURVE25519:"1000000000000000000000000000000014def9dea2f79cd65812631a5cf5d3ed"

ECP check order for SECP192K1
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED
ecp_check_order:MBEDTLS_ECP_DP_SECP192K1:"fffffffffffffffffffffffe26f2fc170f69466a74defd8d"

ECP check order for SECP224K1
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_check_order:MBEDTLS_ECP_DP_SECP224K1:"10000000000000000000000000001dce8d2ec6184caf0a971769fb1f7"

ECP check order for SECP256K1
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecp_check_order:MBEDTLS_ECP_DP_SECP256K1:"fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"

ECP check order for CURVE448
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_check_order:MBEDTLS_ECP_DP_CURVE448:"3fffffffffffffffffffffffffffffffffffffffffffffffffffffff7cca23e9c44edb49aed63690216cc2728dc58f552378c292ab5844f3"

ecp_setup #1 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP192R1)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_mod_setup:"fffffffffffffffffffffffffffffffeffffffffffffffff":MBEDTLS_ECP_DP_SECP192R1:MBEDTLS_ECP_MOD_COORDINATE:0

ecp_setup #2 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP224R1)
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecp_mod_setup:"00000000ffffffffffffffffffffffffffffffff000000000000000000000001":MBEDTLS_ECP_DP_SECP224R1:MBEDTLS_ECP_MOD_COORDINATE:0

ecp_setup #3 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP256R1)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_mod_setup:"ffffffff00000001000000000000000000000000ffffffffffffffffffffffff":MBEDTLS_ECP_DP_SECP256R1:MBEDTLS_ECP_MOD_COORDINATE:0

ecp_setup #4 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP384R1)
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_mod_setup:"fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff":MBEDTLS_ECP_DP_SECP384R1:MBEDTLS_ECP_MOD_COORDINATE:0

ecp_setup #5 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP521R1)
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_mod_setup:"1ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff":MBEDTLS_ECP_DP_SECP521R1:MBEDTLS_ECP_MOD_COORDINATE:0

ecp_setup #6 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_setup:"a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_COORDINATE:0

ecp_setup #7 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_setup:"8cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec53":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_COORDINATE:0

ecp_setup #8 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_setup:"aadd9db8dbe9c48b3fd4e6ae33c9fc07cb308db3b3c9d20ed6639cca703308717d4d9b009bc66842aecda12ae6a380e62881ff2f2d82c68528aa6056583a48f3":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_COORDINATE:0

ecp_setup #9 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_CURVE25519)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_mod_setup:"7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffed":MBEDTLS_ECP_DP_CURVE25519:MBEDTLS_ECP_MOD_COORDINATE:0

ecp_setup #10 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP192K1)
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED
ecp_mod_setup:"fffffffffffffffffffffffffffffffffffffffeffffee37":MBEDTLS_ECP_DP_SECP192K1:MBEDTLS_ECP_MOD_COORDINATE:0

ecp_setup #11 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_setup:"fffffffffffffffffffffffffffffffffffffffffffffffeffffe56d":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_COORDINATE:0

ecp_setup #12 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP256K1)
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecp_mod_setup:"fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f":MBEDTLS_ECP_DP_SECP256K1:MBEDTLS_ECP_MOD_COORDINATE:0

ecp_setup #13 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_CURVE448)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_mod_setup:"000000000000000fffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffffffffffffffffffffffffffffffffffffffffffffffffffff":MBEDTLS_ECP_DP_CURVE448:MBEDTLS_ECP_MOD_COORDINATE:0

ecp_setup #14 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192R1)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_mod_setup:"ffffffffffffffffffffffff99def836146bc9b1b4d22831":MBEDTLS_ECP_DP_SECP192R1:MBEDTLS_ECP_MOD_SCALAR:0

ecp_setup #15 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224R1)
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecp_mod_setup:"ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d":MBEDTLS_ECP_DP_SECP224R1:MBEDTLS_ECP_MOD_SCALAR:0

ecp_setup #16 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256R1)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_mod_setup:"ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551":MBEDTLS_ECP_DP_SECP256R1:MBEDTLS_ECP_MOD_SCALAR:0

ecp_setup #17 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP384R1)
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_mod_setup:"ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973":MBEDTLS_ECP_DP_SECP384R1:MBEDTLS_ECP_MOD_SCALAR:0

ecp_setup #18 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP521R1)
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_mod_setup:"1fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386409":MBEDTLS_ECP_DP_SECP521R1:MBEDTLS_ECP_MOD_SCALAR:0

ecp_setup #19 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_setup:"a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_SCALAR:0

ecp_setup #20 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_setup:"8cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec53":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_COORDINATE:0

ecp_setup #21 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_setup:"aadd9db8dbe9c48b3fd4e6ae33c9fc07cb308db3b3c9d20ed6639cca70330870553e5c414ca92619418661197fac10471db1d381085ddaddb58796829ca90069":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_SCALAR:0

ecp_setup #22 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE25519)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_mod_setup:"1000000000000000000000000000000014def9dea2f79cd65812631a5cf5d3ed":MBEDTLS_ECP_DP_CURVE25519:MBEDTLS_ECP_MOD_SCALAR:0

ecp_setup #23 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192K1)
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED
ecp_mod_setup:"fffffffffffffffffffffffe26f2fc170f69466a74defd8d":MBEDTLS_ECP_DP_SECP192K1:MBEDTLS_ECP_MOD_SCALAR:0

ecp_setup #24 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_setup:"000000010000000000000000000000000001dce8d2ec6184caf0a971769fb1f7":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_SCALAR:0

ecp_setup #25 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256K1)
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecp_mod_setup:"fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141":MBEDTLS_ECP_DP_SECP256K1:MBEDTLS_ECP_MOD_SCALAR:0

ecp_setup #26 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE448)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_mod_setup:"0000000000000003fffffffffffffffffffffffffffffffffffffffffffffffffffffff7cca23e9c44edb49aed63690216cc2728dc58f552378c292ab5844f3":MBEDTLS_ECP_DP_CURVE448:MBEDTLS_ECP_MOD_SCALAR:0

ecp_setup_negative_test #27 Invalid Moduli Type
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_mod_setup:"fffffffffffffffffffffffe26f2fc17f69466a74defd8d":MBEDTLS_ECP_DP_CURVE448:MBEDTLS_ECP_MOD_NONE:MBEDTLS_ERR_ECP_BAD_INPUT_DATA

ecp_setup_negative_test #28 Invalid Curve Type
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_mod_setup:"fffffffffffffffffffffffe26f2fc17f69466a74defd8d":MBEDTLS_ECP_DP_NONE:MBEDTLS_ECP_MOD_SCALAR:MBEDTLS_ERR_ECP_BAD_INPUT_DATA

# The following data be generated by random.getrandbits() in python must less than the named curves' modulus.
# mbedtls_mpi_mod_residue_setup() can be used to check whether it satisfy the requirements.
ecp_mul_inv #1 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192R1)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_mod_mul_inv:"0000000000000000000000000000152d02c7e14af67fe0bf":MBEDTLS_ECP_DP_SECP192R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #2 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192R1)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_mod_mul_inv:"4acca2d7100bad687080217babfb490d23dd6460a0007f24":MBEDTLS_ECP_DP_SECP192R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #3 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192R1)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_mod_mul_inv:"c4fd9a06df9b4efa94531578af8b5886ec0ada82884199f7":MBEDTLS_ECP_DP_SECP192R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #4 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224R1)
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecp_mod_mul_inv:"f9c4728bef9fba3e7d856a8e2ff62f20c2a57bf64f6d707f0829a8ff":MBEDTLS_ECP_DP_SECP224R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #5 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224R1)
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecp_mod_mul_inv:"cee8071ade3e016fd47627782f6543814dd6ab7e6f432679ddacf9ed":MBEDTLS_ECP_DP_SECP224R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #6 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224R1)
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecp_mod_mul_inv:"326258467dcbf4d1ab1665a4c5036cb35f4c9231199b58166b3966c6":MBEDTLS_ECP_DP_SECP224R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #7 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256R1)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_mod_mul_inv:"c36eadeab80f149cd51a1ed6311270ae2e4acc6734e787135f499c3a97f1edc3":MBEDTLS_ECP_DP_SECP256R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #8 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256R1)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_mod_mul_inv:"e384042f3130be8a796b221724cf1127a44290804cfbeb7fb6f57142a2a5cddd":MBEDTLS_ECP_DP_SECP256R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #9 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256R1)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_mod_mul_inv:"f1d356376f03b5dbf0fd08bde5c4293115f7c7911f7a3ec3f90557602eb20147":MBEDTLS_ECP_DP_SECP256R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #10 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP384R1)
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_mod_mul_inv:"a3137cd9b0c9e75a871f92e3ab6b284069ee06cd9c0afb2368fd8d381afcfecc553cb6b3f29216038d268a8d8fcd00f7":MBEDTLS_ECP_DP_SECP384R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #11 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP384R1)
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_mod_mul_inv:"a340ca2e1f39f89261f20a23881cde271e36b32add90cbc1801d2375d6db664df297df2364aaafbb9ba3d4672e4fd022":MBEDTLS_ECP_DP_SECP384R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #12 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP384R1)
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_mod_mul_inv:"491b1d169c9262fd737847c13bb7370d91825fe985cfa000d4b9bd3c22e7b63016122c53156fae4757943a819a1ced6d":MBEDTLS_ECP_DP_SECP384R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #13 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP521R1)
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_mod_mul_inv:"1477156c589f498b61beb35f57662410d8821f3a1ee4a5968a8009618dbe4afda408809822eb0e994fbf9da1659c1ea21b151db97cd1f1567fa4b9327967e0aa591":MBEDTLS_ECP_DP_SECP521R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #14 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP521R1)
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_mod_mul_inv:"158dd0fdd801513590d221009f2b6c212f2b30214cc3b1f80aaf9142dc9f328c8e2b0af83e1acdb102d85f287d77188c2b8e7911cf9452f5014966f28da330e1fa6":MBEDTLS_ECP_DP_SECP521R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #15 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP521R1)
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_mod_mul_inv:"1e53d580521a1cff4cd72576c13fecb2cbcf39453f2b437f0c8dc78d7982a37749f099942ce693751ec43407c3acf46315132ea2a9ae5fa9253408da2375d2b58fc":MBEDTLS_ECP_DP_SECP521R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #16 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_mul_inv:"96e729c5c64b7f54375c2779f034acc1f32c26358a621ab421b9c4d4c11ddb28":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #17 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_mul_inv:"7491ad896c2a0ec65950db5c91e9b573a77839fd576481da85f5a77c7ceccce0":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #18 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_mul_inv:"8d9454c7494b6e08d068391c811cb23cbe9318246a6c021b0018745eb6918751":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #18.1 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_mul_inv:"a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5376":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #19 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_mul_inv:"3aff86b1ee706d38e4995b76f6433d9173c5d3ec19b43ff0a3d53ac20965c911":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #20 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_mul_inv:"8bcf768f2f7d6d22133de97f5e463337ff030e662d6f6724d5bad27e27be5dc0":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #21 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_mul_inv:"435ed5da780b83a0130fc8f03e8e224d5bb4ae2eeeba214b8156544c4ae85944":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #22 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_mul_inv:"4003a648cfeda3a738a3e05933c6ce5098ab6dc943821cfc485f8991caaba99979ced9bb237c6b24997db8359a4a659f":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #23 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_mul_inv:"62b4355dc4cc6d76fc1633c46222c6ad5efaf6de931f0d25217d3dcebfd443fec31eeba68688717275a039863d03a48b":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #24 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_mul_inv:"80acca473c3fcee61d13a0a766ed0dcd5f50277f576ff6f3461664d436e2054ad7ecc8b7c0a9424fbda1d431c540c05a":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #24.1 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_mul_inv:"8cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec52":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #25 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_mul_inv:"371851bd69a5a1734b195c6ad6b041f51d94718cb437ab4a0a14ee5fa5fccd29328f3e77bfa2e4c58195ccb55cdc6a4":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #26 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_mul_inv:"82b18c3794463edeb80760098f7d392569dde6ed5ec21753b66e4e9b79e2f3e89bfc9fea1a2ffda2c285a0cc4afeab0":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #27 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_mul_inv:"7c17dc9df00c870a701c07186bd74b752abb6a9e17ee1c6342403e75d6fa7431b32e2495eb3f5e67c6519b43c6f69e28":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #28 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_mul_inv:"27e445caeb0d6752bd051f36a6d21ccdf67ba9b8238f2552aba237c3c72f3d384e7df2a25f95b779c7f38a4123741e2c691c4d5b87b231e4a98ecb9166a73674":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #29 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_mul_inv:"7b1dc9c166abbcd7a1a6b1ec375a3125aa3257d1d40e781f1ac9023dba4248415aa0eaea6fa8ce460f85fdae3f62fbb4bdcb953328f5d5664b71f70f681c0f4e":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #30 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_mul_inv:"8be202ecb80ae3f6fe07a17b03c14997668b37d029d38943245c8a6cd1cbce3d57cfc673886a22db7ab8686570881a5dc1d9855aa6618c52df55a04510e00bba":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #30.1 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_mul_inv:"aadd9db8dbe9c48b3fd4e6ae33c9fc07cb308db3b3c9d20ed6639cca703308717d4d9b009bc66842aecda12ae6a380e62881ff2f2d82c68528aa6056583a48f2":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #31 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_mul_inv:"572a5522bc45566df4c7575b91fdbc74975fd59380339b5aa23cbce2204744793ca3255705f5d9ba48335f36baf462010680f1e35cca26468d7d8f4223988189":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #32 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_mul_inv:"99c2751d157489ab961a7bf1be12c8575cc56c99c6db8e2273450f6332ecdd3cd9b33763acd0509d8b98250462319bfd7cfbfc87c99ce31c15cefab651bc088c":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #33 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_mul_inv:"3169122b79ab7add228eed5b4de336dcb5daae8136b5cb184c08752d727c954840f8e2ad6860245128f6931a4598578679a65aa6e4b138a349586c57d03d2216":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #34 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE25519)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_mod_mul_inv:"1000000000000000000000000000000014def9dea2079cd65812631a5cf5d3ed":MBEDTLS_ECP_DP_CURVE25519:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #35 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE25519)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_mod_mul_inv:"1000000000000000000000000000000010caf49570936f75d70f03efac6c1c19":MBEDTLS_ECP_DP_CURVE25519:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #36 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE25519)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_mod_mul_inv:"468de1bfdbb20b67371bc5ad0f2bc3e70705b6d85c14ad75daafdbd1502cfd1":MBEDTLS_ECP_DP_CURVE25519:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #37 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192K1)
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED
ecp_mod_mul_inv:"2228b202d612f2e66d8ca00b7e1c19a737ee7db2708d91cd":MBEDTLS_ECP_DP_SECP192K1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #38 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192K1)
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED
ecp_mod_mul_inv:"40c0451d06b0d622c65b8336c4c9abe8828f6fd5d5c1abde":MBEDTLS_ECP_DP_SECP192K1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #39 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192K1)
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED
ecp_mod_mul_inv:"d2a10413f48d7bcc18a9b7c53c7914c5302c9c9e48b2eb62":MBEDTLS_ECP_DP_SECP192K1:MBEDTLS_ECP_MOD_SCALAR

# Public values of secp224K1 have 225 bits, if we randomly generate only 224 bits, we should add the leading '0'
# to make the limbs match with each other and make the function mbedtls_mpi_mod_residue_setup() happy.
ecp_mul_inv #40 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_mul_inv:"0cc154fe846d6b9f51d6166a8d1bb969ff634ab9af95cc89d01669c86":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #40.1 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_mul_inv:"1000000000000000000000000000075ea446a83291f5136799781cfbd":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #41 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_mul_inv:"0614cf6b720cc9dcc6d3bb36bb46cf285e23a083b067be8c93b51cbb4":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #41.1 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_mul_inv:"1000000000000000000000000000059232050dc913da533ec71073ce3":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #42 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_mul_inv:"071b3a40f3e2b8984e8cc238b7725870da10cb2de37f430da2da68645":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #42.1 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_mul_inv:"10000000000000000000000000000aca628de662cdbd5cb4dc69efbb8":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #43 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256K1)
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecp_mod_mul_inv:"9fd95fed98cc1c2ef91b5dc02fa84f63597e15a3326c07f2918afb3ffd093343":MBEDTLS_ECP_DP_SECP256K1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #44 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256K1)
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecp_mod_mul_inv:"5ddbd441c7037e11caaa9878216c5cfeae67864260429eab4529b56c2661f3de":MBEDTLS_ECP_DP_SECP256K1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #45 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256K1)
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecp_mod_mul_inv:"f8d3f3c02fd712f711d8e30d0d4c142eb106e5f75c25f55b3f983bc5c83c568a":MBEDTLS_ECP_DP_SECP256K1:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #46 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE448)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_mod_mul_inv:"0000000000000003fffffffffffffffffffffffffffffffffffffffffffffffffffffff11ca23e9c44edb49aed63690216cc2728dc58f552378c292ab5844f3":MBEDTLS_ECP_DP_CURVE448:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #47 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE448)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_mod_mul_inv:"0000000000000003fffffffffffffffffffffffffffffffffffffffffffffffffffffff0169d3f35081924aeaf1beac2f2720557c9bdf6b42cdceb54c6160ba":MBEDTLS_ECP_DP_CURVE448:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #48 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE448)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_mod_mul_inv:"0000000000000003fffffffffffffffffffffffffffffffffffffffffffffffffffffff01243a939d867d7e0a75a8568d4d66de88f3ecc1ad37f91a8f9d7d70":MBEDTLS_ECP_DP_CURVE448:MBEDTLS_ECP_MOD_SCALAR

ecp_mul_inv #49 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP192R1)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_mod_mul_inv:"0000000000000000000000000000152d02c7e14af67fe0bf":MBEDTLS_ECP_DP_SECP192R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #50 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP192R1)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_mod_mul_inv:"4acca2d7100bad687080217babfb490d23dd6460a0007f24":MBEDTLS_ECP_DP_SECP192R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #51 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP192R1)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_mod_mul_inv:"c4fd9a06df9b4efa94531578af8b5886ec0ada82884199f7":MBEDTLS_ECP_DP_SECP192R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #51.1 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP192R1)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_mod_mul_inv:"fffffffffffffffffffffffffffffffefffffffffffffffe":MBEDTLS_ECP_DP_SECP192R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #52 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP224R1)
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecp_mod_mul_inv:"0f9c4728bef9fba3e7d856a8e2ff62f20c2a57bf64f6d707f0829a8ff":MBEDTLS_ECP_DP_SECP224R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #53 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP224R1)
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecp_mod_mul_inv:"0cee8071ade3e016fd47627782f6543814dd6ab7e6f432679ddacf9ed":MBEDTLS_ECP_DP_SECP224R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #54 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP224R1)
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecp_mod_mul_inv:"00326258467dcbf4d1ab1665a4c5036cb35f4c9231199b58166b3966c6":MBEDTLS_ECP_DP_SECP224R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #54.1 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP224R1)
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecp_mod_mul_inv:"00ffffffffffffffffffffffffffffffff000000000000000000000000":MBEDTLS_ECP_DP_SECP224R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #55 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP256R1)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_mod_mul_inv:"c36eadeab80f149cd51a1ed6311270ae2e4acc6734e787135f499c3a97f1edc3":MBEDTLS_ECP_DP_SECP256R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #56 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP256R1)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_mod_mul_inv:"e384042f3130be8a796b221724cf1127a44290804cfbeb7fb6f57142a2a5cddd":MBEDTLS_ECP_DP_SECP256R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #57 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP256R1)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_mod_mul_inv:"f1d356376f03b5dbf0fd08bde5c4293115f7c7911f7a3ec3f90557602eb20147":MBEDTLS_ECP_DP_SECP256R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #57.1 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP256R1)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_mod_mul_inv:"ffffffff00000001000000000000000000000000fffffffffffffffffffffffe":MBEDTLS_ECP_DP_SECP256R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #58 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP384R1)
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_mod_mul_inv:"a3137cd9b0c9e75a871f92e3ab6b284069ee06cd9c0afb2368fd8d381afcfecc553cb6b3f29216038d268a8d8fcd00f7":MBEDTLS_ECP_DP_SECP384R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #59 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP384R1)
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_mod_mul_inv:"a340ca2e1f39f89261f20a23881cde271e36b32add90cbc1801d2375d6db664df297df2364aaafbb9ba3d4672e4fd022":MBEDTLS_ECP_DP_SECP384R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #60 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP384R1)
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_mod_mul_inv:"491b1d169c9262fd737847c13bb7370d91825fe985cfa000d4b9bd3c22e7b63016122c53156fae4757943a819a1ced6d":MBEDTLS_ECP_DP_SECP384R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #60.1 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP384R1)
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_mod_mul_inv:"fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000fffffffe":MBEDTLS_ECP_DP_SECP384R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #61 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP521R1)
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_mod_mul_inv:"1477156c589f498b61beb35f57662410d8821f3a1ee4a5968a8009618dbe4afda408809822eb0e994fbf9da1659c1ea21b151db97cd1f1567fa4b9327967e0aa591":MBEDTLS_ECP_DP_SECP521R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #62 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP521R1)
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_mod_mul_inv:"158dd0fdd801513590d221009f2b6c212f2b30214cc3b1f80aaf9142dc9f328c8e2b0af83e1acdb102d85f287d77188c2b8e7911cf9452f5014966f28da330e1fa6":MBEDTLS_ECP_DP_SECP521R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #63 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP521R1)
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_mod_mul_inv:"1e53d580521a1cff4cd72576c13fecb2cbcf39453f2b437f0c8dc78d7982a37749f099942ce693751ec43407c3acf46315132ea2a9ae5fa9253408da2375d2b58fc":MBEDTLS_ECP_DP_SECP521R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #63.1 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP521R1)
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_mod_mul_inv:"1fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe":MBEDTLS_ECP_DP_SECP521R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #64 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_CURVE25519)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_mod_mul_inv:"1000000000000000000000000000000014def9dea2079cd65812631a5cf5d3ed":MBEDTLS_ECP_DP_CURVE25519:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #65 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_CURVE25519)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_mod_mul_inv:"1000000000000000000000000000000010caf49570936f75d70f03efac6c1c19":MBEDTLS_ECP_DP_CURVE25519:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #66 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_CURVE25519)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_mod_mul_inv:"468de1bfdbb20b67371bc5ad0f2bc3e70705b6d85c14ad75daafdbd1502cfd1":MBEDTLS_ECP_DP_CURVE25519:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #66.1 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_CURVE25519)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_mod_mul_inv:"7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffec":MBEDTLS_ECP_DP_CURVE25519:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #67 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP192K1)
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED
ecp_mod_mul_inv:"2228b202d612f2e66d8ca00b7e1c19a737ee7db2708d91cd":MBEDTLS_ECP_DP_SECP192K1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #68 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP192K1)
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED
ecp_mod_mul_inv:"40c0451d06b0d622c65b8336c4c9abe8828f6fd5d5c1abde":MBEDTLS_ECP_DP_SECP192K1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #69 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP192K1)
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED
ecp_mod_mul_inv:"d2a10413f48d7bcc18a9b7c53c7914c5302c9c9e48b2eb62":MBEDTLS_ECP_DP_SECP192K1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #69.1 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP192K1)
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED
ecp_mod_mul_inv:"fffffffffffffffffffffffffffffffffffffffeffffee36":MBEDTLS_ECP_DP_SECP192K1:MBEDTLS_ECP_MOD_COORDINATE

# For coordinate moduli of secp224K1 the values are selected as one for
# modulus - 1, and four random values, generated with
# random.getrandbits(224) % modulus with a seed(2, 2).
ecp_mul_inv #70 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_mul_inv:"fffffffffffffffffffffffffffffffffffffffffffffffeffffe56c":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #71 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_mul_inv:"15ba2bdd177219d30e7a269fd95bafc8f2a4d27bdcf4bb99f4bea973":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #72 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_mul_inv:"da94e3e8ab73738fcf1822ffbc6887782b491044d5e341245c6e4337":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #73 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_mul_inv:"94c9c9500925e4749b575bd13653f8dd9b1f282e4067c3584ee207f8":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #74 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_mul_inv:"cdbd47d364be8049a372db8f6e405d93ffed9235288bc781ae662675":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #75 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_mul_inv:"8b4f2fc15f3f57ebf30b94fa82523e86feac7eb7dc38f519b91751da":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #76 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP256K1)
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecp_mod_mul_inv:"9fd95fed98cc1c2ef91b5dc02fa84f63597e15a3326c07f2918afb3ffd093343":MBEDTLS_ECP_DP_SECP256K1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #77 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP256K1)
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecp_mod_mul_inv:"5ddbd441c7037e11caaa9878216c5cfeae67864260429eab4529b56c2661f3de":MBEDTLS_ECP_DP_SECP256K1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #78 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP256K1)
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecp_mod_mul_inv:"f8d3f3c02fd712f711d8e30d0d4c142eb106e5f75c25f55b3f983bc5c83c568a":MBEDTLS_ECP_DP_SECP256K1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #78.1 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP256K1)
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecp_mod_mul_inv:"fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2e":MBEDTLS_ECP_DP_SECP256K1:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #79 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_CURVE448)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_mod_mul_inv:"0000000000000003fffffffffffffffffffffffffffffffffffffffffffffffffffffff11ca23e9c44edb49aed63690216cc2728dc58f552378c292ab5844f3":MBEDTLS_ECP_DP_CURVE448:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #80 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_CURVE448)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_mod_mul_inv:"0000000000000003fffffffffffffffffffffffffffffffffffffffffffffffffffffff0169d3f35081924aeaf1beac2f2720557c9bdf6b42cdceb54c6160ba":MBEDTLS_ECP_DP_CURVE448:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #81 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_CURVE448)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_mod_mul_inv:"0000000000000003fffffffffffffffffffffffffffffffffffffffffffffffffffffff01243a939d867d7e0a75a8568d4d66de88f3ecc1ad37f91a8f9d7d70":MBEDTLS_ECP_DP_CURVE448:MBEDTLS_ECP_MOD_COORDINATE

ecp_mul_inv #81.1 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_CURVE448)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_mod_mul_inv:"000000000000000fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffffffffffffffffffffffffffffffffffffffffffffffffffffe":MBEDTLS_ECP_DP_CURVE448:MBEDTLS_ECP_MOD_COORDINATE

# The following data was generated using python's standard random library,
# initialised with seed(2,2) and random.getrandbits(curve bits). Curve bits are 192,256,384,520.
# They must be less than the named curves' modulus. mbedtls_mpi_mod_residue_setup()
# can be used to check whether they satisfy the requirements.

# Use the test data "modulus - 1" and "modulus - 2" to ensure the sum overflow case be tested.
ecp_add_sub #1.0 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192R1)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_mod_add_sub:"ffffffffffffffffffffffff99def836146bc9b1b4d22830":"ffffffffffffffffffffffff99def836146bc9b1b4d2282f":MBEDTLS_ECP_DP_SECP192R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #1 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192R1)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_mod_add_sub:"177219d30e7a269fd95bafc8f2a4d27bdcf4bb99f4bea973":"cf1822ffbc6887782b491044d5e341245c6e433715ba2bdd":MBEDTLS_ECP_DP_SECP192R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #2 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192R1)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_mod_add_sub:"3653f8dd9b1f282e4067c3584ee207f8da94e3e8ab73738f":"ffed9235288bc781ae66267594c9c9500925e4749b575bd1":MBEDTLS_ECP_DP_SECP192R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #3 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192R1)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_mod_add_sub:"dc38f519b91751dacdbd47d364be8049a372db8f6e405d93":"ef8acd128b4f2fc15f3f57ebf30b94fa82523e86feac7eb7":MBEDTLS_ECP_DP_SECP192R1:MBEDTLS_ECP_MOD_SCALAR

# Use the test data "modulus - 1" and "modulus - 2" to ensure the sum overflow case be tested.
ecp_add_sub #4.0 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224R1)
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecp_mod_add_sub:"ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c":"ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3b":MBEDTLS_ECP_DP_SECP224R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #4 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224R1)
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecp_mod_add_sub:"706a045defc044a09325626e6b58de744ab6cce80877b6f71e1f6d2":"6c71c4a66148a86fe8624fab5186ee32ee8d7ee9770348a05d300cb9":MBEDTLS_ECP_DP_SECP224R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #5 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224R1)
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecp_mod_add_sub:"3c7295782d6c797f8f7d9b782a1be9cd8697bbd0e2520e33e44c5055":"829a48d422fe99a22c70501e533c91352d3d854e061b90303b08c6e3":MBEDTLS_ECP_DP_SECP224R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #6 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224R1)
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecp_mod_add_sub:"2e8d4b8a8f54f8ceacaab39e83844b40ffa9b9f15c14bc4a829e07b0":"867e5e15bc01bfce6a27e0dfcbf8754472154e76e4c11ab2fec3f6b3":MBEDTLS_ECP_DP_SECP224R1:MBEDTLS_ECP_MOD_SCALAR

# Use the test data "modulus - 1" and "modulus - 2" to ensure the sum overflow case be tested.
ecp_add_sub #7.0 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256R1)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_mod_add_sub:"ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550":"ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254f":MBEDTLS_ECP_DP_SECP256R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #7 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256R1)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_mod_add_sub:"5ca495fa5a91c89b97eeab64ca2ce6bc5d3fd983c34c769fe89204e2e8168561":"665d7435c1066932f4767f26294365b2721dea3bf63f23d0dbe53fcafb2147df":MBEDTLS_ECP_DP_SECP256R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #8 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256R1)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_mod_add_sub:"47733e847d718d733ff98ff387c56473a7a83ee0761ebfd2bd143fa9b714210c":"a9643a295a9ac6decbd4d3e2d4dec9ef83f0be4e80371eb97f81375eecc1cb63":MBEDTLS_ECP_DP_SECP256R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #9 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256R1)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_mod_add_sub:"b9d39cca91551e8259cc60b17604e4b4e73695c3e652c71a74667bffe202849d":"f0caeef038c89b38a8acb5137c9260dc74e088a9b9492f258ebdbfe3eb9ac688":MBEDTLS_ECP_DP_SECP256R1:MBEDTLS_ECP_MOD_SCALAR

# Use the test data "modulus - 1" and "modulus - 2" to ensure the sum overflow case be tested.
ecp_add_sub #10.0 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP384R1)
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_mod_add_sub:"ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972":"ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52971":MBEDTLS_ECP_DP_SECP384R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #10 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP384R1)
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_mod_add_sub:"7ad1f45ae9500ec9c5e2486c44a4a8f69dc8db48e86ec9c6e06f291b2a838af8d5c44a4eb3172062d08f1bb2531d6460":"9da59b74a6c3181c81e220df848b1df78feb994a81167346d4c0dca8b4c9e755cc9c3adcf515a8234da4daeb4f3f8777":MBEDTLS_ECP_DP_SECP384R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #11 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP384R1)
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_mod_add_sub:"e1cf4f589f8e4ce0af29d115ef24bd625dd961e6830b54fa7d28f93435339774bb1e386c4fd5079e681b8f5896838b76":"1b2d19a2beaa14a7ff3fe32a30ffc4eed0a7bd04e85bfcdd0227eeb7b9d7d01f5769da05d205bbfcc8c69069134bccd3":MBEDTLS_ECP_DP_SECP384R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #12 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP384R1)
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_mod_add_sub:"c11e60de1b343f52ea748db9e020307aaeb6db2c3a038a709779ac1f45e9dd320c855fdfa7251af0930cdbd30f0ad2a8":"e5e138e26c4454b90f756132e16dce72f18e859835e1f291d322a7353ead4efe440e2b4fda9c025a22f1a83185b98f5f":MBEDTLS_ECP_DP_SECP384R1:MBEDTLS_ECP_MOD_SCALAR

# Use the test data "modulus - 1" and "modulus - 2" to ensure the sum overflow case be tested.
ecp_add_sub #13.0 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP521R1)
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_mod_add_sub:"01fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386408":"01fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffa51868783bf2f966b7fcc0148f709a5d03bb5c9b8899c47aebb6fb71e91386407":MBEDTLS_ECP_DP_SECP521R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #13 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP521R1)
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_mod_add_sub:"1ba0a76c196067cfdcb11457d9cf45e2fa01d7f4275153924800600571fac3a5b263fdf57cd2c0064975c3747465cc36c270e8a35b10828d569c268a20eb78ac332":"1cb0b0c995e96e6bc4d62b47204007ee4fab105d83e85e951862f0981aebc1b00d92838e766ef9b6bf2d037fe2e20b6a8464174e75a5f834da70569c018eb2b5693":MBEDTLS_ECP_DP_SECP521R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #14 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP521R1)
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_mod_add_sub:"177d1f71575653a45c49390aa51cf5192bbf67da14be11d56ba0b4a2969d8055a9f03f2d71581d8e830112ff0f0948eccaf8877acf26c377c13f719726fd70bdda":"1f5790813e32748dd1db4917fc09f20dbb0dcc93f0e66dfe717c17313394391b6e2e6eacb0f0bb7be72bd6d25009aeb7fa0c4169b148d2f527e72daf0a54ef25c07":MBEDTLS_ECP_DP_SECP521R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #15 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP521R1)
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_mod_add_sub:"164c7f3860895bfa81384ae65e920a63ac1f2b64df6dff07870c9d531ae72a47403063238da1a1fe3f9d6a179fa50f96cd4aff9261aa92c0e6f17ec940639bc2ccd":"1f58ed5d1b7b310b730049dd332a73fa0b26b75196cf87eb8a09b27ec714307c68c425424a1574f1eedf5b0f16cdfdb839424d201e653f53d6883ca1c107ca6e706":MBEDTLS_ECP_DP_SECP521R1:MBEDTLS_ECP_MOD_SCALAR

# Use the test data "modulus - 1" and "modulus - 2" to ensure the sum overflow case be tested.
ecp_add_sub #16.0 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_add_sub:"a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5376":"a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5375":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_COORDINATE

# The following data was generated using python's standard random library,
# initialised with seed(3,2) and random.getrandbits(curve bits). Curve bits are 256,384,512.
ecp_add_sub #16 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_add_sub:"795b929e9a9a80fdea7b5bf55eb561a4216363698b529b4a97b750923ceb3ffd":"781f9c58d6645fa9e8a8529f035efa259b08923d10c67fd994b2b8fda02f34a6":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_add_sub #17 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_add_sub:"8a7d43b578633074b7970386fee29476311624273bfd1d338d0038ec42650644":"3b5f3d86268ecc45dc6bf1e1a399f82a65aa9c8279f248b08cb4a0d7d6225675":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_add_sub #18 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_add_sub:"3e0a813bdc2ae9963d2e49085ef3430ed038db4de38378426d0b944a2863a7f":"af438d297524d6af51e8722c21b609228ce6f2410645d51c6f8da3eabe19f58":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_COORDINATE

# Use the test data "modulus - 1" and "modulus - 2" to ensure the sum overflow case be tested.
ecp_add_sub #19.0 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_add_sub:"a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a6":"a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a5":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_add_sub #19 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_add_sub:"984181177906159644f9794cdd933160d2d5844307f062cec7b317d94d1fe09f":"6d4b9adbebcd1f5ec9c18070b6d13089633a50eee0f9e038eb8f624fb804d820":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #20 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_add_sub:"2257989fef829c88f6ced90a71d2af7293b05a04cd085b71ba6676b3651c5253":"420b0ebe378c74dc7eb0adf422cedafb092fdddf18f2c41c5d92b243e0fd67dd":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #21 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_add_sub:"6bd0638b4d100d8fdaf0105ba06c05a1c76abf436fa84dcaac0ae4e2f729b4c8":"6856e45b95c76ab488bafad959d5450592f3277b62c82185d55ec1a581daad10":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_SCALAR

# Use the test data "modulus - 1" and "modulus - 2" to ensure the sum overflow case be tested.
ecp_add_sub #22.0 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_add_sub:"8cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec52":"8cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec51":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_add_sub #22 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_add_sub:"4a5792b26aba54efa25994fc58aaac8176f7f138456bb11bd997c6f7cb3a88f684b5b4de4abcc4e46bd881fd21334eb0":"454608a5737b6ed79182c3c8e288b16437d02410a675a109bdf84ab55632a44614777e962b56363cf5efd434db045aae":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_add_sub #23 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_add_sub:"439e7fa9987aa6bdd805f5d25e80dfffc2134f15500b2f292f6c48f65d2c29382d6b76db51ed2f1599f8eee797b9580f":"21a4cadebc344f4baf091db491bae46af8abffd606e44edfd0247e4cc5b3b5d31ad8df8e608d9499c98c9e514ce74654":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_add_sub #24 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_add_sub:"7d500f7cbcefd0a747679714b4fab1019bde81635a427c37ead6b3cbade562bc5a58b185775c303c551b7f9da0996d52":"4c736db374d0df35a0c2995f40498cb35e819615f69b31ce0570ceeead0faadaf47076520f81f60c96e1689405adc011":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_COORDINATE

# Use the test data "modulus - 1" and "modulus - 2" to ensure the sum overflow case be tested.
ecp_add_sub #25.0 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_add_sub:"8cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046564":"8cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046563":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #25 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_add_sub:"1aa4b64091b1078e926baeafe79a27e68ab12c32f6f22f41538e504edc52bdcab2d87d5e29c0e596b2109307abd8952c":"846008638daf051b79e4444ed6897d8fc5ab8f2f33dc30a8f1233c76f31b6928298956cfca65f8e9f66ad57e1464134":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #26 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_add_sub:"7b6471e2103ef3c21fdaf62548f2f8ed445fad2a92d3043afcf249f3d4e441c3a20ab57c360c4979a7cf94d7b6bcb64f":"897897da86640cb0051490eaa9b38f203d3221cc4cc576f280d0dfba2bfc7ffd1eeda989becbde017b25f34a035d7017":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #27 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_add_sub:"526ef7026988f4fe5a8181b691406be110d7c25ccf3d0b35815a3d516a91f397bc73a83fd63ed5ba385ac4bda9bf98c":"8a7db67fdc960f12f8d45cb940a230e6201a95cc5762e3571d140ed89cb6c63de9bfec51f06516210da1920569eb8cb4":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_SCALAR

# Use the test data "modulus - 1" and "modulus - 2" to ensure the sum overflow case be tested.
ecp_add_sub #28.0 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_add_sub:"aadd9db8dbe9c48b3fd4e6ae33c9fc07cb308db3b3c9d20ed6639cca703308717d4d9b009bc66842aecda12ae6a380e62881ff2f2d82c68528aa6056583a48f2":"aadd9db8dbe9c48b3fd4e6ae33c9fc07cb308db3b3c9d20ed6639cca703308717d4d9b009bc66842aecda12ae6a380e62881ff2f2d82c68528aa6056583a48f1":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_add_sub #28 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_add_sub:"46150f34caab02c83d4d071b2bda77121e84949cd11a8404e33c37f188ddf9181f49e090328475a738868e9b5a124b1d0fb5d240c846756acfc1d5507a299d74":"4ca44e40943e5a2248d4a701f3d13a7bb243f13dd61005357b5f2ea9ac6cc64e1d76f9d1d80caa4d068508d51f0c6f07da305f2cd76ee016576b7da1060344bf":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_add_sub #29 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_add_sub:"3f8de0e1457a46a7c1a9425a0cc8557466789723dcd06050922631c6a0ec66f37ccce34401ebd454ebb679b4d2d0d09720e469aace595c72e3bf018debf8e3d9":"a2fd39d9615906a78a943011c859e78da6782c0b9abc3e5b75f828935f8eec2c0aff87582db5db0591157d5f1474683acb984da361574803b9191d5cb74e9504":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_add_sub #30 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_add_sub:"125fdb0f50884d442833e1d550de93987d7015fc808aefcf83f18d61160c7c39b674c4f4dabd2a4c08736a21f985732a7b99a1261183c1860cc1e0331fe78154":"6b153e7ab1b20f01f34624556ba6cc6d50a078d8b3effcadc29237ff7f03ca9ea0a0304d5f56ed310d95a7016e7ceb10e2f416a79f781c980b1ed724cd18e1a9":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_COORDINATE

# Use the test data "modulus - 1" and "modulus - 2" to ensure the sum overflow case be tested.
ecp_add_sub #31.0 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_add_sub:"aadd9db8dbe9c48b3fd4e6ae33c9fc07cb308db3b3c9d20ed6639cca70330870553e5c414ca92619418661197fac10471db1d381085ddaddb58796829ca90068":"aadd9db8dbe9c48b3fd4e6ae33c9fc07cb308db3b3c9d20ed6639cca70330870553e5c414ca92619418661197fac10471db1d381085ddaddb58796829ca90067":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #31 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_add_sub:"8da65a44ef3f7a401993edb1bfbc2a588df13f021b538e133d019261b7149706876cfe7c82e63e71904a896fc4758a8dff09f0150948f14b16baa014cc7ab32f":"731323ee13201b6215fa8a36d04d65c3974f6606cc57efacd9a68b4125321dc9703d20db1f69af34524ab0a892ca38f37f961cd3ebdc77a0496be3975f99ac4":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #32 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_add_sub:"3c3a447d80144a61601545c415508f3cf76060ee6b104fc58e7fdffb59ac3e68f052e38f658a2d349975c9765e129a3740bdcb7464cb7c6cf14fc8f2c0e836c4":"2331df8142351e6ec69ae2d6308b24cbe3e255b43df9ba79411171b4da97fa8037a5ae35f56e539311bb4e07ace3ca83c6ff46a4b7ba6c95a5f3b3fa3c1a7547":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #33 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_add_sub:"71bf2f08e9f7f9da70376bad2555e5ee6d966bcd5a91d4c949cc37677d2519b34ac7eb999581b2eb394c3b17ac666bfb292c157fdc0754a6b1d5f0224c3a235":"174907806c5d14842eea9771503c14af0b869300dd771fce2b72143f41483337ef0bfa78e656abc109691290dbcceb43acd62c6ab46977d09f355e742feb67af":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_SCALAR

# Use the test data "modulus - 1" and "modulus - 2" to ensure the sum overflow case be tested.
ecp_add_sub #34.0 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE25519)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_mod_add_sub:"1000000000000000000000000000000014def9dea2f79cd65812631a5cf5d3ec":"1000000000000000000000000000000014def9dea2f79cd65812631a5cf5d3eb":MBEDTLS_ECP_DP_CURVE25519:MBEDTLS_ECP_MOD_SCALAR

# The following data was generated using python's standard random library,
# initialised with seed(4,2) and random.getrandbits(curve bits). Curve bits are 128,254,192,256,448.
ecp_add_sub #34 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE25519)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_mod_add_sub:"01710cf527ac435a7a97c643656412a9b8a1abcd1a6916c74da4f9fc3c6da5d7":"0fd72445ccea71ff4a14876aeaff1a098ca5996666ceab360512bd1311072231":MBEDTLS_ECP_DP_CURVE25519:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #35 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE25519)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_mod_add_sub:"10000000000000000000000000000000110a8010ce80c4b0a4042bb3d4341aad":"1000000000000000000000000000000010a8c61e3184ff27459142deccea2645":MBEDTLS_ECP_DP_CURVE25519:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #36 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE25519)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_mod_add_sub:"0c79d67946d4ac7a5c3902b38963dc6e8534f45738d048ec0f1099c6c3e1b258":"0690526ed6f0b09f165c8ce36e2f24b43000de01b2ed40ed3addccb2c33be0a":MBEDTLS_ECP_DP_CURVE25519:MBEDTLS_ECP_MOD_SCALAR

# Use the test data "modulus - 1" and "modulus - 2" to ensure the sum overflow case be tested.
ecp_add_sub #37.0 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192K1)
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED
ecp_mod_add_sub:"fffffffffffffffffffffffe26f2fc170f69466a74defd8c":"fffffffffffffffffffffffe26f2fc170f69466a74defd8b":MBEDTLS_ECP_DP_SECP192K1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #37 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192K1)
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED
ecp_mod_add_sub:"f5ff0c03bb5d7385de08caa1a08179104a25e4664f5253a0":"f1cfd99216df648647adec26793d0e453f5082492d83a823":MBEDTLS_ECP_DP_SECP192K1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #38 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192K1)
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED
ecp_mod_add_sub:"d8441b5616332aca5f552773e14b0190d93936e1daca3c06":"d7288ff68c320f89f1347e0cdd905ecfd160c5d0ef412ed6":MBEDTLS_ECP_DP_SECP192K1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #39 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192K1)
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED
ecp_mod_add_sub:"3fb62d2c81862fc9634f806fabf4a07c566002249b191bf4":"b474c7e89286a1754abcb06ae8abb93f01d89a024cdce7a6":MBEDTLS_ECP_DP_SECP192K1:MBEDTLS_ECP_MOD_SCALAR

# Use the test data "modulus - 1" and "modulus - 2" to ensure the sum overflow case be tested.
ecp_add_sub #40.0 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_add_sub:"10000000000000000000000000001dce8d2ec6184caf0a971769fb1f6":"10000000000000000000000000001dce8d2ec6184caf0a971769fb1f5":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #40 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_add_sub:"069f85e3131f3b9238224b122c3e4a892d9196ada4fcfa583e1df8af9":"0a5e333cb88dcf94384d4cd1f47ca7883ff5a52f1a05885ac7671863c":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #41 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_add_sub:"03bb4a570294c4ea3738d243a6e58d5ca49c7b59b995253fd6c79a3de":"032111ac1ac7cc4a4ff4dab102522d53857c49391b36cc9aa78a330a1":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #42 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_add_sub:"00bdbc23a14c15c910b11ad28cc21ce88d0060cc54278c2614e1bcb38":"070ef55b1a1f65507a2909cb633e238b4e9dd38b869ace91311021c9e":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #42.1 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_add_sub:"100000000000000000000000000006f985b17b9662f0733c846bbe9e8":"10000000000000000000000000000a26a52175b7a96b98b5fbf37a2be":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_SCALAR

# Use the test data "modulus - 1" and "modulus - 2" to ensure the sum overflow case be tested.
ecp_add_sub #43.0 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256K1)
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecp_mod_add_sub:"fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140":"fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd036413f":MBEDTLS_ECP_DP_SECP256K1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #43 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256K1)
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecp_mod_add_sub:"5b69dc230af5ac870692b534758240df4a7a03052d733dcdef40af2e54c0ce68":"acdac615bc20f6264922b9ccf469aef8f6e7d078e55b85dd1525f363b281b888":MBEDTLS_ECP_DP_SECP256K1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #44 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256K1)
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecp_mod_add_sub:"b54a23020fc5b043d6e4a51519d9c9cc52d32377e78131c132decd6b8efbc170":"272515cdf74c381652595daf49fbac3652a3b18104a7f00753be4721f5b9e1f5":MBEDTLS_ECP_DP_SECP256K1:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #45 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256K1)
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecp_mod_add_sub:"1f44ebd13cc75f3edcb285f89d8cf4d4950b16ffc3e1ac3b4708d9893a973000":"ae17584a9ed9c621de97faf0f17ca82cdc82f2526911c9dda6e46653c676176a":MBEDTLS_ECP_DP_SECP256K1:MBEDTLS_ECP_MOD_SCALAR

# Use the test data "modulus - 1" and "modulus - 2" to ensure the sum overflow case be tested.
ecp_add_sub #46.0 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE448)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_mod_add_sub:"00000000000000003fffffffffffffffffffffffffffffffffffffffffffffffffffffff7cca23e9c44edb49aed63690216cc2728dc58f552378c292ab5844f2":"00000000000000003fffffffffffffffffffffffffffffffffffffffffffffffffffffff7cca23e9c44edb49aed63690216cc2728dc58f552378c292ab5844f1":MBEDTLS_ECP_DP_CURVE448:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #46 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE448)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_mod_add_sub:"0000000000000003f28adf9f6396ae3994b971761b2ceba40031ad622ed93874ac034cf71b34e47e4e2aafd310096249e2387a54b1cef3913e7d611d163b764":"0000000000000003f924aec4a53583bff4788955cdb7f4ccde9d231c8a38e7b5d7d255f2b68beef746ccfcd0b77d43a5d02db430267ce8c92b607d554d08ce6":MBEDTLS_ECP_DP_CURVE448:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #47 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE448)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_mod_add_sub:"0000000000000003f9874f82b2df98dbcb3fd500e2637300fecf10e0f30e0051d1615ad353a09cfeaa1b2956c8826ec350d775dfb53e13d7077b81d18dbb0c1":"0000000000000003fd5b8c21f4d4cc5091b5ffbff651b9052496e1e3fc24ec0952989c17d9c649a8bd5bb710a77ec0c9b44baf5264ed787f87a7976ad448abd":MBEDTLS_ECP_DP_CURVE448:MBEDTLS_ECP_MOD_SCALAR

ecp_add_sub #48 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE448)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_mod_add_sub:"0000000000000003f7defb1691e8e3b705620733deaaddd33a760e17a4e9ba333445533fcd71d42a6d00e3468c946b0ff353728c6173d944afbfae4877c606f":"0000000000000003f96c1d081a3cfe300dc4c27fa2ebbc37396957d4bf81156d86b88de3a9312ca5be57d93fa3549b71895aa36bd5231f38146a2f0970425b":MBEDTLS_ECP_DP_CURVE448:MBEDTLS_ECP_MOD_SCALAR

# The following data was generated using python's standard random library,
# initialised with seed(6,2) and random.getrandbits(curve bits). Curve bits are 192,224,256,384,520.
# They must be less than the named curves' modulus. mbedtls_mpi_mod_residue_setup()
# can be used to check whether they satisfy the requirements.
ecp_read_write #1 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192R1)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_mod_read_write:"c320a4737c2b3abe14a03569d26b949692e5dfe8cb1855fe":MBEDTLS_ECP_DP_SECP192R1:MBEDTLS_ECP_MOD_SCALAR

ecp_read_write #2 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192R1)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_mod_read_write:"9623d7cfa9ae7a34254499c7001d9a88096d373742f9a039":MBEDTLS_ECP_DP_SECP192R1:MBEDTLS_ECP_MOD_SCALAR

ecp_read_write #3 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224R1)
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecp_mod_read_write:"df5ca32ebad5ccc232b7228fcd4a55577d24b39645cf8aa4059a91e1":MBEDTLS_ECP_DP_SECP224R1:MBEDTLS_ECP_MOD_SCALAR

ecp_read_write #4 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224R1)
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecp_mod_read_write:"c527e27951c342505f877031bc1e3ac1c27db4ecf72c2c2678629522":MBEDTLS_ECP_DP_SECP224R1:MBEDTLS_ECP_MOD_SCALAR

ecp_read_write #7 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256R1)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_mod_read_write:"903c2ac9316774fe181e290aae9af1698a0c510089ce5ef7e91b4ad169fc5360":MBEDTLS_ECP_DP_SECP256R1:MBEDTLS_ECP_MOD_SCALAR

ecp_read_write #8 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256R1)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_mod_read_write:"9c2c0ac2cda95957a9b3d1a243f9300cba98666ace1c9c17b313fc7e8db9b92c":MBEDTLS_ECP_DP_SECP256R1:MBEDTLS_ECP_MOD_SCALAR

ecp_read_write #10 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP384R1)
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_mod_read_write:"401fe4fcce06294d68f22599ccdf540b5cb53ec017d7ab26fd80206055e8b3eb6cb9185ed822e2f9168e5087af895f5b":MBEDTLS_ECP_DP_SECP384R1:MBEDTLS_ECP_MOD_SCALAR

ecp_read_write #11 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP384R1)
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_mod_read_write:"bb1e330f38d2e6418f918e24a8b0188cbe19514a28a0aaab3642b1932793637c16cf5c51801fd9ab31a5bf371f970cf":MBEDTLS_ECP_DP_SECP384R1:MBEDTLS_ECP_MOD_SCALAR

ecp_read_write #13 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP521R1)
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_mod_read_write:"1477156c589f498b61beb35f57662410d8821f3a1ee4a5968a8009618dbe4afda408809822eb0e994fbf9da1659c1ea21b151db97cd1f1567fa4b9327967e0aa591":MBEDTLS_ECP_DP_SECP521R1:MBEDTLS_ECP_MOD_SCALAR

ecp_read_write #14 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP521R1)
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_mod_read_write:"158dd0fdd801513590d221009f2b6c212f2b30214cc3b1f80aaf9142dc9f328c8e2b0af83e1acdb102d85f287d77188c2b8e7911cf9452f5014966f28da330e1fa6":MBEDTLS_ECP_DP_SECP521R1:MBEDTLS_ECP_MOD_SCALAR

# The following data was generated using python's standard random library,
# initialised with seed(7,2) and random.getrandbits(curve bits). Curve bits are 256,384,512.
ecp_read_write #15 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_read_write:"6b4cb2424a23d5962217beaddbc496cb8e81973e0becd7b03898d190f9ebdacc":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_read_write #16 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_read_write:"36f675cc81e74ef5e8e25d940ed904759531985d5d9dc9f81818e811892f902b":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_read_write #17 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_read_write:"8d116ece1738f7d93d9c172411e20b8f6b0d549b6f03675a1600a35a099950d8":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_SCALAR

ecp_read_write #18 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_read_write:"a170b33839263059f28c105d1fb17c2390c192cfd3ac94af0f21ddb66cad4a26":MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_SCALAR

ecp_read_write #19 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_read_write:"7f15052434b9b5df9e7769b10f4205b4907a70c31012f037b64ce4228c38fb2918f135d25f557203301850c5a38fd547":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_read_write #20 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_read_write:"3f98e2774cbd87ad5c90a9587403e430ec66a78795e761d17731af10506bf2efc6f877186d76b07e881ed162ae2eb154":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_read_write #21 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_read_write:"57ee05cde00902c77ebff206867347214cdd2055930d6eaf14f4733f3e7d1bfbc7a2ea20b2f14c942e05319acb5c7427":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_SCALAR

ecp_read_write #22 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_read_write:"5790f82ec1d3fcff2a3af4d46b0a18e8830e07bc1e398f1012bd4acefaecbd389be4bcfc49b64a0872e6cc3ababced20":MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_SCALAR

ecp_read_write #23 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_read_write:"57ee05cde00902c77ebff206867347214cdd2055930d6eaf14f4733f3e7d1bfbc7a2ea20b2f14c942e05319acb5c74273f98e2774cbd87ad5c90a9587403e430":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_read_write #24 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_read_write:"6bf46c697d2caf82eeeacbe226e875555790f82ec1d3fcff2a3af4d46b0a18e8830e07bc1e398f1012bd4acefaecbd389be4bcfc49b64a0872e6cc3ababced20":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_read_write #25 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_read_write:"7f26144b98289fcd59a54a7bb1fee08f571242425051c1ccd17f9acae01f5057ca02135e92b1d3f28ede0d7ac3baea9e13deef86ab1031d0f646e1f40a097c97":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_SCALAR

ecp_read_write #26 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_read_write:"4f426dcbb394fb36bb2d420f0f88080b10a3d6b2aa05e11ab2715945795e8229451abd81f1d69ed617f5e837d70820fe119a72d174c9df6acc011cdd9474031b":MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_SCALAR

# The following data was generated using python's standard random library,
# initialised with seed(8,2) and random.getrandbits(252).
ecp_read_write #27 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE25519)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_mod_read_write:"00b3510bb46ee1da317017a6205738d16018366cf658f7a75ed34fe53a096533":MBEDTLS_ECP_DP_CURVE25519:MBEDTLS_ECP_MOD_SCALAR

# The following data was generated using python's standard random library random.getrandbits(252).
ecp_read_write #28 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE25519)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_mod_read_write:"06694f22359b154881a0d5b3ffc6e35ccfaf00103f584ad4230824d215ceb3a1":MBEDTLS_ECP_DP_CURVE25519:MBEDTLS_ECP_MOD_SCALAR

# The least 122 bits were generated by random.getrandbits(122)
ecp_read_write #29 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE25519)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_mod_read_write:"1000000000000000000000000000000011f319877589ca4a07c15471a4517d6c":MBEDTLS_ECP_DP_CURVE25519:MBEDTLS_ECP_MOD_SCALAR

# The following data was generated by random.getrandbits(192).
ecp_read_write #30 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192K1)
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED
ecp_mod_read_write:"e5b8063831360a4092b850ad7eb72f8263f65da874007cb4":MBEDTLS_ECP_DP_SECP192K1:MBEDTLS_ECP_MOD_SCALAR

ecp_read_write #31 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192K1)
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED
ecp_mod_read_write:"c24f6aa83bf36a147c2f7ad016edc5d467164890d49d0ac1":MBEDTLS_ECP_DP_SECP192K1:MBEDTLS_ECP_MOD_SCALAR

# The following data was generated by random.getrandbits(224).
ecp_read_write #32 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_read_write:"0e6edaf80796d3bc4685ca8af852a5fba444adf42b37f5722051e2670":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_SCALAR

ecp_read_write #33 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_read_write:"018dff3934223aa56a9b7e3ea1d1d784fb9db434b610b1631e941aa79":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_SCALAR

# The least 112 bits were generated by random.getrandbits(112)
ecp_read_write #34 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_read_write:"1000000000000000000000000000162e910269470d0718c1afdd9a78d":MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_SCALAR

# The following data was generated by random.getrandbits(256).
ecp_read_write #35 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256K1)
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecp_mod_read_write:"3c02e56756a3e9570edca4eca92d04a31b941f4360908405d45c39a39ec353c1":MBEDTLS_ECP_DP_SECP256K1:MBEDTLS_ECP_MOD_SCALAR

ecp_read_write #36 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256K1)
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecp_mod_read_write:"353a0106e6c08269844dbc0ca65423a9e744b24e7f61701e1607b1c4b0f91306":MBEDTLS_ECP_DP_SECP256K1:MBEDTLS_ECP_MOD_SCALAR

# The least 440 bits were generated by random.getrandbits(440)
ecp_read_write #37 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE448)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_mod_read_write:"000000000000003fdc3d71f22ff5fd25f0f21231a06a7cb3aa75ab7d1944ff09974b85f2306d4a8a2ad16e107ac8069b51c6322463278ecef2d30194df943c":MBEDTLS_ECP_DP_CURVE448:MBEDTLS_ECP_MOD_SCALAR

ecp_read_write #38 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE448)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_mod_read_write:"000000000000003f9fbd8780ed55037ea03260d7ef27bba4d70dfcf3332eb05b6659eab3bfcd5d50545214b0afb81e8824918818fd64f799ef936ac3a8db56":MBEDTLS_ECP_DP_CURVE448:MBEDTLS_ECP_MOD_SCALAR

ecp_random #1 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP192R1)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_SECP192R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_random #2 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192R1)
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_SECP192R1:MBEDTLS_ECP_MOD_SCALAR

ecp_random #3 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP224R1)
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_SECP224R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_random #4 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224R1)
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_SECP224R1:MBEDTLS_ECP_MOD_SCALAR

ecp_random #5 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP256R1)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_SECP256R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_random #6 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256R1)
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_SECP256R1:MBEDTLS_ECP_MOD_SCALAR

ecp_random #7 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP384R1)
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_SECP384R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_random #8 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP384R1)
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_SECP384R1:MBEDTLS_ECP_MOD_SCALAR

ecp_random #9 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP521R1)
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_SECP521R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_random #10 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP521R1)
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_SECP521R1:MBEDTLS_ECP_MOD_SCALAR

ecp_random #11 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_random #12 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP256R1)
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_BP256R1:MBEDTLS_ECP_MOD_SCALAR

ecp_random #13 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_random #14 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP384R1)
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_BP384R1:MBEDTLS_ECP_MOD_SCALAR

ecp_random #15 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_COORDINATE

ecp_random #16 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_BP512R1)
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_BP512R1:MBEDTLS_ECP_MOD_SCALAR

ecp_random #17 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_CURVE25519)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_CURVE25519:MBEDTLS_ECP_MOD_COORDINATE

ecp_random #18 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_CURVE25519)
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_CURVE25519:MBEDTLS_ECP_MOD_SCALAR

ecp_random #19 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP192K1)
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_SECP192K1:MBEDTLS_ECP_MOD_COORDINATE

ecp_random #20 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP192K1)
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_SECP192K1:MBEDTLS_ECP_MOD_SCALAR

ecp_random #21 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_COORDINATE

ecp_random #22 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP224K1)
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_SECP224K1:MBEDTLS_ECP_MOD_SCALAR

ecp_random #23 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_SECP256K1)
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_SECP256K1:MBEDTLS_ECP_MOD_COORDINATE

ecp_random #24 MBEDTLS_ECP_MOD_SCALAR(MBEDTLS_ECP_DP_SECP256K1)
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_SECP256K1:MBEDTLS_ECP_MOD_SCALAR

ecp_random #25 MBEDTLS_ECP_MOD_COORDINATE(MBEDTLS_ECP_DP_CURVE448)
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED
ecp_mod_random:MBEDTLS_ECP_DP_CURVE448:MBEDTLS_ECP_MOD_COORDINATE

ecp variant check
check_variant:
