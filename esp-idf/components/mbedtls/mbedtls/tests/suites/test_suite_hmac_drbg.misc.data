HMAC_DRBG entropy usage SHA-1
depends_on:MBEDTLS_MD_CAN_SHA1
hmac_drbg_entropy_usage:MBEDTLS_MD_SHA1

HMAC_DRBG entropy usage SHA-224
depends_on:MBEDTLS_MD_CAN_SHA224
hmac_drbg_entropy_usage:MBEDTLS_MD_SHA224

HMAC_DRBG entropy usage SHA-256
depends_on:MBEDTLS_MD_CAN_SHA256
hmac_drbg_entropy_usage:MBEDTLS_MD_SHA256

HMAC_DRBG entropy usage SHA-384
depends_on:MBEDTLS_MD_CAN_SHA384
hmac_drbg_entropy_usage:MBEDTLS_MD_SHA384

HMAC_DRBG entropy usage SHA-512
depends_on:MBEDTLS_MD_CAN_SHA512
hmac_drbg_entropy_usage:MBEDTLS_MD_SHA512

HMAC_DRBG entropy usage SHA3-224
depends_on:MBEDTLS_MD_CAN_SHA3_224
hmac_drbg_entropy_usage:MBEDTLS_MD_SHA3_224

HMAC_DRBG entropy usage SHA3-256
depends_on:MBEDTLS_MD_CAN_SHA3_256
hmac_drbg_entropy_usage:MBEDTLS_MD_SHA3_256

HMAC_DRBG entropy usage SHA3-384
depends_on:MBEDTLS_MD_CAN_SHA3_384
hmac_drbg_entropy_usage:MBEDTLS_MD_SHA3_384

HMAC_DRBG entropy usage SHA3-512
depends_on:MBEDTLS_MD_CAN_SHA3_512
hmac_drbg_entropy_usage:MBEDTLS_MD_SHA3_512

HMAC_DRBG write/update seed file SHA-1 [#1]
depends_on:MBEDTLS_MD_CAN_SHA1
hmac_drbg_seed_file:MBEDTLS_MD_SHA1:"../framework/data_files/hmac_drbg_seed":0

HMAC_DRBG write/update seed file SHA-1 [#2]
depends_on:MBEDTLS_MD_CAN_SHA1
hmac_drbg_seed_file:MBEDTLS_MD_SHA1:"no_such_dir/file":MBEDTLS_ERR_HMAC_DRBG_FILE_IO_ERROR

HMAC_DRBG write/update seed file SHA-224 [#1]
depends_on:MBEDTLS_MD_CAN_SHA224
hmac_drbg_seed_file:MBEDTLS_MD_SHA224:"../framework/data_files/hmac_drbg_seed":0

HMAC_DRBG write/update seed file SHA-224 [#2]
depends_on:MBEDTLS_MD_CAN_SHA224
hmac_drbg_seed_file:MBEDTLS_MD_SHA224:"no_such_dir/file":MBEDTLS_ERR_HMAC_DRBG_FILE_IO_ERROR

HMAC_DRBG write/update seed file SHA-256 [#1]
depends_on:MBEDTLS_MD_CAN_SHA256
hmac_drbg_seed_file:MBEDTLS_MD_SHA256:"../framework/data_files/hmac_drbg_seed":0

HMAC_DRBG write/update seed file SHA-256 [#2]
depends_on:MBEDTLS_MD_CAN_SHA256
hmac_drbg_seed_file:MBEDTLS_MD_SHA256:"no_such_dir/file":MBEDTLS_ERR_HMAC_DRBG_FILE_IO_ERROR

HMAC_DRBG write/update seed file SHA-384 [#1]
depends_on:MBEDTLS_MD_CAN_SHA384
hmac_drbg_seed_file:MBEDTLS_MD_SHA384:"../framework/data_files/hmac_drbg_seed":0

HMAC_DRBG write/update seed file SHA-384 [#2]
depends_on:MBEDTLS_MD_CAN_SHA384
hmac_drbg_seed_file:MBEDTLS_MD_SHA384:"no_such_dir/file":MBEDTLS_ERR_HMAC_DRBG_FILE_IO_ERROR

HMAC_DRBG write/update seed file SHA-512 [#1]
depends_on:MBEDTLS_MD_CAN_SHA512
hmac_drbg_seed_file:MBEDTLS_MD_SHA512:"../framework/data_files/hmac_drbg_seed":0

HMAC_DRBG write/update seed file SHA-512 [#2]
depends_on:MBEDTLS_MD_CAN_SHA512
hmac_drbg_seed_file:MBEDTLS_MD_SHA512:"no_such_dir/file":MBEDTLS_ERR_HMAC_DRBG_FILE_IO_ERROR

HMAC_DRBG write/update seed file SHA3-224 [#1]
depends_on:MBEDTLS_MD_CAN_SHA3_224
hmac_drbg_seed_file:MBEDTLS_MD_SHA3_224:"../framework/data_files/hmac_drbg_seed":0

HMAC_DRBG write/update seed file SHA3-224 [#2]
depends_on:MBEDTLS_MD_CAN_SHA3_224
hmac_drbg_seed_file:MBEDTLS_MD_SHA3_224:"no_such_dir/file":MBEDTLS_ERR_HMAC_DRBG_FILE_IO_ERROR

HMAC_DRBG write/update seed file SHA3-256 [#1]
depends_on:MBEDTLS_MD_CAN_SHA3_256
hmac_drbg_seed_file:MBEDTLS_MD_SHA3_256:"../framework/data_files/hmac_drbg_seed":0

HMAC_DRBG write/update seed file SHA3-256 [#2]
depends_on:MBEDTLS_MD_CAN_SHA3_256
hmac_drbg_seed_file:MBEDTLS_MD_SHA3_256:"no_such_dir/file":MBEDTLS_ERR_HMAC_DRBG_FILE_IO_ERROR

HMAC_DRBG write/update seed file SHA3-384 [#1]
depends_on:MBEDTLS_MD_CAN_SHA3_384
hmac_drbg_seed_file:MBEDTLS_MD_SHA3_384:"../framework/data_files/hmac_drbg_seed":0

HMAC_DRBG write/update seed file SHA3-384 [#2]
depends_on:MBEDTLS_MD_CAN_SHA3_384
hmac_drbg_seed_file:MBEDTLS_MD_SHA3_384:"no_such_dir/file":MBEDTLS_ERR_HMAC_DRBG_FILE_IO_ERROR

HMAC_DRBG write/update seed file SHA3-512 [#1]
depends_on:MBEDTLS_MD_CAN_SHA3_512
hmac_drbg_seed_file:MBEDTLS_MD_SHA3_512:"../framework/data_files/hmac_drbg_seed":0

HMAC_DRBG write/update seed file SHA3-512 [#2]
depends_on:MBEDTLS_MD_CAN_SHA3_512
hmac_drbg_seed_file:MBEDTLS_MD_SHA3_512:"no_such_dir/file":MBEDTLS_ERR_HMAC_DRBG_FILE_IO_ERROR

HMAC_DRBG from buffer SHA-1
depends_on:MBEDTLS_MD_CAN_SHA1
hmac_drbg_buf:MBEDTLS_MD_SHA1

HMAC_DRBG from buffer SHA-224
depends_on:MBEDTLS_MD_CAN_SHA224
hmac_drbg_buf:MBEDTLS_MD_SHA224

HMAC_DRBG from buffer SHA-256
depends_on:MBEDTLS_MD_CAN_SHA256
hmac_drbg_buf:MBEDTLS_MD_SHA256

HMAC_DRBG from buffer SHA-384
depends_on:MBEDTLS_MD_CAN_SHA384
hmac_drbg_buf:MBEDTLS_MD_SHA384

HMAC_DRBG from buffer SHA-512
depends_on:MBEDTLS_MD_CAN_SHA512
hmac_drbg_buf:MBEDTLS_MD_SHA512

HMAC_DRBG from buffer SHA3-224
depends_on:MBEDTLS_MD_CAN_SHA3_224
hmac_drbg_buf:MBEDTLS_MD_SHA3_224

HMAC_DRBG from buffer SHA3-256
depends_on:MBEDTLS_MD_CAN_SHA3_256
hmac_drbg_buf:MBEDTLS_MD_SHA3_256

HMAC_DRBG from buffer SHA3-384
depends_on:MBEDTLS_MD_CAN_SHA3_384
hmac_drbg_buf:MBEDTLS_MD_SHA3_384

HMAC_DRBG from buffer SHA3-512
depends_on:MBEDTLS_MD_CAN_SHA3_512
hmac_drbg_buf:MBEDTLS_MD_SHA3_512

HMAC_DRBG self test
hmac_drbg_selftest:
