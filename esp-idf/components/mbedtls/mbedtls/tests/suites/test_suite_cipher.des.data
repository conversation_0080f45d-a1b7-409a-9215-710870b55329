DES CBC Decrypt empty buffer
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
dec_empty_buf:MBEDTLS_CIPHER_DES_CBC:0:0

DES EDE CBC Decrypt empty buffer
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
dec_empty_buf:MBEDTLS_CIPHER_DES_EDE_CBC:0:0

DES EDE3 CBC Decrypt empty buffer
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
dec_empty_buf:MBEDTLS_CIPHER_DES_EDE3_CBC:0:0

DES Encrypt and decrypt 0 bytes [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:0:MBEDTLS_PADDING_PKCS7

DES Encrypt and decrypt 1 byte
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:1:MBEDTLS_PADDING_PKCS7

DES Encrypt and decrypt 2 bytes
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:2:MBEDTLS_PADDING_PKCS7

DES Encrypt and decrypt 7 bytes
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:7:MBEDTLS_PADDING_PKCS7

DES Encrypt and decrypt 8 bytes
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:8:MBEDTLS_PADDING_PKCS7

DES Encrypt and decrypt 9 bytes
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:9:MBEDTLS_PADDING_PKCS7

DES Encrypt and decrypt 15 bytes
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:15:MBEDTLS_PADDING_PKCS7

DES Encrypt and decrypt 16 bytes
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:16:MBEDTLS_PADDING_PKCS7

DES Encrypt and decrypt 17 bytes
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:17:MBEDTLS_PADDING_PKCS7

DES Encrypt and decrypt 31 bytes
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:31:MBEDTLS_PADDING_PKCS7

DES Encrypt and decrypt 32 bytes [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:32:MBEDTLS_PADDING_PKCS7

DES Encrypt and decrypt 33 bytes [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:33:MBEDTLS_PADDING_PKCS7

DES Encrypt and decrypt 47 bytes
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:47:MBEDTLS_PADDING_PKCS7

DES Encrypt and decrypt 48 bytes
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:48:MBEDTLS_PADDING_PKCS7

DES Encrypt and decrypt 49 bytes
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:49:MBEDTLS_PADDING_PKCS7

DES Encrypt and decrypt 0 bytes with one and zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:0:MBEDTLS_PADDING_ONE_AND_ZEROS

DES Encrypt and decrypt 1 byte with one and zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:1:MBEDTLS_PADDING_ONE_AND_ZEROS

DES Encrypt and decrypt 2 bytes with one and zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:2:MBEDTLS_PADDING_ONE_AND_ZEROS

DES Encrypt and decrypt 7 bytes with one and zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:7:MBEDTLS_PADDING_ONE_AND_ZEROS

DES Encrypt and decrypt 8 bytes with one and zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:8:MBEDTLS_PADDING_ONE_AND_ZEROS

DES Encrypt and decrypt 9 bytes with one and zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:9:MBEDTLS_PADDING_ONE_AND_ZEROS

DES Encrypt and decrypt 15 bytes with one and zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:15:MBEDTLS_PADDING_ONE_AND_ZEROS

DES Encrypt and decrypt 16 bytes with one and zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:16:MBEDTLS_PADDING_ONE_AND_ZEROS

DES Encrypt and decrypt 17 bytes with one and zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:17:MBEDTLS_PADDING_ONE_AND_ZEROS

DES Encrypt and decrypt 31 bytes with one and zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:31:MBEDTLS_PADDING_ONE_AND_ZEROS

DES Encrypt and decrypt 32 bytes with one and zeros padding [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:32:MBEDTLS_PADDING_ONE_AND_ZEROS

DES Encrypt and decrypt 32 bytes with one and zeros padding [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:33:MBEDTLS_PADDING_ONE_AND_ZEROS

DES Encrypt and decrypt 47 bytes with one and zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:47:MBEDTLS_PADDING_ONE_AND_ZEROS

DES Encrypt and decrypt 48 bytes with one and zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:48:MBEDTLS_PADDING_ONE_AND_ZEROS

DES Encrypt and decrypt 49 bytes with one and zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:49:MBEDTLS_PADDING_ONE_AND_ZEROS

DES Encrypt and decrypt 0 bytes with zeros and len padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:0:MBEDTLS_PADDING_ZEROS_AND_LEN

DES Encrypt and decrypt 1 byte with zeros and len padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:1:MBEDTLS_PADDING_ZEROS_AND_LEN

DES Encrypt and decrypt 2 bytes with zeros and len padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:2:MBEDTLS_PADDING_ZEROS_AND_LEN

DES Encrypt and decrypt 7 bytes with zeros and len padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:7:MBEDTLS_PADDING_ZEROS_AND_LEN

DES Encrypt and decrypt 8 bytes with zeros and len padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:8:MBEDTLS_PADDING_ZEROS_AND_LEN

DES Encrypt and decrypt 9 bytes with zeros and len padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:9:MBEDTLS_PADDING_ZEROS_AND_LEN

DES Encrypt and decrypt 15 bytes with zeros and len padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:15:MBEDTLS_PADDING_ZEROS_AND_LEN

DES Encrypt and decrypt 16 bytes with zeros and len padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:16:MBEDTLS_PADDING_ZEROS_AND_LEN

DES Encrypt and decrypt 17 bytes with zeros and len padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:17:MBEDTLS_PADDING_ZEROS_AND_LEN

DES Encrypt and decrypt 31 bytes with zeros and len padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:31:MBEDTLS_PADDING_ZEROS_AND_LEN

DES Encrypt and decrypt 32 bytes with zeros and len padding [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:32:MBEDTLS_PADDING_ZEROS_AND_LEN

DES Encrypt and decrypt 32 bytes with zeros and len padding [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:33:MBEDTLS_PADDING_ZEROS_AND_LEN

DES Encrypt and decrypt 47 bytes with zeros and len padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:47:MBEDTLS_PADDING_ZEROS_AND_LEN

DES Encrypt and decrypt 48 bytes with zeros and len padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:48:MBEDTLS_PADDING_ZEROS_AND_LEN

DES Encrypt and decrypt 49 bytes with zeros and len padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:49:MBEDTLS_PADDING_ZEROS_AND_LEN

DES Encrypt and decrypt 0 bytes with zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:0:MBEDTLS_PADDING_ZEROS

DES Encrypt and decrypt 1 byte with zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:1:MBEDTLS_PADDING_ZEROS

DES Encrypt and decrypt 2 bytes with zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:2:MBEDTLS_PADDING_ZEROS

DES Encrypt and decrypt 7 bytes with zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:7:MBEDTLS_PADDING_ZEROS

DES Encrypt and decrypt 8 bytes with zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:8:MBEDTLS_PADDING_ZEROS

DES Encrypt and decrypt 9 bytes with zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:9:MBEDTLS_PADDING_ZEROS

DES Encrypt and decrypt 15 bytes with zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:15:MBEDTLS_PADDING_ZEROS

DES Encrypt and decrypt 16 bytes with zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:16:MBEDTLS_PADDING_ZEROS

DES Encrypt and decrypt 17 bytes with zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:17:MBEDTLS_PADDING_ZEROS

DES Encrypt and decrypt 31 bytes with zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:31:MBEDTLS_PADDING_ZEROS

DES Encrypt and decrypt 32 bytes with zeros padding [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:32:MBEDTLS_PADDING_ZEROS

DES Encrypt and decrypt 32 bytes with zeros padding [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:33:MBEDTLS_PADDING_ZEROS

DES Encrypt and decrypt 47 bytes with zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:47:MBEDTLS_PADDING_ZEROS

DES Encrypt and decrypt 48 bytes with zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:48:MBEDTLS_PADDING_ZEROS

DES Encrypt and decrypt 49 bytes with zeros padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:49:MBEDTLS_PADDING_ZEROS

DES Encrypt and decrypt 0 bytes with no padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:0:MBEDTLS_PADDING_NONE

DES Encrypt and decrypt 8 bytes with no padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:8:MBEDTLS_PADDING_NONE

DES Encrypt and decrypt 16 bytes with no padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:16:MBEDTLS_PADDING_NONE

DES Encrypt and decrypt 32 bytes with no padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:32:MBEDTLS_PADDING_NONE

DES Encrypt and decrypt 48 bytes with no padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_DES_CBC:"DES-CBC":64:48:MBEDTLS_PADDING_NONE

DES Try encrypting 1 bytes with no padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_fail:MBEDTLS_CIPHER_DES_CBC:MBEDTLS_PADDING_NONE:64:1:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

DES Try encrypting 2 bytes with no padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_fail:MBEDTLS_CIPHER_DES_CBC:MBEDTLS_PADDING_NONE:64:2:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

DES Try encrypting 7 bytes with no padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_fail:MBEDTLS_CIPHER_DES_CBC:MBEDTLS_PADDING_NONE:64:7:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

DES Try encrypting 9 bytes with no padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_fail:MBEDTLS_CIPHER_DES_CBC:MBEDTLS_PADDING_NONE:64:9:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

DES Try encrypting 15 bytes with no padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_fail:MBEDTLS_CIPHER_DES_CBC:MBEDTLS_PADDING_NONE:64:15:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

DES Try encrypting 17 bytes with no padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_fail:MBEDTLS_CIPHER_DES_CBC:MBEDTLS_PADDING_NONE:64:17:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

DES Try encrypting 31 bytes with no padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_fail:MBEDTLS_CIPHER_DES_CBC:MBEDTLS_PADDING_NONE:64:31:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

DES Try encrypting 33 bytes with no padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_fail:MBEDTLS_CIPHER_DES_CBC:MBEDTLS_PADDING_NONE:64:33:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

DES Try encrypting 47 bytes with no padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_fail:MBEDTLS_CIPHER_DES_CBC:MBEDTLS_PADDING_NONE:64:47:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

DES Try encrypting 49 bytes with no padding
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_fail:MBEDTLS_CIPHER_DES_CBC:MBEDTLS_PADDING_NONE:64:49:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

DES Encrypt and decrypt 0 bytes in multiple parts
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_CBC:64:0:0:MBEDTLS_PADDING_PKCS7:0:0:0:0

DES Encrypt and decrypt 1 bytes in multiple parts 1
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_CBC:64:1:0:MBEDTLS_PADDING_PKCS7:0:0:0:0

DES Encrypt and decrypt 1 bytes in multiple parts 2
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_CBC:64:0:1:MBEDTLS_PADDING_PKCS7:0:0:0:0

DES Encrypt and decrypt 16 bytes in multiple parts 1
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_CBC:64:16:0:MBEDTLS_PADDING_PKCS7:16:0:8:8

DES Encrypt and decrypt 16 bytes in multiple parts 2
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_CBC:64:0:16:MBEDTLS_PADDING_PKCS7:0:16:0:16

DES Encrypt and decrypt 16 bytes in multiple parts 3
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_CBC:64:1:15:MBEDTLS_PADDING_PKCS7:0:16:0:16

DES Encrypt and decrypt 16 bytes in multiple parts 4
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_CBC:64:15:1:MBEDTLS_PADDING_PKCS7:8:8:8:8

DES Encrypt and decrypt 22 bytes in multiple parts 1 [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_CBC:64:15:7:MBEDTLS_PADDING_PKCS7:8:8:8:8

DES Encrypt and decrypt 22 bytes in multiple parts 1 [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_CBC:64:16:6:MBEDTLS_PADDING_PKCS7:16:0:8:8

DES Encrypt and decrypt 22 bytes in multiple parts 1 [#3]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_CBC:64:17:6:MBEDTLS_PADDING_PKCS7:16:0:16:0

DES Encrypt and decrypt 32 bytes in multiple parts 1
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_CBC:64:16:16:MBEDTLS_PADDING_PKCS7:16:16:8:24

DES Encrypt and decrypt 0 bytes [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE_CBC:"DES-EDE-CBC":128:0:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 1 byte [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE_CBC:"DES-EDE-CBC":128:1:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 2 bytes [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE_CBC:"DES-EDE-CBC":128:2:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 7 bytes [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE_CBC:"DES-EDE-CBC":128:7:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 8 bytes [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE_CBC:"DES-EDE-CBC":128:8:MBEDTLS_PADDING_NONE

DES3 Encrypt and decrypt 9 bytes [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE_CBC:"DES-EDE-CBC":128:9:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 15 bytes [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE_CBC:"DES-EDE-CBC":128:15:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 16 bytes [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE_CBC:"DES-EDE-CBC":128:16:MBEDTLS_PADDING_NONE

DES3 Encrypt and decrypt 17 bytes [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE_CBC:"DES-EDE-CBC":128:17:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 31 bytes [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE_CBC:"DES-EDE-CBC":128:31:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 32 bytes [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE_CBC:"DES-EDE-CBC":128:32:MBEDTLS_PADDING_NONE

DES3 Encrypt and decrypt 33 bytes [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE_CBC:"DES-EDE-CBC":128:33:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 47 bytes [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE_CBC:"DES-EDE-CBC":128:47:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 48 bytes [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE_CBC:"DES-EDE-CBC":128:48:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 49 bytes [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE_CBC:"DES-EDE-CBC":128:49:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 0 bytes in multiple parts [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE_CBC:128:0:0:MBEDTLS_PADDING_PKCS7:0:0:0:0

DES3 Encrypt and decrypt 1 bytes in multiple parts 1 [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE_CBC:128:1:0:MBEDTLS_PADDING_PKCS7:0:0:0:0

DES3 Encrypt and decrypt 1 bytes in multiple parts 2 [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE_CBC:128:0:1:MBEDTLS_PADDING_PKCS7:0:0:0:0

DES3 Encrypt and decrypt 16 bytes in multiple parts 1 [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE_CBC:128:16:0:MBEDTLS_PADDING_PKCS7:16:0:8:8

DES3 Encrypt and decrypt 16 bytes in multiple parts 2 [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE_CBC:128:0:16:MBEDTLS_PADDING_PKCS7:0:16:0:16

DES3 Encrypt and decrypt 16 bytes in multiple parts 3 [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE_CBC:128:1:15:MBEDTLS_PADDING_PKCS7:0:16:0:16

DES3 Encrypt and decrypt 16 bytes in multiple parts 4 [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE_CBC:128:15:1:MBEDTLS_PADDING_PKCS7:8:8:8:8

DES3 Encrypt and decrypt 22 bytes in multiple parts 1 [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE_CBC:128:15:7:MBEDTLS_PADDING_PKCS7:8:8:8:8

DES3 Encrypt and decrypt 22 bytes in multiple parts 1 [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE_CBC:128:16:6:MBEDTLS_PADDING_PKCS7:16:0:8:8

DES3 Encrypt and decrypt 22 bytes in multiple parts 1 [#3]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE_CBC:128:17:6:MBEDTLS_PADDING_PKCS7:16:0:16:0

DES3 Encrypt and decrypt 32 bytes in multiple parts 1 [#1]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE_CBC:128:16:16:MBEDTLS_PADDING_PKCS7:16:16:8:24

DES3 Encrypt and decrypt 0 bytes
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE3_CBC:"DES-EDE3-CBC":192:0:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 1 byte [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE3_CBC:"DES-EDE3-CBC":192:1:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 2 bytes [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE3_CBC:"DES-EDE3-CBC":192:2:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 7 bytes [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE3_CBC:"DES-EDE3-CBC":192:7:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 8 bytes [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE3_CBC:"DES-EDE3-CBC":192:8:MBEDTLS_PADDING_NONE

DES3 Encrypt and decrypt 8 bytes (PKCS7 padding) [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE3_CBC:"DES-EDE3-CBC":192:8:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 9 bytes [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE3_CBC:"DES-EDE3-CBC":192:9:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 15 bytes [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE3_CBC:"DES-EDE3-CBC":192:15:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 16 bytes [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE3_CBC:"DES-EDE3-CBC":192:16:MBEDTLS_PADDING_NONE

DES3 Encrypt and decrypt 16 bytes (PKCS7 padding) [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE3_CBC:"DES-EDE3-CBC":192:16:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 17 bytes [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE3_CBC:"DES-EDE3-CBC":192:17:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 31 bytes [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE3_CBC:"DES-EDE3-CBC":192:31:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 32 bytes [#3]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE3_CBC:"DES-EDE3-CBC":192:32:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 32 bytes (PKCS7 padding) [#3]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE3_CBC:"DES-EDE3-CBC":192:32:MBEDTLS_PADDING_NONE

DES3 Encrypt and decrypt 33 bytes [#4]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE3_CBC:"DES-EDE3-CBC":192:33:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 47 bytes [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE3_CBC:"DES-EDE3-CBC":192:47:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 48 bytes [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE3_CBC:"DES-EDE3-CBC":192:48:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 49 bytes [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_DES_EDE3_CBC:"DES-EDE3-CBC":192:49:MBEDTLS_PADDING_PKCS7

DES3 Encrypt and decrypt 0 bytes in multiple parts [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE3_CBC:192:0:0:MBEDTLS_PADDING_PKCS7:0:0:0:0

DES3 Encrypt and decrypt 1 bytes in multiple parts 1 [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE3_CBC:192:1:0:MBEDTLS_PADDING_PKCS7:0:0:0:0

DES3 Encrypt and decrypt 1 bytes in multiple parts 2 [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE3_CBC:192:0:1:MBEDTLS_PADDING_PKCS7:0:0:0:0

DES3 Encrypt and decrypt 16 bytes in multiple parts 1 [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE3_CBC:192:16:0:MBEDTLS_PADDING_PKCS7:16:0:8:8

DES3 Encrypt and decrypt 16 bytes in multiple parts 2 [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE3_CBC:192:0:16:MBEDTLS_PADDING_PKCS7:0:16:0:16

DES3 Encrypt and decrypt 16 bytes in multiple parts 3 [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE3_CBC:192:1:15:MBEDTLS_PADDING_PKCS7:0:16:0:16

DES3 Encrypt and decrypt 16 bytes in multiple parts 4 [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE3_CBC:192:15:1:MBEDTLS_PADDING_PKCS7:8:8:8:8

DES3 Encrypt and decrypt 22 bytes in multiple parts 1 [#4]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE3_CBC:192:15:7:MBEDTLS_PADDING_PKCS7:8:8:8:8

DES3 Encrypt and decrypt 22 bytes in multiple parts 1 [#5]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE3_CBC:192:16:6:MBEDTLS_PADDING_PKCS7:16:0:8:8

DES3 Encrypt and decrypt 22 bytes in multiple parts 1 [#6]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE3_CBC:192:17:6:MBEDTLS_PADDING_PKCS7:16:0:16:0

DES3 Encrypt and decrypt 32 bytes in multiple parts 1 [#2]
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_DES_EDE3_CBC:192:16:16:MBEDTLS_PADDING_PKCS7:16:16:8:24

DES ECB Encrypt test vector (OpenSSL) #1
depends_on:MBEDTLS_DES_C
test_vec_ecb:MBEDTLS_CIPHER_DES_ECB:MBEDTLS_ENCRYPT:"0000000000000000":"0000000000000000":"8CA64DE9C1B123A7":0

DES ECB Encrypt test vector (OpenSSL) #2
depends_on:MBEDTLS_DES_C
test_vec_ecb:MBEDTLS_CIPHER_DES_ECB:MBEDTLS_ENCRYPT:"FFFFFFFFFFFFFFFF":"FFFFFFFFFFFFFFFF":"7359B2163E4EDC58":0

DES ECB Encrypt test vector (OpenSSL) #3
depends_on:MBEDTLS_DES_C
test_vec_ecb:MBEDTLS_CIPHER_DES_ECB:MBEDTLS_ENCRYPT:"FEDCBA9876543210":"0123456789ABCDEF":"ED39D950FA74BCC4":0

DES ECB Decrypt test vector (OpenSSL) #1
depends_on:MBEDTLS_DES_C
test_vec_ecb:MBEDTLS_CIPHER_DES_ECB:MBEDTLS_DECRYPT:"0000000000000000":"8CA64DE9C1B123A7":"0000000000000000":0

DES ECB Decrypt test vector (OpenSSL) #2
depends_on:MBEDTLS_DES_C
test_vec_ecb:MBEDTLS_CIPHER_DES_ECB:MBEDTLS_DECRYPT:"FFFFFFFFFFFFFFFF":"7359B2163E4EDC58":"FFFFFFFFFFFFFFFF":0

DES ECB Decrypt test vector (OpenSSL) #3
depends_on:MBEDTLS_DES_C
test_vec_ecb:MBEDTLS_CIPHER_DES_ECB:MBEDTLS_DECRYPT:"43297FAD38E373FE":"EA676B2CB7DB2B7A":"762514B829BF486A":0

DES3-EDE ECB Encrypt test vector (OpenSSL) #1
depends_on:MBEDTLS_DES_C
test_vec_ecb:MBEDTLS_CIPHER_DES_EDE_ECB:MBEDTLS_ENCRYPT:"0000000000000000FFFFFFFFFFFFFFFF":"0000000000000000":"9295B59BB384736E":0

DES3-EDE ECB Encrypt test vector (OpenSSL) #2
depends_on:MBEDTLS_DES_C
test_vec_ecb:MBEDTLS_CIPHER_DES_EDE_ECB:MBEDTLS_ENCRYPT:"FFFFFFFFFFFFFFFF3000000000000000":"FFFFFFFFFFFFFFFF":"199E9D6DF39AA816":0

DES3-EDE ECB Decrypt test vector (OpenSSL) #1
depends_on:MBEDTLS_DES_C
test_vec_ecb:MBEDTLS_CIPHER_DES_EDE_ECB:MBEDTLS_DECRYPT:"0000000000000000FFFFFFFFFFFFFFFF":"9295B59BB384736E":"0000000000000000":0

DES3-EDE ECB Decrypt test vector (OpenSSL) #2
depends_on:MBEDTLS_DES_C
test_vec_ecb:MBEDTLS_CIPHER_DES_EDE_ECB:MBEDTLS_DECRYPT:"FFFFFFFFFFFFFFFF3000000000000000":"199E9D6DF39AA816":"FFFFFFFFFFFFFFFF":0

Check set padding - DES
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
check_set_padding:MBEDTLS_CIPHER_DES_EDE_CBC

Check set padding - Triple DES
depends_on:MBEDTLS_DES_C:MBEDTLS_CIPHER_MODE_CBC
check_set_padding:MBEDTLS_CIPHER_DES_EDE3_CBC
