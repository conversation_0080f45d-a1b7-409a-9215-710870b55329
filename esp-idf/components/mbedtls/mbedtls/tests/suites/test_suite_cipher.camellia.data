CAMELLIA-128 CBC - Decrypt empty buffer
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
dec_empty_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:0:0

CAMELLIA-256 CBC - Decrypt empty buffer
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
dec_empty_buf:MBEDTLS_CIPHER_CAMELLIA_256_CBC:0:0

CAMELLIA Encrypt and decrypt 0 bytes [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:0:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 1 byte [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:1:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 2 bytes [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:2:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 7 bytes [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:7:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 8 bytes [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:8:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 9 bytes [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:9:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 15 bytes [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:15:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 16 bytes [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:16:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 17 bytes [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:17:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 31 bytes [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:31:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 32 bytes [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:32:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 33 bytes [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:33:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 47 bytes [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:47:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 48 bytes [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:48:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 49 bytes [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:49:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 0 bytes with one and zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:0:MBEDTLS_PADDING_ONE_AND_ZEROS

CAMELLIA Encrypt and decrypt 1 byte with one and zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:1:MBEDTLS_PADDING_ONE_AND_ZEROS

CAMELLIA Encrypt and decrypt 2 bytes with one and zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:2:MBEDTLS_PADDING_ONE_AND_ZEROS

CAMELLIA Encrypt and decrypt 7 bytes with one and zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:7:MBEDTLS_PADDING_ONE_AND_ZEROS

CAMELLIA Encrypt and decrypt 8 bytes with one and zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:8:MBEDTLS_PADDING_ONE_AND_ZEROS

CAMELLIA Encrypt and decrypt 9 bytes with one and zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:9:MBEDTLS_PADDING_ONE_AND_ZEROS

CAMELLIA Encrypt and decrypt 15 bytes with one and zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:15:MBEDTLS_PADDING_ONE_AND_ZEROS

CAMELLIA Encrypt and decrypt 16 bytes with one and zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:16:MBEDTLS_PADDING_ONE_AND_ZEROS

CAMELLIA Encrypt and decrypt 17 bytes with one and zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:17:MBEDTLS_PADDING_ONE_AND_ZEROS

CAMELLIA Encrypt and decrypt 31 bytes with one and zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:31:MBEDTLS_PADDING_ONE_AND_ZEROS

CAMELLIA Encrypt and decrypt 32 bytes with one and zeros padding [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:32:MBEDTLS_PADDING_ONE_AND_ZEROS

CAMELLIA Encrypt and decrypt 32 bytes with one and zeros padding [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:33:MBEDTLS_PADDING_ONE_AND_ZEROS

CAMELLIA Encrypt and decrypt 47 bytes with one and zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:47:MBEDTLS_PADDING_ONE_AND_ZEROS

CAMELLIA Encrypt and decrypt 48 bytes with one and zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:48:MBEDTLS_PADDING_ONE_AND_ZEROS

CAMELLIA Encrypt and decrypt 49 bytes with one and zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:49:MBEDTLS_PADDING_ONE_AND_ZEROS

CAMELLIA Encrypt and decrypt 0 bytes with zeros and len padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:0:MBEDTLS_PADDING_ZEROS_AND_LEN

CAMELLIA Encrypt and decrypt 1 byte with zeros and len padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:1:MBEDTLS_PADDING_ZEROS_AND_LEN

CAMELLIA Encrypt and decrypt 2 bytes with zeros and len padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:2:MBEDTLS_PADDING_ZEROS_AND_LEN

CAMELLIA Encrypt and decrypt 7 bytes with zeros and len padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:7:MBEDTLS_PADDING_ZEROS_AND_LEN

CAMELLIA Encrypt and decrypt 8 bytes with zeros and len padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:8:MBEDTLS_PADDING_ZEROS_AND_LEN

CAMELLIA Encrypt and decrypt 9 bytes with zeros and len padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:9:MBEDTLS_PADDING_ZEROS_AND_LEN

CAMELLIA Encrypt and decrypt 15 bytes with zeros and len padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:15:MBEDTLS_PADDING_ZEROS_AND_LEN

CAMELLIA Encrypt and decrypt 16 bytes with zeros and len padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:16:MBEDTLS_PADDING_ZEROS_AND_LEN

CAMELLIA Encrypt and decrypt 17 bytes with zeros and len padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:17:MBEDTLS_PADDING_ZEROS_AND_LEN

CAMELLIA Encrypt and decrypt 31 bytes with zeros and len padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:31:MBEDTLS_PADDING_ZEROS_AND_LEN

CAMELLIA Encrypt and decrypt 32 bytes with zeros and len padding [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:32:MBEDTLS_PADDING_ZEROS_AND_LEN

CAMELLIA Encrypt and decrypt 32 bytes with zeros and len padding [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:33:MBEDTLS_PADDING_ZEROS_AND_LEN

CAMELLIA Encrypt and decrypt 47 bytes with zeros and len padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:47:MBEDTLS_PADDING_ZEROS_AND_LEN

CAMELLIA Encrypt and decrypt 48 bytes with zeros and len padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:48:MBEDTLS_PADDING_ZEROS_AND_LEN

CAMELLIA Encrypt and decrypt 49 bytes with zeros and len padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:49:MBEDTLS_PADDING_ZEROS_AND_LEN

CAMELLIA Encrypt and decrypt 0 bytes with zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:0:MBEDTLS_PADDING_ZEROS

CAMELLIA Encrypt and decrypt 1 byte with zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:1:MBEDTLS_PADDING_ZEROS

CAMELLIA Encrypt and decrypt 2 bytes with zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:2:MBEDTLS_PADDING_ZEROS

CAMELLIA Encrypt and decrypt 7 bytes with zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:7:MBEDTLS_PADDING_ZEROS

CAMELLIA Encrypt and decrypt 8 bytes with zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:8:MBEDTLS_PADDING_ZEROS

CAMELLIA Encrypt and decrypt 9 bytes with zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:9:MBEDTLS_PADDING_ZEROS

CAMELLIA Encrypt and decrypt 15 bytes with zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:15:MBEDTLS_PADDING_ZEROS

CAMELLIA Encrypt and decrypt 16 bytes with zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:16:MBEDTLS_PADDING_ZEROS

CAMELLIA Encrypt and decrypt 17 bytes with zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:17:MBEDTLS_PADDING_ZEROS

CAMELLIA Encrypt and decrypt 31 bytes with zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:31:MBEDTLS_PADDING_ZEROS

CAMELLIA Encrypt and decrypt 32 bytes with zeros padding [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:32:MBEDTLS_PADDING_ZEROS

CAMELLIA Encrypt and decrypt 32 bytes with zeros padding [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:33:MBEDTLS_PADDING_ZEROS

CAMELLIA Encrypt and decrypt 47 bytes with zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:47:MBEDTLS_PADDING_ZEROS

CAMELLIA Encrypt and decrypt 48 bytes with zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:48:MBEDTLS_PADDING_ZEROS

CAMELLIA Encrypt and decrypt 49 bytes with zeros padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_ZEROS
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:49:MBEDTLS_PADDING_ZEROS

CAMELLIA Encrypt and decrypt 0 bytes with no padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:0:MBEDTLS_PADDING_NONE

CAMELLIA Encrypt and decrypt 16 bytes with no padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:16:MBEDTLS_PADDING_NONE

CAMELLIA Encrypt and decrypt 32 bytes with no padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:32:MBEDTLS_PADDING_NONE

CAMELLIA Encrypt and decrypt 48 bytes with no padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CBC:"CAMELLIA-128-CBC":128:48:MBEDTLS_PADDING_NONE

CAMELLIA Try encrypting 1 bytes with no padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_PADDING_NONE:128:1:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

CAMELLIA Try encrypting 2 bytes with no padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_PADDING_NONE:128:2:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

CAMELLIA Try encrypting 7 bytes with no padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_PADDING_NONE:128:7:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

CAMELLIA Try encrypting 8 bytes with no padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_PADDING_NONE:128:8:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

CAMELLIA Try encrypting 9 bytes with no padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_PADDING_NONE:128:9:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

CAMELLIA Try encrypting 15 bytes with no padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_PADDING_NONE:128:15:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

CAMELLIA Try encrypting 17 bytes with no padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_PADDING_NONE:128:17:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

CAMELLIA Try encrypting 31 bytes with no padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_PADDING_NONE:128:31:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

CAMELLIA Try encrypting 33 bytes with no padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_PADDING_NONE:128:33:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

CAMELLIA Try encrypting 47 bytes with no padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_PADDING_NONE:128:47:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

CAMELLIA Try encrypting 49 bytes with no padding
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_fail:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_PADDING_NONE:128:49:MBEDTLS_ERR_CIPHER_FULL_BLOCK_EXPECTED

CAMELLIA Encrypt and decrypt 0 bytes in multiple parts [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CBC:128:0:0:MBEDTLS_PADDING_PKCS7:0:0:0:0

CAMELLIA Encrypt and decrypt 1 bytes in multiple parts 1 [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CBC:128:1:0:MBEDTLS_PADDING_PKCS7:0:0:0:0

CAMELLIA Encrypt and decrypt 1 bytes in multiple parts 2 [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CBC:128:0:1:MBEDTLS_PADDING_PKCS7:0:0:0:0

CAMELLIA Encrypt and decrypt 16 bytes in multiple parts 1 [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CBC:128:16:0:MBEDTLS_PADDING_PKCS7:16:0:0:16

CAMELLIA Encrypt and decrypt 16 bytes in multiple parts 2 [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CBC:128:0:16:MBEDTLS_PADDING_PKCS7:0:16:0:16

CAMELLIA Encrypt and decrypt 16 bytes in multiple parts 3 [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CBC:128:1:15:MBEDTLS_PADDING_PKCS7:0:16:0:16

CAMELLIA Encrypt and decrypt 16 bytes in multiple parts 4 [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CBC:128:15:1:MBEDTLS_PADDING_PKCS7:0:16:0:16

CAMELLIA Encrypt and decrypt 22 bytes in multiple parts 1 [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CBC:128:15:7:MBEDTLS_PADDING_PKCS7:0:16:0:16

CAMELLIA Encrypt and decrypt 22 bytes in multiple parts 1 [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CBC:128:16:6:MBEDTLS_PADDING_PKCS7:16:0:0:16

CAMELLIA Encrypt and decrypt 22 bytes in multiple parts 1 [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CBC:128:17:6:MBEDTLS_PADDING_PKCS7:16:0:16:0

CAMELLIA Encrypt and decrypt 32 bytes in multiple parts 1 [#1]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CBC:128:16:16:MBEDTLS_PADDING_PKCS7:16:16:0:32

CAMELLIA Encrypt and decrypt 0 bytes [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:"CAMELLIA-128-CFB128":128:0:-1

CAMELLIA Encrypt and decrypt 1 byte [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:"CAMELLIA-128-CFB128":128:1:-1

CAMELLIA Encrypt and decrypt 2 bytes [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:"CAMELLIA-128-CFB128":128:2:-1

CAMELLIA Encrypt and decrypt 7 bytes [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:"CAMELLIA-128-CFB128":128:7:-1

CAMELLIA Encrypt and decrypt 8 bytes [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:"CAMELLIA-128-CFB128":128:8:-1

CAMELLIA Encrypt and decrypt 9 bytes [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:"CAMELLIA-128-CFB128":128:9:-1

CAMELLIA Encrypt and decrypt 15 bytes [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:"CAMELLIA-128-CFB128":128:15:-1

CAMELLIA Encrypt and decrypt 16 bytes [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:"CAMELLIA-128-CFB128":128:16:-1

CAMELLIA Encrypt and decrypt 17 bytes [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:"CAMELLIA-128-CFB128":128:17:-1

CAMELLIA Encrypt and decrypt 31 bytes [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:"CAMELLIA-128-CFB128":128:31:-1

CAMELLIA Encrypt and decrypt 32 bytes [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:"CAMELLIA-128-CFB128":128:32:-1

CAMELLIA Encrypt and decrypt 32 bytes [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:"CAMELLIA-128-CFB128":128:33:-1

CAMELLIA Encrypt and decrypt 47 bytes [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:"CAMELLIA-128-CFB128":128:47:-1

CAMELLIA Encrypt and decrypt 48 bytes [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:"CAMELLIA-128-CFB128":128:48:-1

CAMELLIA Encrypt and decrypt 49 bytes [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:"CAMELLIA-128-CFB128":128:49:-1

CAMELLIA Encrypt and decrypt 0 bytes in multiple parts [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:128:0:0:-1:0:0:0:0

CAMELLIA Encrypt and decrypt 1 bytes in multiple parts 1 [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:128:1:0:-1:1:0:1:0

CAMELLIA Encrypt and decrypt 1 bytes in multiple parts 2 [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:128:0:1:-1:0:1:0:1

CAMELLIA Encrypt and decrypt 16 bytes in multiple parts 1 [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:128:16:0:-1:16:0:16:0

CAMELLIA Encrypt and decrypt 16 bytes in multiple parts 2 [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:128:0:16:-1:0:16:0:16

CAMELLIA Encrypt and decrypt 16 bytes in multiple parts 3 [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:128:1:15:-1:1:15:1:15

CAMELLIA Encrypt and decrypt 16 bytes in multiple parts 4 [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:128:15:1:-1:15:1:15:1

CAMELLIA Encrypt and decrypt 22 bytes in multiple parts 1 [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:128:15:7:-1:15:7:15:7

CAMELLIA Encrypt and decrypt 22 bytes in multiple parts 1 [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:128:16:6:-1:16:6:16:6

CAMELLIA Encrypt and decrypt 22 bytes in multiple parts 1 [#6]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:128:17:6:-1:17:6:17:6

CAMELLIA Encrypt and decrypt 32 bytes in multiple parts 1 [#2]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CFB
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CFB128:128:16:16:-1:16:16:16:16

CAMELLIA Encrypt and decrypt 0 bytes [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CTR:"CAMELLIA-128-CTR":128:0:-1

CAMELLIA Encrypt and decrypt 1 byte [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CTR:"CAMELLIA-128-CTR":128:1:-1

CAMELLIA Encrypt and decrypt 2 bytes [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CTR:"CAMELLIA-128-CTR":128:2:-1

CAMELLIA Encrypt and decrypt 7 bytes [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CTR:"CAMELLIA-128-CTR":128:7:-1

CAMELLIA Encrypt and decrypt 8 bytes [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CTR:"CAMELLIA-128-CTR":128:8:-1

CAMELLIA Encrypt and decrypt 9 bytes [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CTR:"CAMELLIA-128-CTR":128:9:-1

CAMELLIA Encrypt and decrypt 15 bytes [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CTR:"CAMELLIA-128-CTR":128:15:-1

CAMELLIA Encrypt and decrypt 16 bytes [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CTR:"CAMELLIA-128-CTR":128:16:-1

CAMELLIA Encrypt and decrypt 17 bytes [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CTR:"CAMELLIA-128-CTR":128:17:-1

CAMELLIA Encrypt and decrypt 31 bytes [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CTR:"CAMELLIA-128-CTR":128:31:-1

CAMELLIA Encrypt and decrypt 32 bytes [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CTR:"CAMELLIA-128-CTR":128:32:-1

CAMELLIA Encrypt and decrypt 32 bytes [#6]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CTR:"CAMELLIA-128-CTR":128:33:-1

CAMELLIA Encrypt and decrypt 47 bytes [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CTR:"CAMELLIA-128-CTR":128:47:-1

CAMELLIA Encrypt and decrypt 48 bytes [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CTR:"CAMELLIA-128-CTR":128:48:-1

CAMELLIA Encrypt and decrypt 49 bytes [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CTR:"CAMELLIA-128-CTR":128:49:-1

CAMELLIA Encrypt and decrypt 0 bytes in multiple parts [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CTR:128:0:0:-1:0:0:0:0

CAMELLIA Encrypt and decrypt 1 bytes in multiple parts 1 [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CTR:128:1:0:-1:1:0:1:0

CAMELLIA Encrypt and decrypt 1 bytes in multiple parts 2 [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CTR:128:0:1:-1:0:1:0:1

CAMELLIA Encrypt and decrypt 16 bytes in multiple parts 1 [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CTR:128:16:0:-1:16:0:16:0

CAMELLIA Encrypt and decrypt 16 bytes in multiple parts 2 [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CTR:128:0:16:-1:0:16:0:16

CAMELLIA Encrypt and decrypt 16 bytes in multiple parts 3 [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CTR:128:1:15:-1:1:15:1:15

CAMELLIA Encrypt and decrypt 16 bytes in multiple parts 4 [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CTR:128:15:1:-1:15:1:15:1

CAMELLIA Encrypt and decrypt 22 bytes in multiple parts 1 [#7]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CTR:128:15:7:-1:15:7:15:7

CAMELLIA Encrypt and decrypt 22 bytes in multiple parts 1 [#8]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CTR:128:16:6:-1:16:6:16:6

CAMELLIA Encrypt and decrypt 22 bytes in multiple parts 1 [#9]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CTR:128:17:6:-1:17:6:17:6

CAMELLIA Encrypt and decrypt 32 bytes in multiple parts 1 [#3]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CTR
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CTR:128:16:16:-1:16:16:16:16

CAMELLIA Encrypt and decrypt 0 bytes [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CBC:"CAMELLIA-192-CBC":192:0:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 1 byte [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CBC:"CAMELLIA-192-CBC":192:1:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 2 bytes [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CBC:"CAMELLIA-192-CBC":192:2:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 7 bytes [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CBC:"CAMELLIA-192-CBC":192:7:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 8 bytes [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CBC:"CAMELLIA-192-CBC":192:8:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 9 bytes [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CBC:"CAMELLIA-192-CBC":192:9:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 15 bytes [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CBC:"CAMELLIA-192-CBC":192:15:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 16 bytes [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CBC:"CAMELLIA-192-CBC":192:16:MBEDTLS_PADDING_NONE

CAMELLIA Encrypt and decrypt 17 bytes [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CBC:"CAMELLIA-192-CBC":192:17:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 31 bytes [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CBC:"CAMELLIA-192-CBC":192:31:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 32 bytes [#7]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CBC:"CAMELLIA-192-CBC":192:32:MBEDTLS_PADDING_NONE

CAMELLIA Encrypt and decrypt 33 bytes [#8]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CBC:"CAMELLIA-192-CBC":192:33:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 47 bytes [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CBC:"CAMELLIA-192-CBC":192:47:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 48 bytes [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CBC:"CAMELLIA-192-CBC":192:48:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 49 bytes [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CBC:"CAMELLIA-192-CBC":192:49:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 0 bytes in multiple parts [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CBC:192:0:0:MBEDTLS_PADDING_PKCS7:0:0:0:0

CAMELLIA Encrypt and decrypt 1 bytes in multiple parts 1 [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CBC:192:1:0:MBEDTLS_PADDING_PKCS7:0:0:0:0

CAMELLIA Encrypt and decrypt 1 bytes in multiple parts 2 [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CBC:192:0:1:MBEDTLS_PADDING_PKCS7:0:0:0:0

CAMELLIA Encrypt and decrypt 16 bytes in multiple parts 1 [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CBC:192:16:0:MBEDTLS_PADDING_PKCS7:16:0:0:16

CAMELLIA Encrypt and decrypt 16 bytes in multiple parts 2 [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CBC:192:0:16:MBEDTLS_PADDING_PKCS7:0:16:0:16

CAMELLIA Encrypt and decrypt 16 bytes in multiple parts 3 [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CBC:192:1:15:MBEDTLS_PADDING_PKCS7:0:16:0:16

CAMELLIA Encrypt and decrypt 16 bytes in multiple parts 4 [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CBC:192:15:1:MBEDTLS_PADDING_PKCS7:0:16:0:16

CAMELLIA Encrypt and decrypt 22 bytes in multiple parts 1 [#10]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CBC:192:15:7:MBEDTLS_PADDING_PKCS7:0:16:0:16

CAMELLIA Encrypt and decrypt 22 bytes in multiple parts 1 [#11]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CBC:192:16:6:MBEDTLS_PADDING_PKCS7:16:0:0:16

CAMELLIA Encrypt and decrypt 22 bytes in multiple parts 1 [#12]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CBC:192:17:6:MBEDTLS_PADDING_PKCS7:16:0:16:0

CAMELLIA Encrypt and decrypt 32 bytes in multiple parts 1 [#4]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CBC:192:16:16:MBEDTLS_PADDING_PKCS7:16:16:0:32

CAMELLIA Encrypt and decrypt 0 bytes [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CBC:"CAMELLIA-256-CBC":256:0:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 1 byte [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CBC:"CAMELLIA-256-CBC":256:1:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 2 bytes [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CBC:"CAMELLIA-256-CBC":256:2:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 7 bytes [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CBC:"CAMELLIA-256-CBC":256:7:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 8 bytes [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CBC:"CAMELLIA-256-CBC":256:8:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 9 bytes [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CBC:"CAMELLIA-256-CBC":256:9:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 15 bytes [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CBC:"CAMELLIA-256-CBC":256:15:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 16 bytes [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CBC:"CAMELLIA-256-CBC":256:16:MBEDTLS_PADDING_NONE

CAMELLIA Encrypt and decrypt 17 bytes [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CBC:"CAMELLIA-256-CBC":256:17:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 31 bytes [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CBC:"CAMELLIA-256-CBC":256:31:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 32 bytes [#9]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CBC:"CAMELLIA-256-CBC":256:32:MBEDTLS_PADDING_NONE

CAMELLIA Encrypt and decrypt 33 bytes [#10]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CBC:"CAMELLIA-256-CBC":256:33:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 47 bytes [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CBC:"CAMELLIA-256-CBC":256:47:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 48 bytes [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CBC:"CAMELLIA-256-CBC":256:48:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 49 bytes [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CBC:"CAMELLIA-256-CBC":256:49:MBEDTLS_PADDING_PKCS7

CAMELLIA Encrypt and decrypt 0 bytes in multiple parts [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CBC:256:0:0:MBEDTLS_PADDING_PKCS7:0:0:0:0

CAMELLIA Encrypt and decrypt 1 bytes in multiple parts 1 [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CBC:256:1:0:MBEDTLS_PADDING_PKCS7:0:0:0:0

CAMELLIA Encrypt and decrypt 1 bytes in multiple parts 2 [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CBC:256:0:1:MBEDTLS_PADDING_PKCS7:0:0:0:0

CAMELLIA Encrypt and decrypt 16 bytes in multiple parts 1 [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CBC:256:16:0:MBEDTLS_PADDING_PKCS7:16:0:0:16

CAMELLIA Encrypt and decrypt 16 bytes in multiple parts 2 [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CBC:256:0:16:MBEDTLS_PADDING_PKCS7:0:16:0:16

CAMELLIA Encrypt and decrypt 16 bytes in multiple parts 3 [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CBC:256:1:15:MBEDTLS_PADDING_PKCS7:0:16:0:16

CAMELLIA Encrypt and decrypt 16 bytes in multiple parts 4 [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CBC:256:15:1:MBEDTLS_PADDING_PKCS7:0:16:0:16

CAMELLIA Encrypt and decrypt 22 bytes in multiple parts 1 [#13]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CBC:256:15:7:MBEDTLS_PADDING_PKCS7:0:16:0:16

CAMELLIA Encrypt and decrypt 22 bytes in multiple parts 1 [#14]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CBC:256:16:6:MBEDTLS_PADDING_PKCS7:16:0:0:16

CAMELLIA Encrypt and decrypt 22 bytes in multiple parts 1 [#15]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CBC:256:17:6:MBEDTLS_PADDING_PKCS7:16:0:16:0

CAMELLIA Encrypt and decrypt 32 bytes in multiple parts 1 [#5]
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_PADDING_PKCS7
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CBC:256:16:16:MBEDTLS_PADDING_PKCS7:16:16:0:32

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 0 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:"CAMELLIA-128-CCM*-NO-TAG":128:0:-1

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 1 byte
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:"CAMELLIA-128-CCM*-NO-TAG":128:1:-1

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 2 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:"CAMELLIA-128-CCM*-NO-TAG":128:2:-1

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 7 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:"CAMELLIA-128-CCM*-NO-TAG":128:7:-1

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 8 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:"CAMELLIA-128-CCM*-NO-TAG":128:8:-1

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 9 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:"CAMELLIA-128-CCM*-NO-TAG":128:9:-1

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 15 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:"CAMELLIA-128-CCM*-NO-TAG":128:15:-1

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 16 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:"CAMELLIA-128-CCM*-NO-TAG":128:16:-1

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 17 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:"CAMELLIA-128-CCM*-NO-TAG":128:17:-1

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 31 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:"CAMELLIA-128-CCM*-NO-TAG":128:31:-1

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 32 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:"CAMELLIA-128-CCM*-NO-TAG":128:32:-1

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 33 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:"CAMELLIA-128-CCM*-NO-TAG":128:33:-1

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 47 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:"CAMELLIA-128-CCM*-NO-TAG":128:47:-1

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 48 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:"CAMELLIA-128-CCM*-NO-TAG":128:48:-1

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 49 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:"CAMELLIA-128-CCM*-NO-TAG":128:49:-1

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 0 bytes in multiple parts
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:128:0:0:-1:0:0:0:0

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 1 bytes in multiple parts 1
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:128:1:0:-1:1:0:1:0

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 1 bytes in multiple parts 2
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:128:0:1:-1:0:1:0:1

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 1
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:128:16:0:-1:16:0:16:0

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 2
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:128:0:16:-1:0:16:0:16

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 3
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:128:1:15:-1:1:15:1:15

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 4
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:128:15:1:-1:15:1:15:1

CAMELLIA-128 CCM*-NO-TAG - Encrypt and decrypt 22 bytes in multiple parts 1
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:128:15:7:-1:15:7:15:7

CAMELLIA-128-CCM*-NO-TAG - Encrypt and decrypt 22 bytes in multiple parts 2
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:128:16:6:-1:16:6:16:6

CAMELLIA-128-CCM*-NO-TAG - Encrypt and decrypt 23 bytes in multiple parts 1
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:128:17:6:-1:17:6:17:6

CAMELLIA-128-CCM*-NO-TAG - Encrypt and decrypt 32 bytes in multiple parts 1
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:128:16:16:-1:16:16:16:16

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 0 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:"CAMELLIA-192-CCM*-NO-TAG":192:0:-1

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 1 byte
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:"CAMELLIA-192-CCM*-NO-TAG":192:1:-1

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 2 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:"CAMELLIA-192-CCM*-NO-TAG":192:2:-1

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 7 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:"CAMELLIA-192-CCM*-NO-TAG":192:7:-1

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 8 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:"CAMELLIA-192-CCM*-NO-TAG":192:8:-1

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 9 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:"CAMELLIA-192-CCM*-NO-TAG":192:9:-1

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 15 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:"CAMELLIA-192-CCM*-NO-TAG":192:15:-1

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 16 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:"CAMELLIA-192-CCM*-NO-TAG":192:16:-1

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 17 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:"CAMELLIA-192-CCM*-NO-TAG":192:17:-1

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 31 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:"CAMELLIA-192-CCM*-NO-TAG":192:31:-1

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 32 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:"CAMELLIA-192-CCM*-NO-TAG":192:32:-1

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 33 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:"CAMELLIA-192-CCM*-NO-TAG":192:33:-1

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 47 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:"CAMELLIA-192-CCM*-NO-TAG":192:47:-1

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 48 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:"CAMELLIA-192-CCM*-NO-TAG":192:48:-1

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 49 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:"CAMELLIA-192-CCM*-NO-TAG":192:49:-1

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 0 bytes in multiple parts
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:192:0:0:-1:0:0:0:0

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 1 bytes in multiple parts 1
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:192:1:0:-1:1:0:1:0

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 1 bytes in multiple parts 2
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:192:0:1:-1:0:1:0:1

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 1
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:192:16:0:-1:16:0:16:0

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 2
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:192:0:16:-1:0:16:0:16

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 3
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:192:1:15:-1:1:15:1:15

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 4
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:192:15:1:-1:15:1:15:1

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 22 bytes in multiple parts 1
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:192:15:7:-1:15:7:15:7

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 22 bytes in multiple parts 2
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:192:16:6:-1:16:6:16:6

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 23 bytes in multiple parts 1
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:192:17:6:-1:17:6:17:6

CAMELLIA-192-CCM*-NO-TAG - Encrypt and decrypt 32 bytes in multiple parts 1
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:192:16:16:-1:16:16:16:16

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 0 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:"CAMELLIA-256-CCM*-NO-TAG":256:0:-1

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 1 byte
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:"CAMELLIA-256-CCM*-NO-TAG":256:1:-1

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 2 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:"CAMELLIA-256-CCM*-NO-TAG":256:2:-1

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 7 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:"CAMELLIA-256-CCM*-NO-TAG":256:7:-1

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 8 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:"CAMELLIA-256-CCM*-NO-TAG":256:8:-1

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 9 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:"CAMELLIA-256-CCM*-NO-TAG":256:9:-1

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 15 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:"CAMELLIA-256-CCM*-NO-TAG":256:15:-1

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 16 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:"CAMELLIA-256-CCM*-NO-TAG":256:16:-1

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 17 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:"CAMELLIA-256-CCM*-NO-TAG":256:17:-1

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 31 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:"CAMELLIA-256-CCM*-NO-TAG":256:31:-1

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 32 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:"CAMELLIA-256-CCM*-NO-TAG":256:32:-1

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 33 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:"CAMELLIA-256-CCM*-NO-TAG":256:33:-1

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 47 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:"CAMELLIA-256-CCM*-NO-TAG":256:47:-1

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 48 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:"CAMELLIA-256-CCM*-NO-TAG":256:48:-1

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 49 bytes
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:"CAMELLIA-256-CCM*-NO-TAG":256:49:-1

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 0 bytes in multiple parts
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:256:0:0:-1:0:0:0:0

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 1 bytes in multiple parts 1
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:256:1:0:-1:1:0:1:0

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 1 bytes in multiple parts 2
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:256:0:1:-1:0:1:0:1

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 1
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:256:16:0:-1:16:0:16:0

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 2
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:256:0:16:-1:0:16:0:16

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 3
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:256:1:15:-1:1:15:1:15

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 16 bytes in multiple parts 4
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:256:15:1:-1:15:1:15:1

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 22 bytes in multiple parts 1
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:256:15:7:-1:15:7:15:7

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 22 bytes in multiple parts 2
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:256:16:6:-1:16:6:16:6

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 23 bytes in multiple parts 1
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:256:17:6:-1:17:6:17:6

CAMELLIA-256-CCM*-NO-TAG - Encrypt and decrypt 32 bytes in multiple parts 1
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
enc_dec_buf_multipart:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:256:16:16:-1:16:16:16:16

CAMELLIA-128-CCM*-NO-TAG crypt Encrypt
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
test_vec_crypt:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:MBEDTLS_ENCRYPT:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"00000003020100A0A1A2A3A4A5":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E":"BA737185E719310492F38A5F1251DA55FAFBC949848A0D":0:0

CAMELLIA-128-CCM*-NO-TAG crypt Decrypt
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
test_vec_crypt:MBEDTLS_CIPHER_CAMELLIA_128_CCM_STAR_NO_TAG:MBEDTLS_DECRYPT:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"00000003020100A0A1A2A3A4A5":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E":"BA737185E719310492F38A5F1251DA55FAFBC949848A0D":0:0

CAMELLIA-192-CCM*-NO-TAG crypt Encrypt
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
test_vec_crypt:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:MBEDTLS_ENCRYPT:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECFC0C1C2C3C4C5C6C7":"00000003020100A0A1A2A3A4A5":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E":"c3ceaa2a68fb31d8347a83950f25f3a7956b8a284a5b35":0:0

CAMELLIA-192-CCM*-NO-TAG crypt Decrypt
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
test_vec_crypt:MBEDTLS_CIPHER_CAMELLIA_192_CCM_STAR_NO_TAG:MBEDTLS_DECRYPT:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECFC0C1C2C3C4C5C6C7":"00000003020100A0A1A2A3A4A5":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E":"c3ceaa2a68fb31d8347a83950f25f3a7956b8a284a5b35":0:0

CAMELLIA-256-CCM*-NO-TAG crypt Encrypt
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
test_vec_crypt:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:MBEDTLS_ENCRYPT:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECFC0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"00000003020100A0A1A2A3A4A5":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E":"933f749801d0e1262cd101831defd8366ab2a22e7c03cd":0:0

CAMELLIA-256-CCM*-NO-TAG crypt Decrypt
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CCM_C
test_vec_crypt:MBEDTLS_CIPHER_CAMELLIA_256_CCM_STAR_NO_TAG:MBEDTLS_DECRYPT:"C0C1C2C3C4C5C6C7C8C9CACBCCCDCECFC0C1C2C3C4C5C6C7C8C9CACBCCCDCECF":"00000003020100A0A1A2A3A4A5":"08090A0B0C0D0E0F101112131415161718191A1B1C1D1E":"933f749801d0e1262cd101831defd8366ab2a22e7c03cd":0:0

Check set padding - 128 bit key
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC
check_set_padding:MBEDTLS_CIPHER_CAMELLIA_128_CBC

Check set padding - 192 bit key
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC
check_set_padding:MBEDTLS_CIPHER_CAMELLIA_192_CBC

Check set padding - 256 bit key
depends_on:MBEDTLS_CAMELLIA_C:MBEDTLS_CIPHER_MODE_CBC
check_set_padding:MBEDTLS_CIPHER_CAMELLIA_256_CBC
