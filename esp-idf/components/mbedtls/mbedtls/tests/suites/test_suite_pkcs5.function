/* BEGIN_HEADER */
#include "mbedtls/pkcs5.h"
#include "mbedtls/cipher.h"
/* END_HEADER */

/* BEGIN_DEPENDENCIES
 * depends_on:MBEDTLS_PKCS5_C
 * END_DEPENDENCIES
 */

/* BEGIN_CASE */
void pbkdf2_hmac(int hash, data_t *pw_str, data_t *salt_str,
                 int it_cnt, int key_len, data_t *result_key_string)
{
    unsigned char key[100];

    MD_PSA_INIT();
    TEST_ASSERT(mbedtls_pkcs5_pbkdf2_hmac_ext(hash, pw_str->x, pw_str->len,
                                              salt_str->x, salt_str->len,
                                              it_cnt, key_len, key) == 0);

    TEST_ASSERT(mbedtls_test_hexcmp(key, result_key_string->x,
                                    key_len, result_key_string->len) == 0);

exit:
    MD_PSA_DONE();
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_ASN1_PARSE_C:MBEDTLS_CIPHER_C */
void pbes2_encrypt(int params_tag, data_t *params_hex, data_t *pw,
                   data_t *data, int outsize, int ref_ret,
                   data_t *ref_out)
{
    int my_ret;
    mbedtls_asn1_buf params;
    unsigned char *my_out = NULL;
#if defined(MBEDTLS_CIPHER_PADDING_PKCS7)
    size_t my_out_len = 0;
#endif
    MD_PSA_INIT();

    params.tag = params_tag;
    params.p = params_hex->x;
    params.len = params_hex->len;

    ASSERT_ALLOC(my_out, outsize);

#if defined(MBEDTLS_TEST_DEPRECATED)
    if (ref_ret != MBEDTLS_ERR_ASN1_BUF_TOO_SMALL) {
        my_ret = mbedtls_pkcs5_pbes2(&params, MBEDTLS_PKCS5_ENCRYPT,
                                     pw->x, pw->len, data->x, data->len, my_out);
        TEST_EQUAL(my_ret, ref_ret);
    }
    if (ref_ret == 0) {
        ASSERT_COMPARE(my_out, ref_out->len,
                       ref_out->x, ref_out->len);
    }
#endif

#if defined(MBEDTLS_CIPHER_PADDING_PKCS7)
    my_ret = mbedtls_pkcs5_pbes2_ext(&params, MBEDTLS_PKCS5_ENCRYPT,
                                     pw->x, pw->len, data->x, data->len, my_out,
                                     outsize, &my_out_len);
    TEST_EQUAL(my_ret, ref_ret);
    if (ref_ret == 0) {
        ASSERT_COMPARE(my_out, my_out_len,
                       ref_out->x, ref_out->len);
    }
#endif

exit:
    mbedtls_free(my_out);
    MD_PSA_DONE();
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_ASN1_PARSE_C:MBEDTLS_CIPHER_C */
void pbes2_decrypt(int params_tag, data_t *params_hex, data_t *pw,
                   data_t *data, int outsize, int ref_ret,
                   data_t *ref_out)
{
    int my_ret;
    mbedtls_asn1_buf params;
    unsigned char *my_out = NULL;
#if defined(MBEDTLS_CIPHER_PADDING_PKCS7)
    size_t my_out_len = 0;
#endif

    MD_PSA_INIT();

    params.tag = params_tag;
    params.p = params_hex->x;
    params.len = params_hex->len;

    ASSERT_ALLOC(my_out, outsize);

#if defined(MBEDTLS_TEST_DEPRECATED)
    if (ref_ret != MBEDTLS_ERR_ASN1_BUF_TOO_SMALL) {
        my_ret = mbedtls_pkcs5_pbes2(&params, MBEDTLS_PKCS5_DECRYPT,
                                     pw->x, pw->len, data->x, data->len, my_out);
        TEST_EQUAL(my_ret, ref_ret);
    }
    if (ref_ret == 0) {
        ASSERT_COMPARE(my_out, ref_out->len,
                       ref_out->x, ref_out->len);
    }
#endif

#if defined(MBEDTLS_CIPHER_PADDING_PKCS7)
    my_ret = mbedtls_pkcs5_pbes2_ext(&params, MBEDTLS_PKCS5_DECRYPT,
                                     pw->x, pw->len, data->x, data->len, my_out,
                                     outsize, &my_out_len);
    TEST_EQUAL(my_ret, ref_ret);
    if (ref_ret == 0) {
        ASSERT_COMPARE(my_out, my_out_len,
                       ref_out->x, ref_out->len);
    }
#endif

exit:
    mbedtls_free(my_out);
    MD_PSA_DONE();
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_SELF_TEST */
void pkcs5_selftest()
{
    MD_PSA_INIT();
    TEST_ASSERT(mbedtls_pkcs5_self_test(1) == 0);

exit:
    MD_PSA_DONE();
}
/* END_CASE */
