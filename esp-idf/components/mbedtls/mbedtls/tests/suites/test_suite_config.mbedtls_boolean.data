# Automatically generated by generate_config_tests.py. Do not edit!

Config: MBEDTLS_AESCE_C
depends_on:MBEDTLS_AESCE_C:MBEDTLS_AES_C
pass:

Config: !MBEDTLS_AESCE_C
depends_on:!MBEDTLS_AESCE_C:MBEDTLS_AES_C
pass:

Config: MBEDTLS_AESNI_C
depends_on:MBEDTLS_AESNI_C:MBEDTLS_AES_C
pass:

Config: !MBEDTLS_AESNI_C
depends_on:!MBEDTLS_AESNI_C:MBEDTLS_AES_C
pass:

Config: MBEDTLS_AES_C
depends_on:MBEDTLS_AES_C
pass:

Config: !MBEDTLS_AES_C
depends_on:!MBEDTLS_AES_C
pass:

Config: MBEDTLS_AES_FEWER_TABLES
depends_on:MBEDTLS_AES_FEWER_TABLES:MBEDTLS_AES_C
pass:

Config: !MBEDTLS_AES_FEWER_TABLES
depends_on:!MBEDTLS_AES_FEWER_TABLES:MBEDTLS_AES_C
pass:

Config: MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
depends_on:MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:MBEDTLS_AES_C
pass:

Config: !MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:MBEDTLS_AES_C
pass:

Config: MBEDTLS_AES_ROM_TABLES
depends_on:MBEDTLS_AES_ROM_TABLES:MBEDTLS_AES_C
pass:

Config: !MBEDTLS_AES_ROM_TABLES
depends_on:!MBEDTLS_AES_ROM_TABLES:MBEDTLS_AES_C
pass:

Config: MBEDTLS_AES_USE_HARDWARE_ONLY
depends_on:MBEDTLS_AES_USE_HARDWARE_ONLY:MBEDTLS_AES_C
pass:

Config: !MBEDTLS_AES_USE_HARDWARE_ONLY
depends_on:!MBEDTLS_AES_USE_HARDWARE_ONLY:MBEDTLS_AES_C
pass:

Config: MBEDTLS_ARIA_C
depends_on:MBEDTLS_ARIA_C
pass:

Config: !MBEDTLS_ARIA_C
depends_on:!MBEDTLS_ARIA_C
pass:

Config: MBEDTLS_ASN1_PARSE_C
depends_on:MBEDTLS_ASN1_PARSE_C
pass:

Config: !MBEDTLS_ASN1_PARSE_C
depends_on:!MBEDTLS_ASN1_PARSE_C
pass:

Config: MBEDTLS_ASN1_WRITE_C
depends_on:MBEDTLS_ASN1_WRITE_C
pass:

Config: !MBEDTLS_ASN1_WRITE_C
depends_on:!MBEDTLS_ASN1_WRITE_C
pass:

Config: MBEDTLS_BASE64_C
depends_on:MBEDTLS_BASE64_C
pass:

Config: !MBEDTLS_BASE64_C
depends_on:!MBEDTLS_BASE64_C
pass:

Config: MBEDTLS_BIGNUM_C
depends_on:MBEDTLS_BIGNUM_C
pass:

Config: !MBEDTLS_BIGNUM_C
depends_on:!MBEDTLS_BIGNUM_C
pass:

Config: MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
depends_on:MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
pass:

Config: !MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
depends_on:!MBEDTLS_BLOCK_CIPHER_NO_DECRYPT
pass:

Config: MBEDTLS_CAMELLIA_C
depends_on:MBEDTLS_CAMELLIA_C
pass:

Config: !MBEDTLS_CAMELLIA_C
depends_on:!MBEDTLS_CAMELLIA_C
pass:

Config: MBEDTLS_CAMELLIA_SMALL_MEMORY
depends_on:MBEDTLS_CAMELLIA_SMALL_MEMORY:MBEDTLS_CAMELLIA_C
pass:

Config: !MBEDTLS_CAMELLIA_SMALL_MEMORY
depends_on:!MBEDTLS_CAMELLIA_SMALL_MEMORY:MBEDTLS_CAMELLIA_C
pass:

Config: MBEDTLS_CCM_C
depends_on:MBEDTLS_CCM_C
pass:

Config: !MBEDTLS_CCM_C
depends_on:!MBEDTLS_CCM_C
pass:

Config: MBEDTLS_CHACHA20_C
depends_on:MBEDTLS_CHACHA20_C
pass:

Config: !MBEDTLS_CHACHA20_C
depends_on:!MBEDTLS_CHACHA20_C
pass:

Config: MBEDTLS_CHACHAPOLY_C
depends_on:MBEDTLS_CHACHAPOLY_C
pass:

Config: !MBEDTLS_CHACHAPOLY_C
depends_on:!MBEDTLS_CHACHAPOLY_C
pass:

Config: MBEDTLS_CHECK_RETURN_WARNING
depends_on:MBEDTLS_CHECK_RETURN_WARNING
pass:

Config: !MBEDTLS_CHECK_RETURN_WARNING
depends_on:!MBEDTLS_CHECK_RETURN_WARNING
pass:

Config: MBEDTLS_CIPHER_C
depends_on:MBEDTLS_CIPHER_C
pass:

Config: !MBEDTLS_CIPHER_C
depends_on:!MBEDTLS_CIPHER_C
pass:

Config: MBEDTLS_CIPHER_MODE_CBC
depends_on:MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_C
pass:

Config: !MBEDTLS_CIPHER_MODE_CBC
depends_on:!MBEDTLS_CIPHER_MODE_CBC:MBEDTLS_CIPHER_C
pass:

Config: MBEDTLS_CIPHER_MODE_CFB
depends_on:MBEDTLS_CIPHER_MODE_CFB:MBEDTLS_CIPHER_C
pass:

Config: !MBEDTLS_CIPHER_MODE_CFB
depends_on:!MBEDTLS_CIPHER_MODE_CFB:MBEDTLS_CIPHER_C
pass:

Config: MBEDTLS_CIPHER_MODE_CTR
depends_on:MBEDTLS_CIPHER_MODE_CTR:MBEDTLS_CIPHER_C
pass:

Config: !MBEDTLS_CIPHER_MODE_CTR
depends_on:!MBEDTLS_CIPHER_MODE_CTR:MBEDTLS_CIPHER_C
pass:

Config: MBEDTLS_CIPHER_MODE_OFB
depends_on:MBEDTLS_CIPHER_MODE_OFB:MBEDTLS_CIPHER_C
pass:

Config: !MBEDTLS_CIPHER_MODE_OFB
depends_on:!MBEDTLS_CIPHER_MODE_OFB:MBEDTLS_CIPHER_C
pass:

Config: MBEDTLS_CIPHER_MODE_XTS
depends_on:MBEDTLS_CIPHER_MODE_XTS:MBEDTLS_CIPHER_C
pass:

Config: !MBEDTLS_CIPHER_MODE_XTS
depends_on:!MBEDTLS_CIPHER_MODE_XTS:MBEDTLS_CIPHER_C
pass:

Config: MBEDTLS_CIPHER_NULL_CIPHER
depends_on:MBEDTLS_CIPHER_NULL_CIPHER:MBEDTLS_CIPHER_C
pass:

Config: !MBEDTLS_CIPHER_NULL_CIPHER
depends_on:!MBEDTLS_CIPHER_NULL_CIPHER:MBEDTLS_CIPHER_C
pass:

Config: MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
depends_on:MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pass:

Config: !MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS
depends_on:!MBEDTLS_CIPHER_PADDING_ONE_AND_ZEROS:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pass:

Config: MBEDTLS_CIPHER_PADDING_PKCS7
depends_on:MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pass:

Config: !MBEDTLS_CIPHER_PADDING_PKCS7
depends_on:!MBEDTLS_CIPHER_PADDING_PKCS7:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pass:

Config: MBEDTLS_CIPHER_PADDING_ZEROS
depends_on:MBEDTLS_CIPHER_PADDING_ZEROS:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pass:

Config: !MBEDTLS_CIPHER_PADDING_ZEROS
depends_on:!MBEDTLS_CIPHER_PADDING_ZEROS:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pass:

Config: MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
depends_on:MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pass:

Config: !MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN
depends_on:!MBEDTLS_CIPHER_PADDING_ZEROS_AND_LEN:MBEDTLS_CIPHER_C:MBEDTLS_CIPHER_MODE_CBC
pass:

Config: MBEDTLS_CMAC_C
depends_on:MBEDTLS_CMAC_C
pass:

Config: !MBEDTLS_CMAC_C
depends_on:!MBEDTLS_CMAC_C
pass:

Config: MBEDTLS_CTR_DRBG_C
depends_on:MBEDTLS_CTR_DRBG_C
pass:

Config: !MBEDTLS_CTR_DRBG_C
depends_on:!MBEDTLS_CTR_DRBG_C
pass:

Config: MBEDTLS_CTR_DRBG_USE_128_BIT_KEY
depends_on:MBEDTLS_CTR_DRBG_USE_128_BIT_KEY:MBEDTLS_CTR_DRBG_C
pass:

Config: !MBEDTLS_CTR_DRBG_USE_128_BIT_KEY
depends_on:!MBEDTLS_CTR_DRBG_USE_128_BIT_KEY:MBEDTLS_CTR_DRBG_C
pass:

Config: MBEDTLS_DEBUG_C
depends_on:MBEDTLS_DEBUG_C
pass:

Config: !MBEDTLS_DEBUG_C
depends_on:!MBEDTLS_DEBUG_C
pass:

Config: MBEDTLS_DEPRECATED_REMOVED
depends_on:MBEDTLS_DEPRECATED_REMOVED
pass:

Config: !MBEDTLS_DEPRECATED_REMOVED
depends_on:!MBEDTLS_DEPRECATED_REMOVED
pass:

Config: MBEDTLS_DEPRECATED_WARNING
depends_on:MBEDTLS_DEPRECATED_WARNING
pass:

Config: !MBEDTLS_DEPRECATED_WARNING
depends_on:!MBEDTLS_DEPRECATED_WARNING
pass:

Config: MBEDTLS_DES_C
depends_on:MBEDTLS_DES_C
pass:

Config: !MBEDTLS_DES_C
depends_on:!MBEDTLS_DES_C
pass:

Config: MBEDTLS_DHM_C
depends_on:MBEDTLS_DHM_C
pass:

Config: !MBEDTLS_DHM_C
depends_on:!MBEDTLS_DHM_C
pass:

Config: MBEDTLS_ECDH_C
depends_on:MBEDTLS_ECDH_C
pass:

Config: !MBEDTLS_ECDH_C
depends_on:!MBEDTLS_ECDH_C
pass:

Config: MBEDTLS_ECDH_VARIANT_EVEREST_ENABLED
depends_on:MBEDTLS_ECDH_VARIANT_EVEREST_ENABLED:MBEDTLS_ECDH_C
pass:

Config: !MBEDTLS_ECDH_VARIANT_EVEREST_ENABLED
depends_on:!MBEDTLS_ECDH_VARIANT_EVEREST_ENABLED:MBEDTLS_ECDH_C
pass:

Config: MBEDTLS_ECDSA_C
depends_on:MBEDTLS_ECDSA_C
pass:

Config: !MBEDTLS_ECDSA_C
depends_on:!MBEDTLS_ECDSA_C
pass:

Config: MBEDTLS_ECDSA_DETERMINISTIC
depends_on:MBEDTLS_ECDSA_DETERMINISTIC:MBEDTLS_ECDSA_C
pass:

Config: !MBEDTLS_ECDSA_DETERMINISTIC
depends_on:!MBEDTLS_ECDSA_DETERMINISTIC:MBEDTLS_ECDSA_C
pass:

Config: MBEDTLS_ECJPAKE_C
depends_on:MBEDTLS_ECJPAKE_C
pass:

Config: !MBEDTLS_ECJPAKE_C
depends_on:!MBEDTLS_ECJPAKE_C
pass:

Config: MBEDTLS_ECP_C
depends_on:MBEDTLS_ECP_C
pass:

Config: !MBEDTLS_ECP_C
depends_on:!MBEDTLS_ECP_C
pass:

Config: MBEDTLS_ECP_DP_BP256R1_ENABLED
depends_on:MBEDTLS_ECP_DP_BP256R1_ENABLED:MBEDTLS_ECP_C
pass:

Config: !MBEDTLS_ECP_DP_BP256R1_ENABLED
depends_on:!MBEDTLS_ECP_DP_BP256R1_ENABLED:MBEDTLS_ECP_C
pass:

Config: MBEDTLS_ECP_DP_BP384R1_ENABLED
depends_on:MBEDTLS_ECP_DP_BP384R1_ENABLED:MBEDTLS_ECP_C
pass:

Config: !MBEDTLS_ECP_DP_BP384R1_ENABLED
depends_on:!MBEDTLS_ECP_DP_BP384R1_ENABLED:MBEDTLS_ECP_C
pass:

Config: MBEDTLS_ECP_DP_BP512R1_ENABLED
depends_on:MBEDTLS_ECP_DP_BP512R1_ENABLED:MBEDTLS_ECP_C
pass:

Config: !MBEDTLS_ECP_DP_BP512R1_ENABLED
depends_on:!MBEDTLS_ECP_DP_BP512R1_ENABLED:MBEDTLS_ECP_C
pass:

Config: MBEDTLS_ECP_DP_CURVE25519_ENABLED
depends_on:MBEDTLS_ECP_DP_CURVE25519_ENABLED:MBEDTLS_ECP_C
pass:

Config: !MBEDTLS_ECP_DP_CURVE25519_ENABLED
depends_on:!MBEDTLS_ECP_DP_CURVE25519_ENABLED:MBEDTLS_ECP_C
pass:

Config: MBEDTLS_ECP_DP_CURVE448_ENABLED
depends_on:MBEDTLS_ECP_DP_CURVE448_ENABLED:MBEDTLS_ECP_C
pass:

Config: !MBEDTLS_ECP_DP_CURVE448_ENABLED
depends_on:!MBEDTLS_ECP_DP_CURVE448_ENABLED:MBEDTLS_ECP_C
pass:

Config: MBEDTLS_ECP_DP_SECP192K1_ENABLED
depends_on:MBEDTLS_ECP_DP_SECP192K1_ENABLED:MBEDTLS_ECP_C
pass:

Config: !MBEDTLS_ECP_DP_SECP192K1_ENABLED
depends_on:!MBEDTLS_ECP_DP_SECP192K1_ENABLED:MBEDTLS_ECP_C
pass:

Config: MBEDTLS_ECP_DP_SECP192R1_ENABLED
depends_on:MBEDTLS_ECP_DP_SECP192R1_ENABLED:MBEDTLS_ECP_C
pass:

Config: !MBEDTLS_ECP_DP_SECP192R1_ENABLED
depends_on:!MBEDTLS_ECP_DP_SECP192R1_ENABLED:MBEDTLS_ECP_C
pass:

Config: MBEDTLS_ECP_DP_SECP224K1_ENABLED
depends_on:MBEDTLS_ECP_DP_SECP224K1_ENABLED:MBEDTLS_ECP_C
pass:

Config: !MBEDTLS_ECP_DP_SECP224K1_ENABLED
depends_on:!MBEDTLS_ECP_DP_SECP224K1_ENABLED:MBEDTLS_ECP_C
pass:

Config: MBEDTLS_ECP_DP_SECP224R1_ENABLED
depends_on:MBEDTLS_ECP_DP_SECP224R1_ENABLED:MBEDTLS_ECP_C
pass:

Config: !MBEDTLS_ECP_DP_SECP224R1_ENABLED
depends_on:!MBEDTLS_ECP_DP_SECP224R1_ENABLED:MBEDTLS_ECP_C
pass:

Config: MBEDTLS_ECP_DP_SECP256K1_ENABLED
depends_on:MBEDTLS_ECP_DP_SECP256K1_ENABLED:MBEDTLS_ECP_C
pass:

Config: !MBEDTLS_ECP_DP_SECP256K1_ENABLED
depends_on:!MBEDTLS_ECP_DP_SECP256K1_ENABLED:MBEDTLS_ECP_C
pass:

Config: MBEDTLS_ECP_DP_SECP256R1_ENABLED
depends_on:MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_ECP_C
pass:

Config: !MBEDTLS_ECP_DP_SECP256R1_ENABLED
depends_on:!MBEDTLS_ECP_DP_SECP256R1_ENABLED:MBEDTLS_ECP_C
pass:

Config: MBEDTLS_ECP_DP_SECP384R1_ENABLED
depends_on:MBEDTLS_ECP_DP_SECP384R1_ENABLED:MBEDTLS_ECP_C
pass:

Config: !MBEDTLS_ECP_DP_SECP384R1_ENABLED
depends_on:!MBEDTLS_ECP_DP_SECP384R1_ENABLED:MBEDTLS_ECP_C
pass:

Config: MBEDTLS_ECP_DP_SECP521R1_ENABLED
depends_on:MBEDTLS_ECP_DP_SECP521R1_ENABLED:MBEDTLS_ECP_C
pass:

Config: !MBEDTLS_ECP_DP_SECP521R1_ENABLED
depends_on:!MBEDTLS_ECP_DP_SECP521R1_ENABLED:MBEDTLS_ECP_C
pass:

Config: MBEDTLS_ECP_NIST_OPTIM
depends_on:MBEDTLS_ECP_NIST_OPTIM:MBEDTLS_ECP_C
pass:

Config: !MBEDTLS_ECP_NIST_OPTIM
depends_on:!MBEDTLS_ECP_NIST_OPTIM:MBEDTLS_ECP_C
pass:

Config: MBEDTLS_ECP_NO_FALLBACK
depends_on:MBEDTLS_ECP_NO_FALLBACK:MBEDTLS_ECP_C
pass:

Config: !MBEDTLS_ECP_NO_FALLBACK
depends_on:!MBEDTLS_ECP_NO_FALLBACK:MBEDTLS_ECP_C
pass:

Config: MBEDTLS_ECP_RESTARTABLE
depends_on:MBEDTLS_ECP_RESTARTABLE:MBEDTLS_ECP_C
pass:

Config: !MBEDTLS_ECP_RESTARTABLE
depends_on:!MBEDTLS_ECP_RESTARTABLE:MBEDTLS_ECP_C
pass:

Config: MBEDTLS_ECP_WITH_MPI_UINT
depends_on:MBEDTLS_ECP_WITH_MPI_UINT:MBEDTLS_ECP_C
pass:

Config: !MBEDTLS_ECP_WITH_MPI_UINT
depends_on:!MBEDTLS_ECP_WITH_MPI_UINT:MBEDTLS_ECP_C
pass:

Config: MBEDTLS_ENTROPY_C
depends_on:MBEDTLS_ENTROPY_C
pass:

Config: !MBEDTLS_ENTROPY_C
depends_on:!MBEDTLS_ENTROPY_C
pass:

Config: MBEDTLS_ENTROPY_FORCE_SHA256
depends_on:MBEDTLS_ENTROPY_FORCE_SHA256:MBEDTLS_ENTROPY_C
pass:

Config: !MBEDTLS_ENTROPY_FORCE_SHA256
depends_on:!MBEDTLS_ENTROPY_FORCE_SHA256:MBEDTLS_ENTROPY_C
pass:

Config: MBEDTLS_ENTROPY_NV_SEED
depends_on:MBEDTLS_ENTROPY_NV_SEED:MBEDTLS_ENTROPY_C
pass:

Config: !MBEDTLS_ENTROPY_NV_SEED
depends_on:!MBEDTLS_ENTROPY_NV_SEED:MBEDTLS_ENTROPY_C
pass:

Config: MBEDTLS_ERROR_C
depends_on:MBEDTLS_ERROR_C
pass:

Config: !MBEDTLS_ERROR_C
depends_on:!MBEDTLS_ERROR_C
pass:

Config: MBEDTLS_ERROR_STRERROR_DUMMY
depends_on:MBEDTLS_ERROR_STRERROR_DUMMY:!MBEDTLS_ERROR_C
pass:

Config: !MBEDTLS_ERROR_STRERROR_DUMMY
depends_on:!MBEDTLS_ERROR_STRERROR_DUMMY:!MBEDTLS_ERROR_C
pass:

Config: MBEDTLS_FS_IO
depends_on:MBEDTLS_FS_IO
pass:

Config: !MBEDTLS_FS_IO
depends_on:!MBEDTLS_FS_IO
pass:

Config: MBEDTLS_GCM_C
depends_on:MBEDTLS_GCM_C
pass:

Config: !MBEDTLS_GCM_C
depends_on:!MBEDTLS_GCM_C
pass:

Config: MBEDTLS_GCM_LARGE_TABLE
depends_on:MBEDTLS_GCM_LARGE_TABLE:MBEDTLS_GCM_C
pass:

Config: !MBEDTLS_GCM_LARGE_TABLE
depends_on:!MBEDTLS_GCM_LARGE_TABLE:MBEDTLS_GCM_C
pass:

Config: MBEDTLS_GENPRIME
depends_on:MBEDTLS_GENPRIME:MBEDTLS_RSA_C
pass:

Config: !MBEDTLS_GENPRIME
depends_on:!MBEDTLS_GENPRIME:MBEDTLS_RSA_C
pass:

Config: MBEDTLS_HAVE_ASM
depends_on:MBEDTLS_HAVE_ASM
pass:

Config: !MBEDTLS_HAVE_ASM
depends_on:!MBEDTLS_HAVE_ASM
pass:

Config: MBEDTLS_HAVE_SSE2
depends_on:MBEDTLS_HAVE_SSE2
pass:

Config: !MBEDTLS_HAVE_SSE2
depends_on:!MBEDTLS_HAVE_SSE2
pass:

Config: MBEDTLS_HAVE_TIME
depends_on:MBEDTLS_HAVE_TIME
pass:

Config: !MBEDTLS_HAVE_TIME
depends_on:!MBEDTLS_HAVE_TIME
pass:

Config: MBEDTLS_HAVE_TIME_DATE
depends_on:MBEDTLS_HAVE_TIME_DATE
pass:

Config: !MBEDTLS_HAVE_TIME_DATE
depends_on:!MBEDTLS_HAVE_TIME_DATE
pass:

Config: MBEDTLS_HKDF_C
depends_on:MBEDTLS_HKDF_C
pass:

Config: !MBEDTLS_HKDF_C
depends_on:!MBEDTLS_HKDF_C
pass:

Config: MBEDTLS_HMAC_DRBG_C
depends_on:MBEDTLS_HMAC_DRBG_C
pass:

Config: !MBEDTLS_HMAC_DRBG_C
depends_on:!MBEDTLS_HMAC_DRBG_C
pass:

Config: MBEDTLS_KEY_EXCHANGE_DHE_PSK_ENABLED
depends_on:MBEDTLS_KEY_EXCHANGE_DHE_PSK_ENABLED
pass:

Config: !MBEDTLS_KEY_EXCHANGE_DHE_PSK_ENABLED
depends_on:!MBEDTLS_KEY_EXCHANGE_DHE_PSK_ENABLED
pass:

Config: MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED
depends_on:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED
pass:

Config: !MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED
depends_on:!MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED
pass:

Config: MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
depends_on:MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
pass:

Config: !MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
depends_on:!MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
pass:

Config: MBEDTLS_KEY_EXCHANGE_ECDHE_PSK_ENABLED
depends_on:MBEDTLS_KEY_EXCHANGE_ECDHE_PSK_ENABLED
pass:

Config: !MBEDTLS_KEY_EXCHANGE_ECDHE_PSK_ENABLED
depends_on:!MBEDTLS_KEY_EXCHANGE_ECDHE_PSK_ENABLED
pass:

Config: MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED
depends_on:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED
pass:

Config: !MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED
depends_on:!MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED
pass:

Config: MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA_ENABLED
depends_on:MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA_ENABLED
pass:

Config: !MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA_ENABLED
depends_on:!MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA_ENABLED
pass:

Config: MBEDTLS_KEY_EXCHANGE_ECDH_RSA_ENABLED
depends_on:MBEDTLS_KEY_EXCHANGE_ECDH_RSA_ENABLED
pass:

Config: !MBEDTLS_KEY_EXCHANGE_ECDH_RSA_ENABLED
depends_on:!MBEDTLS_KEY_EXCHANGE_ECDH_RSA_ENABLED
pass:

Config: MBEDTLS_KEY_EXCHANGE_ECJPAKE_ENABLED
depends_on:MBEDTLS_KEY_EXCHANGE_ECJPAKE_ENABLED
pass:

Config: !MBEDTLS_KEY_EXCHANGE_ECJPAKE_ENABLED
depends_on:!MBEDTLS_KEY_EXCHANGE_ECJPAKE_ENABLED
pass:

Config: MBEDTLS_KEY_EXCHANGE_PSK_ENABLED
depends_on:MBEDTLS_KEY_EXCHANGE_PSK_ENABLED
pass:

Config: !MBEDTLS_KEY_EXCHANGE_PSK_ENABLED
depends_on:!MBEDTLS_KEY_EXCHANGE_PSK_ENABLED
pass:

Config: MBEDTLS_KEY_EXCHANGE_RSA_ENABLED
depends_on:MBEDTLS_KEY_EXCHANGE_RSA_ENABLED
pass:

Config: !MBEDTLS_KEY_EXCHANGE_RSA_ENABLED
depends_on:!MBEDTLS_KEY_EXCHANGE_RSA_ENABLED
pass:

Config: MBEDTLS_KEY_EXCHANGE_RSA_PSK_ENABLED
depends_on:MBEDTLS_KEY_EXCHANGE_RSA_PSK_ENABLED
pass:

Config: !MBEDTLS_KEY_EXCHANGE_RSA_PSK_ENABLED
depends_on:!MBEDTLS_KEY_EXCHANGE_RSA_PSK_ENABLED
pass:

Config: MBEDTLS_LMS_C
depends_on:MBEDTLS_LMS_C
pass:

Config: !MBEDTLS_LMS_C
depends_on:!MBEDTLS_LMS_C
pass:

Config: MBEDTLS_LMS_PRIVATE
depends_on:MBEDTLS_LMS_PRIVATE:MBEDTLS_LMS_C
pass:

Config: !MBEDTLS_LMS_PRIVATE
depends_on:!MBEDTLS_LMS_PRIVATE:MBEDTLS_LMS_C
pass:

Config: MBEDTLS_MD5_C
depends_on:MBEDTLS_MD5_C
pass:

Config: !MBEDTLS_MD5_C
depends_on:!MBEDTLS_MD5_C
pass:

Config: MBEDTLS_MD_C
depends_on:MBEDTLS_MD_C
pass:

Config: !MBEDTLS_MD_C
depends_on:!MBEDTLS_MD_C
pass:

Config: MBEDTLS_MEMORY_BACKTRACE
depends_on:MBEDTLS_MEMORY_BACKTRACE
pass:

Config: !MBEDTLS_MEMORY_BACKTRACE
depends_on:!MBEDTLS_MEMORY_BACKTRACE
pass:

Config: MBEDTLS_MEMORY_BUFFER_ALLOC_C
depends_on:MBEDTLS_MEMORY_BUFFER_ALLOC_C
pass:

Config: !MBEDTLS_MEMORY_BUFFER_ALLOC_C
depends_on:!MBEDTLS_MEMORY_BUFFER_ALLOC_C
pass:

Config: MBEDTLS_MEMORY_DEBUG
depends_on:MBEDTLS_MEMORY_DEBUG
pass:

Config: !MBEDTLS_MEMORY_DEBUG
depends_on:!MBEDTLS_MEMORY_DEBUG
pass:

Config: MBEDTLS_NET_C
depends_on:MBEDTLS_NET_C
pass:

Config: !MBEDTLS_NET_C
depends_on:!MBEDTLS_NET_C
pass:

Config: MBEDTLS_NIST_KW_C
depends_on:MBEDTLS_NIST_KW_C
pass:

Config: !MBEDTLS_NIST_KW_C
depends_on:!MBEDTLS_NIST_KW_C
pass:

Config: MBEDTLS_NO_64BIT_MULTIPLICATION
depends_on:MBEDTLS_NO_64BIT_MULTIPLICATION
pass:

Config: !MBEDTLS_NO_64BIT_MULTIPLICATION
depends_on:!MBEDTLS_NO_64BIT_MULTIPLICATION
pass:

Config: MBEDTLS_NO_DEFAULT_ENTROPY_SOURCES
depends_on:MBEDTLS_NO_DEFAULT_ENTROPY_SOURCES:MBEDTLS_ENTROPY_C
pass:

Config: !MBEDTLS_NO_DEFAULT_ENTROPY_SOURCES
depends_on:!MBEDTLS_NO_DEFAULT_ENTROPY_SOURCES:MBEDTLS_ENTROPY_C
pass:

Config: MBEDTLS_NO_PLATFORM_ENTROPY
depends_on:MBEDTLS_NO_PLATFORM_ENTROPY:MBEDTLS_ENTROPY_C
pass:

Config: !MBEDTLS_NO_PLATFORM_ENTROPY
depends_on:!MBEDTLS_NO_PLATFORM_ENTROPY:MBEDTLS_ENTROPY_C
pass:

Config: MBEDTLS_NO_UDBL_DIVISION
depends_on:MBEDTLS_NO_UDBL_DIVISION
pass:

Config: !MBEDTLS_NO_UDBL_DIVISION
depends_on:!MBEDTLS_NO_UDBL_DIVISION
pass:

Config: MBEDTLS_OID_C
depends_on:MBEDTLS_OID_C
pass:

Config: !MBEDTLS_OID_C
depends_on:!MBEDTLS_OID_C
pass:

Config: MBEDTLS_PADLOCK_C
depends_on:MBEDTLS_PADLOCK_C
pass:

Config: !MBEDTLS_PADLOCK_C
depends_on:!MBEDTLS_PADLOCK_C
pass:

Config: MBEDTLS_PEM_PARSE_C
depends_on:MBEDTLS_PEM_PARSE_C
pass:

Config: !MBEDTLS_PEM_PARSE_C
depends_on:!MBEDTLS_PEM_PARSE_C
pass:

Config: MBEDTLS_PEM_WRITE_C
depends_on:MBEDTLS_PEM_WRITE_C
pass:

Config: !MBEDTLS_PEM_WRITE_C
depends_on:!MBEDTLS_PEM_WRITE_C
pass:

Config: MBEDTLS_PKCS12_C
depends_on:MBEDTLS_PKCS12_C
pass:

Config: !MBEDTLS_PKCS12_C
depends_on:!MBEDTLS_PKCS12_C
pass:

Config: MBEDTLS_PKCS1_V15
depends_on:MBEDTLS_PKCS1_V15:MBEDTLS_RSA_C
pass:

Config: !MBEDTLS_PKCS1_V15
depends_on:!MBEDTLS_PKCS1_V15:MBEDTLS_RSA_C
pass:

Config: MBEDTLS_PKCS1_V21
depends_on:MBEDTLS_PKCS1_V21:MBEDTLS_RSA_C
pass:

Config: !MBEDTLS_PKCS1_V21
depends_on:!MBEDTLS_PKCS1_V21:MBEDTLS_RSA_C
pass:

Config: MBEDTLS_PKCS5_C
depends_on:MBEDTLS_PKCS5_C
pass:

Config: !MBEDTLS_PKCS5_C
depends_on:!MBEDTLS_PKCS5_C
pass:

Config: MBEDTLS_PKCS7_C
depends_on:MBEDTLS_PKCS7_C
pass:

Config: !MBEDTLS_PKCS7_C
depends_on:!MBEDTLS_PKCS7_C
pass:

Config: MBEDTLS_PK_C
depends_on:MBEDTLS_PK_C
pass:

Config: !MBEDTLS_PK_C
depends_on:!MBEDTLS_PK_C
pass:

Config: MBEDTLS_PK_PARSE_C
depends_on:MBEDTLS_PK_PARSE_C
pass:

Config: !MBEDTLS_PK_PARSE_C
depends_on:!MBEDTLS_PK_PARSE_C
pass:

Config: MBEDTLS_PK_PARSE_EC_COMPRESSED
depends_on:MBEDTLS_PK_PARSE_EC_COMPRESSED:MBEDTLS_PK_C:MBEDTLS_PK_HAVE_ECC_KEYS
pass:

Config: !MBEDTLS_PK_PARSE_EC_COMPRESSED
depends_on:!MBEDTLS_PK_PARSE_EC_COMPRESSED:MBEDTLS_PK_C:MBEDTLS_PK_HAVE_ECC_KEYS
pass:

Config: MBEDTLS_PK_PARSE_EC_EXTENDED
depends_on:MBEDTLS_PK_PARSE_EC_EXTENDED:MBEDTLS_PK_C:MBEDTLS_PK_HAVE_ECC_KEYS
pass:

Config: !MBEDTLS_PK_PARSE_EC_EXTENDED
depends_on:!MBEDTLS_PK_PARSE_EC_EXTENDED:MBEDTLS_PK_C:MBEDTLS_PK_HAVE_ECC_KEYS
pass:

Config: MBEDTLS_PK_RSA_ALT_SUPPORT
depends_on:MBEDTLS_PK_RSA_ALT_SUPPORT:MBEDTLS_PK_C
pass:

Config: !MBEDTLS_PK_RSA_ALT_SUPPORT
depends_on:!MBEDTLS_PK_RSA_ALT_SUPPORT:MBEDTLS_PK_C
pass:

Config: MBEDTLS_PK_WRITE_C
depends_on:MBEDTLS_PK_WRITE_C
pass:

Config: !MBEDTLS_PK_WRITE_C
depends_on:!MBEDTLS_PK_WRITE_C
pass:

Config: MBEDTLS_PLATFORM_C
depends_on:MBEDTLS_PLATFORM_C
pass:

Config: !MBEDTLS_PLATFORM_C
depends_on:!MBEDTLS_PLATFORM_C
pass:

Config: MBEDTLS_PLATFORM_EXIT_ALT
depends_on:MBEDTLS_PLATFORM_EXIT_ALT:MBEDTLS_PLATFORM_C
pass:

Config: !MBEDTLS_PLATFORM_EXIT_ALT
depends_on:!MBEDTLS_PLATFORM_EXIT_ALT:MBEDTLS_PLATFORM_C
pass:

Config: MBEDTLS_PLATFORM_FPRINTF_ALT
depends_on:MBEDTLS_PLATFORM_FPRINTF_ALT:MBEDTLS_PLATFORM_C
pass:

Config: !MBEDTLS_PLATFORM_FPRINTF_ALT
depends_on:!MBEDTLS_PLATFORM_FPRINTF_ALT:MBEDTLS_PLATFORM_C
pass:

Config: MBEDTLS_PLATFORM_MEMORY
depends_on:MBEDTLS_PLATFORM_MEMORY:MBEDTLS_PLATFORM_C
pass:

Config: !MBEDTLS_PLATFORM_MEMORY
depends_on:!MBEDTLS_PLATFORM_MEMORY:MBEDTLS_PLATFORM_C
pass:

Config: MBEDTLS_PLATFORM_NO_STD_FUNCTIONS
depends_on:MBEDTLS_PLATFORM_NO_STD_FUNCTIONS:MBEDTLS_PLATFORM_C
pass:

Config: !MBEDTLS_PLATFORM_NO_STD_FUNCTIONS
depends_on:!MBEDTLS_PLATFORM_NO_STD_FUNCTIONS:MBEDTLS_PLATFORM_C
pass:

Config: MBEDTLS_PLATFORM_NV_SEED_ALT
depends_on:MBEDTLS_PLATFORM_NV_SEED_ALT:MBEDTLS_PLATFORM_C
pass:

Config: !MBEDTLS_PLATFORM_NV_SEED_ALT
depends_on:!MBEDTLS_PLATFORM_NV_SEED_ALT:MBEDTLS_PLATFORM_C
pass:

Config: MBEDTLS_PLATFORM_PRINTF_ALT
depends_on:MBEDTLS_PLATFORM_PRINTF_ALT:MBEDTLS_PLATFORM_C
pass:

Config: !MBEDTLS_PLATFORM_PRINTF_ALT
depends_on:!MBEDTLS_PLATFORM_PRINTF_ALT:MBEDTLS_PLATFORM_C
pass:

Config: MBEDTLS_PLATFORM_SETBUF_ALT
depends_on:MBEDTLS_PLATFORM_SETBUF_ALT:MBEDTLS_PLATFORM_C
pass:

Config: !MBEDTLS_PLATFORM_SETBUF_ALT
depends_on:!MBEDTLS_PLATFORM_SETBUF_ALT:MBEDTLS_PLATFORM_C
pass:

Config: MBEDTLS_PLATFORM_SNPRINTF_ALT
depends_on:MBEDTLS_PLATFORM_SNPRINTF_ALT:MBEDTLS_PLATFORM_C
pass:

Config: !MBEDTLS_PLATFORM_SNPRINTF_ALT
depends_on:!MBEDTLS_PLATFORM_SNPRINTF_ALT:MBEDTLS_PLATFORM_C
pass:

Config: MBEDTLS_PLATFORM_TIME_ALT
depends_on:MBEDTLS_PLATFORM_TIME_ALT:MBEDTLS_PLATFORM_C
pass:

Config: !MBEDTLS_PLATFORM_TIME_ALT
depends_on:!MBEDTLS_PLATFORM_TIME_ALT:MBEDTLS_PLATFORM_C
pass:

Config: MBEDTLS_PLATFORM_VSNPRINTF_ALT
depends_on:MBEDTLS_PLATFORM_VSNPRINTF_ALT:MBEDTLS_PLATFORM_C
pass:

Config: !MBEDTLS_PLATFORM_VSNPRINTF_ALT
depends_on:!MBEDTLS_PLATFORM_VSNPRINTF_ALT:MBEDTLS_PLATFORM_C
pass:

Config: MBEDTLS_POLY1305_C
depends_on:MBEDTLS_POLY1305_C
pass:

Config: !MBEDTLS_POLY1305_C
depends_on:!MBEDTLS_POLY1305_C
pass:

Config: MBEDTLS_PSA_ASSUME_EXCLUSIVE_BUFFERS
depends_on:MBEDTLS_PSA_ASSUME_EXCLUSIVE_BUFFERS:MBEDTLS_PSA_CRYPTO_C
pass:

Config: !MBEDTLS_PSA_ASSUME_EXCLUSIVE_BUFFERS
depends_on:!MBEDTLS_PSA_ASSUME_EXCLUSIVE_BUFFERS:MBEDTLS_PSA_CRYPTO_C
pass:

Config: MBEDTLS_PSA_CRYPTO_BUILTIN_KEYS
depends_on:MBEDTLS_PSA_CRYPTO_BUILTIN_KEYS:MBEDTLS_PSA_CRYPTO_C
pass:

Config: !MBEDTLS_PSA_CRYPTO_BUILTIN_KEYS
depends_on:!MBEDTLS_PSA_CRYPTO_BUILTIN_KEYS:MBEDTLS_PSA_CRYPTO_C
pass:

Config: MBEDTLS_PSA_CRYPTO_C
depends_on:MBEDTLS_PSA_CRYPTO_C
pass:

Config: !MBEDTLS_PSA_CRYPTO_C
depends_on:!MBEDTLS_PSA_CRYPTO_C
pass:

Config: MBEDTLS_PSA_CRYPTO_CLIENT
depends_on:MBEDTLS_PSA_CRYPTO_CLIENT:!MBEDTLS_PSA_CRYPTO_C
pass:

Config: !MBEDTLS_PSA_CRYPTO_CLIENT
depends_on:!MBEDTLS_PSA_CRYPTO_CLIENT:!MBEDTLS_PSA_CRYPTO_C
pass:

Config: MBEDTLS_PSA_CRYPTO_CONFIG
depends_on:MBEDTLS_PSA_CRYPTO_CONFIG:MBEDTLS_PSA_CRYPTO_C
pass:

Config: !MBEDTLS_PSA_CRYPTO_CONFIG
depends_on:!MBEDTLS_PSA_CRYPTO_CONFIG:MBEDTLS_PSA_CRYPTO_C
pass:

Config: MBEDTLS_PSA_CRYPTO_EXTERNAL_RNG
depends_on:MBEDTLS_PSA_CRYPTO_EXTERNAL_RNG:MBEDTLS_PSA_CRYPTO_C
pass:

Config: !MBEDTLS_PSA_CRYPTO_EXTERNAL_RNG
depends_on:!MBEDTLS_PSA_CRYPTO_EXTERNAL_RNG:MBEDTLS_PSA_CRYPTO_C
pass:

Config: MBEDTLS_PSA_CRYPTO_KEY_ID_ENCODES_OWNER
depends_on:MBEDTLS_PSA_CRYPTO_KEY_ID_ENCODES_OWNER:MBEDTLS_PSA_CRYPTO_C
pass:

Config: !MBEDTLS_PSA_CRYPTO_KEY_ID_ENCODES_OWNER
depends_on:!MBEDTLS_PSA_CRYPTO_KEY_ID_ENCODES_OWNER:MBEDTLS_PSA_CRYPTO_C
pass:

Config: MBEDTLS_PSA_CRYPTO_SE_C
depends_on:MBEDTLS_PSA_CRYPTO_SE_C
pass:

Config: !MBEDTLS_PSA_CRYPTO_SE_C
depends_on:!MBEDTLS_PSA_CRYPTO_SE_C
pass:

Config: MBEDTLS_PSA_CRYPTO_SPM
depends_on:MBEDTLS_PSA_CRYPTO_SPM:MBEDTLS_PSA_CRYPTO_C
pass:

Config: !MBEDTLS_PSA_CRYPTO_SPM
depends_on:!MBEDTLS_PSA_CRYPTO_SPM:MBEDTLS_PSA_CRYPTO_C
pass:

Config: MBEDTLS_PSA_CRYPTO_STORAGE_C
depends_on:MBEDTLS_PSA_CRYPTO_STORAGE_C
pass:

Config: !MBEDTLS_PSA_CRYPTO_STORAGE_C
depends_on:!MBEDTLS_PSA_CRYPTO_STORAGE_C
pass:

Config: MBEDTLS_PSA_INJECT_ENTROPY
depends_on:MBEDTLS_PSA_INJECT_ENTROPY:MBEDTLS_PSA_CRYPTO_C
pass:

Config: !MBEDTLS_PSA_INJECT_ENTROPY
depends_on:!MBEDTLS_PSA_INJECT_ENTROPY:MBEDTLS_PSA_CRYPTO_C
pass:

Config: MBEDTLS_PSA_ITS_FILE_C
depends_on:MBEDTLS_PSA_ITS_FILE_C
pass:

Config: !MBEDTLS_PSA_ITS_FILE_C
depends_on:!MBEDTLS_PSA_ITS_FILE_C
pass:

Config: MBEDTLS_PSA_KEY_STORE_DYNAMIC
depends_on:MBEDTLS_PSA_KEY_STORE_DYNAMIC
pass:

Config: !MBEDTLS_PSA_KEY_STORE_DYNAMIC
depends_on:!MBEDTLS_PSA_KEY_STORE_DYNAMIC
pass:

Config: MBEDTLS_PSA_P256M_DRIVER_ENABLED
depends_on:MBEDTLS_PSA_P256M_DRIVER_ENABLED
pass:

Config: !MBEDTLS_PSA_P256M_DRIVER_ENABLED
depends_on:!MBEDTLS_PSA_P256M_DRIVER_ENABLED
pass:

Config: MBEDTLS_PSA_STATIC_KEY_SLOTS
depends_on:MBEDTLS_PSA_STATIC_KEY_SLOTS
pass:

Config: !MBEDTLS_PSA_STATIC_KEY_SLOTS
depends_on:!MBEDTLS_PSA_STATIC_KEY_SLOTS
pass:

Config: MBEDTLS_RIPEMD160_C
depends_on:MBEDTLS_RIPEMD160_C
pass:

Config: !MBEDTLS_RIPEMD160_C
depends_on:!MBEDTLS_RIPEMD160_C
pass:

Config: MBEDTLS_RSA_C
depends_on:MBEDTLS_RSA_C
pass:

Config: !MBEDTLS_RSA_C
depends_on:!MBEDTLS_RSA_C
pass:

Config: MBEDTLS_RSA_NO_CRT
depends_on:MBEDTLS_RSA_NO_CRT:MBEDTLS_RSA_C
pass:

Config: !MBEDTLS_RSA_NO_CRT
depends_on:!MBEDTLS_RSA_NO_CRT:MBEDTLS_RSA_C
pass:

Config: MBEDTLS_SELF_TEST
depends_on:MBEDTLS_SELF_TEST
pass:

Config: !MBEDTLS_SELF_TEST
depends_on:!MBEDTLS_SELF_TEST
pass:

Config: MBEDTLS_SHA1_C
depends_on:MBEDTLS_SHA1_C
pass:

Config: !MBEDTLS_SHA1_C
depends_on:!MBEDTLS_SHA1_C
pass:

Config: MBEDTLS_SHA224_C
depends_on:MBEDTLS_SHA224_C
pass:

Config: !MBEDTLS_SHA224_C
depends_on:!MBEDTLS_SHA224_C
pass:

Config: MBEDTLS_SHA256_C
depends_on:MBEDTLS_SHA256_C
pass:

Config: !MBEDTLS_SHA256_C
depends_on:!MBEDTLS_SHA256_C
pass:

Config: MBEDTLS_SHA256_SMALLER
depends_on:MBEDTLS_SHA256_SMALLER:MBEDTLS_SHA256_C
pass:

Config: !MBEDTLS_SHA256_SMALLER
depends_on:!MBEDTLS_SHA256_SMALLER:MBEDTLS_SHA256_C
pass:

Config: MBEDTLS_SHA256_USE_A64_CRYPTO_IF_PRESENT
depends_on:MBEDTLS_SHA256_USE_A64_CRYPTO_IF_PRESENT:MBEDTLS_SHA256_C
pass:

Config: !MBEDTLS_SHA256_USE_A64_CRYPTO_IF_PRESENT
depends_on:!MBEDTLS_SHA256_USE_A64_CRYPTO_IF_PRESENT:MBEDTLS_SHA256_C
pass:

Config: MBEDTLS_SHA256_USE_A64_CRYPTO_ONLY
depends_on:MBEDTLS_SHA256_USE_A64_CRYPTO_ONLY:MBEDTLS_SHA256_C
pass:

Config: !MBEDTLS_SHA256_USE_A64_CRYPTO_ONLY
depends_on:!MBEDTLS_SHA256_USE_A64_CRYPTO_ONLY:MBEDTLS_SHA256_C
pass:

Config: MBEDTLS_SHA256_USE_ARMV8_A_CRYPTO_IF_PRESENT
depends_on:MBEDTLS_SHA256_USE_ARMV8_A_CRYPTO_IF_PRESENT:MBEDTLS_SHA256_C
pass:

Config: !MBEDTLS_SHA256_USE_ARMV8_A_CRYPTO_IF_PRESENT
depends_on:!MBEDTLS_SHA256_USE_ARMV8_A_CRYPTO_IF_PRESENT:MBEDTLS_SHA256_C
pass:

Config: MBEDTLS_SHA256_USE_ARMV8_A_CRYPTO_ONLY
depends_on:MBEDTLS_SHA256_USE_ARMV8_A_CRYPTO_ONLY:MBEDTLS_SHA256_C
pass:

Config: !MBEDTLS_SHA256_USE_ARMV8_A_CRYPTO_ONLY
depends_on:!MBEDTLS_SHA256_USE_ARMV8_A_CRYPTO_ONLY:MBEDTLS_SHA256_C
pass:

Config: MBEDTLS_SHA384_C
depends_on:MBEDTLS_SHA384_C
pass:

Config: !MBEDTLS_SHA384_C
depends_on:!MBEDTLS_SHA384_C
pass:

Config: MBEDTLS_SHA3_C
depends_on:MBEDTLS_SHA3_C
pass:

Config: !MBEDTLS_SHA3_C
depends_on:!MBEDTLS_SHA3_C
pass:

Config: MBEDTLS_SHA512_C
depends_on:MBEDTLS_SHA512_C
pass:

Config: !MBEDTLS_SHA512_C
depends_on:!MBEDTLS_SHA512_C
pass:

Config: MBEDTLS_SHA512_SMALLER
depends_on:MBEDTLS_SHA512_SMALLER:MBEDTLS_SHA512_C
pass:

Config: !MBEDTLS_SHA512_SMALLER
depends_on:!MBEDTLS_SHA512_SMALLER:MBEDTLS_SHA512_C
pass:

Config: MBEDTLS_SHA512_USE_A64_CRYPTO_IF_PRESENT
depends_on:MBEDTLS_SHA512_USE_A64_CRYPTO_IF_PRESENT:MBEDTLS_SHA512_C
pass:

Config: !MBEDTLS_SHA512_USE_A64_CRYPTO_IF_PRESENT
depends_on:!MBEDTLS_SHA512_USE_A64_CRYPTO_IF_PRESENT:MBEDTLS_SHA512_C
pass:

Config: MBEDTLS_SHA512_USE_A64_CRYPTO_ONLY
depends_on:MBEDTLS_SHA512_USE_A64_CRYPTO_ONLY:MBEDTLS_SHA512_C
pass:

Config: !MBEDTLS_SHA512_USE_A64_CRYPTO_ONLY
depends_on:!MBEDTLS_SHA512_USE_A64_CRYPTO_ONLY:MBEDTLS_SHA512_C
pass:

Config: MBEDTLS_SSL_ALL_ALERT_MESSAGES
depends_on:MBEDTLS_SSL_ALL_ALERT_MESSAGES:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: !MBEDTLS_SSL_ALL_ALERT_MESSAGES
depends_on:!MBEDTLS_SSL_ALL_ALERT_MESSAGES:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: MBEDTLS_SSL_ALPN
depends_on:MBEDTLS_SSL_ALPN:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: !MBEDTLS_SSL_ALPN
depends_on:!MBEDTLS_SSL_ALPN:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: MBEDTLS_SSL_ASYNC_PRIVATE
depends_on:MBEDTLS_SSL_ASYNC_PRIVATE:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: !MBEDTLS_SSL_ASYNC_PRIVATE
depends_on:!MBEDTLS_SSL_ASYNC_PRIVATE:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: MBEDTLS_SSL_CACHE_C
depends_on:MBEDTLS_SSL_CACHE_C
pass:

Config: !MBEDTLS_SSL_CACHE_C
depends_on:!MBEDTLS_SSL_CACHE_C
pass:

Config: MBEDTLS_SSL_CLI_ALLOW_WEAK_CERTIFICATE_VERIFICATION_WITHOUT_HOSTNAME
depends_on:MBEDTLS_SSL_CLI_ALLOW_WEAK_CERTIFICATE_VERIFICATION_WITHOUT_HOSTNAME:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: !MBEDTLS_SSL_CLI_ALLOW_WEAK_CERTIFICATE_VERIFICATION_WITHOUT_HOSTNAME
depends_on:!MBEDTLS_SSL_CLI_ALLOW_WEAK_CERTIFICATE_VERIFICATION_WITHOUT_HOSTNAME:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: MBEDTLS_SSL_CLI_C
depends_on:MBEDTLS_SSL_CLI_C
pass:

Config: !MBEDTLS_SSL_CLI_C
depends_on:!MBEDTLS_SSL_CLI_C
pass:

Config: MBEDTLS_SSL_CONTEXT_SERIALIZATION
depends_on:MBEDTLS_SSL_CONTEXT_SERIALIZATION:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: !MBEDTLS_SSL_CONTEXT_SERIALIZATION
depends_on:!MBEDTLS_SSL_CONTEXT_SERIALIZATION:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: MBEDTLS_SSL_COOKIE_C
depends_on:MBEDTLS_SSL_COOKIE_C
pass:

Config: !MBEDTLS_SSL_COOKIE_C
depends_on:!MBEDTLS_SSL_COOKIE_C
pass:

Config: MBEDTLS_SSL_DEBUG_ALL
depends_on:MBEDTLS_SSL_DEBUG_ALL:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: !MBEDTLS_SSL_DEBUG_ALL
depends_on:!MBEDTLS_SSL_DEBUG_ALL:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: MBEDTLS_SSL_DTLS_ANTI_REPLAY
depends_on:MBEDTLS_SSL_DTLS_ANTI_REPLAY:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_DTLS
pass:

Config: !MBEDTLS_SSL_DTLS_ANTI_REPLAY
depends_on:!MBEDTLS_SSL_DTLS_ANTI_REPLAY:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_DTLS
pass:

Config: MBEDTLS_SSL_DTLS_CLIENT_PORT_REUSE
depends_on:MBEDTLS_SSL_DTLS_CLIENT_PORT_REUSE:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_DTLS
pass:

Config: !MBEDTLS_SSL_DTLS_CLIENT_PORT_REUSE
depends_on:!MBEDTLS_SSL_DTLS_CLIENT_PORT_REUSE:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_DTLS
pass:

Config: MBEDTLS_SSL_DTLS_CONNECTION_ID
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_DTLS
pass:

Config: !MBEDTLS_SSL_DTLS_CONNECTION_ID
depends_on:!MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_DTLS
pass:

Config: MBEDTLS_SSL_DTLS_HELLO_VERIFY
depends_on:MBEDTLS_SSL_DTLS_HELLO_VERIFY:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_DTLS
pass:

Config: !MBEDTLS_SSL_DTLS_HELLO_VERIFY
depends_on:!MBEDTLS_SSL_DTLS_HELLO_VERIFY:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_DTLS
pass:

Config: MBEDTLS_SSL_DTLS_SRTP
depends_on:MBEDTLS_SSL_DTLS_SRTP:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_DTLS
pass:

Config: !MBEDTLS_SSL_DTLS_SRTP
depends_on:!MBEDTLS_SSL_DTLS_SRTP:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_DTLS
pass:

Config: MBEDTLS_SSL_EARLY_DATA
depends_on:MBEDTLS_SSL_EARLY_DATA:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_TLS1_3
pass:

Config: !MBEDTLS_SSL_EARLY_DATA
depends_on:!MBEDTLS_SSL_EARLY_DATA:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_TLS1_3
pass:

Config: MBEDTLS_SSL_ENCRYPT_THEN_MAC
depends_on:MBEDTLS_SSL_ENCRYPT_THEN_MAC:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: !MBEDTLS_SSL_ENCRYPT_THEN_MAC
depends_on:!MBEDTLS_SSL_ENCRYPT_THEN_MAC:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: MBEDTLS_SSL_EXTENDED_MASTER_SECRET
depends_on:MBEDTLS_SSL_EXTENDED_MASTER_SECRET:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: !MBEDTLS_SSL_EXTENDED_MASTER_SECRET
depends_on:!MBEDTLS_SSL_EXTENDED_MASTER_SECRET:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: MBEDTLS_SSL_KEEP_PEER_CERTIFICATE
depends_on:MBEDTLS_SSL_KEEP_PEER_CERTIFICATE:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: !MBEDTLS_SSL_KEEP_PEER_CERTIFICATE
depends_on:!MBEDTLS_SSL_KEEP_PEER_CERTIFICATE:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: MBEDTLS_SSL_MAX_FRAGMENT_LENGTH
depends_on:MBEDTLS_SSL_MAX_FRAGMENT_LENGTH:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: !MBEDTLS_SSL_MAX_FRAGMENT_LENGTH
depends_on:!MBEDTLS_SSL_MAX_FRAGMENT_LENGTH:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: MBEDTLS_SSL_PROTO_DTLS
depends_on:MBEDTLS_SSL_PROTO_DTLS:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: !MBEDTLS_SSL_PROTO_DTLS
depends_on:!MBEDTLS_SSL_PROTO_DTLS:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: MBEDTLS_SSL_PROTO_TLS1_2
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: !MBEDTLS_SSL_PROTO_TLS1_2
depends_on:!MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: MBEDTLS_SSL_PROTO_TLS1_3
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: !MBEDTLS_SSL_PROTO_TLS1_3
depends_on:!MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: MBEDTLS_SSL_RECORD_SIZE_LIMIT
depends_on:MBEDTLS_SSL_RECORD_SIZE_LIMIT:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: !MBEDTLS_SSL_RECORD_SIZE_LIMIT
depends_on:!MBEDTLS_SSL_RECORD_SIZE_LIMIT:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: MBEDTLS_SSL_RENEGOTIATION
depends_on:MBEDTLS_SSL_RENEGOTIATION:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: !MBEDTLS_SSL_RENEGOTIATION
depends_on:!MBEDTLS_SSL_RENEGOTIATION:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: MBEDTLS_SSL_SERVER_NAME_INDICATION
depends_on:MBEDTLS_SSL_SERVER_NAME_INDICATION:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: !MBEDTLS_SSL_SERVER_NAME_INDICATION
depends_on:!MBEDTLS_SSL_SERVER_NAME_INDICATION:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: MBEDTLS_SSL_SESSION_TICKETS
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: !MBEDTLS_SSL_SESSION_TICKETS
depends_on:!MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: MBEDTLS_SSL_SRV_C
depends_on:MBEDTLS_SSL_SRV_C
pass:

Config: !MBEDTLS_SSL_SRV_C
depends_on:!MBEDTLS_SSL_SRV_C
pass:

Config: MBEDTLS_SSL_TICKET_C
depends_on:MBEDTLS_SSL_TICKET_C
pass:

Config: !MBEDTLS_SSL_TICKET_C
depends_on:!MBEDTLS_SSL_TICKET_C
pass:

Config: MBEDTLS_SSL_TLS1_3_COMPATIBILITY_MODE
depends_on:MBEDTLS_SSL_TLS1_3_COMPATIBILITY_MODE:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_TLS1_3
pass:

Config: !MBEDTLS_SSL_TLS1_3_COMPATIBILITY_MODE
depends_on:!MBEDTLS_SSL_TLS1_3_COMPATIBILITY_MODE:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_TLS1_3
pass:

Config: MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED
depends_on:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_TLS1_3
pass:

Config: !MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED
depends_on:!MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_TLS1_3
pass:

Config: MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_PSK_ENABLED
depends_on:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_PSK_ENABLED:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_TLS1_3
pass:

Config: !MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_PSK_ENABLED
depends_on:!MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_PSK_ENABLED:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_TLS1_3
pass:

Config: MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_PSK_EPHEMERAL_ENABLED
depends_on:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_PSK_EPHEMERAL_ENABLED:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_TLS1_3
pass:

Config: !MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_PSK_EPHEMERAL_ENABLED
depends_on:!MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_PSK_EPHEMERAL_ENABLED:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_TLS1_3
pass:

Config: MBEDTLS_SSL_TLS_C
depends_on:MBEDTLS_SSL_TLS_C
pass:

Config: !MBEDTLS_SSL_TLS_C
depends_on:!MBEDTLS_SSL_TLS_C
pass:

Config: MBEDTLS_SSL_VARIABLE_BUFFER_LENGTH
depends_on:MBEDTLS_SSL_VARIABLE_BUFFER_LENGTH:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: !MBEDTLS_SSL_VARIABLE_BUFFER_LENGTH
depends_on:!MBEDTLS_SSL_VARIABLE_BUFFER_LENGTH:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SRV_C
pass:

Config: MBEDTLS_TEST_CONSTANT_FLOW_MEMSAN
depends_on:MBEDTLS_TEST_CONSTANT_FLOW_MEMSAN
pass:

Config: !MBEDTLS_TEST_CONSTANT_FLOW_MEMSAN
depends_on:!MBEDTLS_TEST_CONSTANT_FLOW_MEMSAN
pass:

Config: MBEDTLS_TEST_CONSTANT_FLOW_VALGRIND
depends_on:MBEDTLS_TEST_CONSTANT_FLOW_VALGRIND
pass:

Config: !MBEDTLS_TEST_CONSTANT_FLOW_VALGRIND
depends_on:!MBEDTLS_TEST_CONSTANT_FLOW_VALGRIND
pass:

Config: MBEDTLS_TEST_HOOKS
depends_on:MBEDTLS_TEST_HOOKS
pass:

Config: !MBEDTLS_TEST_HOOKS
depends_on:!MBEDTLS_TEST_HOOKS
pass:

Config: MBEDTLS_THREADING_C
depends_on:MBEDTLS_THREADING_C
pass:

Config: !MBEDTLS_THREADING_C
depends_on:!MBEDTLS_THREADING_C
pass:

Config: MBEDTLS_THREADING_PTHREAD
depends_on:MBEDTLS_THREADING_PTHREAD:MBEDTLS_THREADING_C
pass:

Config: !MBEDTLS_THREADING_PTHREAD
depends_on:!MBEDTLS_THREADING_PTHREAD:MBEDTLS_THREADING_C
pass:

Config: MBEDTLS_TIMING_C
depends_on:MBEDTLS_TIMING_C
pass:

Config: !MBEDTLS_TIMING_C
depends_on:!MBEDTLS_TIMING_C
pass:

Config: MBEDTLS_USE_PSA_CRYPTO
depends_on:MBEDTLS_USE_PSA_CRYPTO
pass:

Config: !MBEDTLS_USE_PSA_CRYPTO
depends_on:!MBEDTLS_USE_PSA_CRYPTO
pass:

Config: MBEDTLS_VERSION_C
depends_on:MBEDTLS_VERSION_C
pass:

Config: !MBEDTLS_VERSION_C
depends_on:!MBEDTLS_VERSION_C
pass:

Config: MBEDTLS_VERSION_FEATURES
depends_on:MBEDTLS_VERSION_FEATURES:MBEDTLS_VERSION_C
pass:

Config: !MBEDTLS_VERSION_FEATURES
depends_on:!MBEDTLS_VERSION_FEATURES:MBEDTLS_VERSION_C
pass:

Config: MBEDTLS_X509_CREATE_C
depends_on:MBEDTLS_X509_CREATE_C
pass:

Config: !MBEDTLS_X509_CREATE_C
depends_on:!MBEDTLS_X509_CREATE_C
pass:

Config: MBEDTLS_X509_CRL_PARSE_C
depends_on:MBEDTLS_X509_CRL_PARSE_C
pass:

Config: !MBEDTLS_X509_CRL_PARSE_C
depends_on:!MBEDTLS_X509_CRL_PARSE_C
pass:

Config: MBEDTLS_X509_CRT_PARSE_C
depends_on:MBEDTLS_X509_CRT_PARSE_C
pass:

Config: !MBEDTLS_X509_CRT_PARSE_C
depends_on:!MBEDTLS_X509_CRT_PARSE_C
pass:

Config: MBEDTLS_X509_CRT_WRITE_C
depends_on:MBEDTLS_X509_CRT_WRITE_C
pass:

Config: !MBEDTLS_X509_CRT_WRITE_C
depends_on:!MBEDTLS_X509_CRT_WRITE_C
pass:

Config: MBEDTLS_X509_CSR_PARSE_C
depends_on:MBEDTLS_X509_CSR_PARSE_C
pass:

Config: !MBEDTLS_X509_CSR_PARSE_C
depends_on:!MBEDTLS_X509_CSR_PARSE_C
pass:

Config: MBEDTLS_X509_CSR_WRITE_C
depends_on:MBEDTLS_X509_CSR_WRITE_C
pass:

Config: !MBEDTLS_X509_CSR_WRITE_C
depends_on:!MBEDTLS_X509_CSR_WRITE_C
pass:

Config: MBEDTLS_X509_REMOVE_INFO
depends_on:MBEDTLS_X509_REMOVE_INFO
pass:

Config: !MBEDTLS_X509_REMOVE_INFO
depends_on:!MBEDTLS_X509_REMOVE_INFO
pass:

Config: MBEDTLS_X509_RSASSA_PSS_SUPPORT
depends_on:MBEDTLS_X509_RSASSA_PSS_SUPPORT
pass:

Config: !MBEDTLS_X509_RSASSA_PSS_SUPPORT
depends_on:!MBEDTLS_X509_RSASSA_PSS_SUPPORT
pass:

Config: MBEDTLS_X509_TRUSTED_CERTIFICATE_CALLBACK
depends_on:MBEDTLS_X509_TRUSTED_CERTIFICATE_CALLBACK
pass:

Config: !MBEDTLS_X509_TRUSTED_CERTIFICATE_CALLBACK
depends_on:!MBEDTLS_X509_TRUSTED_CERTIFICATE_CALLBACK
pass:

Config: MBEDTLS_X509_USE_C
depends_on:MBEDTLS_X509_USE_C
pass:

Config: !MBEDTLS_X509_USE_C
depends_on:!MBEDTLS_X509_USE_C
pass:

# End of automatically generated file.
