# The following two manually crafted test cases are redundant with
# systematically generated test cases, but useful to have as an anchor when
# debugging changes to the test code or to the test case generation.

PSA storage read: AES-GCM+CTR
depends_on:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_GCM:PSA_WANT_ALG_CTR
key_storage_read:PSA_KEY_LIFETIME_PERSISTENT:PSA_KEY_TYPE_AES:128:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_ENCRYPT:PSA_ALG_GCM:PSA_ALG_CTR:"404142434445464748494a4b4c4d4e4f":"505341004b45590000000000010000000024800001010000000250050010c00410000000404142434445464748494a4b4c4d4e4f":1

PSA storage save: AES-GCM+CTR
depends_on:PSA_WANT_KEY_TYPE_AES
key_storage_save:PSA_KEY_LIFETIME_PERSISTENT:PSA_KEY_TYPE_AES:128:PSA_KEY_USAGE_EXPORT | PSA_KEY_USAGE_ENCRYPT:PSA_ALG_GCM:PSA_ALG_CTR:"404142434445464748494a4b4c4d4e4f":"505341004b45590000000000010000000024800001010000000250050010c00410000000404142434445464748494a4b4c4d4e4f"

# Create a persistent key which is larger than MBEDTLS_PSA_STATIC_KEY_SLOT_BUFFER_SIZE
# so that when psa_get_key_attributes() tries to load it from the storage it will fail.
PSA storage read: key larger than MBEDTLS_PSA_STATIC_KEY_SLOT_BUFFER_SIZE
depends_on:PSA_WANT_KEY_TYPE_RAW_DATA:MBEDTLS_PSA_STATIC_KEY_SLOTS
key_storage_read:PSA_KEY_LIFETIME_PERSISTENT:PSA_KEY_TYPE_RAW_DATA:PSA_BYTES_TO_BITS(MBEDTLS_PSA_STATIC_KEY_SLOT_BUFFER_SIZE + 1):PSA_KEY_USAGE_EXPORT:PSA_ALG_NONE:PSA_ALG_NONE:"":"":TEST_FLAG_OVERSIZED_KEY
