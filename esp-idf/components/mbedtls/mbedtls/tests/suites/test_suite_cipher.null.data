NULL Encrypt and decrypt 0 bytes
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf:MBEDTLS_CIPHER_NULL:"NULL":0:0:-1

NULL Encrypt and decrypt 1 bytes
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf:MBEDTLS_CIPHER_NULL:"NULL":0:1:-1

NULL Encrypt and decrypt 2 bytes
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf:MBEDTLS_CIPHER_NULL:"NULL":0:2:-1

NULL Encrypt and decrypt 7 bytes
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf:MBEDTLS_CIPHER_NULL:"NULL":0:7:-1

NULL Encrypt and decrypt 8 bytes
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf:MBEDTLS_CIPHER_NULL:"NULL":0:8:-1

NULL Encrypt and decrypt 9 bytes
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf:MBEDTLS_CIPHER_NULL:"NULL":0:9:-1

NULL Encrypt and decrypt 15 bytes
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf:MBEDTLS_CIPHER_NULL:"NULL":0:15:-1

NULL Encrypt and decrypt 16 bytes
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf:MBEDTLS_CIPHER_NULL:"NULL":0:16:-1

NULL Encrypt and decrypt 31 bytes
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf:MBEDTLS_CIPHER_NULL:"NULL":0:31:-1

NULL Encrypt and decrypt 32 bytes
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf:MBEDTLS_CIPHER_NULL:"NULL":0:32:-1

NULL Encrypt and decrypt 33 bytes
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf:MBEDTLS_CIPHER_NULL:"NULL":0:33:-1

NULL Encrypt and decrypt 47 bytes
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf:MBEDTLS_CIPHER_NULL:"NULL":0:47:-1

NULL Encrypt and decrypt 48 bytes
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf:MBEDTLS_CIPHER_NULL:"NULL":0:48:-1

NULL Encrypt and decrypt 49 bytes
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf:MBEDTLS_CIPHER_NULL:"NULL":0:49:-1

NULL Encrypt and decrypt 1 bytes in multiple parts 1
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf_multipart:MBEDTLS_CIPHER_NULL:0:1:0:-1:1:0:1:0

NULL Encrypt and decrypt 1 bytes in multiple parts 2
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf_multipart:MBEDTLS_CIPHER_NULL:0:0:1:-1:0:1:0:1

NULL Encrypt and decrypt 16 bytes in multiple parts 1
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf_multipart:MBEDTLS_CIPHER_NULL:0:16:0:-1:16:0:16:0

NULL Encrypt and decrypt 16 bytes in multiple parts 2
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf_multipart:MBEDTLS_CIPHER_NULL:0:0:16:-1:0:16:0:16

NULL Encrypt and decrypt 16 bytes in multiple parts 3
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf_multipart:MBEDTLS_CIPHER_NULL:0:1:15:-1:1:15:1:15

NULL Encrypt and decrypt 16 bytes in multiple parts 4
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf_multipart:MBEDTLS_CIPHER_NULL:0:15:1:-1:15:1:15:1

NULL Encrypt and decrypt 22 bytes in multiple parts 1 [#1]
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf_multipart:MBEDTLS_CIPHER_NULL:0:15:7:-1:15:7:15:7

NULL Encrypt and decrypt 22 bytes in multiple parts 1 [#2]
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf_multipart:MBEDTLS_CIPHER_NULL:0:16:6:-1:16:6:16:6

NULL Encrypt and decrypt 22 bytes in multiple parts 1 [#3]
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf_multipart:MBEDTLS_CIPHER_NULL:0:17:6:-1:17:6:17:6

NULL Encrypt and decrypt 32 bytes in multiple parts 1
depends_on:MBEDTLS_CIPHER_NULL_CIPHER
enc_dec_buf_multipart:MBEDTLS_CIPHER_NULL:0:16:16:-1:16:16:16:16
