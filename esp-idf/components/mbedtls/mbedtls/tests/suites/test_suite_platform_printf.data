# The test cases for printf and integers have two purposes: they exercise
# the printf function family, and they exercise the passing of integers
# and strings through the test framework.

printf "%d", 0
printf_int:"%d":0:"0"

printf "%d", -0
printf_int:"%d":-0:"0"

printf "%d", 0x0
printf_int:"%d":0x0:"0"

printf "%d", 0x00
printf_int:"%d":0x00:"0"

printf "%d", 0x000000000000000000000000000000000000000000
printf_int:"%d":0x000000000000000000000000000000000000000000:"0"

printf "%d", -0x0
printf_int:"%d":-0x0:"0"

printf "%d", 1
printf_int:"%d":1:"1"

printf "%d", 0x1
printf_int:"%d":0x1:"1"

printf "%d", 0x0000000000000000000000000000000000000000001
printf_int:"%d":0x0000000000000000000000000000000000000000001:"1"

printf "%d", -1
printf_int:"%d":-1:"-1"

printf "%d", -0x1
printf_int:"%d":-0x1:"-1"

printf "%d", -0x0000000000000000000000000000000000000000001
printf_int:"%d":-0x0000000000000000000000000000000000000000001:"-1"

printf "%d", 2147483647
printf_int:"%d":2147483647:"2147483647"

printf "%d", 0x7fffffff
printf_int:"%d":0x7fffffff:"2147483647"

printf "%d", -2147483647
printf_int:"%d":-2147483647:"-2147483647"

printf "%d", -0x7fffffff
printf_int:"%d":-0x7fffffff:"-2147483647"

printf "%d", -2147483648
printf_int:"%d":-2147483648:"-2147483648"

printf "%d", -0x80000000
printf_int:"%d":-0x80000000:"-2147483648"

# Test that LONG_MAX is coming out untruncated through the test framework.
printf "%lx", LONG_MAX
printf_long_max:"%lx":LONG_MAX

# The next few test cases exercise how the test framework handles special
# characters in strings.
printf "%c%c", SPACE, SPACE
printf_char2:"%c%c":SPACE_CHAR:SPACE_CHAR:"  "

printf "%c%c", NEWLINE, SPACE
printf_char2:"%c%c":NEWLINE_CHAR:SPACE_CHAR:"\n "

printf "%c%c", DOUBLE QUOTE, SPACE
printf_char2:"%c%c":DOUBLE_QUOTE_CHAR:SPACE_CHAR:"\" "

printf "%c%c", COLON, SPACE
printf_char2:"%c%c":COLON_CHAR:SPACE_CHAR:"\: "

printf "%c%c", QUESTION, SPACE
printf_char2:"%c%c":QUESTION_CHAR:SPACE_CHAR:"? "

printf "%c%c", BACKSLASH, SPACE
printf_char2:"%c%c":BACKSLASH_CHAR:SPACE_CHAR:"\\ "

printf "%c%c", SPACE, BACKSLASH
printf_char2:"%c%c":SPACE_CHAR:BACKSLASH_CHAR:" \\"

printf "%c%c", COLON, COLON
printf_char2:"%c%c":COLON_CHAR:COLON_CHAR:"\:\:"

printf "%c%c", COLON, NEWLINE
printf_char2:"%c%c":COLON_CHAR:NEWLINE_CHAR:"\:\n"

printf "%c%c", QUESTION, QUESTION
printf_char2:"%c%c":QUESTION_CHAR:QUESTION_CHAR:"??"

printf "%c%c", QUESTION, NEWLINE
printf_char2:"%c%c":QUESTION_CHAR:NEWLINE_CHAR:"?\n"

printf "%c%c", BACKSLASH, NEWLINE
printf_char2:"%c%c":BACKSLASH_CHAR:NEWLINE_CHAR:"\\\n"

printf "%c%c", BACKSLASH, DOUBLE QUOTE
printf_char2:"%c%c":BACKSLASH_CHAR:DOUBLE_QUOTE_CHAR:"\\\""

printf "%c%c", BACKSLASH, COLON
printf_char2:"%c%c":BACKSLASH_CHAR:COLON_CHAR:"\\\:"

printf "%c%c", BACKSLASH, QUESTION
printf_char2:"%c%c":BACKSLASH_CHAR:QUESTION_CHAR:"\\?"

printf "%c%c", BACKSLASH, BACKSLASH
printf_char2:"%c%c":BACKSLASH_CHAR:BACKSLASH_CHAR:"\\\\"

printf "%c%c", BACKSLASH, n
printf_char2:"%c%c":BACKSLASH_CHAR:LOWERCASE_N_CHAR:"\\n"
