Attempt to register multiple PSKs
test_multiple_psks:

Attempt to register multiple PSKS, incl. opaque PSK, #0
test_multiple_psks_opaque:0

Attempt to register multiple PSKs, incl. opaque PSK, #1
test_multiple_psks_opaque:1

Attempt to register multiple PSKs, incl. opaque PSK, #2
test_multiple_psks_opaque:2

Test callback buffer sanity
test_callback_buffer_sanity:

Callback buffer test: Exercise simple write/read
test_callback_buffer:50:25:25:25:25:0:0:0:0

Callback buffer test: Filling up the buffer
test_callback_buffer:50:50:50:50:50:0:0:0:0

Callback buffer test: Filling up the buffer in two steps
test_callback_buffer:50:20:20:0:0:30:30:50:50

Callback buffer test: Reading out the buffer in two steps
test_callback_buffer:50:50:50:30:30:0:0:20:20

Callback buffer test: Data wraps in buffer
test_callback_buffer:50:45:45:10:10:10:10:45:45

Callback buffer test: Data starts at the end
test_callback_buffer:50:50:50:49:49:10:10:11:11

Callback buffer test: Can write less than requested
test_callback_buffer:50:75:50:30:30:25:25:45:45

Callback buffer test: Can read less than requested
test_callback_buffer:50:25:25:30:25:5:5:5:5

Callback buffer test: Writing to full buffer
test_callback_buffer:50:50:50:0:0:10:0:60:50

Callback buffer test: Reading from empty buffer
test_callback_buffer:50:0:0:10:0:0:0:0:0

Test mock socket sanity
ssl_mock_sanity:

Test mock blocking TCP connection
ssl_mock_tcp:1

Test mock non-blocking TCP connection
ssl_mock_tcp:0

Test mock blocking TCP connection (interleaving)
ssl_mock_tcp_interleaving:1

Test mock non-blocking TCP connection (interleaving)
ssl_mock_tcp_interleaving:0

Message queue - sanity
ssl_message_queue_sanity:

Message queue - basic test
ssl_message_queue_basic:

Message queue - overflow/underflow
ssl_message_queue_overflow_underflow:

Message queue - interleaved
ssl_message_queue_interleaved:

Message queue - insufficient buffer
ssl_message_queue_insufficient_buffer:

Message transport mock - uninitialized structures
ssl_message_mock_uninitialized:

Message transport mock - basic test
ssl_message_mock_basic:

Message transport mock - queue overflow/underflow
ssl_message_mock_queue_overflow_underflow:

Message transport mock - socket overflow
ssl_message_mock_socket_overflow:

Message transport mock - truncated message
ssl_message_mock_truncated:

Message transport mock - socket read error
ssl_message_mock_socket_read_error:

Message transport mock - one-way interleaved sends/reads
ssl_message_mock_interleaved_one_way:

Message transport mock - two-way interleaved sends/reads
ssl_message_mock_interleaved_two_ways:

Test mbedtls_endpoint sanity for the client
mbedtls_endpoint_sanity:MBEDTLS_SSL_IS_CLIENT

Test mbedtls_endpoint sanity for the server
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
mbedtls_endpoint_sanity:MBEDTLS_SSL_IS_SERVER

TLS 1.2:Move client handshake to HELLO_REQUEST
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_HELLO_REQUEST:1

TLS 1.2:Move client handshake to CLIENT_HELLO
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_CLIENT_HELLO:1

TLS 1.2:Move client handshake to SERVER_HELLO
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_SERVER_HELLO:1

TLS 1.2:Move client handshake to SERVER_CERTIFICATE
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_SERVER_CERTIFICATE:1

TLS 1.2:Move client handshake to SERVER_KEY_EXCHANGE
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_SERVER_KEY_EXCHANGE:1

TLS 1.2:Move client handshake to CERTIFICATE_REQUEST
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_CERTIFICATE_REQUEST:1

TLS 1.2:Move client handshake to SERVER_HELLO_DONE
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_SERVER_HELLO_DONE:1

TLS 1.2:Move client handshake to CLIENT_CERTIFICATE
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_CLIENT_CERTIFICATE:1

TLS 1.2:Move client handshake to CLIENT_KEY_EXCHANGE
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_CLIENT_KEY_EXCHANGE:1

TLS 1.2:Move client handshake to CERTIFICATE_VERIFY
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_CERTIFICATE_VERIFY:1

TLS 1.2:Move client handshake to CLIENT_CHANGE_CIPHER_SPEC
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_CLIENT_CHANGE_CIPHER_SPEC:1

TLS 1.2:Move client handshake to CLIENT_FINISHED
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_CLIENT_FINISHED:1

TLS 1.2:Move client handshake to SERVER_CHANGE_CIPHER_SPEC
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_SERVER_CHANGE_CIPHER_SPEC:1

TLS 1.2:Move client handshake to SERVER_FINISHED
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_SERVER_FINISHED:1

TLS 1.2:Move client handshake to FLUSH_BUFFERS
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_FLUSH_BUFFERS:1

TLS 1.2:Move client handshake to HANDSHAKE_WRAPUP
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_HANDSHAKE_WRAPUP:1

TLS 1.2:Move client handshake to HANDSHAKE_OVER
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_HANDSHAKE_OVER:1

TLS 1.3:Move client handshake to HELLO_REQUEST
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_HELLO_REQUEST:1

TLS 1.3:Move client handshake to CLIENT_HELLO
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_CLIENT_HELLO:1

TLS 1.3:Move client handshake to SERVER_HELLO
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_SERVER_HELLO:1

TLS 1.3:Move client handshake to ENCRYPTED_EXTENSIONS
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_ENCRYPTED_EXTENSIONS:1

TLS 1.3:Move client handshake to CERTIFICATE_REQUEST
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_CERTIFICATE_REQUEST:1

TLS 1.3:Move client handshake to SERVER_CERTIFICATE
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED:MBEDTLS_PKCS1_V21:MBEDTLS_X509_RSASSA_PSS_SUPPORT
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_SERVER_CERTIFICATE:1

TLS 1.3:Move client handshake to CERTIFICATE_VERIFY
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED:MBEDTLS_PKCS1_V21:MBEDTLS_X509_RSASSA_PSS_SUPPORT
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_CERTIFICATE_VERIFY:1

TLS 1.3:Move client handshake to SERVER_FINISHED
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED:MBEDTLS_PKCS1_V21:MBEDTLS_X509_RSASSA_PSS_SUPPORT
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_SERVER_FINISHED:1

TLS 1.3:Move client handshake to CLIENT_CERTIFICATE
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED:MBEDTLS_PKCS1_V21:MBEDTLS_X509_RSASSA_PSS_SUPPORT
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_CLIENT_CERTIFICATE:1

TLS 1.3:Move client handshake to CLIENT_CERTIFICATE_VERIFY
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED:MBEDTLS_PKCS1_V21:MBEDTLS_X509_RSASSA_PSS_SUPPORT
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_CLIENT_CERTIFICATE_VERIFY:1

TLS 1.3:Move client handshake to CLIENT_FINISHED
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED:MBEDTLS_PKCS1_V21:MBEDTLS_X509_RSASSA_PSS_SUPPORT
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_CLIENT_FINISHED:1

TLS 1.3:Move client handshake to FLUSH_BUFFERS
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED:MBEDTLS_PKCS1_V21:MBEDTLS_X509_RSASSA_PSS_SUPPORT
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_FLUSH_BUFFERS:1

TLS 1.3:Move client handshake to HANDSHAKE_WRAPUP
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED:MBEDTLS_PKCS1_V21:MBEDTLS_X509_RSASSA_PSS_SUPPORT
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_HANDSHAKE_WRAPUP:1

TLS 1.3:Move client handshake to CLIENT_CCS_AFTER_SERVER_FINISHED
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED:MBEDTLS_PKCS1_V21:MBEDTLS_X509_RSASSA_PSS_SUPPORT:MBEDTLS_SSL_TLS1_3_COMPATIBILITY_MODE
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_CLIENT_CCS_AFTER_SERVER_FINISHED:1

TLS 1.2:Move server handshake to HELLO_REQUEST
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_HELLO_REQUEST:1

TLS 1.2:Move server handshake to CLIENT_HELLO
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_CLIENT_HELLO:1

TLS 1.2:Move server handshake to SERVER_HELLO
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_SERVER_HELLO:1

TLS 1.2:Move server handshake to SERVER_CERTIFICATE
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_SERVER_CERTIFICATE:1

TLS 1.2:Move server handshake to SERVER_KEY_EXCHANGE
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_SERVER_KEY_EXCHANGE:1

TLS 1.2:Move server handshake to CERTIFICATE_REQUEST
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_CERTIFICATE_REQUEST:1

TLS 1.2:Move server handshake to SERVER_HELLO_DONE
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_SERVER_HELLO_DONE:1

TLS 1.2:Move server handshake to CLIENT_CERTIFICATE
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_CLIENT_CERTIFICATE:1

TLS 1.2:Move server handshake to CLIENT_KEY_EXCHANGE
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_CLIENT_KEY_EXCHANGE:1

TLS 1.2:Move server handshake to CERTIFICATE_VERIFY
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_CERTIFICATE_VERIFY:1

TLS 1.2:Move server handshake to CLIENT_CHANGE_CIPHER_SPEC
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_CLIENT_CHANGE_CIPHER_SPEC:1

TLS 1.2:Move server handshake to CLIENT_FINISHED
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_CLIENT_FINISHED:1

TLS 1.2:Move server handshake to SERVER_CHANGE_CIPHER_SPEC
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_SERVER_CHANGE_CIPHER_SPEC:1

TLS 1.2:Move server handshake to SERVER_FINISHED
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_SERVER_FINISHED:1

TLS 1.2:Move server handshake to FLUSH_BUFFERS
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_FLUSH_BUFFERS:1

TLS 1.2:Move server handshake to HANDSHAKE_WRAPUP
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_HANDSHAKE_WRAPUP:1

TLS 1.2:Move server handshake to HANDSHAKE_OVER
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_HANDSHAKE_OVER:1

TLS 1.3:Move server handshake to HELLO_REQUEST
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_HELLO_REQUEST:1

TLS 1.3:Move server handshake to CLIENT_HELLO
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_CLIENT_HELLO:1

TLS 1.3:Move server handshake to SERVER_HELLO
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_SERVER_HELLO:1

TLS 1.3:Move server handshake to ENCRYPTED_EXTENSIONS
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_ENCRYPTED_EXTENSIONS:1

TLS 1.3:Move server handshake to CERTIFICATE_REQUEST
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_CERTIFICATE_REQUEST:1

TLS 1.3:Move server handshake to SERVER_CERTIFICATE
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_SERVER_CERTIFICATE:1

TLS 1.3:Move server handshake to CERTIFICATE_VERIFY
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED:MBEDTLS_PKCS1_V21:MBEDTLS_X509_RSASSA_PSS_SUPPORT
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_CERTIFICATE_VERIFY:1

TLS 1.3:Move server handshake to SERVER_CCS_AFTER_SERVER_HELLO
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_COMPATIBILITY_MODE
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_SERVER_CCS_AFTER_SERVER_HELLO:1

TLS 1.3:Move server handshake to SERVER_FINISHED
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED:MBEDTLS_PKCS1_V21:MBEDTLS_X509_RSASSA_PSS_SUPPORT
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_SERVER_FINISHED:1

TLS 1.3:Move server handshake to CLIENT_FINISHED
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED:MBEDTLS_PKCS1_V21:MBEDTLS_X509_RSASSA_PSS_SUPPORT
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_CLIENT_FINISHED:1

TLS 1.3:Move server handshake to HANDSHAKE_WRAPUP
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED:MBEDTLS_PKCS1_V21:MBEDTLS_X509_RSASSA_PSS_SUPPORT
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_HANDSHAKE_WRAPUP:1

TLS 1.3:Move server handshake to CLIENT_CERTIFICATE
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED:MBEDTLS_PKCS1_V21:MBEDTLS_X509_RSASSA_PSS_SUPPORT
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_CLIENT_CERTIFICATE:1

TLS 1.3:Move server handshake to CLIENT_CERTIFICATE_VERIFY
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED:MBEDTLS_PKCS1_V21:MBEDTLS_X509_RSASSA_PSS_SUPPORT
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_CLIENT_CERTIFICATE_VERIFY:1

TLS 1.2:Negative test moving clients ssl to state: VERIFY_REQUEST_SENT
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
move_handshake_to_state:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_SERVER_HELLO_VERIFY_REQUEST_SENT:0

TLS 1.2:Negative test moving servers ssl to state: NEW_SESSION_TICKET
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
move_handshake_to_state:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_NEW_SESSION_TICKET:0

Handshake, tls1_2
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
handshake_version:0:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_2

Handshake, tls1_3
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED:MBEDTLS_PKCS1_V21:MBEDTLS_X509_RSASSA_PSS_SUPPORT
handshake_version:0:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_VERSION_TLS1_3

Handshake, ECDHE-RSA-WITH-AES-256-GCM-SHA384
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_cipher:"TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384":MBEDTLS_PK_RSA:0

Handshake, RSA-WITH-AES-128-CCM
depends_on:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_SSL_HAVE_AES:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_RSA_ENABLED
handshake_cipher:"TLS-RSA-WITH-AES-128-CCM":MBEDTLS_PK_RSA:0

Handshake, DHE-RSA-WITH-AES-256-CBC-SHA256
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_cipher:"TLS-DHE-RSA-WITH-AES-256-CBC-SHA256":MBEDTLS_PK_RSA:0

Handshake, ECDHE-RSA-WITH-AES-256-CBC-SHA384
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA384:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_cipher:"TLS-ECDHE-RSA-WITH-AES-256-CBC-SHA384":MBEDTLS_PK_RSA:0

Handshake, ECDHE-ECDSA-WITH-AES-256-CCM
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_cipher:"TLS-ECDHE-ECDSA-WITH-AES-256-CCM":MBEDTLS_PK_ECDSA:0

Handshake, ECDH-ECDSA-WITH-CAMELLIA-256-CBC-SHA384
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA_ENABLED
handshake_cipher:"TLS-ECDH-ECDSA-WITH-CAMELLIA-256-CBC-SHA384":MBEDTLS_PK_ECDSA:0

Handshake, PSK-WITH-AES-128-CBC-SHA
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_MD_CAN_SHA1:MBEDTLS_KEY_EXCHANGE_PSK_ENABLED
handshake_psk_cipher:"TLS-PSK-WITH-AES-128-CBC-SHA":MBEDTLS_PK_RSA:"abc123":0

DTLS Handshake, tls1_2
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_PROTO_DTLS:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
handshake_version:1:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_2

DTLS Handshake, ECDHE-RSA-WITH-AES-256-GCM-SHA384
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:MBEDTLS_SSL_PROTO_DTLS:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_cipher:"TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384":MBEDTLS_PK_RSA:1

DTLS Handshake, RSA-WITH-AES-128-CCM
depends_on:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_SSL_HAVE_AES:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_SSL_PROTO_DTLS:MBEDTLS_KEY_EXCHANGE_RSA_ENABLED
handshake_cipher:"TLS-RSA-WITH-AES-128-CCM":MBEDTLS_PK_RSA:1

DTLS Handshake, DHE-RSA-WITH-AES-256-CBC-SHA256
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA256:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_SSL_PROTO_DTLS:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_cipher:"TLS-DHE-RSA-WITH-AES-256-CBC-SHA256":MBEDTLS_PK_RSA:1

DTLS Handshake, ECDHE-RSA-WITH-AES-256-CBC-SHA384
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA384:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_SSL_PROTO_DTLS:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_cipher:"TLS-ECDHE-RSA-WITH-AES-256-CBC-SHA384":MBEDTLS_PK_RSA:1

DTLS Handshake, ECDHE-ECDSA-WITH-AES-256-CCM
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_SSL_PROTO_DTLS:MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_cipher:"TLS-ECDHE-ECDSA-WITH-AES-256-CCM":MBEDTLS_PK_ECDSA:1

DTLS Handshake, ECDH-ECDSA-WITH-CAMELLIA-256-CBC-SHA384
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_SSL_PROTO_DTLS:MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA_ENABLED
handshake_cipher:"TLS-ECDH-ECDSA-WITH-CAMELLIA-256-CBC-SHA384":MBEDTLS_PK_ECDSA:1

DTLS Handshake, PSK-WITH-AES-128-CBC-SHA
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_SSL_PROTO_DTLS:MBEDTLS_MD_CAN_SHA1:MBEDTLS_KEY_EXCHANGE_PSK_ENABLED
handshake_psk_cipher:"TLS-PSK-WITH-AES-128-CBC-SHA":MBEDTLS_PK_RSA:"abc123":1

DTLS Handshake with serialization, tls1_2
depends_on:MBEDTLS_RSA_C:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_SSL_PROTO_DTLS
handshake_serialization

DTLS Handshake fragmentation, MFL=512, DHE-RSA-WITH-AES-256-CBC-SHA256
depends_on:MBEDTLS_SSL_PROTO_DTLS:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:MBEDTLS_MD_CAN_SHA256
handshake_fragmentation:MBEDTLS_SSL_MAX_FRAG_LEN_512:1:1:"TLS-DHE-RSA-WITH-AES-256-CBC-SHA256"

DTLS Handshake fragmentation, MFL=1024, DHE-RSA-WITH-AES-256-CBC-SHA256
depends_on:MBEDTLS_SSL_PROTO_DTLS:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:MBEDTLS_MD_CAN_SHA256
handshake_fragmentation:MBEDTLS_SSL_MAX_FRAG_LEN_1024:0:1:"TLS-DHE-RSA-WITH-AES-256-CBC-SHA256"

DTLS Handshake fragmentation, MFL=512, ECDHE-RSA-WITH-AES-256-CBC-SHA384
depends_on:MBEDTLS_SSL_PROTO_DTLS:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:MBEDTLS_MD_CAN_SHA384
handshake_fragmentation:MBEDTLS_SSL_MAX_FRAG_LEN_512:1:1:"TLS-ECDHE-RSA-WITH-AES-256-CBC-SHA384"

DTLS Handshake fragmentation, MFL=1024, ECDHE-RSA-WITH-AES-256-CBC-SHA384
depends_on:MBEDTLS_SSL_PROTO_DTLS:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:MBEDTLS_MD_CAN_SHA384
handshake_fragmentation:MBEDTLS_SSL_MAX_FRAG_LEN_1024:0:1:"TLS-ECDHE-RSA-WITH-AES-256-CBC-SHA384"

Handshake min/max version check, all -> 1.2
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:!MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_CAN_HANDLE_RSA_TEST_KEY
handshake_version:0:MBEDTLS_SSL_VERSION_UNKNOWN:MBEDTLS_SSL_VERSION_UNKNOWN:MBEDTLS_SSL_VERSION_UNKNOWN:MBEDTLS_SSL_VERSION_UNKNOWN:MBEDTLS_SSL_VERSION_TLS1_2

Handshake min/max version check, all -> 1.3
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_TEST_AT_LEAST_ONE_TLS1_3_CIPHERSUITE:MBEDTLS_SSL_TLS1_3_KEY_EXCHANGE_MODE_EPHEMERAL_ENABLED:MBEDTLS_PKCS1_V21:MBEDTLS_X509_RSASSA_PSS_SUPPORT
handshake_version:0:MBEDTLS_SSL_VERSION_UNKNOWN:MBEDTLS_SSL_VERSION_UNKNOWN:MBEDTLS_SSL_VERSION_UNKNOWN:MBEDTLS_SSL_VERSION_UNKNOWN:MBEDTLS_SSL_VERSION_TLS1_3

Handshake, select RSA-WITH-AES-256-CBC-SHA256, non-opaque
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-RSA-WITH-AES-256-CBC-SHA256":MBEDTLS_PK_RSA:"":PSA_ALG_NONE:PSA_ALG_NONE:0:0:MBEDTLS_TLS_RSA_WITH_AES_256_CBC_SHA256

Handshake, select RSA-WITH-AES-256-CBC-SHA256, opaque
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_RSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-RSA-WITH-AES-256-CBC-SHA256":MBEDTLS_PK_RSA:"":PSA_ALG_RSA_PKCS1V15_CRYPT:PSA_ALG_NONE:PSA_KEY_USAGE_DECRYPT:0:MBEDTLS_TLS_RSA_WITH_AES_256_CBC_SHA256

Handshake, select RSA-WITH-AES-256-CBC-SHA256, opaque, bad alg
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_RSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-RSA-WITH-AES-256-CBC-SHA256":MBEDTLS_PK_RSA:"":PSA_ALG_RSA_PKCS1V15_SIGN(PSA_ALG_ANY_HASH):PSA_ALG_NONE:PSA_KEY_USAGE_DECRYPT:MBEDTLS_ERR_SSL_HANDSHAKE_FAILURE:0

Handshake, select RSA-WITH-AES-256-CBC-SHA256, opaque, bad usage
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_RSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-RSA-WITH-AES-256-CBC-SHA256":MBEDTLS_PK_RSA:"":PSA_ALG_RSA_PKCS1V15_CRYPT:PSA_ALG_NONE:PSA_KEY_USAGE_DERIVE:MBEDTLS_ERR_SSL_HANDSHAKE_FAILURE:0

Handshake, select RSA-PSK-WITH-AES-256-CBC-SHA384, non-opaque
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_RSA_PSK_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-RSA-PSK-WITH-AES-256-CBC-SHA384":MBEDTLS_PK_RSA:"abc123":PSA_ALG_NONE:PSA_ALG_NONE:0:0:MBEDTLS_TLS_RSA_PSK_WITH_AES_256_CBC_SHA384

Handshake, select RSA-PSK-WITH-AES-256-CBC-SHA384, opaque
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_RSA_PSK_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-RSA-PSK-WITH-AES-256-CBC-SHA384":MBEDTLS_PK_RSA:"abc123":PSA_ALG_RSA_PKCS1V15_CRYPT:PSA_ALG_NONE:PSA_KEY_USAGE_DECRYPT:0:MBEDTLS_TLS_RSA_PSK_WITH_AES_256_CBC_SHA384

Handshake, select RSA-PSK-WITH-AES-256-CBC-SHA384, opaque, bad alg
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_RSA_PSK_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-RSA-PSK-WITH-AES-256-CBC-SHA384":MBEDTLS_PK_RSA:"abc123":PSA_ALG_RSA_PKCS1V15_SIGN(PSA_ALG_ANY_HASH):PSA_ALG_NONE:PSA_KEY_USAGE_DECRYPT:MBEDTLS_ERR_SSL_HANDSHAKE_FAILURE:0

Handshake, select RSA-PSK-WITH-AES-256-CBC-SHA384, opaque, bad usage
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_RSA_PSK_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-RSA-PSK-WITH-AES-256-CBC-SHA384":MBEDTLS_PK_RSA:"abc123":PSA_ALG_RSA_PKCS1V15_CRYPT:PSA_ALG_NONE:PSA_KEY_USAGE_DERIVE:MBEDTLS_ERR_SSL_HANDSHAKE_FAILURE:0

Handshake, select RSA-PSK-WITH-AES-256-CBC-SHA384, opaque, no psk
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_RSA_PSK_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-RSA-PSK-WITH-AES-256-CBC-SHA384":MBEDTLS_PK_RSA:"":PSA_ALG_RSA_PKCS1V15_CRYPT:PSA_ALG_NONE:PSA_KEY_USAGE_DECRYPT:MBEDTLS_ERR_SSL_HANDSHAKE_FAILURE:0

Handshake, select DHE-RSA-WITH-AES-256-GCM-SHA384, non-opaque
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-DHE-RSA-WITH-AES-256-GCM-SHA384":MBEDTLS_PK_RSA:"":PSA_ALG_NONE:PSA_ALG_NONE:0:0:MBEDTLS_TLS_DHE_RSA_WITH_AES_256_GCM_SHA384

Handshake, select DHE-RSA-WITH-AES-256-GCM-SHA384, opaque, PSA_ALG_ANY_HASH
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-DHE-RSA-WITH-AES-256-GCM-SHA384":MBEDTLS_PK_RSA:"":PSA_ALG_RSA_PKCS1V15_SIGN(PSA_ALG_ANY_HASH):PSA_ALG_NONE:PSA_KEY_USAGE_SIGN_HASH:0:MBEDTLS_TLS_DHE_RSA_WITH_AES_256_GCM_SHA384

Handshake, select DHE-RSA-WITH-AES-256-GCM-SHA384, opaque, PSA_ALG_SHA_384
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-DHE-RSA-WITH-AES-256-GCM-SHA384":MBEDTLS_PK_RSA:"":PSA_ALG_RSA_PKCS1V15_SIGN(PSA_ALG_SHA_384):PSA_ALG_NONE:PSA_KEY_USAGE_SIGN_HASH:0:MBEDTLS_TLS_DHE_RSA_WITH_AES_256_GCM_SHA384

Handshake, select DHE-RSA-WITH-AES-256-GCM-SHA384, opaque, invalid alg
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-DHE-RSA-WITH-AES-256-GCM-SHA384":MBEDTLS_PK_RSA:"":PSA_ALG_RSA_PKCS1V15_SIGN(PSA_ALG_SHA_256):PSA_ALG_NONE:PSA_KEY_USAGE_SIGN_HASH:MBEDTLS_ERR_SSL_HANDSHAKE_FAILURE:0

Handshake, select DHE-RSA-WITH-AES-256-GCM-SHA384, opaque, bad alg
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-DHE-RSA-WITH-AES-256-GCM-SHA384":MBEDTLS_PK_RSA:"":PSA_ALG_RSA_PKCS1V15_CRYPT:PSA_ALG_NONE:PSA_KEY_USAGE_SIGN_HASH:MBEDTLS_ERR_SSL_HANDSHAKE_FAILURE:0

Handshake, select DHE-RSA-WITH-AES-256-GCM-SHA384, opaque, bad usage
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-DHE-RSA-WITH-AES-256-GCM-SHA384":MBEDTLS_PK_RSA:"":PSA_ALG_RSA_PKCS1V15_SIGN(PSA_ALG_ANY_HASH):PSA_ALG_NONE:PSA_KEY_USAGE_DERIVE:MBEDTLS_ERR_SSL_HANDSHAKE_FAILURE:0

Handshake, select ECDHE-RSA-WITH-AES-256-GCM-SHA384, non-opaque
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384":MBEDTLS_PK_RSA:"":PSA_ALG_NONE:PSA_ALG_NONE:0:0:MBEDTLS_TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384

Handshake, select ECDHE-RSA-WITH-AES-256-GCM-SHA384, opaque, PSA_ALG_ANY_HASH
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384":MBEDTLS_PK_RSA:"":PSA_ALG_RSA_PKCS1V15_SIGN(PSA_ALG_ANY_HASH):PSA_ALG_NONE:PSA_KEY_USAGE_SIGN_HASH:0:MBEDTLS_TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384

Handshake, select ECDHE-RSA-WITH-AES-256-GCM-SHA384, opaque, PSA_ALG_SHA_384
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384":MBEDTLS_PK_RSA:"":PSA_ALG_RSA_PKCS1V15_SIGN(PSA_ALG_SHA_384):PSA_ALG_NONE:PSA_KEY_USAGE_SIGN_HASH:0:MBEDTLS_TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384

Handshake, select ECDHE-RSA-WITH-AES-256-GCM-SHA384, opaque, invalid alg
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384":MBEDTLS_PK_RSA:"":PSA_ALG_RSA_PKCS1V15_SIGN(PSA_ALG_SHA_256):PSA_ALG_NONE:PSA_KEY_USAGE_SIGN_HASH:MBEDTLS_ERR_SSL_HANDSHAKE_FAILURE:0

Handshake, select ECDHE-RSA-WITH-AES-256-GCM-SHA384, opaque, bad alg
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384":MBEDTLS_PK_RSA:"":PSA_ALG_RSA_PSS(PSA_ALG_ANY_HASH):PSA_ALG_NONE:PSA_KEY_USAGE_SIGN_HASH:MBEDTLS_ERR_SSL_HANDSHAKE_FAILURE:0

Handshake, select ECDHE-RSA-WITH-AES-256-GCM-SHA384, opaque, bad usage
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_RSA_C:MBEDTLS_PK_HAVE_ECC_KEYS:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384":MBEDTLS_PK_RSA:"":PSA_ALG_RSA_PKCS1V15_SIGN(PSA_ALG_ANY_HASH):PSA_ALG_NONE:PSA_KEY_USAGE_DERIVE:MBEDTLS_ERR_SSL_HANDSHAKE_FAILURE:0

Handshake, select ECDHE-ECDSA-WITH-AES-256-CCM, non-opaque
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-ECDHE-ECDSA-WITH-AES-256-CCM":MBEDTLS_PK_ECDSA:"":PSA_ALG_NONE:PSA_ALG_NONE:0:0:MBEDTLS_TLS_ECDHE_ECDSA_WITH_AES_256_CCM

Handshake, select ECDHE-ECDSA-WITH-AES-256-CCM, opaque, PSA_ALG_ANY_HASH
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-ECDHE-ECDSA-WITH-AES-256-CCM":MBEDTLS_PK_ECDSA:"":PSA_ALG_ECDSA(PSA_ALG_ANY_HASH):PSA_ALG_NONE:PSA_KEY_USAGE_SIGN_HASH:0:MBEDTLS_TLS_ECDHE_ECDSA_WITH_AES_256_CCM

Handshake, select ECDHE-ECDSA-WITH-AES-256-CCM, opaque, PSA_ALG_SHA_256
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-ECDHE-ECDSA-WITH-AES-256-CCM":MBEDTLS_PK_ECDSA:"":PSA_ALG_ECDSA(PSA_ALG_SHA_256):PSA_ALG_NONE:PSA_KEY_USAGE_SIGN_HASH:0:MBEDTLS_TLS_ECDHE_ECDSA_WITH_AES_256_CCM

Handshake, select ECDHE-ECDSA-WITH-AES-256-CCM, opaque, bad alg
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-ECDHE-ECDSA-WITH-AES-256-CCM":MBEDTLS_PK_ECDSA:"":PSA_ALG_ECDH:PSA_ALG_NONE:PSA_KEY_USAGE_SIGN_HASH:MBEDTLS_ERR_SSL_HANDSHAKE_FAILURE:0

Handshake, select ECDHE-ECDSA-WITH-AES-256-CCM, opaque, bad usage
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-ECDHE-ECDSA-WITH-AES-256-CCM":MBEDTLS_PK_ECDSA:"":PSA_ALG_ECDSA(PSA_ALG_ANY_HASH):PSA_ALG_NONE:PSA_KEY_USAGE_DERIVE:MBEDTLS_ERR_SSL_HANDSHAKE_FAILURE:0

Handshake, select ECDH-RSA-WITH-AES-256-CBC-SHA384, non-opaque
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_RSA_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_PK_CAN_ECDSA_SIGN:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDH_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-ECDH-RSA-WITH-AES-256-CBC-SHA384":MBEDTLS_PK_ECDSA:"":PSA_ALG_NONE:PSA_ALG_NONE:0:0:MBEDTLS_TLS_ECDH_RSA_WITH_AES_256_CBC_SHA384

Handshake, select ECDH-RSA-WITH-AES-256-CBC-SHA384, opaque
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_RSA_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_PK_CAN_ECDSA_SIGN:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDH_RSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-ECDH-RSA-WITH-AES-256-CBC-SHA384":MBEDTLS_PK_ECDSA:"":PSA_ALG_ECDH:PSA_ALG_NONE:PSA_KEY_USAGE_DERIVE:0:MBEDTLS_TLS_ECDH_RSA_WITH_AES_256_CBC_SHA384

Handshake, select ECDH-RSA-WITH-AES-256-CBC-SHA384, opaque, bad alg
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_RSA_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_PK_CAN_ECDSA_SIGN:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDH_RSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-ECDH-RSA-WITH-AES-256-CBC-SHA384":MBEDTLS_PK_ECDSA:"":PSA_ALG_ECDSA(PSA_ALG_ANY_HASH):PSA_ALG_NONE:PSA_KEY_USAGE_DERIVE:MBEDTLS_ERR_SSL_HANDSHAKE_FAILURE:0

Handshake, select ECDH-RSA-WITH-AES-256-CBC-SHA384, opaque, bad usage
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_RSA_C:MBEDTLS_PK_CAN_ECDSA_VERIFY:MBEDTLS_PK_CAN_ECDSA_SIGN:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDH_RSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
handshake_ciphersuite_select:"TLS-ECDH-RSA-WITH-AES-256-CBC-SHA384":MBEDTLS_PK_ECDSA:"":PSA_ALG_ECDH:PSA_ALG_NONE:PSA_KEY_USAGE_DECRYPT:MBEDTLS_ERR_SSL_HANDSHAKE_FAILURE:0

Handshake, select ECDH-ECDSA-WITH-CAMELLIA-256-CBC-SHA384, non-opaque
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA_ENABLED
handshake_ciphersuite_select:"TLS-ECDH-ECDSA-WITH-CAMELLIA-256-CBC-SHA384":MBEDTLS_PK_ECDSA:"":PSA_ALG_NONE:PSA_ALG_NONE:0:0:MBEDTLS_TLS_ECDH_ECDSA_WITH_CAMELLIA_256_CBC_SHA384

Handshake, select ECDH-ECDSA-WITH-CAMELLIA-256-CBC-SHA384, opaque, PSA_ALG_ANY_HASH
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO
handshake_ciphersuite_select:"TLS-ECDH-ECDSA-WITH-CAMELLIA-256-CBC-SHA384":MBEDTLS_PK_ECDSA:"":PSA_ALG_ECDSA(PSA_ALG_ANY_HASH):PSA_ALG_ECDH:PSA_KEY_USAGE_SIGN_HASH|PSA_KEY_USAGE_DERIVE:0:MBEDTLS_TLS_ECDH_ECDSA_WITH_CAMELLIA_256_CBC_SHA384

Handshake, select ECDH-ECDSA-WITH-CAMELLIA-256-CBC-SHA384, opaque, PSA_ALG_SHA_384
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO
handshake_ciphersuite_select:"TLS-ECDH-ECDSA-WITH-CAMELLIA-256-CBC-SHA384":MBEDTLS_PK_ECDSA:"":PSA_ALG_ECDSA(PSA_ALG_SHA_384):PSA_ALG_ECDH:PSA_KEY_USAGE_SIGN_HASH|PSA_KEY_USAGE_DERIVE:0:MBEDTLS_TLS_ECDH_ECDSA_WITH_CAMELLIA_256_CBC_SHA384

Handshake, select ECDH-ECDSA-WITH-CAMELLIA-256-CBC-SHA384, opaque, missing alg
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO
handshake_ciphersuite_select:"TLS-ECDH-ECDSA-WITH-CAMELLIA-256-CBC-SHA384":MBEDTLS_PK_ECDSA:"":PSA_ALG_ECDSA(PSA_ALG_ANY_HASH):PSA_ALG_NONE:PSA_KEY_USAGE_SIGN_HASH|PSA_KEY_USAGE_DERIVE:MBEDTLS_ERR_SSL_HANDSHAKE_FAILURE:0

Handshake, select ECDH-ECDSA-WITH-CAMELLIA-256-CBC-SHA384, opaque, missing usage
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_ECP_HAVE_SECP384R1:MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA_ENABLED:MBEDTLS_USE_PSA_CRYPTO
handshake_ciphersuite_select:"TLS-ECDH-ECDSA-WITH-CAMELLIA-256-CBC-SHA384":MBEDTLS_PK_ECDSA:"":PSA_ALG_ECDSA(PSA_ALG_ANY_HASH):PSA_ALG_ECDH:PSA_KEY_USAGE_SIGN_HASH:MBEDTLS_ERR_SSL_HANDSHAKE_FAILURE:0

Sending app data via TLS, MFL=512 without fragmentation
depends_on:MBEDTLS_SSL_MAX_FRAGMENT_LENGTH
app_data_tls:MBEDTLS_SSL_MAX_FRAG_LEN_512:400:512:1:1

Sending app data via TLS, MFL=512 with fragmentation
depends_on:MBEDTLS_SSL_MAX_FRAGMENT_LENGTH
app_data_tls:MBEDTLS_SSL_MAX_FRAG_LEN_512:513:1536:2:3

Sending app data via TLS, MFL=1024 without fragmentation
depends_on:MBEDTLS_SSL_MAX_FRAGMENT_LENGTH
app_data_tls:MBEDTLS_SSL_MAX_FRAG_LEN_1024:1000:1024:1:1

Sending app data via TLS, MFL=1024 with fragmentation
depends_on:MBEDTLS_SSL_MAX_FRAGMENT_LENGTH
app_data_tls:MBEDTLS_SSL_MAX_FRAG_LEN_1024:1025:5120:2:5

Sending app data via TLS, MFL=2048 without fragmentation
depends_on:MBEDTLS_SSL_MAX_FRAGMENT_LENGTH
app_data_tls:MBEDTLS_SSL_MAX_FRAG_LEN_2048:2000:2048:1:1

Sending app data via TLS, MFL=2048 with fragmentation
depends_on:MBEDTLS_SSL_MAX_FRAGMENT_LENGTH
app_data_tls:MBEDTLS_SSL_MAX_FRAG_LEN_2048:2049:8192:2:4

Sending app data via TLS, MFL=4096 without fragmentation
depends_on:MBEDTLS_SSL_MAX_FRAGMENT_LENGTH
app_data_tls:MBEDTLS_SSL_MAX_FRAG_LEN_4096:4000:4096:1:1

Sending app data via TLS, MFL=4096 with fragmentation
depends_on:MBEDTLS_SSL_MAX_FRAGMENT_LENGTH
app_data_tls:MBEDTLS_SSL_MAX_FRAG_LEN_4096:4097:12288:2:3

Sending app data via TLS without MFL and without fragmentation
app_data_tls:MBEDTLS_SSL_MAX_FRAG_LEN_NONE:16001:16384:1:1

Sending app data via TLS without MFL and with fragmentation
app_data_tls:MBEDTLS_SSL_MAX_FRAG_LEN_NONE:16385:100000:2:7

Sending app data via DTLS, MFL=512 without fragmentation
depends_on:MBEDTLS_SSL_MAX_FRAGMENT_LENGTH
app_data_dtls:MBEDTLS_SSL_MAX_FRAG_LEN_512:400:512:1:1

Sending app data via DTLS, MFL=512 with fragmentation
depends_on:MBEDTLS_SSL_MAX_FRAGMENT_LENGTH
app_data_dtls:MBEDTLS_SSL_MAX_FRAG_LEN_512:513:1536:0:0

Sending app data via DTLS, MFL=1024 without fragmentation
depends_on:MBEDTLS_SSL_MAX_FRAGMENT_LENGTH
app_data_dtls:MBEDTLS_SSL_MAX_FRAG_LEN_1024:1000:1024:1:1

Sending app data via DTLS, MFL=1024 with fragmentation
depends_on:MBEDTLS_SSL_MAX_FRAGMENT_LENGTH
app_data_dtls:MBEDTLS_SSL_MAX_FRAG_LEN_1024:1025:5120:0:0

Sending app data via DTLS, MFL=2048 without fragmentation
depends_on:MBEDTLS_SSL_MAX_FRAGMENT_LENGTH
app_data_dtls:MBEDTLS_SSL_MAX_FRAG_LEN_2048:2000:2048:1:1

Sending app data via DTLS, MFL=2048 with fragmentation
depends_on:MBEDTLS_SSL_MAX_FRAGMENT_LENGTH
app_data_dtls:MBEDTLS_SSL_MAX_FRAG_LEN_2048:2049:8192:0:0

Sending app data via DTLS, MFL=4096 without fragmentation
depends_on:MBEDTLS_SSL_MAX_FRAGMENT_LENGTH
app_data_dtls:MBEDTLS_SSL_MAX_FRAG_LEN_4096:4000:4096:1:1

Sending app data via DTLS, MFL=4096 with fragmentation
depends_on:MBEDTLS_SSL_MAX_FRAGMENT_LENGTH
app_data_dtls:MBEDTLS_SSL_MAX_FRAG_LEN_4096:4097:12288:0:0

Sending app data via DTLS, without MFL and without fragmentation
app_data_dtls:MBEDTLS_SSL_MAX_FRAG_LEN_NONE:16001:16384:1:1

Sending app data via DTLS, without MFL and with fragmentation
app_data_dtls:MBEDTLS_SSL_MAX_FRAG_LEN_NONE:16385:100000:0:0

DTLS renegotiation: no legacy renegotiation
renegotiation:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION

DTLS renegotiation: legacy renegotiation
renegotiation:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION

DTLS renegotiation: legacy break handshake
renegotiation:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE

DTLS serialization with MFL=512
resize_buffers_serialize_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_512

DTLS serialization with MFL=1024
resize_buffers_serialize_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_1024

DTLS serialization with MFL=2048
resize_buffers_serialize_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_2048

DTLS serialization with MFL=4096
resize_buffers_serialize_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_4096

DTLS no legacy renegotiation with MFL=512
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_512:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION:""

DTLS no legacy renegotiation with MFL=1024
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_1024:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION:""

DTLS no legacy renegotiation with MFL=2048
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_2048:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION:""

DTLS no legacy renegotiation with MFL=4096
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_4096:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION:""

DTLS legacy allow renegotiation with MFL=512
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_512:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION:""

DTLS legacy allow renegotiation with MFL=1024
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_1024:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION:""

DTLS legacy allow renegotiation with MFL=2048
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_2048:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION:""

DTLS legacy allow renegotiation with MFL=4096
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_4096:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION:""

DTLS legacy break handshake renegotiation with MFL=512
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_512:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE:""

DTLS legacy break handshake renegotiation with MFL=1024
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_1024:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE:""

DTLS legacy break handshake renegotiation with MFL=2048
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_2048:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE:""

DTLS legacy break handshake renegotiation with MFL=4096
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_4096:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE:""

DTLS no legacy renegotiation with MFL=512, ECDHE-RSA-WITH-AES-256-GCM-SHA384
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_512:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION:"TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384"

DTLS no legacy renegotiation with MFL=1024, ECDHE-RSA-WITH-AES-256-GCM-SHA384
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_1024:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION:"TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384"

DTLS no legacy renegotiation with MFL=2048, ECDHE-RSA-WITH-AES-256-GCM-SHA384
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_2048:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION:"TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384"

DTLS no legacy renegotiation with MFL=4096, ECDHE-RSA-WITH-AES-256-GCM-SHA384
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_4096:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION:"TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384"

DTLS legacy allow renegotiation with MFL=512, ECDHE-RSA-WITH-AES-256-GCM-SHA384
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_512:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION:"TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384"

DTLS legacy allow renegotiation with MFL=1024, ECDHE-RSA-WITH-AES-256-GCM-SHA384
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_1024:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION:"TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384"

DTLS legacy allow renegotiation with MFL=2048, ECDHE-RSA-WITH-AES-256-GCM-SHA384
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_2048:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION:"TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384"

DTLS legacy allow renegotiation with MFL=4096, ECDHE-RSA-WITH-AES-256-GCM-SHA384
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_4096:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION:"TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384"

DTLS legacy break handshake renegotiation with MFL=512, ECDHE-RSA-WITH-AES-256-GCM-SHA384
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_512:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE:"TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384"

DTLS legacy break handshake renegotiation with MFL=1024, ECDHE-RSA-WITH-AES-256-GCM-SHA384
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_1024:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE:"TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384"

DTLS legacy break handshake renegotiation with MFL=2048, ECDHE-RSA-WITH-AES-256-GCM-SHA384
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_2048:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE:"TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384"

DTLS legacy break handshake renegotiation with MFL=4096, ECDHE-RSA-WITH-AES-256-GCM-SHA384
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_GCM:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_4096:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE:"TLS-ECDHE-RSA-WITH-AES-256-GCM-SHA384"

DTLS no legacy renegotiation with MFL=512, RSA-WITH-AES-128-CCM
depends_on:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_SSL_HAVE_AES:MBEDTLS_KEY_EXCHANGE_RSA_ENABLED
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_512:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION:"TLS-RSA-WITH-AES-128-CCM"

DTLS no legacy renegotiation with MFL=1024, RSA-WITH-AES-128-CCM
depends_on:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_SSL_HAVE_AES:MBEDTLS_KEY_EXCHANGE_RSA_ENABLED
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_1024:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION:"TLS-RSA-WITH-AES-128-CCM"

DTLS no legacy renegotiation with MFL=2048, RSA-WITH-AES-128-CCM
depends_on:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_SSL_HAVE_AES:MBEDTLS_KEY_EXCHANGE_RSA_ENABLED
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_2048:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION:"TLS-RSA-WITH-AES-128-CCM"

DTLS no legacy renegotiation with MFL=4096, RSA-WITH-AES-128-CCM
depends_on:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_SSL_HAVE_AES:MBEDTLS_KEY_EXCHANGE_RSA_ENABLED
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_4096:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION:"TLS-RSA-WITH-AES-128-CCM"

DTLS legacy allow renegotiation with MFL=512, RSA-WITH-AES-128-CCM
depends_on:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_SSL_HAVE_AES:MBEDTLS_KEY_EXCHANGE_RSA_ENABLED
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_512:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION:"TLS-RSA-WITH-AES-128-CCM"

DTLS legacy allow renegotiation with MFL=1024, RSA-WITH-AES-128-CCM
depends_on:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_SSL_HAVE_AES:MBEDTLS_KEY_EXCHANGE_RSA_ENABLED
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_1024:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION:"TLS-RSA-WITH-AES-128-CCM"

DTLS legacy allow renegotiation with MFL=2048, RSA-WITH-AES-128-CCM
depends_on:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_SSL_HAVE_AES:MBEDTLS_KEY_EXCHANGE_RSA_ENABLED
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_2048:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION:"TLS-RSA-WITH-AES-128-CCM"

DTLS legacy allow renegotiation with MFL=4096, RSA-WITH-AES-128-CCM
depends_on:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_SSL_HAVE_AES:MBEDTLS_KEY_EXCHANGE_RSA_ENABLED
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_4096:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION:"TLS-RSA-WITH-AES-128-CCM"

DTLS legacy break handshake renegotiation with MFL=512, RSA-WITH-AES-128-CCM
depends_on:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_SSL_HAVE_AES:MBEDTLS_KEY_EXCHANGE_RSA_ENABLED
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_512:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE:"TLS-RSA-WITH-AES-128-CCM"

DTLS legacy break handshake renegotiation with MFL=1024, RSA-WITH-AES-128-CCM
depends_on:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_SSL_HAVE_AES:MBEDTLS_KEY_EXCHANGE_RSA_ENABLED
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_1024:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE:"TLS-RSA-WITH-AES-128-CCM"

DTLS legacy break handshake renegotiation with MFL=2048, RSA-WITH-AES-128-CCM
depends_on:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_SSL_HAVE_AES:MBEDTLS_KEY_EXCHANGE_RSA_ENABLED
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_2048:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE:"TLS-RSA-WITH-AES-128-CCM"

DTLS legacy break handshake renegotiation with MFL=4096, RSA-WITH-AES-128-CCM
depends_on:MBEDTLS_SSL_HAVE_CCM:MBEDTLS_SSL_HAVE_AES:MBEDTLS_KEY_EXCHANGE_RSA_ENABLED
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_4096:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE:"TLS-RSA-WITH-AES-128-CCM"

DTLS no legacy renegotiation with MFL=512, DHE-RSA-WITH-AES-256-CBC-SHA256
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA256:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_512:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION:"TLS-DHE-RSA-WITH-AES-256-CBC-SHA256"

DTLS no legacy renegotiation with MFL=1024, DHE-RSA-WITH-AES-256-CBC-SHA256
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA256:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_1024:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION:"TLS-DHE-RSA-WITH-AES-256-CBC-SHA256"

DTLS no legacy renegotiation with MFL=2048, DHE-RSA-WITH-AES-256-CBC-SHA256
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA256:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_2048:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION:"TLS-DHE-RSA-WITH-AES-256-CBC-SHA256"

DTLS no legacy renegotiation with MFL=4096, DHE-RSA-WITH-AES-256-CBC-SHA256
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA256:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_4096:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION:"TLS-DHE-RSA-WITH-AES-256-CBC-SHA256"

DTLS legacy allow renegotiation with MFL=512, DHE-RSA-WITH-AES-256-CBC-SHA256
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA256:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_512:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION:"TLS-DHE-RSA-WITH-AES-256-CBC-SHA256"

DTLS legacy allow renegotiation with MFL=1024, DHE-RSA-WITH-AES-256-CBC-SHA256
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA256:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_1024:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION:"TLS-DHE-RSA-WITH-AES-256-CBC-SHA256"

DTLS legacy allow renegotiation with MFL=2048, DHE-RSA-WITH-AES-256-CBC-SHA256
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA256:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_2048:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION:"TLS-DHE-RSA-WITH-AES-256-CBC-SHA256"

DTLS legacy allow renegotiation with MFL=4096, DHE-RSA-WITH-AES-256-CBC-SHA256
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA256:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_4096:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION:"TLS-DHE-RSA-WITH-AES-256-CBC-SHA256"

DTLS legacy break handshake renegotiation with MFL=512, DHE-RSA-WITH-AES-256-CBC-SHA256
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA256:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_512:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE:"TLS-DHE-RSA-WITH-AES-256-CBC-SHA256"

DTLS legacy break handshake renegotiation with MFL=1024, DHE-RSA-WITH-AES-256-CBC-SHA256
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA256:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_1024:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE:"TLS-DHE-RSA-WITH-AES-256-CBC-SHA256"

DTLS legacy break handshake renegotiation with MFL=2048, DHE-RSA-WITH-AES-256-CBC-SHA256
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA256:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_2048:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE:"TLS-DHE-RSA-WITH-AES-256-CBC-SHA256"

DTLS legacy break handshake renegotiation with MFL=4096, DHE-RSA-WITH-AES-256-CBC-SHA256
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA256:MBEDTLS_KEY_EXCHANGE_DHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_4096:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE:"TLS-DHE-RSA-WITH-AES-256-CBC-SHA256"

DTLS no legacy renegotiation with MFL=512, ECDHE-RSA-WITH-AES-256-CBC-SHA384
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA384:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_512:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION:"TLS-ECDHE-RSA-WITH-AES-256-CBC-SHA384"

DTLS no legacy renegotiation with MFL=1024, ECDHE-RSA-WITH-AES-256-CBC-SHA384
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA384:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_1024:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION:"TLS-ECDHE-RSA-WITH-AES-256-CBC-SHA384"

DTLS no legacy renegotiation with MFL=2048, ECDHE-RSA-WITH-AES-256-CBC-SHA384
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA384:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_2048:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION:"TLS-ECDHE-RSA-WITH-AES-256-CBC-SHA384"

DTLS no legacy renegotiation with MFL=4096, ECDHE-RSA-WITH-AES-256-CBC-SHA384
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA384:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_4096:MBEDTLS_SSL_LEGACY_NO_RENEGOTIATION:"TLS-ECDHE-RSA-WITH-AES-256-CBC-SHA384"

DTLS legacy allow renegotiation with MFL=512, ECDHE-RSA-WITH-AES-256-CBC-SHA384
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA384:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_512:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION:"TLS-ECDHE-RSA-WITH-AES-256-CBC-SHA384"

DTLS legacy allow renegotiation with MFL=1024, ECDHE-RSA-WITH-AES-256-CBC-SHA384
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA384:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_1024:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION:"TLS-ECDHE-RSA-WITH-AES-256-CBC-SHA384"

DTLS legacy allow renegotiation with MFL=2048, ECDHE-RSA-WITH-AES-256-CBC-SHA384
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA384:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_2048:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION:"TLS-ECDHE-RSA-WITH-AES-256-CBC-SHA384"

DTLS legacy allow renegotiation with MFL=4096, ECDHE-RSA-WITH-AES-256-CBC-SHA384
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA384:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_4096:MBEDTLS_SSL_LEGACY_ALLOW_RENEGOTIATION:"TLS-ECDHE-RSA-WITH-AES-256-CBC-SHA384"

DTLS legacy break handshake renegotiation with MFL=512, ECDHE-RSA-WITH-AES-256-CBC-SHA384
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA384:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_512:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE:"TLS-ECDHE-RSA-WITH-AES-256-CBC-SHA384"

DTLS legacy break handshake renegotiation with MFL=1024, ECDHE-RSA-WITH-AES-256-CBC-SHA384
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA384:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_1024:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE:"TLS-ECDHE-RSA-WITH-AES-256-CBC-SHA384"

DTLS legacy break handshake renegotiation with MFL=2048, ECDHE-RSA-WITH-AES-256-CBC-SHA384
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA384:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_2048:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE:"TLS-ECDHE-RSA-WITH-AES-256-CBC-SHA384"

DTLS legacy break handshake renegotiation with MFL=4096, ECDHE-RSA-WITH-AES-256-CBC-SHA384
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_MD_CAN_SHA384:MBEDTLS_KEY_EXCHANGE_ECDHE_RSA_ENABLED:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
resize_buffers_renegotiate_mfl:MBEDTLS_SSL_MAX_FRAG_LEN_4096:MBEDTLS_SSL_LEGACY_BREAK_HANDSHAKE:"TLS-ECDHE-RSA-WITH-AES-256-CBC-SHA384"

SSL DTLS replay: initial state, seqnum 0
ssl_dtls_replay:"":"000000000000":0

SSL DTLS replay: 0 seen, 1 arriving
ssl_dtls_replay:"000000000000":"000000000001":0

SSL DTLS replay: 0 seen, 0 replayed
ssl_dtls_replay:"000000000000":"000000000000":-1

SSL DTLS replay: 0-1 seen, 2 arriving
ssl_dtls_replay:"000000000000000000000001":"000000000002":0

SSL DTLS replay: 0-1 seen, 1 replayed
ssl_dtls_replay:"000000000000000000000001":"000000000001":-1

SSL DTLS replay: 0-1 seen, 0 replayed
ssl_dtls_replay:"000000000000000000000001":"000000000000":-1

SSL DTLS replay: new
ssl_dtls_replay:"abcd12340000abcd12340001abcd12340003":"abcd12340004":0

SSL DTLS replay: way new
ssl_dtls_replay:"abcd12340000abcd12340001abcd12340003":"abcd12350000":0

SSL DTLS replay: delayed
ssl_dtls_replay:"abcd12340000abcd12340001abcd12340003":"abcd12340002":0

SSL DTLS replay: last replayed
ssl_dtls_replay:"abcd12340000abcd12340001abcd12340003":"abcd12340003":-1

SSL DTLS replay: older replayed
ssl_dtls_replay:"abcd12340000abcd12340001abcd12340003":"abcd12340001":-1

SSL DTLS replay: most recent in window, replayed
ssl_dtls_replay:"abcd12340000abcd12340002abcd12340003":"abcd12340002":-1

SSL DTLS replay: oldest in window, replayed
ssl_dtls_replay:"abcd12340000abcd12340001abcd1234003f":"abcd12340000":-1

SSL DTLS replay: oldest in window, not replayed
ssl_dtls_replay:"abcd12340001abcd12340002abcd1234003f":"abcd12340000":0

SSL DTLS replay: just out of the window
ssl_dtls_replay:"abcd12340001abcd12340002abcd1234003f":"abcd1233ffff":-1

SSL DTLS replay: way out of the window
ssl_dtls_replay:"abcd12340001abcd12340002abcd1234003f":"abcd12330000":-1

SSL DTLS replay: big jump then replay
ssl_dtls_replay:"abcd12340000abcd12340100":"abcd12340100":-1

SSL DTLS replay: big jump then new
ssl_dtls_replay:"abcd12340000abcd12340100":"abcd12340101":0

SSL DTLS replay: big jump then just delayed
ssl_dtls_replay:"abcd12340000abcd12340100":"abcd123400ff":0

SSL SET_HOSTNAME memory leak: call ssl_set_hostname twice
ssl_set_hostname_twice:"server0":"server1"

SSL session serialization: Wrong major version
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
ssl_session_serialize_version_check:1:0:0:0:0:MBEDTLS_SSL_VERSION_TLS1_2

SSL session serialization: Wrong minor version
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
ssl_session_serialize_version_check:0:1:0:0:0:MBEDTLS_SSL_VERSION_TLS1_2

SSL session serialization: Wrong patch version
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
ssl_session_serialize_version_check:0:0:1:0:0:MBEDTLS_SSL_VERSION_TLS1_2

SSL session serialization: Wrong config
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
ssl_session_serialize_version_check:0:0:0:1:0:MBEDTLS_SSL_VERSION_TLS1_2

TLS 1.3: CLI: session serialization: Wrong major version
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SESSION_TICKETS
ssl_session_serialize_version_check:1:0:0:0:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3

TLS 1.3: CLI: session serialization: Wrong minor version
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SESSION_TICKETS
ssl_session_serialize_version_check:0:1:0:0:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3

TLS 1.3: CLI: session serialization: Wrong patch version
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SESSION_TICKETS
ssl_session_serialize_version_check:0:0:1:0:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3

TLS 1.3: CLI: session serialization: Wrong config
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_SESSION_TICKETS
ssl_session_serialize_version_check:0:0:0:1:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3

TLS 1.3: SRV: session serialization: Wrong major version
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_SESSION_TICKETS
ssl_session_serialize_version_check:1:0:0:0:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3

TLS 1.3: SRV: session serialization: Wrong minor version
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_SESSION_TICKETS
ssl_session_serialize_version_check:0:1:0:0:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3

TLS 1.3: SRV: session serialization: Wrong patch version
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_SESSION_TICKETS
ssl_session_serialize_version_check:0:0:1:0:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3

TLS 1.3: SRV: session serialization: Wrong config
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_SESSION_TICKETS
ssl_session_serialize_version_check:0:0:0:1:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3

Test Session id & Ciphersuite accessors TLS 1.2
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
ssl_session_id_accessors_check:MBEDTLS_SSL_VERSION_TLS1_2

Test Session id & Ciphersuite accessors TLS 1.3
depends_on:MBEDTLS_SSL_PROTO_TLS1_3
ssl_session_id_accessors_check:MBEDTLS_SSL_VERSION_TLS1_3

Record crypt, AES-128-CBC, 1.2, SHA-384
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-128-CBC, 1.2, SHA-384, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-128-CBC, 1.2, SHA-384, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-128-CBC, 1.2, SHA-384, EtM
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-128-CBC, 1.2, SHA-384, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-128-CBC, 1.2, SHA-384, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-128-CBC, 1.2, SHA-256
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-128-CBC, 1.2, SHA-256, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-128-CBC, 1.2, SHA-256, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-128-CBC, 1.2, SHA-256, EtM
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-128-CBC, 1.2, SHA-256, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-128-CBC, 1.2, SHA-256, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-128-CBC, 1.2, SHA-1
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-128-CBC, 1.2, SHA-1, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-128-CBC, 1.2, SHA-1, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-128-CBC, 1.2, SHA-1, EtM
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-128-CBC, 1.2, SHA-1, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-128-CBC, 1.2, SHA-1, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-128-CBC, 1.2, MD5
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-128-CBC, 1.2, MD5, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-128-CBC, 1.2, MD5, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-128-CBC, 1.2, MD5, EtM
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-128-CBC, 1.2, MD5, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-128-CBC, 1.2, MD5, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-256-CBC, 1.2, SHA-384
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-256-CBC, 1.2, SHA-384, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-256-CBC, 1.2, SHA-384, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-256-CBC, 1.2, SHA-384, EtM
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-256-CBC, 1.2, SHA-384, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-256-CBC, 1.2, SHA-384, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-256-CBC, 1.2, SHA-256
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-256-CBC, 1.2, SHA-256, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-256-CBC, 1.2, SHA-256, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-256-CBC, 1.2, SHA-256, EtM
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-256-CBC, 1.2, SHA-256, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-256-CBC, 1.2, SHA-256, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-256-CBC, 1.2, SHA-1
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-256-CBC, 1.2, SHA-1, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-256-CBC, 1.2, SHA-1, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-256-CBC, 1.2, SHA-1, EtM
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-256-CBC, 1.2, SHA-1, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-256-CBC, 1.2, SHA-1, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-256-CBC, 1.2, MD5
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-256-CBC, 1.2, MD5, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-256-CBC, 1.2, MD5, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-256-CBC, 1.2, MD5, EtM
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-256-CBC, 1.2, MD5, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-256-CBC, 1.2, MD5, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, ARIA-128-CBC, 1.2, SHA-384
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, ARIA-128-CBC, 1.2, SHA-384, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, ARIA-128-CBC, 1.2, SHA-384, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, ARIA-128-CBC, 1.2, SHA-384, EtM
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, ARIA-128-CBC, 1.2, SHA-384, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, ARIA-128-CBC, 1.2, SHA-384, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, ARIA-128-CBC, 1.2, SHA-256
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, ARIA-128-CBC, 1.2, SHA-256, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, ARIA-128-CBC, 1.2, SHA-256, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, ARIA-128-CBC, 1.2, SHA-256, EtM
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, ARIA-128-CBC, 1.2, SHA-256, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, ARIA-128-CBC, 1.2, SHA-256, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, ARIA-128-CBC, 1.2, SHA-1
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, ARIA-128-CBC, 1.2, SHA-1, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, ARIA-128-CBC, 1.2, SHA-1, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, ARIA-128-CBC, 1.2, SHA-1, EtM
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, ARIA-128-CBC, 1.2, SHA-1, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, ARIA-128-CBC, 1.2, SHA-1, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, ARIA-128-CBC, 1.2, MD5
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, ARIA-128-CBC, 1.2, MD5, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, ARIA-128-CBC, 1.2, MD5, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, ARIA-128-CBC, 1.2, MD5, EtM
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, ARIA-128-CBC, 1.2, MD5, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, ARIA-128-CBC, 1.2, MD5, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, ARIA-256-CBC, 1.2, SHA-384
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, ARIA-256-CBC, 1.2, SHA-384, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, ARIA-256-CBC, 1.2, SHA-384, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, ARIA-256-CBC, 1.2, SHA-384, EtM
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, ARIA-256-CBC, 1.2, SHA-384, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, ARIA-256-CBC, 1.2, SHA-384, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, ARIA-256-CBC, 1.2, SHA-256
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, ARIA-256-CBC, 1.2, SHA-256, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, ARIA-256-CBC, 1.2, SHA-256, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, ARIA-256-CBC, 1.2, SHA-256, EtM
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, ARIA-256-CBC, 1.2, SHA-256, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, ARIA-256-CBC, 1.2, SHA-256, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, ARIA-256-CBC, 1.2, SHA-1
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, ARIA-256-CBC, 1.2, SHA-1, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, ARIA-256-CBC, 1.2, SHA-1, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, ARIA-256-CBC, 1.2, SHA-1, EtM
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, ARIA-256-CBC, 1.2, SHA-1, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, ARIA-256-CBC, 1.2, SHA-1, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, ARIA-256-CBC, 1.2, MD5
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, ARIA-256-CBC, 1.2, MD5, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, ARIA-256-CBC, 1.2, MD5, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, ARIA-256-CBC, 1.2, MD5, EtM
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, ARIA-256-CBC, 1.2, MD5, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, ARIA-256-CBC, 1.2, MD5, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-128-CBC, 1.2, SHA-384
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-128-CBC, 1.2, SHA-384, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-128-CBC, 1.2, SHA-384, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-128-CBC, 1.2, SHA-384, EtM
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-128-CBC, 1.2, SHA-384, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-128-CBC, 1.2, SHA-384, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-128-CBC, 1.2, SHA-256
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-128-CBC, 1.2, SHA-256, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-128-CBC, 1.2, SHA-256, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-128-CBC, 1.2, SHA-256, EtM
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-128-CBC, 1.2, SHA-256, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-128-CBC, 1.2, SHA-256, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-128-CBC, 1.2, SHA-1
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-128-CBC, 1.2, SHA-1, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-128-CBC, 1.2, SHA-1, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-128-CBC, 1.2, SHA-1, EtM
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-128-CBC, 1.2, SHA-1, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-128-CBC, 1.2, SHA-1, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-128-CBC, 1.2, MD5
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-128-CBC, 1.2, MD5, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-128-CBC, 1.2, MD5, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-128-CBC, 1.2, MD5, EtM
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-128-CBC, 1.2, MD5, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-128-CBC, 1.2, MD5, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-256-CBC, 1.2, SHA-384
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-256-CBC, 1.2, SHA-384, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-256-CBC, 1.2, SHA-384, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-256-CBC, 1.2, SHA-384, EtM
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-256-CBC, 1.2, SHA-384, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-256-CBC, 1.2, SHA-384, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-256-CBC, 1.2, SHA-256
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-256-CBC, 1.2, SHA-256, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-256-CBC, 1.2, SHA-256, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-256-CBC, 1.2, SHA-256, EtM
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-256-CBC, 1.2, SHA-256, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-256-CBC, 1.2, SHA-256, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-256-CBC, 1.2, SHA-1
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-256-CBC, 1.2, SHA-1, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-256-CBC, 1.2, SHA-1, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-256-CBC, 1.2, SHA-1, EtM
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-256-CBC, 1.2, SHA-1, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-256-CBC, 1.2, SHA-1, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-256-CBC, 1.2, MD5
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-256-CBC, 1.2, MD5, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-256-CBC, 1.2, MD5, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-256-CBC, 1.2, MD5, EtM
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-256-CBC, 1.2, MD5, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-256-CBC, 1.2, MD5, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-128-GCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-128-GCM, 1.3
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_3:0:0

Record crypt, AES-128-GCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-128-GCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-192-GCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_192_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-192-GCM, 1.3
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_HAVE_GCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_192_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_3:0:0

Record crypt, AES-192-GCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_192_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-192-GCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_192_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-256-GCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-256-GCM, 1.3
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_HAVE_GCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_3:0:0

Record crypt, AES-256-GCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-256-GCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-128-GCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-128-GCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-128-GCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-192-GCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_192_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-192-GCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_192_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-192-GCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_192_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-256-GCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-256-GCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-256-GCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-128-CCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-128-CCM, 1.3
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_3:0:0

Record crypt, AES-128-CCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-128-CCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-128-CCM, 1.2, short tag
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-128-CCM, 1.2, short tag, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-128-CCM, 1.2, short tag, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_AES_128_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-192-CCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_192_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-192-CCM, 1.3
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_192_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_3:0:0

Record crypt, AES-192-CCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_192_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-192-CCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_192_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-192-CCM, 1.2, short tag
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_192_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-192-CCM, 1.2, short tag, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_192_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-192-CCM, 1.2, short tag, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_192_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-256-CCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-256-CCM, 1.3
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_3:0:0

Record crypt, AES-256-CCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-256-CCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, AES-256-CCM, 1.2, short tag
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, AES-256-CCM, 1.2, short tag, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, AES-256-CCM, 1.2, short tag, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record:MBEDTLS_CIPHER_AES_256_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-128-CCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-128-CCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-128-CCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-128-CCM, 1.2, short tag
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-128-CCM, 1.2, short tag, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-128-CCM, 1.2, short tag, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_128_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-192-CCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_192_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-192-CCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_192_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-192-CCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_192_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-192-CCM, 1.2, short tag
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_192_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-192-CCM, 1.2, short tag, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_192_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-192-CCM, 1.2, short tag, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_192_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-256-CCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-256-CCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-256-CCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, CAMELLIA-256-CCM, 1.2, short tag
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, CAMELLIA-256-CCM, 1.2, short tag, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, CAMELLIA-256-CCM, 1.2, short tag, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record:MBEDTLS_CIPHER_CAMELLIA_256_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, NULL cipher, 1.2, SHA-384
depends_on:MBEDTLS_CIPHER_NULL_CIPHER:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record:MBEDTLS_CIPHER_NULL:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, NULL cipher, 1.2, SHA-384, EtM
depends_on:MBEDTLS_CIPHER_NULL_CIPHER:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_NULL:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, NULL cipher, 1.2, SHA-256
depends_on:MBEDTLS_CIPHER_NULL_CIPHER:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record:MBEDTLS_CIPHER_NULL:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, NULL cipher, 1.2, SHA-256, EtM
depends_on:MBEDTLS_CIPHER_NULL_CIPHER:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_NULL:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, NULL cipher, 1.2, SHA-1
depends_on:MBEDTLS_CIPHER_NULL_CIPHER:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record:MBEDTLS_CIPHER_NULL:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, NULL cipher, 1.2, SHA-1, EtM
depends_on:MBEDTLS_CIPHER_NULL_CIPHER:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_NULL:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, NULL cipher, 1.2, MD5
depends_on:MBEDTLS_CIPHER_NULL_CIPHER:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record:MBEDTLS_CIPHER_NULL:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, NULL cipher, 1.2, MD5, EtM
depends_on:MBEDTLS_CIPHER_NULL_CIPHER:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record:MBEDTLS_CIPHER_NULL:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, ChachaPoly
depends_on:MBEDTLS_SSL_HAVE_CHACHAPOLY:MBEDTLS_SSL_PROTO_TLS1_2
ssl_crypt_record:MBEDTLS_CIPHER_CHACHA20_POLY1305:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, ChachaPoly, 1.3
depends_on:MBEDTLS_SSL_HAVE_CHACHAPOLY:MBEDTLS_SSL_PROTO_TLS1_3
ssl_crypt_record:MBEDTLS_CIPHER_CHACHA20_POLY1305:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_3:0:0

Record crypt, little space, ChachaPoly
depends_on:MBEDTLS_SSL_HAVE_CHACHAPOLY:MBEDTLS_SSL_PROTO_TLS1_2
ssl_crypt_record_small:MBEDTLS_CIPHER_CHACHA20_POLY1305:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, ChachaPoly, 1.3
depends_on:MBEDTLS_SSL_HAVE_CHACHAPOLY:MBEDTLS_SSL_PROTO_TLS1_3
ssl_crypt_record_small:MBEDTLS_CIPHER_CHACHA20_POLY1305:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_3:0:0

Record crypt, little space, ChachaPoly, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CHACHAPOLY:MBEDTLS_SSL_PROTO_TLS1_2
ssl_crypt_record_small:MBEDTLS_CIPHER_CHACHA20_POLY1305:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, ChachaPoly, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CHACHAPOLY:MBEDTLS_SSL_PROTO_TLS1_2
ssl_crypt_record_small:MBEDTLS_CIPHER_CHACHA20_POLY1305:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-128-CBC, 1.2, SHA-384
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-128-CBC, 1.2, SHA-384, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-128-CBC, 1.2, SHA-384, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-128-CBC, 1.2, SHA-384, EtM
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-128-CBC, 1.2, SHA-384, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-128-CBC, 1.2, SHA-384, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-128-CBC, 1.2, SHA-256
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-128-CBC, 1.2, SHA-256, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-128-CBC, 1.2, SHA-256, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-128-CBC, 1.2, SHA-256, EtM
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-128-CBC, 1.2, SHA-256, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-128-CBC, 1.2, SHA-256, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-128-CBC, 1.2, SHA-1
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-128-CBC, 1.2, SHA-1, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-128-CBC, 1.2, SHA-1, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-128-CBC, 1.2, SHA-1, EtM
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-128-CBC, 1.2, SHA-1, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-128-CBC, 1.2, SHA-1, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-128-CBC, 1.2, MD5
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-128-CBC, 1.2, MD5, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-128-CBC, 1.2, MD5, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-128-CBC, 1.2, MD5, EtM
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-128-CBC, 1.2, MD5, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-128-CBC, 1.2, MD5, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-256-CBC, 1.2, SHA-384
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-256-CBC, 1.2, SHA-384, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-256-CBC, 1.2, SHA-384, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-256-CBC, 1.2, SHA-384, EtM
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-256-CBC, 1.2, SHA-384, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-256-CBC, 1.2, SHA-384, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-256-CBC, 1.2, SHA-256
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-256-CBC, 1.2, SHA-256, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-256-CBC, 1.2, SHA-256, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-256-CBC, 1.2, SHA-256, EtM
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-256-CBC, 1.2, SHA-256, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-256-CBC, 1.2, SHA-256, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-256-CBC, 1.2, SHA-1
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-256-CBC, 1.2, SHA-1, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-256-CBC, 1.2, SHA-1, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-256-CBC, 1.2, SHA-1, EtM
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-256-CBC, 1.2, SHA-1, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-256-CBC, 1.2, SHA-1, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-256-CBC, 1.2, MD5
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-256-CBC, 1.2, MD5, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-256-CBC, 1.2, MD5, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-256-CBC, 1.2, MD5, EtM
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-256-CBC, 1.2, MD5, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-256-CBC, 1.2, MD5, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, ARIA-128-CBC, 1.2, SHA-384
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, ARIA-128-CBC, 1.2, SHA-384, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, ARIA-128-CBC, 1.2, SHA-384, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, ARIA-128-CBC, 1.2, SHA-384, EtM
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, ARIA-128-CBC, 1.2, SHA-384, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, ARIA-128-CBC, 1.2, SHA-384, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, ARIA-128-CBC, 1.2, SHA-256
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, ARIA-128-CBC, 1.2, SHA-256, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, ARIA-128-CBC, 1.2, SHA-256, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, ARIA-128-CBC, 1.2, SHA-256, EtM
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, ARIA-128-CBC, 1.2, SHA-256, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, ARIA-128-CBC, 1.2, SHA-256, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, ARIA-128-CBC, 1.2, SHA-1
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, ARIA-128-CBC, 1.2, SHA-1, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, ARIA-128-CBC, 1.2, SHA-1, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, ARIA-128-CBC, 1.2, SHA-1, EtM
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, ARIA-128-CBC, 1.2, SHA-1, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, ARIA-128-CBC, 1.2, SHA-1, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, ARIA-128-CBC, 1.2, MD5
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, ARIA-128-CBC, 1.2, MD5, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, ARIA-128-CBC, 1.2, MD5, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, ARIA-128-CBC, 1.2, MD5, EtM
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, ARIA-128-CBC, 1.2, MD5, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, ARIA-128-CBC, 1.2, MD5, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_128_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, ARIA-256-CBC, 1.2, SHA-384
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, ARIA-256-CBC, 1.2, SHA-384, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, ARIA-256-CBC, 1.2, SHA-384, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, ARIA-256-CBC, 1.2, SHA-384, EtM
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, ARIA-256-CBC, 1.2, SHA-384, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, ARIA-256-CBC, 1.2, SHA-384, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, ARIA-256-CBC, 1.2, SHA-256
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, ARIA-256-CBC, 1.2, SHA-256, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, ARIA-256-CBC, 1.2, SHA-256, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, ARIA-256-CBC, 1.2, SHA-256, EtM
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, ARIA-256-CBC, 1.2, SHA-256, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, ARIA-256-CBC, 1.2, SHA-256, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, ARIA-256-CBC, 1.2, SHA-1
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, ARIA-256-CBC, 1.2, SHA-1, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, ARIA-256-CBC, 1.2, SHA-1, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, ARIA-256-CBC, 1.2, SHA-1, EtM
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, ARIA-256-CBC, 1.2, SHA-1, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, ARIA-256-CBC, 1.2, SHA-1, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, ARIA-256-CBC, 1.2, MD5
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, ARIA-256-CBC, 1.2, MD5, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, ARIA-256-CBC, 1.2, MD5, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, ARIA-256-CBC, 1.2, MD5, EtM
depends_on:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, ARIA-256-CBC, 1.2, MD5, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, ARIA-256-CBC, 1.2, MD5, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_ARIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_ARIA_256_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-128-CBC, 1.2, SHA-384
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-128-CBC, 1.2, SHA-384, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-128-CBC, 1.2, SHA-384, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-128-CBC, 1.2, SHA-384, EtM
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-128-CBC, 1.2, SHA-384, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-128-CBC, 1.2, SHA-384, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-128-CBC, 1.2, SHA-256
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-128-CBC, 1.2, SHA-256, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-128-CBC, 1.2, SHA-256, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-128-CBC, 1.2, SHA-256, EtM
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-128-CBC, 1.2, SHA-256, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-128-CBC, 1.2, SHA-256, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-128-CBC, 1.2, SHA-1
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-128-CBC, 1.2, SHA-1, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-128-CBC, 1.2, SHA-1, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-128-CBC, 1.2, SHA-1, EtM
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-128-CBC, 1.2, SHA-1, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-128-CBC, 1.2, SHA-1, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-128-CBC, 1.2, MD5
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-128-CBC, 1.2, MD5, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-128-CBC, 1.2, MD5, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-128-CBC, 1.2, MD5, EtM
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-128-CBC, 1.2, MD5, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-128-CBC, 1.2, MD5, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-256-CBC, 1.2, SHA-384
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-256-CBC, 1.2, SHA-384, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-256-CBC, 1.2, SHA-384, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-256-CBC, 1.2, SHA-384, EtM
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-256-CBC, 1.2, SHA-384, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-256-CBC, 1.2, SHA-384, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-256-CBC, 1.2, SHA-256
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-256-CBC, 1.2, SHA-256, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-256-CBC, 1.2, SHA-256, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-256-CBC, 1.2, SHA-256, EtM
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-256-CBC, 1.2, SHA-256, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-256-CBC, 1.2, SHA-256, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-256-CBC, 1.2, SHA-1
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-256-CBC, 1.2, SHA-1, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-256-CBC, 1.2, SHA-1, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-256-CBC, 1.2, SHA-1, EtM
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-256-CBC, 1.2, SHA-1, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-256-CBC, 1.2, SHA-1, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-256-CBC, 1.2, MD5
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-256-CBC, 1.2, MD5, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-256-CBC, 1.2, MD5, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-256-CBC, 1.2, MD5, EtM
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-256-CBC, 1.2, MD5, EtM, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-256-CBC, 1.2, MD5, EtM, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_HAVE_CBC:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CBC:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-128-GCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-128-GCM, 1.3
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_3:0:0

Record crypt, little space, AES-128-GCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-128-GCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-192-GCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_192_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-192-GCM, 1.3
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_HAVE_GCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_192_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_3:0:0

Record crypt, little space, AES-192-GCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_192_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-192-GCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_192_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-256-GCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-256-GCM, 1.3
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_HAVE_GCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_3:0:0

Record crypt, little space, AES-256-GCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-256-GCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-128-GCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-128-GCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-128-GCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-192-GCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_192_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-192-GCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_192_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-192-GCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_192_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-256-GCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-256-GCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-256-GCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_GCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_GCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-128-CCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-128-CCM, 1.3
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_3:0:0

Record crypt, little space, AES-128-CCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-128-CCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-128-CCM, 1.2, short tag
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-128-CCM, 1.2, short tag, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-128-CCM, 1.2, short tag, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_128_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-192-CCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_192_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-192-CCM, 1.3
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_192_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_3:0:0

Record crypt, little space, AES-192-CCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_192_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-192-CCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_192_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-192-CCM, 1.2, short tag
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_192_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-192-CCM, 1.2, short tag, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_192_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-192-CCM, 1.2, short tag, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_192_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-256-CCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-256-CCM, 1.3
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_3:0:0

Record crypt, little space, AES-256-CCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-256-CCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, AES-256-CCM, 1.2, short tag
depends_on:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, AES-256-CCM, 1.2, short tag, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, AES-256-CCM, 1.2, short tag, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_AES:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
ssl_crypt_record_small:MBEDTLS_CIPHER_AES_256_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-128-CCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-128-CCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-128-CCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-128-CCM, 1.2, short tag
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-128-CCM, 1.2, short tag, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-128-CCM, 1.2, short tag, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_128_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-192-CCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_192_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-192-CCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_192_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-192-CCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_192_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-192-CCM, 1.2, short tag
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_192_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-192-CCM, 1.2, short tag, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_192_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-192-CCM, 1.2, short tag, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_192_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-256-CCM, 1.2
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-256-CCM, 1.2, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-256-CCM, 1.2, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CCM:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, CAMELLIA-256-CCM, 1.2, short tag
depends_on:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, CAMELLIA-256-CCM, 1.2, short tag, CID 4+4
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:4

Record crypt, little space, CAMELLIA-256-CCM, 1.2, short tag, CID 4+0
depends_on:MBEDTLS_SSL_DTLS_CONNECTION_ID:MBEDTLS_SSL_HAVE_CAMELLIA:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_HAVE_CCM
ssl_crypt_record_small:MBEDTLS_CIPHER_CAMELLIA_256_CCM:MBEDTLS_MD_MD5:0:1:MBEDTLS_SSL_VERSION_TLS1_2:4:0

Record crypt, little space, NULL cipher, 1.2, SHA-384
depends_on:MBEDTLS_CIPHER_NULL_CIPHER:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384
ssl_crypt_record_small:MBEDTLS_CIPHER_NULL:MBEDTLS_MD_SHA384:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, NULL cipher, 1.2, SHA-384, EtM
depends_on:MBEDTLS_CIPHER_NULL_CIPHER:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_NULL:MBEDTLS_MD_SHA384:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, NULL cipher, 1.2, SHA-256
depends_on:MBEDTLS_CIPHER_NULL_CIPHER:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256
ssl_crypt_record_small:MBEDTLS_CIPHER_NULL:MBEDTLS_MD_SHA256:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, NULL cipher, 1.2, SHA-256, EtM
depends_on:MBEDTLS_CIPHER_NULL_CIPHER:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_NULL:MBEDTLS_MD_SHA256:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, NULL cipher, 1.2, SHA-1
depends_on:MBEDTLS_CIPHER_NULL_CIPHER:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1
ssl_crypt_record_small:MBEDTLS_CIPHER_NULL:MBEDTLS_MD_SHA1:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, NULL cipher, 1.2, SHA-1, EtM
depends_on:MBEDTLS_CIPHER_NULL_CIPHER:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_SHA1:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_NULL:MBEDTLS_MD_SHA1:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, NULL cipher, 1.2, MD5
depends_on:MBEDTLS_CIPHER_NULL_CIPHER:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5
ssl_crypt_record_small:MBEDTLS_CIPHER_NULL:MBEDTLS_MD_MD5:0:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

Record crypt, little space, NULL cipher, 1.2, MD5, EtM
depends_on:MBEDTLS_CIPHER_NULL_CIPHER:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_MD_CAN_MD5:MBEDTLS_SSL_ENCRYPT_THEN_MAC
ssl_crypt_record_small:MBEDTLS_CIPHER_NULL:MBEDTLS_MD_MD5:1:0:MBEDTLS_SSL_VERSION_TLS1_2:0:0

SSL TLS 1.3 Key schedule: Secret evolution #1
# Vector from TLS 1.3 Byte by Byte (https://tls13.ulfheim.net/)
# Initial secret to Early Secret
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_key_evolution:PSA_ALG_SHA_256:"":"":"33ad0a1c607ec03b09e6cd9893680ce210adf300aa1f2660e1b22e10f170f92a"

SSL TLS 1.3 Key schedule: Secret evolution #2
# Vector from TLS 1.3 Byte by Byte (https://tls13.ulfheim.net/)
# Early secret to Handshake Secret
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_key_evolution:PSA_ALG_SHA_256:"33ad0a1c607ec03b09e6cd9893680ce210adf300aa1f2660e1b22e10f170f92a":"df4a291baa1eb7cfa6934b29b474baad2697e29f1f920dcc77c8a0a088447624":"fb9fc80689b3a5d02c33243bf69a1b1b20705588a794304a6e7120155edf149a"

SSL TLS 1.3 Key schedule: Secret evolution #3
# Vector from TLS 1.3 Byte by Byte (https://tls13.ulfheim.net/)
# Handshake secret to Master Secret
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_key_evolution:PSA_ALG_SHA_256:"fb9fc80689b3a5d02c33243bf69a1b1b20705588a794304a6e7120155edf149a":"":"7f2882bb9b9a46265941653e9c2f19067118151e21d12e57a7b6aca1f8150c8d"

SSL TLS 1.3 Key schedule: HKDF Expand Label #1
# Vector from TLS 1.3 Byte by Byte (https://tls13.ulfheim.net/)
# Server handshake traffic secret -> Server traffic key
# HKDF-Expand-Label(server_handshake_secret, "key", "", 16)
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_hkdf_expand_label:PSA_ALG_SHA_256:"a2067265e7f0652a923d5d72ab0467c46132eeb968b6a32d311c805868548814":tls13_label_key:"":16:"844780a7acad9f980fa25c114e43402a"

SSL TLS 1.3 Key schedule: HKDF Expand Label #2
# Vector from TLS 1.3 Byte by Byte (https://tls13.ulfheim.net/)
# Server handshake traffic secret -> Server traffic IV
# HKDF-Expand-Label(server_handshake_secret, "iv", "", 12)
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_hkdf_expand_label:PSA_ALG_SHA_256:"a2067265e7f0652a923d5d72ab0467c46132eeb968b6a32d311c805868548814":tls13_label_iv:"":12:"4c042ddc120a38d1417fc815"

SSL TLS 1.3 Key schedule: HKDF Expand Label #3
# Vector from TLS 1.3 Byte by Byte (https://tls13.ulfheim.net/)
# Client handshake traffic secret -> Client traffic key
# HKDF-Expand-Label(client_handshake_secret, "key", "", 16)
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_hkdf_expand_label:PSA_ALG_SHA_256:"ff0e5b965291c608c1e8cd267eefc0afcc5e98a2786373f0db47b04786d72aea":tls13_label_key:"":16:"7154f314e6be7dc008df2c832baa1d39"

SSL TLS 1.3 Key schedule: HKDF Expand Label #4
# Vector from TLS 1.3 Byte by Byte (https://tls13.ulfheim.net/)
# Client handshake traffic secret -> Client traffic IV
# HKDF-Expand-Label(client_handshake_secret, "iv", "", 12)
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_hkdf_expand_label:PSA_ALG_SHA_256:"ff0e5b965291c608c1e8cd267eefc0afcc5e98a2786373f0db47b04786d72aea":tls13_label_iv:"":12:"71abc2cae4c699d47c600268"

SSL TLS 1.3 Key schedule: HKDF Expand Label #5 (RFC 8448)
# Vector from RFC 8448
# Server handshake traffic secret -> Server traffic IV
# HKDF-Expand-Label(server_handshake_secret, "iv", "", 12)
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_hkdf_expand_label:PSA_ALG_SHA_256:"b67b7d690cc16c4e75e54213cb2d37b4e9c912bcded9105d42befd59d391ad38":tls13_label_iv:"":12:"5d313eb2671276ee13000b30"

SSL TLS 1.3 Key schedule: HKDF Expand Label #6 (RFC 8448)
# Vector from RFC 8448
# Server handshake traffic secret -> Server traffic Key
# HKDF-Expand-Label(server_handshake_secret, "key", "", 16)
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_hkdf_expand_label:PSA_ALG_SHA_256:"b67b7d690cc16c4e75e54213cb2d37b4e9c912bcded9105d42befd59d391ad38":tls13_label_key:"":16:"3fce516009c21727d0f2e4e86ee403bc"

SSL TLS 1.3 Key schedule: HKDF Expand Label #7 (RFC 8448)
# Vector from RFC 8448
# Client handshake traffic secret -> Client traffic IV
# HKDF-Expand-Label(client_handshake_secret, "iv", "", 12)
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_hkdf_expand_label:PSA_ALG_SHA_256:"b3eddb126e067f35a780b3abf45e2d8f3b1a950738f52e9600746a0e27a55a21":tls13_label_iv:"":12:"5bd3c71b836e0b76bb73265f"

SSL TLS 1.3 Key schedule: HKDF Expand Label #8 (RFC 8448)
# Vector from RFC 8448
# Client handshake traffic secret -> Client traffic Key
# HKDF-Expand-Label(client_handshake_secret, "key", "", 16)
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_hkdf_expand_label:PSA_ALG_SHA_256:"b3eddb126e067f35a780b3abf45e2d8f3b1a950738f52e9600746a0e27a55a21":tls13_label_key:"":16:"dbfaa693d1762c5b666af5d950258d01"

SSL TLS 1.3 Key schedule: HKDF Expand Label #9 (RFC 8448)
# Calculation of finished_key
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_hkdf_expand_label:PSA_ALG_SHA_256:"2faac08f851d35fea3604fcb4de82dc62c9b164a70974d0462e27f1ab278700f":tls13_label_finished:"":32:"5ace394c26980d581243f627d1150ae27e37fa52364e0a7f20ac686d09cd0e8e"

SSL TLS 1.3 Key schedule: HKDF Expand Label #10 (RFC 8448)
# Calculation of resumption key
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_hkdf_expand_label:PSA_ALG_SHA_256:"7df235f2031d2a051287d02b0241b0bfdaf86cc856231f2d5aba46c434ec196c":tls13_label_resumption:"0000":32:"4ecd0eb6ec3b4d87f5d6028f922ca4c5851a277fd41311c9e62d2c9492e1c4f3"

SSL TLS 1.3 Key schedule: Traffic key generation #1
# Vector from TLS 1.3 Byte by Byte (https://tls13.ulfheim.net/)
# Client/Server handshake traffic secrets -> Client/Server traffic {Key,IV}
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_traffic_key_generation:PSA_ALG_SHA_256:"a2067265e7f0652a923d5d72ab0467c46132eeb968b6a32d311c805868548814":"ff0e5b965291c608c1e8cd267eefc0afcc5e98a2786373f0db47b04786d72aea":12:16:"844780a7acad9f980fa25c114e43402a":"4c042ddc120a38d1417fc815":"7154f314e6be7dc008df2c832baa1d39":"71abc2cae4c699d47c600268"

SSL TLS 1.3 Key schedule: Traffic key generation #2 (RFC 8448)
# Vector RFC 8448
# Client/Server handshake traffic secrets -> Client/Server traffic {Key,IV}
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_traffic_key_generation:PSA_ALG_SHA_256:"a2067265e7f0652a923d5d72ab0467c46132eeb968b6a32d311c805868548814":"ff0e5b965291c608c1e8cd267eefc0afcc5e98a2786373f0db47b04786d72aea":12:16:"844780a7acad9f980fa25c114e43402a":"4c042ddc120a38d1417fc815":"7154f314e6be7dc008df2c832baa1d39":"71abc2cae4c699d47c600268"

SSL TLS 1.3 Key schedule: Derive-Secret( ., "derived", "")
# Vector from TLS 1.3 Byte by Byte (https://tls13.ulfheim.net/)
# Derive-Secret( Early-Secret, "derived", "")
# Tests the case where context isn't yet hashed (empty string here,
# but still needs to be hashed)
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_derive_secret:PSA_ALG_SHA_256:"33ad0a1c607ec03b09e6cd9893680ce210adf300aa1f2660e1b22e10f170f92a":tls13_label_derived:"":32:MBEDTLS_SSL_TLS1_3_CONTEXT_UNHASHED:"6f2615a108c702c5678f54fc9dbab69716c076189c48250cebeac3576c3611ba"

SSL TLS 1.3 Key schedule: Derive-Secret( ., "s ap traffic", hash) #1
# Vector from TLS 1.3 Byte by Byte (https://tls13.ulfheim.net/)
# Derive-Secret( MasterSecret, "s ap traffic", hash)
# Tests the case where context is already hashed
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_derive_secret:PSA_ALG_SHA_256:"7f2882bb9b9a46265941653e9c2f19067118151e21d12e57a7b6aca1f8150c8d":tls13_label_s_ap_traffic:"22844b930e5e0a59a09d5ac35fc032fc91163b193874a265236e568077378d8b":32:MBEDTLS_SSL_TLS1_3_CONTEXT_HASHED:"3fc35ea70693069a277956afa23b8f4543ce68ac595f2aace05cd7a1c92023d5"

SSL TLS 1.3 Key schedule: Derive-Secret( ., "c e traffic", hash)
# Vector from RFC 8448
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_derive_secret:PSA_ALG_SHA_256:"9b2188e9b2fc6d64d71dc329900e20bb41915000f678aa839cbb797cb7d8332c":tls13_label_c_e_traffic:"08ad0fa05d7c7233b1775ba2ff9f4c5b8b59276b7f227f13a976245f5d960913":32:MBEDTLS_SSL_TLS1_3_CONTEXT_HASHED:"3fbbe6a60deb66c30a32795aba0eff7eaa10105586e7be5c09678d63b6caab62"

SSL TLS 1.3 Key schedule: Derive-Secret( ., "e exp master", hash)
# Vector from RFC 8448
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_derive_secret:PSA_ALG_SHA_256:"9b2188e9b2fc6d64d71dc329900e20bb41915000f678aa839cbb797cb7d8332c":tls13_label_e_exp_master:"08ad0fa05d7c7233b1775ba2ff9f4c5b8b59276b7f227f13a976245f5d960913":32:MBEDTLS_SSL_TLS1_3_CONTEXT_HASHED:"b2026866610937d7423e5be90862ccf24c0e6091186d34f812089ff5be2ef7df"

SSL TLS 1.3 Key schedule: Derive-Secret( ., "c hs traffic", hash)
# Vector from RFC 8448
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_derive_secret:PSA_ALG_SHA_256:"005cb112fd8eb4ccc623bb88a07c64b3ede1605363fc7d0df8c7ce4ff0fb4ae6":tls13_label_c_hs_traffic:"f736cb34fe25e701551bee6fd24c1cc7102a7daf9405cb15d97aafe16f757d03":32:MBEDTLS_SSL_TLS1_3_CONTEXT_HASHED:"2faac08f851d35fea3604fcb4de82dc62c9b164a70974d0462e27f1ab278700f"

SSL TLS 1.3 Key schedule: Derive-Secret( ., "s hs traffic", hash)
# Vector from RFC 8448
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_derive_secret:PSA_ALG_SHA_256:"005cb112fd8eb4ccc623bb88a07c64b3ede1605363fc7d0df8c7ce4ff0fb4ae6":tls13_label_s_hs_traffic:"f736cb34fe25e701551bee6fd24c1cc7102a7daf9405cb15d97aafe16f757d03":32:MBEDTLS_SSL_TLS1_3_CONTEXT_HASHED:"fe927ae271312e8bf0275b581c54eef020450dc4ecffaa05a1a35d27518e7803"

SSL TLS 1.3 Key schedule: Derive-Secret( ., "c ap traffic", hash)
# Vector from RFC 8448
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_derive_secret:PSA_ALG_SHA_256:"e2d32d4ed66dd37897a0e80c84107503ce58bf8aad4cb55a5002d77ecb890ece":tls13_label_c_ap_traffic:"b0aeffc46a2cfe33114e6fd7d51f9f04b1ca3c497dab08934a774a9d9ad7dbf3":32:MBEDTLS_SSL_TLS1_3_CONTEXT_HASHED:"2abbf2b8e381d23dbebe1dd2a7d16a8bf484cb4950d23fb7fb7fa8547062d9a1"

SSL TLS 1.3 Key schedule: Derive-Secret( ., "s ap traffic", hash) #2
# Vector from RFC 8448
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_derive_secret:PSA_ALG_SHA_256:"e2d32d4ed66dd37897a0e80c84107503ce58bf8aad4cb55a5002d77ecb890ece":tls13_label_s_ap_traffic:"b0aeffc46a2cfe33114e6fd7d51f9f04b1ca3c497dab08934a774a9d9ad7dbf3":32:MBEDTLS_SSL_TLS1_3_CONTEXT_HASHED:"cc21f1bf8feb7dd5fa505bd9c4b468a9984d554a993dc49e6d285598fb672691"

SSL TLS 1.3 Key schedule: Derive-Secret( ., "exp master", hash)
# Vector from RFC 8448
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_derive_secret:PSA_ALG_SHA_256:"e2d32d4ed66dd37897a0e80c84107503ce58bf8aad4cb55a5002d77ecb890ece":tls13_label_exp_master:"b0aeffc46a2cfe33114e6fd7d51f9f04b1ca3c497dab08934a774a9d9ad7dbf3":32:MBEDTLS_SSL_TLS1_3_CONTEXT_HASHED:"3fd93d4ffddc98e64b14dd107aedf8ee4add23f4510f58a4592d0b201bee56b4"

SSL TLS 1.3 Key schedule: Derive-Secret( ., "res master", hash)
# Vector from RFC 8448
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_derive_secret:PSA_ALG_SHA_256:"e2d32d4ed66dd37897a0e80c84107503ce58bf8aad4cb55a5002d77ecb890ece":tls13_label_res_master:"c3c122e0bd907a4a3ff6112d8fd53dbf89c773d9552e8b6b9d56d361b3a97bf6":32:MBEDTLS_SSL_TLS1_3_CONTEXT_HASHED:"5e95bdf1f89005ea2e9aa0ba85e728e3c19c5fe0c699e3f5bee59faebd0b5406"

SSL TLS 1.3 Key schedule: Early secrets derivation helper
# Vector from RFC 8448
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_derive_early_secrets:PSA_ALG_SHA_256:"9b2188e9b2fc6d64d71dc329900e20bb41915000f678aa839cbb797cb7d8332c":"08ad0fa05d7c7233b1775ba2ff9f4c5b8b59276b7f227f13a976245f5d960913":"3fbbe6a60deb66c30a32795aba0eff7eaa10105586e7be5c09678d63b6caab62":"b2026866610937d7423e5be90862ccf24c0e6091186d34f812089ff5be2ef7df"

SSL TLS 1.3 Key schedule: Handshake secrets derivation helper
# Vector from RFC 8448
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_derive_handshake_secrets:PSA_ALG_SHA_256:"005cb112fd8eb4ccc623bb88a07c64b3ede1605363fc7d0df8c7ce4ff0fb4ae6":"f736cb34fe25e701551bee6fd24c1cc7102a7daf9405cb15d97aafe16f757d03":"2faac08f851d35fea3604fcb4de82dc62c9b164a70974d0462e27f1ab278700f":"fe927ae271312e8bf0275b581c54eef020450dc4ecffaa05a1a35d27518e7803"

SSL TLS 1.3 Record Encryption, tls13.ulfheim.net Example #1
# - Server App Key: 0b6d22c8ff68097ea871c672073773bf
# - Server App IV:  1b13dd9f8d8f17091d34b349
# - Client App Key: 49134b95328f279f0183860589ac6707
# - Client App IV:  bc4dd5f7b98acff85466261d
# - App data payload: 70696e67
# - Complete record:  1703030015c74061535eb12f5f25a781957874742ab7fb305dd5
# - Padding used: No (== granularity 1)
depends_on:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_GCM:PSA_WANT_ALG_SHA_256
ssl_tls13_record_protection:MBEDTLS_TLS1_3_AES_128_GCM_SHA256:MBEDTLS_SSL_IS_CLIENT:0:1:"0b6d22c8ff68097ea871c672073773bf":"1b13dd9f8d8f17091d34b349":"49134b95328f279f0183860589ac6707":"bc4dd5f7b98acff85466261d":"70696e67":"c74061535eb12f5f25a781957874742ab7fb305dd5"

SSL TLS 1.3 Record Encryption, tls13.ulfheim.net Example #2
# - Server App Key: 0b6d22c8ff68097ea871c672073773bf
# - Server App IV:  1b13dd9f8d8f17091d34b349
# - Client App Key: 49134b95328f279f0183860589ac6707
# - Client App IV:  bc4dd5f7b98acff85466261d
# - App data payload: 706f6e67
# - Complete record:  1703030015370e5f168afa7fb16b663ecdfca3dbb81931a90ca7
# - Padding used: No (== granularity 1)
depends_on:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_GCM:PSA_WANT_ALG_SHA_256
ssl_tls13_record_protection:MBEDTLS_TLS1_3_AES_128_GCM_SHA256:MBEDTLS_SSL_IS_SERVER:1:1:"0b6d22c8ff68097ea871c672073773bf":"1b13dd9f8d8f17091d34b349":"49134b95328f279f0183860589ac6707":"bc4dd5f7b98acff85466261d":"706f6e67":"370e5f168afa7fb16b663ecdfca3dbb81931a90ca7"

SSL TLS 1.3 Record Encryption RFC 8448 Example #1
# Application Data record sent by Client in 1-RTT example of RFC 8448, Section 3
# - Server App Key: 9f 02 28 3b 6c 9c 07 ef c2 6b b9 f2 ac 92 e3 56
# - Server App IV:  cf 78 2b 88 dd 83 54 9a ad f1 e9 84
# - Client App Key: 17 42 2d da 59 6e d5 d9 ac d8 90 e3 c6 3f 50 51
# - Client App IV:  5b 78 92 3d ee 08 57 90 33 e5 23 d9
# - App data payload: 00 01 02 03 04 05 06 07 08 09 0a 0b 0c 0d 0e 0f
#                     10 11 12 13 14 15 16 17 18 19 1a 1b 1c 1d 1e 1f
#                     20 21 22 23 24 25 26 27 28 29 2a 2b 2c 2d 2e 2f
#                     30 31
# - Complete record:  17 03 03 00 43 a2 3f 70 54 b6 2c 94 d0 af fa fe
#                     82 28 ba 55 cb ef ac ea 42 f9 14 aa 66 bc ab 3f
#                     2b 98 19 a8 a5 b4 6b 39 5b d5 4a 9a 20 44 1e 2b
#                     62 97 4e 1f 5a 62 92 a2 97 70 14 bd 1e 3d ea e6
#                     3a ee bb 21 69 49 15 e4
# - Padding used: No (== granularity 1)
depends_on:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_GCM:PSA_WANT_ALG_SHA_256
ssl_tls13_record_protection:MBEDTLS_TLS1_3_AES_128_GCM_SHA256:MBEDTLS_SSL_IS_CLIENT:0:1:"9f02283b6c9c07efc26bb9f2ac92e356":"cf782b88dd83549aadf1e984":"17422dda596ed5d9acd890e3c63f5051":"5b78923dee08579033e523d9":"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f3031":"a23f7054b62c94d0affafe8228ba55cbefacea42f914aa66bcab3f2b9819a8a5b46b395bd54a9a20441e2b62974e1f5a6292a2977014bd1e3deae63aeebb21694915e4"

SSL TLS 1.3 Record Encryption RFC 8448 Example #2
# Application Data record sent by Server in 1-RTT example of RFC 8448, Section 3
# - Server App Key: 9f 02 28 3b 6c 9c 07 ef c2 6b b9 f2 ac 92 e3 56
# - Server App IV:  cf 78 2b 88 dd 83 54 9a ad f1 e9 84
# - Client App Key: 17 42 2d da 59 6e d5 d9 ac d8 90 e3 c6 3f 50 51
# - Client App IV:  5b 78 92 3d ee 08 57 90 33 e5 23 d9
# - App data payload: 00 01 02 03 04 05 06 07 08 09 0a 0b 0c 0d 0e 0f
#                     10 11 12 13 14 15 16 17 18 19 1a 1b 1c 1d 1e 1f
#                     20 21 22 23 24 25 26 27 28 29 2a 2b 2c 2d 2e 2f
#                     30 31
# - Complete record:  17 03 03 00 43 2e 93 7e 11 ef 4a c7 40 e5 38 ad
#                     36 00 5f c4 a4 69 32 fc 32 25 d0 5f 82 aa 1b 36
#                     e3 0e fa f9 7d 90 e6 df fc 60 2d cb 50 1a 59 a8
#                     fc c4 9c 4b f2 e5 f0 a2 1c 00 47 c2 ab f3 32 54
#                     0d d0 32 e1 67 c2 95 5d
# - Padding used: No (== granularity 1)
depends_on:PSA_WANT_KEY_TYPE_AES:PSA_WANT_ALG_GCM:PSA_WANT_ALG_SHA_256
ssl_tls13_record_protection:MBEDTLS_TLS1_3_AES_128_GCM_SHA256:MBEDTLS_SSL_IS_SERVER:1:1:"9f02283b6c9c07efc26bb9f2ac92e356":"cf782b88dd83549aadf1e984":"17422dda596ed5d9acd890e3c63f5051":"5b78923dee08579033e523d9":"000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f3031":"2e937e11ef4ac740e538ad36005fc4a46932fc3225d05f82aa1b36e30efaf97d90e6dffc602dcb501a59a8fcc49c4bf2e5f0a21c0047c2abf332540dd032e167c2955d"

SSL TLS 1.3 Key schedule: Application secrets derivation helper
# Vector from RFC 8448
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_derive_application_secrets:PSA_ALG_SHA_256:"e2d32d4ed66dd37897a0e80c84107503ce58bf8aad4cb55a5002d77ecb890ece":"b0aeffc46a2cfe33114e6fd7d51f9f04b1ca3c497dab08934a774a9d9ad7dbf3":"2abbf2b8e381d23dbebe1dd2a7d16a8bf484cb4950d23fb7fb7fa8547062d9a1":"cc21f1bf8feb7dd5fa505bd9c4b468a9984d554a993dc49e6d285598fb672691":"3fd93d4ffddc98e64b14dd107aedf8ee4add23f4510f58a4592d0b201bee56b4"

SSL TLS 1.3 Key schedule: Resumption secrets derivation helper
# Vector from RFC 8448
depends_on:PSA_WANT_ALG_SHA_256
ssl_tls13_derive_resumption_secrets:PSA_ALG_SHA_256:"e2d32d4ed66dd37897a0e80c84107503ce58bf8aad4cb55a5002d77ecb890ece":"c3c122e0bd907a4a3ff6112d8fd53dbf89c773d9552e8b6b9d56d361b3a97bf6":"5e95bdf1f89005ea2e9aa0ba85e728e3c19c5fe0c699e3f5bee59faebd0b5406"

SSL TLS 1.3 Key schedule: PSK binder
# Vector from RFC 8448
# For the resumption PSK, see Section 3, 'generate resumption secret "tls13 resumption"'
# For all other data, see Section 4, 'construct a ClientHello handshake message:'
depends_on:PSA_WANT_ALG_SHA_256:MBEDTLS_ECP_HAVE_CURVE25519
ssl_tls13_create_psk_binder:PSA_ALG_SHA_256:"4ecd0eb6ec3b4d87f5d6028f922ca4c5851a277fd41311c9e62d2c9492e1c4f3":MBEDTLS_SSL_TLS1_3_PSK_RESUMPTION:"63224b2e4573f2d3454ca84b9d009a04f6be9e05711a8396473aefa01e924a14":"3add4fb2d8fdf822a0ca3cf7678ef5e88dae990141c5924d57bb6fa31b9e5f9d"

SSL TLS_PRF MBEDTLS_SSL_TLS_PRF_NONE
ssl_tls_prf:MBEDTLS_SSL_TLS_PRF_NONE:"":"":"test tls_prf label":"":MBEDTLS_ERR_SSL_FEATURE_UNAVAILABLE

SSL TLS_PRF MBEDTLS_SSL_TLS_PRF_SHA384
depends_on:MBEDTLS_MD_CAN_SHA384:MBEDTLS_SSL_PROTO_TLS1_2
ssl_tls_prf:MBEDTLS_SSL_TLS_PRF_SHA384:"1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef":"1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef":"test tls_prf label":"a4206a36eef93f496611c2b7806625c3":0

SSL TLS_PRF MBEDTLS_SSL_TLS_PRF_SHA256
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_SSL_PROTO_TLS1_2
ssl_tls_prf:MBEDTLS_SSL_TLS_PRF_SHA256:"1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef":"1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef":"test tls_prf label":"7f9998393198a02c8d731ccc2ef90b2c":0

SSL TLS_PRF MBEDTLS_SSL_TLS_PRF_SHA384 SHA-384 not enabled
depends_on:!MBEDTLS_MD_CAN_SHA384
ssl_tls_prf:MBEDTLS_SSL_TLS_PRF_SHA384:"1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef":"1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef":"test tls_prf label":"a4206a36eef93f496611c2b7806625c3":MBEDTLS_ERR_SSL_FEATURE_UNAVAILABLE

SSL TLS_PRF MBEDTLS_SSL_TLS_PRF_SHA256 SHA-256 not enabled
depends_on:!MBEDTLS_MD_CAN_SHA256
ssl_tls_prf:MBEDTLS_SSL_TLS_PRF_SHA256:"1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef":"1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef":"test tls_prf label":"7f9998393198a02c8d731ccc2ef90b2c":MBEDTLS_ERR_SSL_FEATURE_UNAVAILABLE

Session serialization, save-load: no ticket, no cert
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
ssl_serialize_session_save_load:0:"":0:MBEDTLS_SSL_VERSION_TLS1_2

Session serialization, save-load: small ticket, no cert
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_PROTO_TLS1_2
ssl_serialize_session_save_load:42:"":0:MBEDTLS_SSL_VERSION_TLS1_2

Session serialization, save-load: large ticket, no cert
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_PROTO_TLS1_2
ssl_serialize_session_save_load:1023:"":0:MBEDTLS_SSL_VERSION_TLS1_2

Session serialization, save-load: no ticket, cert
depends_on:MBEDTLS_X509_USE_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_FS_IO:MBEDTLS_SSL_PROTO_TLS1_2
ssl_serialize_session_save_load:0:"../framework/data_files/server5.crt":0:MBEDTLS_SSL_VERSION_TLS1_2

Session serialization, save-load: small ticket, cert
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_X509_USE_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_FS_IO:MBEDTLS_SSL_PROTO_TLS1_2
ssl_serialize_session_save_load:42:"../framework/data_files/server5.crt":0:MBEDTLS_SSL_VERSION_TLS1_2

Session serialization, save-load: large ticket, cert
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_X509_USE_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_FS_IO:MBEDTLS_SSL_PROTO_TLS1_2
ssl_serialize_session_save_load:1023:"../framework/data_files/server5.crt":0:MBEDTLS_SSL_VERSION_TLS1_2

TLS 1.3: CLI: Session serialization, save-load: no ticket
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_PROTO_TLS1_3
ssl_serialize_session_save_load:0:"":MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3

TLS 1.3: CLI: Session serialization, save-load: small ticket
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_PROTO_TLS1_3
ssl_serialize_session_save_load:42:"":MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3

TLS 1.3: CLI: Session serialization, save-load: large ticket
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_PROTO_TLS1_3
ssl_serialize_session_save_load:1023:"":MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3

TLS 1.3: SRV: Session serialization, save-load: large ticket
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_TLS1_3
ssl_serialize_session_save_load:1023:"":MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3

Session serialization, load-save: no ticket, no cert
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
ssl_serialize_session_load_save:0:"":0:MBEDTLS_SSL_VERSION_TLS1_2

Session serialization, load-save: small ticket, no cert
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_PROTO_TLS1_2
ssl_serialize_session_load_save:42:"":0:MBEDTLS_SSL_VERSION_TLS1_2

Session serialization, load-save: large ticket, no cert
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_PROTO_TLS1_2
ssl_serialize_session_load_save:1023:"":0:MBEDTLS_SSL_VERSION_TLS1_2

Session serialization, load-save: no ticket, cert
depends_on:MBEDTLS_X509_USE_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_FS_IO:MBEDTLS_SSL_PROTO_TLS1_2
ssl_serialize_session_load_save:0:"../framework/data_files/server5.crt":0:MBEDTLS_SSL_VERSION_TLS1_2

Session serialization, load-save: small ticket, cert
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_X509_USE_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_FS_IO
ssl_serialize_session_load_save:42:"../framework/data_files/server5.crt":0:MBEDTLS_SSL_VERSION_TLS1_2

Session serialization, load-save: large ticket, cert
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_X509_USE_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_FS_IO
ssl_serialize_session_load_save:1023:"../framework/data_files/server5.crt":0:MBEDTLS_SSL_VERSION_TLS1_2

TLS 1.3: CLI: Session serialization, load-save: no ticket
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_PROTO_TLS1_3
ssl_serialize_session_load_save:0:"":MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3

TLS 1.3: CLI: Session serialization, load-save: small ticket
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_PROTO_TLS1_3
ssl_serialize_session_load_save:42:"":MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3

TLS 1.3: CLI: Session serialization, load-save: large ticket
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_PROTO_TLS1_3
ssl_serialize_session_load_save:1023:"":MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3

TLS 1.3: SRV: Session serialization, load-save
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_TLS1_3
ssl_serialize_session_load_save:0:"":MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3

Session serialization, save buffer size: no ticket, no cert
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
ssl_serialize_session_save_buf_size:0:"":0:MBEDTLS_SSL_VERSION_TLS1_2

Session serialization, save buffer size: small ticket, no cert
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_PROTO_TLS1_2
ssl_serialize_session_save_buf_size:42:"":0:MBEDTLS_SSL_VERSION_TLS1_2

Session serialization, save buffer size: large ticket, no cert
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_PROTO_TLS1_2
ssl_serialize_session_save_buf_size:1023:"":0:MBEDTLS_SSL_VERSION_TLS1_2

Session serialization, save buffer size: no ticket, cert
depends_on:MBEDTLS_X509_USE_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_FS_IO:MBEDTLS_SSL_PROTO_TLS1_2
ssl_serialize_session_save_buf_size:0:"../framework/data_files/server5.crt":0:MBEDTLS_SSL_VERSION_TLS1_2

Session serialization, save buffer size: small ticket, cert
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_X509_USE_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_FS_IO
ssl_serialize_session_save_buf_size:42:"../framework/data_files/server5.crt":0:MBEDTLS_SSL_VERSION_TLS1_2

Session serialization, save buffer size: large ticket, cert
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_X509_USE_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_FS_IO
ssl_serialize_session_save_buf_size:1023:"../framework/data_files/server5.crt":0:MBEDTLS_SSL_VERSION_TLS1_2

TLS 1.3: CLI: Session serialization, save buffer size: no ticket
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_PROTO_TLS1_3
ssl_serialize_session_save_buf_size:0:"":MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3

TLS 1.3: CLI: Session serialization, save buffer size: small ticket
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_PROTO_TLS1_3
ssl_serialize_session_save_buf_size:42:"":MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3

TLS 1.3: CLI: Session serialization, save buffer size: large ticket
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_SSL_PROTO_TLS1_3
ssl_serialize_session_save_buf_size:1023:"":MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3

TLS 1.3: SRV: Session serialization, save buffer size
depends_on:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_SRV_C:MBEDTLS_SSL_PROTO_TLS1_3
ssl_serialize_session_save_buf_size:0:"":MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3

Session serialization, load buffer size: no ticket, no cert
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
ssl_serialize_session_load_buf_size:0:"":0:MBEDTLS_SSL_VERSION_TLS1_2

Session serialization, load buffer size: small ticket, no cert
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C
ssl_serialize_session_load_buf_size:42:"":0:MBEDTLS_SSL_VERSION_TLS1_2

Session serialization, load buffer size: large ticket, no cert
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C
ssl_serialize_session_load_buf_size:1023:"":0:MBEDTLS_SSL_VERSION_TLS1_2

Session serialization, load buffer size: no ticket, cert
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_X509_USE_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_FS_IO
ssl_serialize_session_load_buf_size:0:"../framework/data_files/server5.crt":0:MBEDTLS_SSL_VERSION_TLS1_2

Session serialization, load buffer size: small ticket, cert
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_X509_USE_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_FS_IO
ssl_serialize_session_load_buf_size:42:"../framework/data_files/server5.crt":0:MBEDTLS_SSL_VERSION_TLS1_2

Session serialization, load buffer size: large ticket, cert
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C:MBEDTLS_X509_USE_C:MBEDTLS_PEM_PARSE_C:MBEDTLS_PK_CAN_ECDSA_SOME:MBEDTLS_ECP_HAVE_SECP256R1:MBEDTLS_MD_CAN_SHA256:MBEDTLS_FS_IO
ssl_serialize_session_load_buf_size:1023:"../framework/data_files/server5.crt":0:MBEDTLS_SSL_VERSION_TLS1_2

TLS 1.3: CLI: Session serialization, load buffer size: no ticket
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C
ssl_serialize_session_load_buf_size:0:"":MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3

TLS 1.3: CLI: Session serialization, load buffer size: small ticket
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C
ssl_serialize_session_load_buf_size:42:"":MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3

TLS 1.3: CLI: Session serialization, load buffer size: large ticket
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_CLI_C
ssl_serialize_session_load_buf_size:1023:"":MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_VERSION_TLS1_3

TLS 1.3: SRV: Session serialization, load buffer size
depends_on:MBEDTLS_SSL_PROTO_TLS1_3:MBEDTLS_SSL_SESSION_TICKETS:MBEDTLS_SSL_SRV_C
ssl_serialize_session_load_buf_size:0:"":MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_VERSION_TLS1_3

Test configuration of EC groups through mbedtls_ssl_conf_curves()
conf_curve:

Test configuration of EC groups through mbedtls_ssl_conf_groups()
conf_group:

Version config: valid client TLS 1.2 only
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
conf_version:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_TRANSPORT_STREAM:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_2:0

Version config: valid client DTLS 1.2 only
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
conf_version:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_TRANSPORT_DATAGRAM:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_2:0

Version config: valid server TLS 1.2 only
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
conf_version:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_TRANSPORT_STREAM:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_2:0

Version config: valid server DTLS 1.2 only
depends_on:MBEDTLS_SSL_PROTO_TLS1_2
conf_version:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_TRANSPORT_DATAGRAM:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_2:0

Version config: invalid client TLS 1.2 only
depends_on:!MBEDTLS_SSL_PROTO_TLS1_2
conf_version:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_TRANSPORT_STREAM:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_ERR_SSL_BAD_CONFIG

Version config: invalid client DTLS 1.2 only
depends_on:!MBEDTLS_SSL_PROTO_TLS1_2
conf_version:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_TRANSPORT_DATAGRAM:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_ERR_SSL_BAD_CONFIG

Version config: invalid server TLS 1.2 only
depends_on:!MBEDTLS_SSL_PROTO_TLS1_2
conf_version:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_TRANSPORT_STREAM:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_ERR_SSL_BAD_CONFIG

Version config: invalid server DTLS 1.2 only
depends_on:!MBEDTLS_SSL_PROTO_TLS1_2
conf_version:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_TRANSPORT_DATAGRAM:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_ERR_SSL_BAD_CONFIG

Version config: valid client TLS 1.3 only
depends_on:MBEDTLS_SSL_PROTO_TLS1_3
conf_version:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_TRANSPORT_STREAM:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_VERSION_TLS1_3:0

Version config: unsupported client DTLS 1.3 only
depends_on:MBEDTLS_SSL_PROTO_TLS1_3
conf_version:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_TRANSPORT_DATAGRAM:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_ERR_SSL_FEATURE_UNAVAILABLE

Version config: valid server TLS 1.3 only
depends_on:MBEDTLS_SSL_PROTO_TLS1_3
conf_version:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_TRANSPORT_STREAM:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_VERSION_TLS1_3:0

Version config: unsupported server DTLS 1.3 only
depends_on:MBEDTLS_SSL_PROTO_TLS1_3
conf_version:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_TRANSPORT_DATAGRAM:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_ERR_SSL_FEATURE_UNAVAILABLE

Version config: invalid client TLS 1.3 only
depends_on:!MBEDTLS_SSL_PROTO_TLS1_3
conf_version:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_TRANSPORT_STREAM:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_ERR_SSL_BAD_CONFIG

Version config: invalid client DTLS 1.3 only
depends_on:!MBEDTLS_SSL_PROTO_TLS1_3
conf_version:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_TRANSPORT_DATAGRAM:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_ERR_SSL_BAD_CONFIG

Version config: invalid server TLS 1.3 only
depends_on:!MBEDTLS_SSL_PROTO_TLS1_3
conf_version:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_TRANSPORT_STREAM:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_ERR_SSL_BAD_CONFIG

Version config: invalid server DTLS 1.3 only
depends_on:!MBEDTLS_SSL_PROTO_TLS1_3
conf_version:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_TRANSPORT_DATAGRAM:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_ERR_SSL_BAD_CONFIG

Version config: valid client hybrid TLS 1.2/3
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_PROTO_TLS1_3
conf_version:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_TRANSPORT_STREAM:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_3:0

Version config: unsupported client hybrid DTLS 1.2/3
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_PROTO_TLS1_3
conf_version:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_TRANSPORT_DATAGRAM:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_ERR_SSL_FEATURE_UNAVAILABLE

Version config: valid server hybrid TLS 1.2/3
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_PROTO_TLS1_3
conf_version:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_TRANSPORT_STREAM:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_3:0

Version config: unsupported server hybrid DTLS 1.2/3
depends_on:MBEDTLS_SSL_PROTO_TLS1_2:MBEDTLS_SSL_PROTO_TLS1_3
conf_version:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_TRANSPORT_DATAGRAM:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_ERR_SSL_FEATURE_UNAVAILABLE

Version config: valid client hybrid TLS 1.2/3, no TLS 1.2
depends_on:!MBEDTLS_SSL_PROTO_TLS1_2
conf_version:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_TRANSPORT_STREAM:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_ERR_SSL_BAD_CONFIG

Version config: unsupported client hybrid DTLS 1.2/3, no TLS 1.2
depends_on:!MBEDTLS_SSL_PROTO_TLS1_2
conf_version:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_TRANSPORT_DATAGRAM:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_ERR_SSL_BAD_CONFIG

Version config: valid server hybrid TLS 1.2/3, no TLS 1.2
depends_on:!MBEDTLS_SSL_PROTO_TLS1_2
conf_version:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_TRANSPORT_STREAM:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_ERR_SSL_BAD_CONFIG

Version config: unsupported server hybrid DTLS 1.2/3, no TLS 1.2
depends_on:!MBEDTLS_SSL_PROTO_TLS1_2
conf_version:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_TRANSPORT_DATAGRAM:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_ERR_SSL_BAD_CONFIG

Version config: valid client hybrid TLS 1.2/3, no TLS 1.3
depends_on:!MBEDTLS_SSL_PROTO_TLS1_3
conf_version:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_TRANSPORT_STREAM:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_ERR_SSL_BAD_CONFIG

Version config: unsupported client hybrid DTLS 1.2/3, no TLS 1.3
depends_on:!MBEDTLS_SSL_PROTO_TLS1_3
conf_version:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_TRANSPORT_DATAGRAM:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_ERR_SSL_BAD_CONFIG

Version config: valid server hybrid TLS 1.2/3, no TLS 1.3
depends_on:!MBEDTLS_SSL_PROTO_TLS1_3
conf_version:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_TRANSPORT_STREAM:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_ERR_SSL_BAD_CONFIG

Version config: unsupported server hybrid DTLS 1.2/3, no TLS 1.3
depends_on:!MBEDTLS_SSL_PROTO_TLS1_3
conf_version:MBEDTLS_SSL_IS_SERVER:MBEDTLS_SSL_TRANSPORT_DATAGRAM:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_SSL_VERSION_TLS1_3:MBEDTLS_ERR_SSL_BAD_CONFIG

Version config: invalid minimum version
conf_version:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_TRANSPORT_STREAM:770:MBEDTLS_SSL_VERSION_TLS1_2:MBEDTLS_ERR_SSL_BAD_CONFIG

Version config: invalid maximum version
conf_version:MBEDTLS_SSL_IS_CLIENT:MBEDTLS_SSL_TRANSPORT_STREAM:MBEDTLS_SSL_VERSION_TLS1_3:773:MBEDTLS_ERR_SSL_BAD_CONFIG

Test accessor into timing_delay_context
timing_final_delay_accessor

Sanity test cid functions
cid_sanity:

Raw key agreement: nominal
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
raw_key_agreement_fail:0

Raw key agreement: bad server key
depends_on:MBEDTLS_MD_CAN_SHA256:MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
raw_key_agreement_fail:1

Force a bad session id length
force_bad_session_id_len

Cookie parsing: nominal run
cookie_parsing:"16fefd0000000000000000002F010000de000000000000011efefd7b7272727272727272727272727272727272727272727272727272727272727d00200000000000000000000000000000000000000000000000000000000000000000":MBEDTLS_ERR_SSL_INTERNAL_ERROR

Cookie parsing: cookie_len overflow
cookie_parsing:"16fefd000000000000000000ea010000de000000000000011efefd7b7272727272727272727272727272727272727272727272727272727272727db97b7373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737373737db963":MBEDTLS_ERR_SSL_DECODE_ERROR

Cookie parsing: non-zero fragment offset
cookie_parsing:"16fefd00000000000000000032010000de000072000000011efefd7b7272727272727272727272727272727272727272727272727272727272727d01730143":MBEDTLS_ERR_SSL_DECODE_ERROR

Cookie parsing: sid_len overflow
cookie_parsing:"16fefd00000000000000000032010000de000000000000011efefd7b7272727272727272727272727272727272727272727272727272727272727dFF730143":MBEDTLS_ERR_SSL_DECODE_ERROR

Cookie parsing: record too short
cookie_parsing:"16fefd0000000000000000002f010000de000000000000011efefd7b7272727272727272727272727272727272727272727272727272727272727dFF":MBEDTLS_ERR_SSL_DECODE_ERROR

Cookie parsing: one byte overread
cookie_parsing:"16fefd0000000000000000002F010000de000000000000011efefd7b7272727272727272727272727272727272727272727272727272727272727d0001":MBEDTLS_ERR_SSL_DECODE_ERROR

TLS 1.3 srv Certificate msg - wrong vector lengths
tls13_server_certificate_msg_invalid_vector_len

EC-JPAKE set password
depends_on:MBEDTLS_KEY_EXCHANGE_ECJPAKE_ENABLED
ssl_ecjpake_set_password:0

EC-JPAKE set opaque password
depends_on:MBEDTLS_KEY_EXCHANGE_ECJPAKE_ENABLED:MBEDTLS_USE_PSA_CRYPTO
ssl_ecjpake_set_password:1

Test Elliptic curves' info parsing
elliptic_curve_get_properties

TLS 1.3 resume session with ticket
tls13_resume_session_with_ticket

TLS 1.3 read early data, early data accepted
tls13_read_early_data:TEST_EARLY_DATA_ACCEPTED

TLS 1.3 read early data, no early data indication
tls13_read_early_data:TEST_EARLY_DATA_NO_INDICATION_SENT

TLS 1.3 read early data, server rejects early data
tls13_read_early_data:TEST_EARLY_DATA_SERVER_REJECTS

TLS 1.3 read early data, discard after HRR
tls13_read_early_data:TEST_EARLY_DATA_HRR

TLS 1.3 cli, early data, same ALPN
depends_on:MBEDTLS_SSL_ALPN
tls13_read_early_data:TEST_EARLY_DATA_SAME_ALPN

TLS 1.3 cli, early data, different ALPN
depends_on:MBEDTLS_SSL_ALPN
tls13_read_early_data:TEST_EARLY_DATA_DIFF_ALPN

TLS 1.3 cli, early data, no initial ALPN
depends_on:MBEDTLS_SSL_ALPN
tls13_read_early_data:TEST_EARLY_DATA_NO_INITIAL_ALPN

TLS 1.3 cli, early data, no later ALPN
depends_on:MBEDTLS_SSL_ALPN
tls13_read_early_data:TEST_EARLY_DATA_NO_LATER_ALPN

TLS 1.3 cli, early data state, early data accepted
tls13_cli_early_data_state:TEST_EARLY_DATA_ACCEPTED

TLS 1.3 cli, early data state, no early data indication
tls13_cli_early_data_state:TEST_EARLY_DATA_NO_INDICATION_SENT

TLS 1.3 cli, early data state, server rejects early data
tls13_cli_early_data_state:TEST_EARLY_DATA_SERVER_REJECTS

TLS 1.3 cli, early data state, hello retry request
tls13_cli_early_data_state:TEST_EARLY_DATA_HRR

TLS 1.3 write early data, early data accepted
tls13_write_early_data:TEST_EARLY_DATA_ACCEPTED

TLS 1.3 write early data, no early data indication
tls13_write_early_data:TEST_EARLY_DATA_NO_INDICATION_SENT

TLS 1.3 write early data, server rejects early data
tls13_write_early_data:TEST_EARLY_DATA_SERVER_REJECTS

TLS 1.3 write early data, hello retry request
tls13_write_early_data:TEST_EARLY_DATA_HRR

TLS 1.3 cli, maximum early data size, default size
tls13_cli_max_early_data_size:-1

TLS 1.3 cli, maximum early data size, zero
tls13_cli_max_early_data_size:0

TLS 1.3 cli, maximum early data size, very small but not 0
tls13_cli_max_early_data_size:3

TLS 1.3 cli, maximum early data size, 93
tls13_cli_max_early_data_size:93

TLS 1.3 srv, max early data size, dflt, wsz=96
tls13_srv_max_early_data_size:TEST_EARLY_DATA_ACCEPTED:-1:96

TLS 1.3 srv, max early data size, dflt, wsz=128
tls13_srv_max_early_data_size:TEST_EARLY_DATA_ACCEPTED:-1:128

TLS 1.3 srv, max early data size, 3, wsz=2
tls13_srv_max_early_data_size:TEST_EARLY_DATA_ACCEPTED:3:2

TLS 1.3 srv, max early data size, 3, wsz=3
tls13_srv_max_early_data_size:TEST_EARLY_DATA_ACCEPTED:3:3

TLS 1.3 srv, max early data size, 98, wsz=23
tls13_srv_max_early_data_size:TEST_EARLY_DATA_ACCEPTED:98:23

TLS 1.3 srv, max early data size, 98, wsz=49
tls13_srv_max_early_data_size:TEST_EARLY_DATA_ACCEPTED:98:49

TLS 1.3 srv, max early data size, server rejects, dflt, wsz=128
tls13_srv_max_early_data_size:TEST_EARLY_DATA_SERVER_REJECTS:-1:128

TLS 1.3 srv, max early data size, server rejects, 3, wsz=3
tls13_srv_max_early_data_size:TEST_EARLY_DATA_SERVER_REJECTS:3:3

TLS 1.3 srv, max early data size, server rejects, 98, wsz=49
tls13_srv_max_early_data_size:TEST_EARLY_DATA_SERVER_REJECTS:98:49

TLS 1.3 srv, max early data size, HRR, dflt, wsz=128
tls13_srv_max_early_data_size:TEST_EARLY_DATA_HRR:-1:128

TLS 1.3 srv, max early data size, HRR, 3, wsz=3
tls13_srv_max_early_data_size:TEST_EARLY_DATA_HRR:3:3

TLS 1.3 srv, max early data size, HRR, 98, wsz=49
tls13_srv_max_early_data_size:TEST_EARLY_DATA_HRR:97:0
