/* BEGIN_HEADER */
#include "mbedtls/sha1.h"
#include "mbedtls/sha256.h"
#include "mbedtls/sha512.h"
#include "mbedtls/sha3.h"
/* END_HEADER */

/* BEGIN_CASE depends_on:MBEDTLS_SHA1_C */
void mbedtls_sha1(data_t *src_str, data_t *hash)
{
    unsigned char output[41];

    memset(output, 0x00, 41);


    TEST_ASSERT(mbedtls_sha1(src_str->x, src_str->len, output) == 0);

    TEST_ASSERT(mbedtls_test_hexcmp(output, hash->x, 20, hash->len) == 0);
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_SHA256_C */
void sha256_invalid_param()
{
    mbedtls_sha256_context ctx;
    unsigned char buf[64] = { 0 };
    size_t const buflen = sizeof(buf);
    int invalid_type = 42;

    TEST_EQUAL(MBEDTLS_ERR_SHA256_BAD_INPUT_DATA,
               mbedtls_sha256_starts(&ctx, invalid_type));

    TEST_EQUAL(MBEDTLS_ERR_SHA256_BAD_INPUT_DATA,
               mbedtls_sha256(buf, buflen,
                              buf, invalid_type));

exit:
    return;
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_SHA224_C */
void sha224(data_t *src_str, data_t *hash)
{
    unsigned char output[57];

    memset(output, 0x00, 57);


    TEST_EQUAL(mbedtls_sha256(src_str->x, src_str->len, output, 1), 0);

    TEST_EQUAL(mbedtls_test_hexcmp(output, hash->x, 28, hash->len), 0);
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_SHA256_C */
void mbedtls_sha256(data_t *src_str, data_t *hash)
{
    unsigned char output[65];

    memset(output, 0x00, 65);


    TEST_EQUAL(mbedtls_sha256(src_str->x, src_str->len, output, 0), 0);

    TEST_EQUAL(mbedtls_test_hexcmp(output, hash->x, 32, hash->len), 0);
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_SHA512_C */
void sha512_invalid_param()
{
    mbedtls_sha512_context ctx;
    unsigned char buf[64] = { 0 };
    size_t const buflen = sizeof(buf);
    int invalid_type = 42;

    TEST_EQUAL(MBEDTLS_ERR_SHA512_BAD_INPUT_DATA,
               mbedtls_sha512_starts(&ctx, invalid_type));

    TEST_EQUAL(MBEDTLS_ERR_SHA512_BAD_INPUT_DATA,
               mbedtls_sha512(buf, buflen,
                              buf, invalid_type));

exit:
    return;
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_SHA384_C */
void sha384(data_t *src_str, data_t *hash)
{
    unsigned char output[97];

    memset(output, 0x00, 97);


    TEST_EQUAL(mbedtls_sha512(src_str->x, src_str->len, output, 1), 0);

    TEST_EQUAL(mbedtls_test_hexcmp(output, hash->x, 48, hash->len), 0);
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_SHA512_C */
void mbedtls_sha512(data_t *src_str, data_t *hash)
{
    unsigned char output[129];

    memset(output, 0x00, 129);


    TEST_EQUAL(mbedtls_sha512(src_str->x, src_str->len, output, 0), 0);

    TEST_EQUAL(mbedtls_test_hexcmp(output, hash->x, 64, hash->len), 0);
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_SHA1_C:MBEDTLS_SELF_TEST */
void sha1_selftest()
{
    TEST_ASSERT(mbedtls_sha1_self_test(1) == 0);
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_SHA224_C:MBEDTLS_SELF_TEST */
void sha224_selftest()
{
    TEST_EQUAL(mbedtls_sha224_self_test(1), 0);
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_SHA256_C:MBEDTLS_SELF_TEST */
void sha256_selftest()
{
    TEST_EQUAL(mbedtls_sha256_self_test(1), 0);
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_SHA384_C:MBEDTLS_SELF_TEST */
void sha384_selftest()
{
    TEST_EQUAL(mbedtls_sha384_self_test(1), 0);
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_SHA512_C:MBEDTLS_SELF_TEST */
void sha512_selftest()
{
    TEST_EQUAL(mbedtls_sha512_self_test(1), 0);
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_SHA3_C */
void mbedtls_sha3(int family, data_t *in, data_t *hash)
{
    unsigned char *output = NULL;

    TEST_CALLOC(output, hash->len);

    TEST_ASSERT(mbedtls_sha3(family, in->x, in->len, output, hash->len) == 0);

    TEST_MEMORY_COMPARE(output, hash->len, hash->x, hash->len);

exit:
    mbedtls_free(output);
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_SHA3_C */
void sha3_invalid_param()
{
    unsigned char output[32];
    mbedtls_sha3_context ctx;

    mbedtls_sha3_init(&ctx);
    TEST_EQUAL(mbedtls_sha3_starts(&ctx, MBEDTLS_SHA3_NONE), MBEDTLS_ERR_SHA3_BAD_INPUT_DATA);

    TEST_EQUAL(mbedtls_sha3_starts(&ctx, MBEDTLS_SHA3_256), 0);
    TEST_EQUAL(mbedtls_sha3_finish(&ctx, output, 0), MBEDTLS_ERR_SHA3_BAD_INPUT_DATA);

    TEST_EQUAL(mbedtls_sha3_starts(&ctx, MBEDTLS_SHA3_256), 0);
    TEST_EQUAL(mbedtls_sha3_finish(&ctx, output, 31), MBEDTLS_ERR_SHA3_BAD_INPUT_DATA);

    TEST_EQUAL(mbedtls_sha3_starts(&ctx, MBEDTLS_SHA3_256), 0);
    TEST_EQUAL(mbedtls_sha3_finish(&ctx, output, 32), 0);

exit:
    return;
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_SHA3_C */
void mbedtls_sha3_multi(int family, data_t *in, data_t *hash)
{
    unsigned char *output = NULL;
    mbedtls_sha3_context ctx;
    const unsigned int block_size = 256;

    TEST_CALLOC(output, hash->len);

    mbedtls_sha3_init(&ctx);
    mbedtls_sha3_starts(&ctx, family);

    for (size_t l = 0; l < in->len; l += block_size) {
        TEST_ASSERT(mbedtls_sha3_update(&ctx, in->x + l, MIN(in->len - l, block_size)) == 0);
    }

    TEST_ASSERT(mbedtls_sha3_finish(&ctx, output, hash->len) == 0);

    TEST_MEMORY_COMPARE(output, hash->len, hash->x, hash->len);

exit:
    mbedtls_free(output);
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_SHA3_C */
void sha3_streaming(int type, data_t *input)
{
    mbedtls_sha3_context ctx;
    unsigned char reference_hash[64];
    unsigned char hash[64];
    size_t chunk_size;
    size_t hash_length = (type == MBEDTLS_SHA3_224 ? 28 :
                          type == MBEDTLS_SHA3_256 ? 32 :
                          type == MBEDTLS_SHA3_384 ? 48 :
                          type == MBEDTLS_SHA3_512 ? 64 :
                          0);

    mbedtls_sha3_init(&ctx);
    memset(reference_hash, 0, sizeof(reference_hash));
    memset(hash, 0, sizeof(hash));
    TEST_ASSERT(hash_length != 0);

    /* Generate a reference hash */
    mbedtls_sha3(type, input->x, input->len, reference_hash, hash_length);

    /* Repeat each test with increasingly-sized data chunks
     * E.g. start by processing bytes individual bytes, then 2-byte chunks,
     * then 3-byte chunks, and so on...
     * At each test ensure that the same hash is generated.
     */
    for (chunk_size = 1; chunk_size < input->len; chunk_size++) {
        size_t i;
        size_t remaining = input->len;

        mbedtls_sha3_init(&ctx);
        TEST_ASSERT(mbedtls_sha3_starts(&ctx, type) == 0);

        for (i = 0; i < input->len; i += chunk_size) {
            size_t len = remaining >= chunk_size ? chunk_size : remaining;
            TEST_ASSERT(mbedtls_sha3_update(&ctx, input->x + i, len) == 0);
            remaining -= len;
        }

        mbedtls_sha3_finish(&ctx, hash, hash_length);
        mbedtls_sha3_free(&ctx);

        TEST_MEMORY_COMPARE(hash, hash_length, reference_hash, hash_length);
    }

exit:
    mbedtls_sha3_free(&ctx);
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_SHA3_C */
void sha3_reuse(data_t *input1, data_t *hash1,
                data_t *input2, data_t *hash2)
{
    unsigned char output[64];
    mbedtls_sha3_context ctx;
    mbedtls_sha3_id type1, type2;

    mbedtls_sha3_init(&ctx);
    switch (hash1->len) {
        case 28: type1 = MBEDTLS_SHA3_224; break;
        case 32: type1 = MBEDTLS_SHA3_256; break;
        case 48: type1 = MBEDTLS_SHA3_384; break;
        case 64: type1 = MBEDTLS_SHA3_512; break;
        default: TEST_FAIL("hash1->len validity"); break;
    }
    switch (hash2->len) {
        case 28: type2 = MBEDTLS_SHA3_224; break;
        case 32: type2 = MBEDTLS_SHA3_256; break;
        case 48: type2 = MBEDTLS_SHA3_384; break;
        case 64: type2 = MBEDTLS_SHA3_512; break;
        default: TEST_FAIL("hash2->len validity"); break;
    }

    /* Round 1 */
    TEST_ASSERT(mbedtls_sha3_starts(&ctx, type1) == 0);
    TEST_ASSERT(mbedtls_sha3_update(&ctx, input1->x, input1->len) == 0);
    TEST_ASSERT(mbedtls_sha3_finish(&ctx, output, sizeof(output)) == 0);
    TEST_MEMORY_COMPARE(output, hash1->len, hash1->x, hash1->len);

    /* Round 2 */
    TEST_ASSERT(mbedtls_sha3_starts(&ctx, type2) == 0);
    TEST_ASSERT(mbedtls_sha3_update(&ctx, input2->x, input2->len) == 0);
    TEST_ASSERT(mbedtls_sha3_finish(&ctx, output, sizeof(output)) == 0);
    TEST_MEMORY_COMPARE(output, hash2->len, hash2->x, hash2->len);

exit:
    mbedtls_sha3_free(&ctx);
}
/* END_CASE */

/* BEGIN_CASE depends_on:MBEDTLS_SHA3_C:MBEDTLS_SELF_TEST */
void sha3_selftest()
{
    TEST_ASSERT(mbedtls_sha3_self_test(0) == 0);
}
/* END_CASE */
