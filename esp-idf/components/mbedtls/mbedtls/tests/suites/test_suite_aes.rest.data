AES-ECB Encrypt (Invalid keylength)
aes_encrypt_ecb:"000000000000000000000000000000":"f34481ec3cc627bacd5dc3fb08f273e6":"0336763e966d92595a567cc9ce537f5e":MBEDTLS_ERR_AES_INVALID_KEY_LENGTH

AES-ECB Decrypt (Invalid keylength)
aes_decrypt_ecb:"000000000000000000000000000000":"f34481ec3cc627bacd5dc3fb08f273e6":"0336763e966d92595a567cc9ce537f5e":MBEDTLS_ERR_AES_INVALID_KEY_LENGTH

AES-256-CBC Encrypt (Invalid input length)
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_encrypt_cbc:"0000000000000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"ffffffffffffffe000000000000000":"":MBEDTLS_ERR_AES_INVALID_INPUT_LENGTH

AES-256-CBC Decrypt (Invalid input length)
depends_on:!MBEDTLS_AES_ONLY_128_BIT_KEY_LENGTH
aes_decrypt_cbc:"0000000000000000000000000000000000000000000000000000000000000000":"00000000000000000000000000000000":"623a52fcea5d443e48d9181ab32c74":"":MBEDTLS_ERR_AES_INVALID_INPUT_LENGTH

AES - Mode Parameter Validation
aes_invalid_mode:

AES - Mandatory Parameter Validation and Valid Parameters
aes_misc_params:

AES Selftest
depends_on:MBEDTLS_SELF_TEST
aes_selftest:
