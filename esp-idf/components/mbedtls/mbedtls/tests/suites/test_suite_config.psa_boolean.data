# Automatically generated by generate_config_tests.py. Do not edit!

Config: PSA_WANT_ALG_CBC_NO_PADDING
depends_on:PSA_WANT_ALG_CBC_NO_PADDING:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_CBC_NO_PADDING
depends_on:!PSA_WANT_ALG_CBC_NO_PADDING:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_CBC_PKCS7
depends_on:PSA_WANT_ALG_CBC_PKCS7:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_CBC_PKCS7
depends_on:!PSA_WANT_ALG_CBC_PKCS7:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_CCM
depends_on:PSA_WANT_ALG_CCM:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_CCM
depends_on:!PSA_WANT_ALG_CCM:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_CCM_STAR_NO_TAG
depends_on:PSA_WANT_ALG_CCM_STAR_NO_TAG:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_CCM_STAR_NO_TAG
depends_on:!PSA_WANT_ALG_CCM_STAR_NO_TAG:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_CFB
depends_on:PSA_WANT_ALG_CFB:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_CFB
depends_on:!PSA_WANT_ALG_CFB:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_CHACHA20_POLY1305
depends_on:PSA_WANT_ALG_CHACHA20_POLY1305:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_CHACHA20_POLY1305
depends_on:!PSA_WANT_ALG_CHACHA20_POLY1305:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_CMAC
depends_on:PSA_WANT_ALG_CMAC:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_CMAC
depends_on:!PSA_WANT_ALG_CMAC:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_CTR
depends_on:PSA_WANT_ALG_CTR:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_CTR
depends_on:!PSA_WANT_ALG_CTR:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_DETERMINISTIC_ECDSA
depends_on:PSA_WANT_ALG_DETERMINISTIC_ECDSA:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_DETERMINISTIC_ECDSA
depends_on:!PSA_WANT_ALG_DETERMINISTIC_ECDSA:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_ECB_NO_PADDING
depends_on:PSA_WANT_ALG_ECB_NO_PADDING:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_ECB_NO_PADDING
depends_on:!PSA_WANT_ALG_ECB_NO_PADDING:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_ECDH
depends_on:PSA_WANT_ALG_ECDH:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_ECDH
depends_on:!PSA_WANT_ALG_ECDH:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_ECDSA
depends_on:PSA_WANT_ALG_ECDSA:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_ECDSA
depends_on:!PSA_WANT_ALG_ECDSA:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_FFDH
depends_on:PSA_WANT_ALG_FFDH:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_FFDH
depends_on:!PSA_WANT_ALG_FFDH:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_GCM
depends_on:PSA_WANT_ALG_GCM:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_GCM
depends_on:!PSA_WANT_ALG_GCM:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_HKDF
depends_on:PSA_WANT_ALG_HKDF:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_HKDF
depends_on:!PSA_WANT_ALG_HKDF:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_HKDF_EXPAND
depends_on:PSA_WANT_ALG_HKDF_EXPAND:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_HKDF_EXPAND
depends_on:!PSA_WANT_ALG_HKDF_EXPAND:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_HKDF_EXTRACT
depends_on:PSA_WANT_ALG_HKDF_EXTRACT:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_HKDF_EXTRACT
depends_on:!PSA_WANT_ALG_HKDF_EXTRACT:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_HMAC
depends_on:PSA_WANT_ALG_HMAC:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_HMAC
depends_on:!PSA_WANT_ALG_HMAC:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_JPAKE
depends_on:PSA_WANT_ALG_JPAKE:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_JPAKE
depends_on:!PSA_WANT_ALG_JPAKE:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_MD5
depends_on:PSA_WANT_ALG_MD5:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_MD5
depends_on:!PSA_WANT_ALG_MD5:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_OFB
depends_on:PSA_WANT_ALG_OFB:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_OFB
depends_on:!PSA_WANT_ALG_OFB:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_PBKDF2_AES_CMAC_PRF_128
depends_on:PSA_WANT_ALG_PBKDF2_AES_CMAC_PRF_128:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_PBKDF2_AES_CMAC_PRF_128
depends_on:!PSA_WANT_ALG_PBKDF2_AES_CMAC_PRF_128:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_PBKDF2_HMAC
depends_on:PSA_WANT_ALG_PBKDF2_HMAC:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_PBKDF2_HMAC
depends_on:!PSA_WANT_ALG_PBKDF2_HMAC:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_RIPEMD160
depends_on:PSA_WANT_ALG_RIPEMD160:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_RIPEMD160
depends_on:!PSA_WANT_ALG_RIPEMD160:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_RSA_OAEP
depends_on:PSA_WANT_ALG_RSA_OAEP:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_RSA_OAEP
depends_on:!PSA_WANT_ALG_RSA_OAEP:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_RSA_PKCS1V15_CRYPT
depends_on:PSA_WANT_ALG_RSA_PKCS1V15_CRYPT:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_RSA_PKCS1V15_CRYPT
depends_on:!PSA_WANT_ALG_RSA_PKCS1V15_CRYPT:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_RSA_PKCS1V15_SIGN
depends_on:PSA_WANT_ALG_RSA_PKCS1V15_SIGN:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_RSA_PKCS1V15_SIGN
depends_on:!PSA_WANT_ALG_RSA_PKCS1V15_SIGN:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_RSA_PSS
depends_on:PSA_WANT_ALG_RSA_PSS:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_RSA_PSS
depends_on:!PSA_WANT_ALG_RSA_PSS:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_SHA3_224
depends_on:PSA_WANT_ALG_SHA3_224:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_SHA3_224
depends_on:!PSA_WANT_ALG_SHA3_224:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_SHA3_256
depends_on:PSA_WANT_ALG_SHA3_256:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_SHA3_256
depends_on:!PSA_WANT_ALG_SHA3_256:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_SHA3_384
depends_on:PSA_WANT_ALG_SHA3_384:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_SHA3_384
depends_on:!PSA_WANT_ALG_SHA3_384:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_SHA3_512
depends_on:PSA_WANT_ALG_SHA3_512:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_SHA3_512
depends_on:!PSA_WANT_ALG_SHA3_512:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_SHA_1
depends_on:PSA_WANT_ALG_SHA_1:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_SHA_1
depends_on:!PSA_WANT_ALG_SHA_1:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_SHA_224
depends_on:PSA_WANT_ALG_SHA_224:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_SHA_224
depends_on:!PSA_WANT_ALG_SHA_224:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_SHA_256
depends_on:PSA_WANT_ALG_SHA_256:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_SHA_256
depends_on:!PSA_WANT_ALG_SHA_256:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_SHA_384
depends_on:PSA_WANT_ALG_SHA_384:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_SHA_384
depends_on:!PSA_WANT_ALG_SHA_384:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_SHA_512
depends_on:PSA_WANT_ALG_SHA_512:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_SHA_512
depends_on:!PSA_WANT_ALG_SHA_512:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_STREAM_CIPHER
depends_on:PSA_WANT_ALG_STREAM_CIPHER:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_STREAM_CIPHER
depends_on:!PSA_WANT_ALG_STREAM_CIPHER:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_TLS12_ECJPAKE_TO_PMS
depends_on:PSA_WANT_ALG_TLS12_ECJPAKE_TO_PMS:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_TLS12_ECJPAKE_TO_PMS
depends_on:!PSA_WANT_ALG_TLS12_ECJPAKE_TO_PMS:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_TLS12_PRF
depends_on:PSA_WANT_ALG_TLS12_PRF:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_TLS12_PRF
depends_on:!PSA_WANT_ALG_TLS12_PRF:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ALG_TLS12_PSK_TO_MS
depends_on:PSA_WANT_ALG_TLS12_PSK_TO_MS:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ALG_TLS12_PSK_TO_MS
depends_on:!PSA_WANT_ALG_TLS12_PSK_TO_MS:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_DH_RFC7919_2048
depends_on:PSA_WANT_DH_RFC7919_2048:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_DH_RFC7919_2048
depends_on:!PSA_WANT_DH_RFC7919_2048:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_DH_RFC7919_3072
depends_on:PSA_WANT_DH_RFC7919_3072:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_DH_RFC7919_3072
depends_on:!PSA_WANT_DH_RFC7919_3072:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_DH_RFC7919_4096
depends_on:PSA_WANT_DH_RFC7919_4096:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_DH_RFC7919_4096
depends_on:!PSA_WANT_DH_RFC7919_4096:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_DH_RFC7919_6144
depends_on:PSA_WANT_DH_RFC7919_6144:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_DH_RFC7919_6144
depends_on:!PSA_WANT_DH_RFC7919_6144:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_DH_RFC7919_8192
depends_on:PSA_WANT_DH_RFC7919_8192:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_DH_RFC7919_8192
depends_on:!PSA_WANT_DH_RFC7919_8192:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ECC_BRAINPOOL_P_R1_256
depends_on:PSA_WANT_ECC_BRAINPOOL_P_R1_256:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ECC_BRAINPOOL_P_R1_256
depends_on:!PSA_WANT_ECC_BRAINPOOL_P_R1_256:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ECC_BRAINPOOL_P_R1_384
depends_on:PSA_WANT_ECC_BRAINPOOL_P_R1_384:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ECC_BRAINPOOL_P_R1_384
depends_on:!PSA_WANT_ECC_BRAINPOOL_P_R1_384:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ECC_BRAINPOOL_P_R1_512
depends_on:PSA_WANT_ECC_BRAINPOOL_P_R1_512:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ECC_BRAINPOOL_P_R1_512
depends_on:!PSA_WANT_ECC_BRAINPOOL_P_R1_512:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ECC_MONTGOMERY_255
depends_on:PSA_WANT_ECC_MONTGOMERY_255:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ECC_MONTGOMERY_255
depends_on:!PSA_WANT_ECC_MONTGOMERY_255:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ECC_MONTGOMERY_448
depends_on:PSA_WANT_ECC_MONTGOMERY_448:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ECC_MONTGOMERY_448
depends_on:!PSA_WANT_ECC_MONTGOMERY_448:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ECC_SECP_K1_192
depends_on:PSA_WANT_ECC_SECP_K1_192:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ECC_SECP_K1_192
depends_on:!PSA_WANT_ECC_SECP_K1_192:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ECC_SECP_K1_256
depends_on:PSA_WANT_ECC_SECP_K1_256:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ECC_SECP_K1_256
depends_on:!PSA_WANT_ECC_SECP_K1_256:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ECC_SECP_R1_192
depends_on:PSA_WANT_ECC_SECP_R1_192:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ECC_SECP_R1_192
depends_on:!PSA_WANT_ECC_SECP_R1_192:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ECC_SECP_R1_224
depends_on:PSA_WANT_ECC_SECP_R1_224:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ECC_SECP_R1_224
depends_on:!PSA_WANT_ECC_SECP_R1_224:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ECC_SECP_R1_256
depends_on:PSA_WANT_ECC_SECP_R1_256:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ECC_SECP_R1_256
depends_on:!PSA_WANT_ECC_SECP_R1_256:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ECC_SECP_R1_384
depends_on:PSA_WANT_ECC_SECP_R1_384:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ECC_SECP_R1_384
depends_on:!PSA_WANT_ECC_SECP_R1_384:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_ECC_SECP_R1_521
depends_on:PSA_WANT_ECC_SECP_R1_521:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_ECC_SECP_R1_521
depends_on:!PSA_WANT_ECC_SECP_R1_521:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_KEY_TYPE_AES
depends_on:PSA_WANT_KEY_TYPE_AES:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_KEY_TYPE_AES
depends_on:!PSA_WANT_KEY_TYPE_AES:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_KEY_TYPE_ARIA
depends_on:PSA_WANT_KEY_TYPE_ARIA:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_KEY_TYPE_ARIA
depends_on:!PSA_WANT_KEY_TYPE_ARIA:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_KEY_TYPE_CAMELLIA
depends_on:PSA_WANT_KEY_TYPE_CAMELLIA:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_KEY_TYPE_CAMELLIA
depends_on:!PSA_WANT_KEY_TYPE_CAMELLIA:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_KEY_TYPE_CHACHA20
depends_on:PSA_WANT_KEY_TYPE_CHACHA20:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_KEY_TYPE_CHACHA20
depends_on:!PSA_WANT_KEY_TYPE_CHACHA20:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_KEY_TYPE_DERIVE
depends_on:PSA_WANT_KEY_TYPE_DERIVE:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_KEY_TYPE_DERIVE
depends_on:!PSA_WANT_KEY_TYPE_DERIVE:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_KEY_TYPE_DES
depends_on:PSA_WANT_KEY_TYPE_DES:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_KEY_TYPE_DES
depends_on:!PSA_WANT_KEY_TYPE_DES:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_KEY_TYPE_DH_KEY_PAIR_BASIC
depends_on:PSA_WANT_KEY_TYPE_DH_KEY_PAIR_BASIC:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_KEY_TYPE_DH_KEY_PAIR_BASIC
depends_on:!PSA_WANT_KEY_TYPE_DH_KEY_PAIR_BASIC:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_KEY_TYPE_DH_KEY_PAIR_DERIVE
depends_on:PSA_WANT_KEY_TYPE_DH_KEY_PAIR_DERIVE:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_DH_KEY_PAIR_BASIC
pass:

Config: !PSA_WANT_KEY_TYPE_DH_KEY_PAIR_DERIVE
depends_on:!PSA_WANT_KEY_TYPE_DH_KEY_PAIR_DERIVE:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_DH_KEY_PAIR_BASIC
pass:

Config: PSA_WANT_KEY_TYPE_DH_KEY_PAIR_EXPORT
depends_on:PSA_WANT_KEY_TYPE_DH_KEY_PAIR_EXPORT:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_DH_KEY_PAIR_BASIC
pass:

Config: !PSA_WANT_KEY_TYPE_DH_KEY_PAIR_EXPORT
depends_on:!PSA_WANT_KEY_TYPE_DH_KEY_PAIR_EXPORT:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_DH_KEY_PAIR_BASIC
pass:

Config: PSA_WANT_KEY_TYPE_DH_KEY_PAIR_GENERATE
depends_on:PSA_WANT_KEY_TYPE_DH_KEY_PAIR_GENERATE:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_DH_KEY_PAIR_BASIC
pass:

Config: !PSA_WANT_KEY_TYPE_DH_KEY_PAIR_GENERATE
depends_on:!PSA_WANT_KEY_TYPE_DH_KEY_PAIR_GENERATE:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_DH_KEY_PAIR_BASIC
pass:

Config: PSA_WANT_KEY_TYPE_DH_KEY_PAIR_IMPORT
depends_on:PSA_WANT_KEY_TYPE_DH_KEY_PAIR_IMPORT:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_DH_KEY_PAIR_BASIC
pass:

Config: !PSA_WANT_KEY_TYPE_DH_KEY_PAIR_IMPORT
depends_on:!PSA_WANT_KEY_TYPE_DH_KEY_PAIR_IMPORT:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_DH_KEY_PAIR_BASIC
pass:

Config: PSA_WANT_KEY_TYPE_DH_PUBLIC_KEY
depends_on:PSA_WANT_KEY_TYPE_DH_PUBLIC_KEY:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_KEY_TYPE_DH_PUBLIC_KEY
depends_on:!PSA_WANT_KEY_TYPE_DH_PUBLIC_KEY:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_KEY_TYPE_ECC_KEY_PAIR
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_KEY_TYPE_ECC_KEY_PAIR
depends_on:!PSA_WANT_KEY_TYPE_ECC_KEY_PAIR:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC
depends_on:!PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_DERIVE
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_DERIVE:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC
pass:

Config: !PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_DERIVE
depends_on:!PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_DERIVE:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC
pass:

Config: PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_EXPORT
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_EXPORT:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC
pass:

Config: !PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_EXPORT
depends_on:!PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_EXPORT:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC
pass:

Config: PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC
pass:

Config: !PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE
depends_on:!PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_GENERATE:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC
pass:

Config: PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_IMPORT
depends_on:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_IMPORT:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC
pass:

Config: !PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_IMPORT
depends_on:!PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_IMPORT:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_ECC_KEY_PAIR_BASIC
pass:

Config: PSA_WANT_KEY_TYPE_ECC_PUBLIC_KEY
depends_on:PSA_WANT_KEY_TYPE_ECC_PUBLIC_KEY:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_KEY_TYPE_ECC_PUBLIC_KEY
depends_on:!PSA_WANT_KEY_TYPE_ECC_PUBLIC_KEY:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_KEY_TYPE_HMAC
depends_on:PSA_WANT_KEY_TYPE_HMAC:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_KEY_TYPE_HMAC
depends_on:!PSA_WANT_KEY_TYPE_HMAC:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_KEY_TYPE_PASSWORD
depends_on:PSA_WANT_KEY_TYPE_PASSWORD:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_KEY_TYPE_PASSWORD
depends_on:!PSA_WANT_KEY_TYPE_PASSWORD:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_KEY_TYPE_PASSWORD_HASH
depends_on:PSA_WANT_KEY_TYPE_PASSWORD_HASH:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_KEY_TYPE_PASSWORD_HASH
depends_on:!PSA_WANT_KEY_TYPE_PASSWORD_HASH:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_KEY_TYPE_RAW_DATA
depends_on:PSA_WANT_KEY_TYPE_RAW_DATA:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_KEY_TYPE_RAW_DATA
depends_on:!PSA_WANT_KEY_TYPE_RAW_DATA:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_KEY_TYPE_RSA_KEY_PAIR
depends_on:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_KEY_TYPE_RSA_KEY_PAIR
depends_on:!PSA_WANT_KEY_TYPE_RSA_KEY_PAIR:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_BASIC
depends_on:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_BASIC:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_BASIC
depends_on:!PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_BASIC:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_DERIVE
depends_on:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_DERIVE:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_BASIC
pass:

Config: !PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_DERIVE
depends_on:!PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_DERIVE:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_BASIC
pass:

Config: PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_EXPORT
depends_on:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_EXPORT:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_BASIC
pass:

Config: !PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_EXPORT
depends_on:!PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_EXPORT:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_BASIC
pass:

Config: PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_GENERATE
depends_on:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_GENERATE:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_BASIC
pass:

Config: !PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_GENERATE
depends_on:!PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_GENERATE:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_BASIC
pass:

Config: PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_IMPORT
depends_on:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_IMPORT:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_BASIC
pass:

Config: !PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_IMPORT
depends_on:!PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_IMPORT:MBEDTLS_PSA_CRYPTO_CLIENT:PSA_WANT_KEY_TYPE_RSA_KEY_PAIR_BASIC
pass:

Config: PSA_WANT_KEY_TYPE_RSA_PUBLIC_KEY
depends_on:PSA_WANT_KEY_TYPE_RSA_PUBLIC_KEY:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

Config: !PSA_WANT_KEY_TYPE_RSA_PUBLIC_KEY
depends_on:!PSA_WANT_KEY_TYPE_RSA_PUBLIC_KEY:MBEDTLS_PSA_CRYPTO_CLIENT
pass:

# End of automatically generated file.
