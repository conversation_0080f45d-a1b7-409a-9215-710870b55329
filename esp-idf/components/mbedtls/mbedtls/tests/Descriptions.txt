test_suites
    The various 'test_suite_XXX' programs from the 'tests' directory, executed
    using 'make check' (Unix make) or 'make test' (Cmake), include test cases
    (reference test vectors, sanity checks, malformed input for parsing
    functions, etc.) for all modules except the SSL modules.

selftests
    The 'programs/test/selftest' program runs the 'XXX_self_test()' functions
    of each individual module. Most of them are included in the respective
    test suite, but some slower ones are only included here.

compat
    The 'tests/compat.sh' script checks interoperability with OpenSSL and
    GnuTLS (and ourselves!) for every common ciphersuite, in every TLS
    version, both ways (client/server), using client authentication or not.
    For each ciphersuite/version/side/authmode it performs a full handshake
    and a small data exchange.

ssl_opt
    The 'tests/ssl-opt.sh' script checks various options and/or operations not
    covered by compat.sh: session resumption (using session cache or tickets),
    renegotiation, SNI, other extensions, etc.
