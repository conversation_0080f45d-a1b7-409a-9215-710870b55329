# Miscellaneous tests related to the TLS handshake layer.
#
# Automatically generated by generate_tls_handshake_tests.py. Do not edit!

# Copyright The Mbed TLS Contributors
# SPDX-License-Identifier: Apache-2.0 OR GPL-2.0-or-later

run_test    "Handshake defragmentation on client: no fragmentation, for reference" \
            "$O_NEXT_SRV -allow_no_dhe_kex" \
            "$P_CLI debug_level=4" \
            0 \
            -C "waiting for more fragments"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on client: len=512, TLS 1.3" \
            "$O_NEXT_SRV -tls1_3 -split_send_frag 512" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 512, 0\\.\\.512 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 512\\.\\." \
            -c "Prepare: waiting for more handshake fragments 512/" \
            -c "Consume: waiting for more handshake fragments 512/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_certificate_authentication
requires_config_enabled MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
run_test    "Handshake defragmentation on client: len=512, TLS 1.2" \
            "$O_NEXT_SRV -tls1_2 -split_send_frag 512" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 512, 0\\.\\.512 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 512\\.\\." \
            -c "Prepare: waiting for more handshake fragments 512/" \
            -c "Consume: waiting for more handshake fragments 512/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on client: len=513, TLS 1.3" \
            "$O_NEXT_SRV -tls1_3 -split_send_frag 513" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 513, 0\\.\\.513 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 513\\.\\." \
            -c "Prepare: waiting for more handshake fragments 513/" \
            -c "Consume: waiting for more handshake fragments 513/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_certificate_authentication
requires_config_enabled MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
run_test    "Handshake defragmentation on client: len=513, TLS 1.2" \
            "$O_NEXT_SRV -tls1_2 -split_send_frag 513" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 513, 0\\.\\.513 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 513\\.\\." \
            -c "Prepare: waiting for more handshake fragments 513/" \
            -c "Consume: waiting for more handshake fragments 513/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on client: len=256, TLS 1.3" \
            "$O_NEXT_SRV -tls1_3 -split_send_frag 256" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 256, 0\\.\\.256 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 256\\.\\." \
            -c "Prepare: waiting for more handshake fragments 256/" \
            -c "Consume: waiting for more handshake fragments 256/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_certificate_authentication
requires_config_enabled MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
run_test    "Handshake defragmentation on client: len=256, TLS 1.2" \
            "$O_NEXT_SRV -tls1_2 -split_send_frag 256" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 256, 0\\.\\.256 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 256\\.\\." \
            -c "Prepare: waiting for more handshake fragments 256/" \
            -c "Consume: waiting for more handshake fragments 256/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on client: len=128, TLS 1.3" \
            "$O_NEXT_SRV -tls1_3 -split_send_frag 128" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 128, 0\\.\\.128 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 128\\.\\." \
            -c "Prepare: waiting for more handshake fragments 128/" \
            -c "Consume: waiting for more handshake fragments 128/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_certificate_authentication
requires_config_enabled MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
run_test    "Handshake defragmentation on client: len=128, TLS 1.2" \
            "$O_NEXT_SRV -tls1_2 -split_send_frag 128" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 128, 0\\.\\.128 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 128\\.\\." \
            -c "Prepare: waiting for more handshake fragments 128/" \
            -c "Consume: waiting for more handshake fragments 128/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on client: len=64, TLS 1.3" \
            "$O_NEXT_SRV -tls1_3 -split_send_frag 64" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 64, 0\\.\\.64 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 64\\.\\." \
            -c "Prepare: waiting for more handshake fragments 64/" \
            -c "Consume: waiting for more handshake fragments 64/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_certificate_authentication
requires_config_enabled MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
run_test    "Handshake defragmentation on client: len=64, TLS 1.2" \
            "$O_NEXT_SRV -tls1_2 -split_send_frag 64" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 64, 0\\.\\.64 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 64\\.\\." \
            -c "Prepare: waiting for more handshake fragments 64/" \
            -c "Consume: waiting for more handshake fragments 64/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on client: len=36, TLS 1.3" \
            "$O_NEXT_SRV -tls1_3 -split_send_frag 36" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 36, 0\\.\\.36 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 36\\.\\." \
            -c "Prepare: waiting for more handshake fragments 36/" \
            -c "Consume: waiting for more handshake fragments 36/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_certificate_authentication
requires_config_enabled MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
run_test    "Handshake defragmentation on client: len=36, TLS 1.2" \
            "$O_NEXT_SRV -tls1_2 -split_send_frag 36" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 36, 0\\.\\.36 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 36\\.\\." \
            -c "Prepare: waiting for more handshake fragments 36/" \
            -c "Consume: waiting for more handshake fragments 36/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on client: len=32, TLS 1.3" \
            "$O_NEXT_SRV -tls1_3 -split_send_frag 32" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 32, 0\\.\\.32 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 32\\.\\." \
            -c "Prepare: waiting for more handshake fragments 32/" \
            -c "Consume: waiting for more handshake fragments 32/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_certificate_authentication
requires_config_enabled MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
run_test    "Handshake defragmentation on client: len=32, TLS 1.2" \
            "$O_NEXT_SRV -tls1_2 -split_send_frag 32" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 32, 0\\.\\.32 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 32\\.\\." \
            -c "Prepare: waiting for more handshake fragments 32/" \
            -c "Consume: waiting for more handshake fragments 32/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on client: len=16, TLS 1.3" \
            "$O_NEXT_SRV -tls1_3 -split_send_frag 16" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 16, 0\\.\\.16 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 16\\.\\." \
            -c "Prepare: waiting for more handshake fragments 16/" \
            -c "Consume: waiting for more handshake fragments 16/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_certificate_authentication
requires_config_enabled MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
run_test    "Handshake defragmentation on client: len=16, TLS 1.2" \
            "$O_NEXT_SRV -tls1_2 -split_send_frag 16" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 16, 0\\.\\.16 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 16\\.\\." \
            -c "Prepare: waiting for more handshake fragments 16/" \
            -c "Consume: waiting for more handshake fragments 16/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on client: len=13, TLS 1.3" \
            "$O_NEXT_SRV -tls1_3 -split_send_frag 13" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 13, 0\\.\\.13 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 13\\.\\." \
            -c "Prepare: waiting for more handshake fragments 13/" \
            -c "Consume: waiting for more handshake fragments 13/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_certificate_authentication
requires_config_enabled MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
run_test    "Handshake defragmentation on client: len=13, TLS 1.2" \
            "$O_NEXT_SRV -tls1_2 -split_send_frag 13" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 13, 0\\.\\.13 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 13\\.\\." \
            -c "Prepare: waiting for more handshake fragments 13/" \
            -c "Consume: waiting for more handshake fragments 13/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on client: len=5, TLS 1.3" \
            "$O_NEXT_SRV -tls1_3 -split_send_frag 5" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 5, 0\\.\\.5 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 5\\.\\." \
            -c "Prepare: waiting for more handshake fragments 5/" \
            -c "Consume: waiting for more handshake fragments 5/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_certificate_authentication
requires_config_enabled MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
run_test    "Handshake defragmentation on client: len=5, TLS 1.2" \
            "$O_NEXT_SRV -tls1_2 -split_send_frag 5" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 5, 0\\.\\.5 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 5\\.\\." \
            -c "Prepare: waiting for more handshake fragments 5/" \
            -c "Consume: waiting for more handshake fragments 5/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on client: len=4, TLS 1.3" \
            "$O_NEXT_SRV -tls1_3 -split_send_frag 4" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 4, 0\\.\\.4 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 4\\.\\." \
            -c "Prepare: waiting for more handshake fragments 4/" \
            -c "Consume: waiting for more handshake fragments 4/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_certificate_authentication
requires_config_enabled MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
run_test    "Handshake defragmentation on client: len=4, TLS 1.2, default" \
            "$O_NEXT_SRV -tls1_2 -split_send_frag 4" \
            "$P_CLI debug_level=4" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 4, 0\\.\\.4 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 4\\.\\." \
            -c "Prepare: waiting for more handshake fragments 4/" \
            -c "Consume: waiting for more handshake fragments 4/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_certificate_authentication
requires_config_enabled MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
run_test    "Handshake defragmentation on client: len=4, TLS 1.2, null" \
            "$O_NEXT_SRV -tls1_2 -split_send_frag 4 -cipher ALL@SECLEVEL=0:COMPLEMENTOFALL@SECLEVEL=0" \
            "$P_CLI debug_level=4 force_ciphersuite=TLS-ECDHE-ECDSA-WITH-NULL-SHA" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 4, 0\\.\\.4 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 4\\.\\." \
            -c "Prepare: waiting for more handshake fragments 4/" \
            -c "Consume: waiting for more handshake fragments 4/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_certificate_authentication
requires_config_enabled MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
run_test    "Handshake defragmentation on client: len=4, TLS 1.2, ChachaPoly" \
            "$O_NEXT_SRV -tls1_2 -split_send_frag 4" \
            "$P_CLI debug_level=4 force_ciphersuite=TLS-ECDHE-ECDSA-WITH-CHACHA20-POLY1305-SHA256" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 4, 0\\.\\.4 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 4\\.\\." \
            -c "Prepare: waiting for more handshake fragments 4/" \
            -c "Consume: waiting for more handshake fragments 4/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_certificate_authentication
requires_config_enabled MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
run_test    "Handshake defragmentation on client: len=4, TLS 1.2, GCM" \
            "$O_NEXT_SRV -tls1_2 -split_send_frag 4" \
            "$P_CLI debug_level=4 force_ciphersuite=TLS-ECDHE-ECDSA-WITH-AES-128-GCM-SHA256" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 4, 0\\.\\.4 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 4\\.\\." \
            -c "Prepare: waiting for more handshake fragments 4/" \
            -c "Consume: waiting for more handshake fragments 4/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_certificate_authentication
requires_config_enabled MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
run_test    "Handshake defragmentation on client: len=4, TLS 1.2, CBC, etm=n" \
            "$O_NEXT_SRV -tls1_2 -split_send_frag 4" \
            "$P_CLI debug_level=4 force_ciphersuite=TLS-ECDHE-ECDSA-WITH-AES-128-CBC-SHA256 etm=0" \
            0 \
            -c "reassembled record" \
            -c "initial handshake fragment: 4, 0\\.\\.4 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 4\\.\\." \
            -c "Prepare: waiting for more handshake fragments 4/" \
            -c "Consume: waiting for more handshake fragments 4/" \
            -C "using encrypt then mac"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_certificate_authentication
requires_config_enabled MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA_ENABLED
requires_config_enabled MBEDTLS_SSL_ENCRYPT_THEN_MAC
run_test    "Handshake defragmentation on client: len=4, TLS 1.2, CBC, etm=y" \
            "$O_NEXT_SRV -tls1_2 -split_send_frag 4" \
            "$P_CLI debug_level=4 force_ciphersuite=TLS-ECDHE-ECDSA-WITH-AES-128-CBC-SHA256 etm=1" \
            0 \
            -c "using encrypt then mac" \
            -c "reassembled record" \
            -c "initial handshake fragment: 4, 0\\.\\.4 of [0-9]\\+" \
            -c "subsequent handshake fragment: [0-9]\\+, 4\\.\\." \
            -c "Prepare: waiting for more handshake fragments 4/" \
            -c "Consume: waiting for more handshake fragments 4/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
run_test    "Handshake defragmentation on client: len=3, TLS 1.3" \
            "$O_NEXT_SRV -tls1_3 -allow_no_dhe_kex -split_send_frag 3" \
            "$P_CLI debug_level=4" \
            1 \
            -c "=> ssl_tls13_process_server_hello" \
            -c "handshake message too short: 3" \
            -c "SSL - An invalid SSL record was received"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
run_test    "Handshake defragmentation on client: len=3, TLS 1.2" \
            "$O_NEXT_SRV -tls1_2 -allow_no_dhe_kex -split_send_frag 3" \
            "$P_CLI debug_level=4" \
            1 \
            -c "handshake message too short: 3" \
            -c "SSL - An invalid SSL record was received"

run_test    "Handshake defragmentation on server: no fragmentation, for reference" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -allow_no_dhe_kex -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -S "waiting for more fragments"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=512, TLS 1.3" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_3 -split_send_frag 512 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 512, 0\\.\\.512 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 512\\.\\." \
            -s "Prepare: waiting for more handshake fragments 512/" \
            -s "Consume: waiting for more handshake fragments 512/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=512, TLS 1.2" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_2 -split_send_frag 512 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 512, 0\\.\\.512 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 512\\.\\." \
            -s "Prepare: waiting for more handshake fragments 512/" \
            -s "Consume: waiting for more handshake fragments 512/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=513, TLS 1.3" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_3 -split_send_frag 513 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 513, 0\\.\\.513 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 513\\.\\." \
            -s "Prepare: waiting for more handshake fragments 513/" \
            -s "Consume: waiting for more handshake fragments 513/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=513, TLS 1.2" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_2 -split_send_frag 513 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 513, 0\\.\\.513 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 513\\.\\." \
            -s "Prepare: waiting for more handshake fragments 513/" \
            -s "Consume: waiting for more handshake fragments 513/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=256, TLS 1.3" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_3 -split_send_frag 256 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 256, 0\\.\\.256 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 256\\.\\." \
            -s "Prepare: waiting for more handshake fragments 256/" \
            -s "Consume: waiting for more handshake fragments 256/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=256, TLS 1.2" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_2 -split_send_frag 256 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 256, 0\\.\\.256 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 256\\.\\." \
            -s "Prepare: waiting for more handshake fragments 256/" \
            -s "Consume: waiting for more handshake fragments 256/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=128, TLS 1.3" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_3 -split_send_frag 128 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 128, 0\\.\\.128 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 128\\.\\." \
            -s "Prepare: waiting for more handshake fragments 128/" \
            -s "Consume: waiting for more handshake fragments 128/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=128, TLS 1.2 with 1.3 support" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_2 -split_send_frag 128 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 128, 0\\.\\.128 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 128\\.\\." \
            -s "Prepare: waiting for more handshake fragments 128/" \
            -s "Consume: waiting for more handshake fragments 128/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=64, TLS 1.3" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_3 -split_send_frag 64 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 64, 0\\.\\.64 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 64\\.\\." \
            -s "Prepare: waiting for more handshake fragments 64/" \
            -s "Consume: waiting for more handshake fragments 64/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=64, TLS 1.2 with 1.3 support" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_2 -split_send_frag 64 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 64, 0\\.\\.64 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 64\\.\\." \
            -s "Prepare: waiting for more handshake fragments 64/" \
            -s "Consume: waiting for more handshake fragments 64/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=36, TLS 1.3" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_3 -split_send_frag 36 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 36, 0\\.\\.36 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 36\\.\\." \
            -s "Prepare: waiting for more handshake fragments 36/" \
            -s "Consume: waiting for more handshake fragments 36/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=36, TLS 1.2 with 1.3 support" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_2 -split_send_frag 36 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 36, 0\\.\\.36 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 36\\.\\." \
            -s "Prepare: waiting for more handshake fragments 36/" \
            -s "Consume: waiting for more handshake fragments 36/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=32, TLS 1.3" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_3 -split_send_frag 32 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 32, 0\\.\\.32 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 32\\.\\." \
            -s "Prepare: waiting for more handshake fragments 32/" \
            -s "Consume: waiting for more handshake fragments 32/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=32, TLS 1.2 with 1.3 support" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_2 -split_send_frag 32 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 32, 0\\.\\.32 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 32\\.\\." \
            -s "Prepare: waiting for more handshake fragments 32/" \
            -s "Consume: waiting for more handshake fragments 32/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=16, TLS 1.3" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_3 -split_send_frag 16 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 16, 0\\.\\.16 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 16\\.\\." \
            -s "Prepare: waiting for more handshake fragments 16/" \
            -s "Consume: waiting for more handshake fragments 16/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=16, TLS 1.2 with 1.3 support" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_2 -split_send_frag 16 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 16, 0\\.\\.16 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 16\\.\\." \
            -s "Prepare: waiting for more handshake fragments 16/" \
            -s "Consume: waiting for more handshake fragments 16/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=13, TLS 1.3" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_3 -split_send_frag 13 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 13, 0\\.\\.13 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 13\\.\\." \
            -s "Prepare: waiting for more handshake fragments 13/" \
            -s "Consume: waiting for more handshake fragments 13/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=13, TLS 1.2 with 1.3 support" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_2 -split_send_frag 13 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 13, 0\\.\\.13 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 13\\.\\." \
            -s "Prepare: waiting for more handshake fragments 13/" \
            -s "Consume: waiting for more handshake fragments 13/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=5, TLS 1.3" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_3 -split_send_frag 5 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 5, 0\\.\\.5 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 5\\.\\." \
            -s "Prepare: waiting for more handshake fragments 5/" \
            -s "Consume: waiting for more handshake fragments 5/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=5, TLS 1.2 with 1.3 support" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_2 -split_send_frag 5 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 5, 0\\.\\.5 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 5\\.\\." \
            -s "Prepare: waiting for more handshake fragments 5/" \
            -s "Consume: waiting for more handshake fragments 5/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=4, TLS 1.3" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_3 -split_send_frag 4 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 4, 0\\.\\.4 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 4\\.\\." \
            -s "Prepare: waiting for more handshake fragments 4/" \
            -s "Consume: waiting for more handshake fragments 4/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
run_test    "Handshake defragmentation on server: len=4, TLS 1.2 with 1.3 support, default" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_2 -split_send_frag 4 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 4, 0\\.\\.4 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 4\\.\\." \
            -s "Prepare: waiting for more handshake fragments 4/" \
            -s "Consume: waiting for more handshake fragments 4/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
requires_ciphersuite_enabled TLS-ECDHE-ECDSA-WITH-NULL-SHA
run_test    "Handshake defragmentation on server: len=4, TLS 1.2 with 1.3 support, null" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_2 -split_send_frag 4 -cipher ECDHE-ECDSA-NULL-SHA@SECLEVEL=0 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 4, 0\\.\\.4 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 4\\.\\." \
            -s "Prepare: waiting for more handshake fragments 4/" \
            -s "Consume: waiting for more handshake fragments 4/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
requires_ciphersuite_enabled TLS-ECDHE-ECDSA-WITH-CHACHA20-POLY1305-SHA256
run_test    "Handshake defragmentation on server: len=4, TLS 1.2 with 1.3 support, ChachaPoly" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_2 -split_send_frag 4 -cipher ECDHE-ECDSA-CHACHA20-POLY1305 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 4, 0\\.\\.4 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 4\\.\\." \
            -s "Prepare: waiting for more handshake fragments 4/" \
            -s "Consume: waiting for more handshake fragments 4/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
requires_ciphersuite_enabled TLS-ECDHE-ECDSA-WITH-AES-128-GCM-SHA256
run_test    "Handshake defragmentation on server: len=4, TLS 1.2 with 1.3 support, GCM" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_2 -split_send_frag 4 -cipher ECDHE-ECDSA-AES128-GCM-SHA256 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 4, 0\\.\\.4 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 4\\.\\." \
            -s "Prepare: waiting for more handshake fragments 4/" \
            -s "Consume: waiting for more handshake fragments 4/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
requires_ciphersuite_enabled TLS-ECDHE-ECDSA-WITH-AES-128-CBC-SHA256
run_test    "Handshake defragmentation on server: len=4, TLS 1.2 with 1.3 support, CBC, etm=n" \
            "$P_SRV debug_level=4 etm=0 auth_mode=required" \
            "$O_NEXT_CLI -tls1_2 -split_send_frag 4 -cipher ECDHE-ECDSA-AES128-SHA256 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "reassembled record" \
            -s "initial handshake fragment: 4, 0\\.\\.4 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 4\\.\\." \
            -s "Prepare: waiting for more handshake fragments 4/" \
            -s "Consume: waiting for more handshake fragments 4/" \
            -S "using encrypt then mac"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
requires_certificate_authentication
requires_ciphersuite_enabled TLS-ECDHE-ECDSA-WITH-AES-128-CBC-SHA256
requires_config_enabled MBEDTLS_SSL_ENCRYPT_THEN_MAC
run_test    "Handshake defragmentation on server: len=4, TLS 1.2 with 1.3 support, CBC, etm=y" \
            "$P_SRV debug_level=4 etm=1 auth_mode=required" \
            "$O_NEXT_CLI -tls1_2 -split_send_frag 4 -cipher ECDHE-ECDSA-AES128-SHA256 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            0 \
            -s "using encrypt then mac" \
            -s "reassembled record" \
            -s "initial handshake fragment: 4, 0\\.\\.4 of [0-9]\\+" \
            -s "subsequent handshake fragment: [0-9]\\+, 4\\.\\." \
            -s "Prepare: waiting for more handshake fragments 4/" \
            -s "Consume: waiting for more handshake fragments 4/"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
run_test    "Handshake defragmentation on server: len=3, TLS 1.3" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_3 -allow_no_dhe_kex -split_send_frag 3 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            1 \
            -s "<= parse client hello" \
            -s "handshake message too short: 3" \
            -s "SSL - An invalid SSL record was received"

requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_2
requires_config_enabled MBEDTLS_SSL_PROTO_TLS1_3
run_test    "Handshake defragmentation on server: len=3, TLS 1.2 with 1.3 support" \
            "$P_SRV debug_level=4 auth_mode=required" \
            "$O_NEXT_CLI -tls1_2 -allow_no_dhe_kex -split_send_frag 3 -cert $DATA_FILES_PATH/server5.crt -key $DATA_FILES_PATH/server5.key" \
            1 \
            -s "<= parse client hello" \
            -s "handshake message too short: 3" \
            -s "SSL - An invalid SSL record was received"

# End of automatically generated file.
