# Python package requirements for Mbed TLS testing.

-r driver.requirements.txt

# Use a known version of <PERSON><PERSON><PERSON>, because new versions tend to add warnings
# that could start rejecting our code.
# 2.4.4 is the version in Ubuntu 20.04. It supports Python >=3.5.
pylint == 2.4.4

# Use a version of mypy that is compatible with our code base.
# mypy <0.940 is known not to work: see commit
#  :/Upgrade mypy to the last version supporting Python 3.6
# mypy >=0.960 is known not to work:
#   https://github.com/Mbed-TLS/mbedtls-framework/issues/50
# mypy 0.942 is the version in Ubuntu 22.04.
mypy == 0.942

# At the time of writing, only needed for tests/scripts/audit-validity-dates.py.
# It needs >=35.0.0 for correct operation, and that requires Python >=3.6,
# but our CI has Python 3.5. So let pip install the newest version that's
# compatible with the running Python: this way we get something good enough
# for mypy and pylint under Python 3.5, and we also get something good enough
# to run audit-validity-dates.py on Python >=3.6.
cryptography # >= 35.0.0

# For building `framework/data_files/server9-bad-saltlen.crt` and check python
# files.
asn1crypto
