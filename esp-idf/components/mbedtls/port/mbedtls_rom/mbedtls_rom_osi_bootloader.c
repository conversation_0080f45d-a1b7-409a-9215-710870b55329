/*
 * SPDX-FileCopyrightText: 2025 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#include "soc/chip_revision.h"
#include "hal/efuse_hal.h"
#include "mbedtls_rom_osi.h"

/* This structure can be automatically generated by the script with rom.mbedtls.ld. */
static const mbedtls_rom_funcs_t mbedtls_rom_funcs_table = {
    /* Fill the ROM functions into mbedtls rom function table. */
    /* aes module */
    ._rom_mbedtls_aes_init = mbedtls_aes_init,
    ._rom_mbedtls_aes_free = mbedtls_aes_free,
    ._rom_mbedtls_aes_setkey_enc = mbedtls_aes_setkey_enc,
    ._rom_mbedtls_aes_setkey_dec = mbedtls_aes_setkey_dec,
    ._rom_mbedtls_aes_crypt_ecb = mbedtls_aes_crypt_ecb,
    ._rom_mbedtls_aes_crypt_cbc = mbedtls_aes_crypt_cbc,
    ._rom_mbedtls_internal_aes_encrypt = mbedtls_internal_aes_encrypt,
    ._rom_mbedtls_internal_aes_decrypt = mbedtls_internal_aes_decrypt,
};

/* This structure can be automatically generated by the script with rom.mbedtls.ld. */
static const mbedtls_rom_eco4_funcs_t mbedtls_rom_eco4_funcs_table = {
    /* Fill the ROM functions into mbedtls rom function table. */
    /* aes module */
    ._rom_mbedtls_aes_init = mbedtls_aes_init,
    ._rom_mbedtls_aes_free = mbedtls_aes_free,
    ._rom_mbedtls_aes_setkey_enc = mbedtls_aes_setkey_enc,
    ._rom_mbedtls_aes_setkey_dec = mbedtls_aes_setkey_dec,
    ._rom_mbedtls_aes_crypt_ecb = mbedtls_aes_crypt_ecb,
    ._rom_mbedtls_aes_crypt_cbc = mbedtls_aes_crypt_cbc,
    ._rom_mbedtls_internal_aes_encrypt = mbedtls_internal_aes_encrypt,
    ._rom_mbedtls_internal_aes_decrypt = mbedtls_internal_aes_decrypt,

    ._rom_mbedtls_aes_xts_init = mbedtls_aes_xts_init,
    ._rom_mbedtls_aes_xts_free = mbedtls_aes_xts_free,
    ._rom_mbedtls_aes_xts_setkey_enc = mbedtls_aes_xts_setkey_enc,
    ._rom_mbedtls_aes_xts_setkey_dec = mbedtls_aes_xts_setkey_dec,
    ._rom_mbedtls_aes_crypt_xts = mbedtls_aes_crypt_xts,
};

void mbedtls_rom_osi_functions_init_bootloader(void)
{
    // /* Export the rom mbedtls functions table pointer */
    extern void *mbedtls_rom_osi_funcs_ptr;

    unsigned chip_version = efuse_hal_chip_revision();
    if ( ESP_CHIP_REV_ABOVE(chip_version, 200) ) {
        /* Initialize the pointer of rom eco4 mbedtls functions table. */
        mbedtls_rom_osi_funcs_ptr = (mbedtls_rom_eco4_funcs_t *)&mbedtls_rom_eco4_funcs_table;
    } else {
        /* Initialize the pointer of rom mbedtls functions table. */
        mbedtls_rom_osi_funcs_ptr = (mbedtls_rom_funcs_t *)&mbedtls_rom_funcs_table;
    }
}
