/*
 * SPDX-FileCopyrightText: 2023-2024 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#if !defined(MBEDTLS_CONFIG_FILE)
#include "mbedtls/config.h"
#else
#include MBEDTLS_CONFIG_FILE
#endif

#include "soc/chip_revision.h"
#include "hal/efuse_hal.h"
#include "mbedtls/platform.h"
#include "mbedtls_rom_osi.h"

void mbedtls_rom_osi_functions_init(void);

static void mbedtls_rom_mutex_init( mbedtls_threading_mutex_t *mutex )
{
    if (mutex == NULL) {
        return;
    }

#if defined(MBEDTLS_THREADING_ALT)
    mutex->mutex = xSemaphoreCreateMutex();
    assert(mutex->mutex != NULL);
#else
    mbedtls_mutex_init(mutex);
#endif
}

static void mbedtls_rom_mutex_free( mbedtls_threading_mutex_t *mutex )
{
    if (mutex == NULL) {
        return;
    }

#if defined(MBEDTLS_THREADING_ALT)
    vSemaphoreDelete(mutex->mutex);
#else
    mbedtls_mutex_free(mutex);
#endif
}

static int mbedtls_rom_mutex_lock( mbedtls_threading_mutex_t *mutex )
{
    if (mutex == NULL) {
        return MBEDTLS_ERR_THREADING_BAD_INPUT_DATA;
    }

#if defined(MBEDTLS_THREADING_ALT)
    if (xSemaphoreTake(mutex->mutex, portMAX_DELAY) != pdTRUE) {
        return MBEDTLS_ERR_THREADING_MUTEX_ERROR;
    }
    return 0;
#else
    return mbedtls_mutex_lock(mutex);
#endif
}

static int mbedtls_rom_mutex_unlock( mbedtls_threading_mutex_t *mutex )
{
    if (mutex == NULL) {
        return MBEDTLS_ERR_THREADING_BAD_INPUT_DATA;
    }

#if defined(MBEDTLS_THREADING_ALT)
    if (xSemaphoreGive(mutex->mutex) != pdTRUE) {
        return MBEDTLS_ERR_THREADING_MUTEX_ERROR;
    }
    return 0;
#else
    return mbedtls_mutex_unlock(mutex);
#endif
}

/* This structure can be automatically generated by the script with rom.mbedtls.ld. */
static const mbedtls_rom_funcs_t mbedtls_rom_funcs_table = {
    /* Fill the ROM functions into mbedtls rom function table. */
    /* aes module */
    ._rom_mbedtls_aes_init = mbedtls_aes_init,
    ._rom_mbedtls_aes_free = mbedtls_aes_free,
    ._rom_mbedtls_aes_setkey_enc = mbedtls_aes_setkey_enc,
    ._rom_mbedtls_aes_setkey_dec = mbedtls_aes_setkey_dec,
    ._rom_mbedtls_aes_crypt_ecb = mbedtls_aes_crypt_ecb,
    ._rom_mbedtls_aes_crypt_cbc = mbedtls_aes_crypt_cbc,
    ._rom_mbedtls_internal_aes_encrypt = mbedtls_internal_aes_encrypt,
    ._rom_mbedtls_internal_aes_decrypt = mbedtls_internal_aes_decrypt,
    /* asn1 module */
    ._rom_mbedtls_asn1_get_len = mbedtls_asn1_get_len,
    ._rom_mbedtls_asn1_get_tag = mbedtls_asn1_get_tag,
    ._rom_mbedtls_asn1_get_bool = mbedtls_asn1_get_bool,
    ._rom_mbedtls_asn1_get_int = mbedtls_asn1_get_int,
    ._rom_mbedtls_asn1_get_bitstring = mbedtls_asn1_get_bitstring,
    ._rom_mbedtls_asn1_get_bitstring_null = mbedtls_asn1_get_bitstring_null,
    ._rom_mbedtls_asn1_get_sequence_of = mbedtls_asn1_get_sequence_of,
    ._rom_mbedtls_asn1_get_mpi = mbedtls_asn1_get_mpi,
    ._rom_mbedtls_asn1_get_alg = mbedtls_asn1_get_alg,
    ._rom_mbedtls_asn1_get_alg_null = mbedtls_asn1_get_alg_null,
    ._rom_mbedtls_asn1_write_len = mbedtls_asn1_write_len,
    ._rom_mbedtls_asn1_write_tag = mbedtls_asn1_write_tag,
    ._rom_mbedtls_asn1_write_mpi = mbedtls_asn1_write_mpi,
    /* base64 module */
    ._rom_mbedtls_base64_decode = mbedtls_base64_decode,
    /* bignum module */
    ._rom_mbedtls_mpi_init = mbedtls_mpi_init,
    ._rom_mbedtls_mpi_free = mbedtls_mpi_free,
    ._rom_mbedtls_mpi_grow = mbedtls_mpi_grow,
    ._rom_mbedtls_mpi_shrink = mbedtls_mpi_shrink,
    ._rom_mbedtls_mpi_copy = mbedtls_mpi_copy,
    ._rom_mbedtls_mpi_safe_cond_assign = mbedtls_mpi_safe_cond_assign,
    ._rom_mbedtls_mpi_safe_cond_swap = mbedtls_mpi_safe_cond_swap,
    ._rom_mbedtls_mpi_lset = mbedtls_mpi_lset,
    ._rom_mbedtls_mpi_get_bit = mbedtls_mpi_get_bit,
    ._rom_mbedtls_mpi_set_bit = mbedtls_mpi_set_bit,
    ._rom_mbedtls_mpi_lsb = mbedtls_mpi_lsb,
    ._rom_mbedtls_mpi_bitlen = mbedtls_mpi_bitlen,
    ._rom_mbedtls_mpi_size = mbedtls_mpi_size,
    ._rom_mbedtls_mpi_read_binary = mbedtls_mpi_read_binary,
    ._rom_mbedtls_mpi_write_binary = mbedtls_mpi_write_binary,
    ._rom_mbedtls_mpi_shift_l = mbedtls_mpi_shift_l,
    ._rom_mbedtls_mpi_shift_r = mbedtls_mpi_shift_r,
    ._rom_mbedtls_mpi_cmp_abs = mbedtls_mpi_cmp_abs,
    ._rom_mbedtls_mpi_cmp_mpi = mbedtls_mpi_cmp_mpi,
    ._rom_mbedtls_mpi_lt_mpi_ct = mbedtls_mpi_lt_mpi_ct,
    ._rom_mbedtls_mpi_cmp_int = mbedtls_mpi_cmp_int,
    ._rom_mbedtls_mpi_add_abs = mbedtls_mpi_add_abs,
    ._rom_mbedtls_mpi_sub_abs = mbedtls_mpi_sub_abs,
    ._rom_mbedtls_mpi_add_mpi = mbedtls_mpi_add_mpi,
    ._rom_mbedtls_mpi_sub_mpi = mbedtls_mpi_sub_mpi,
    ._rom_mbedtls_mpi_add_int = mbedtls_mpi_add_int,
    ._rom_mbedtls_mpi_sub_int = mbedtls_mpi_sub_int,
    ._rom_mbedtls_mpi_mul_mpi = mbedtls_mpi_mul_mpi,
    ._rom_mbedtls_mpi_mul_int = mbedtls_mpi_mul_int,
    ._rom_mbedtls_mpi_div_mpi = mbedtls_mpi_div_mpi,
    ._rom_mbedtls_mpi_div_int = mbedtls_mpi_div_int,
    ._rom_mbedtls_mpi_mod_mpi = mbedtls_mpi_mod_mpi,
    ._rom_mbedtls_mpi_mod_int = mbedtls_mpi_mod_int,
    ._rom_mbedtls_mpi_exp_mod = mbedtls_mpi_exp_mod,
    ._rom_mbedtls_mpi_fill_random = mbedtls_mpi_fill_random,
    ._rom_mbedtls_mpi_gcd = mbedtls_mpi_gcd,
    ._rom_mbedtls_mpi_inv_mod = mbedtls_mpi_inv_mod,
    ._rom_mbedtls_mpi_is_prime_ext = mbedtls_mpi_is_prime_ext,
    /* ccm module */
    ._rom_mbedtls_ccm_star_encrypt_and_tag = mbedtls_ccm_star_encrypt_and_tag,
    ._rom_mbedtls_ccm_star_auth_decrypt = mbedtls_ccm_star_auth_decrypt,
    /* cipher module */
    ._rom_mbedtls_cipher_init = mbedtls_cipher_init,
    ._rom_mbedtls_cipher_set_padding_mode = mbedtls_cipher_set_padding_mode,
    ._rom_mbedtls_cipher_reset = mbedtls_cipher_reset,
    ._rom_mbedtls_cipher_finish = mbedtls_cipher_finish,
    ._rom_mbedtls_cipher_crypt = mbedtls_cipher_crypt,
    ._rom_mbedtls_cipher_cmac_starts = mbedtls_cipher_cmac_starts,
    ._rom_mbedtls_cipher_cmac_update = mbedtls_cipher_cmac_update,
    ._rom_mbedtls_cipher_cmac_finish = mbedtls_cipher_cmac_finish,
    /* ctr drbg module */
    ._rom_mbedtls_ctr_drbg_init = mbedtls_ctr_drbg_init,
    ._rom_mbedtls_ctr_drbg_seed = mbedtls_ctr_drbg_seed,
    ._rom_mbedtls_ctr_drbg_free = mbedtls_ctr_drbg_free,
    ._rom_mbedtls_ctr_drbg_reseed = mbedtls_ctr_drbg_reseed,
    ._rom_mbedtls_ctr_drbg_random_with_add = mbedtls_ctr_drbg_random_with_add,
    ._rom_mbedtls_ctr_drbg_random = mbedtls_ctr_drbg_random,
    /* sha1 module */
    ._rom_mbedtls_sha1_init = mbedtls_sha1_init,
    ._rom_mbedtls_sha1_free = mbedtls_sha1_free,
    ._rom_mbedtls_sha1_clone = mbedtls_sha1_clone,
    ._rom_mbedtls_sha1_starts = mbedtls_sha1_starts,
    ._rom_mbedtls_sha1_finish = mbedtls_sha1_finish,
    /* sha256 module */
    ._rom_mbedtls_sha256_init = mbedtls_sha256_init,
    ._rom_mbedtls_sha256_free = mbedtls_sha256_free,
    ._rom_mbedtls_sha256_clone = mbedtls_sha256_clone,
    ._rom_mbedtls_sha256_starts = mbedtls_sha256_starts,
    ._rom_mbedtls_sha256_finish = mbedtls_sha256_finish,
    ._rom_mbedtls_sha256 = mbedtls_sha256,
    /* sha512 module */
    ._rom_mbedtls_sha512_init = mbedtls_sha512_init,
    ._rom_mbedtls_sha512_free = mbedtls_sha512_free,
    ._rom_mbedtls_sha512_clone = mbedtls_sha512_clone,
    ._rom_mbedtls_sha512_starts = mbedtls_sha512_starts,
    ._rom_mbedtls_sha512_update = mbedtls_sha512_update,
    ._rom_mbedtls_sha512_finish = mbedtls_sha512_finish,
    ._rom_mbedtls_internal_sha512_process = mbedtls_internal_sha512_process,
    ._rom_mbedtls_sha512 = mbedtls_sha512,

    /* Fill the platform functions into mbedtls rom function table. */
    ._mbedtls_mutex_init = mbedtls_rom_mutex_init,
    ._mbedtls_mutex_free = mbedtls_rom_mutex_free,
    ._mbedtls_mutex_lock = mbedtls_rom_mutex_lock,
    ._mbedtls_mutex_unlock = mbedtls_rom_mutex_unlock,
    ._mbedtls_calloc = MBEDTLS_PLATFORM_STD_CALLOC,
    ._mbedtls_free = MBEDTLS_PLATFORM_STD_FREE,

    /* Fill the SHA functions into mbedtls rom function table, since these functions are not exported in the ROM interface. */
    ._mbedtls_sha1_update = mbedtls_sha1_update,
    ._mbedtls_internal_sha1_process = mbedtls_internal_sha1_process,
    ._mbedtls_sha256_update = mbedtls_sha256_update,
    ._mbedtls_internal_sha256_process = mbedtls_internal_sha256_process,
};

/* This structure can be automatically generated by the script with rom.mbedtls.ld. */
static const mbedtls_rom_eco4_funcs_t mbedtls_rom_eco4_funcs_table = {
    /* Fill the ROM functions into mbedtls rom function table. */
    /* aes module */
    ._rom_mbedtls_aes_init = mbedtls_aes_init,
    ._rom_mbedtls_aes_free = mbedtls_aes_free,
    ._rom_mbedtls_aes_setkey_enc = mbedtls_aes_setkey_enc,
    ._rom_mbedtls_aes_setkey_dec = mbedtls_aes_setkey_dec,
    ._rom_mbedtls_aes_crypt_ecb = mbedtls_aes_crypt_ecb,
    ._rom_mbedtls_aes_crypt_cbc = mbedtls_aes_crypt_cbc,
    ._rom_mbedtls_internal_aes_encrypt = mbedtls_internal_aes_encrypt,
    ._rom_mbedtls_internal_aes_decrypt = mbedtls_internal_aes_decrypt,
    /* asn1 module */
    ._rom_mbedtls_asn1_get_len = mbedtls_asn1_get_len,
    ._rom_mbedtls_asn1_get_tag = mbedtls_asn1_get_tag,
    ._rom_mbedtls_asn1_get_bool = mbedtls_asn1_get_bool,
    ._rom_mbedtls_asn1_get_int = mbedtls_asn1_get_int,
    ._rom_mbedtls_asn1_get_bitstring = mbedtls_asn1_get_bitstring,
    ._rom_mbedtls_asn1_get_bitstring_null = mbedtls_asn1_get_bitstring_null,
    ._rom_mbedtls_asn1_get_sequence_of = mbedtls_asn1_get_sequence_of,
    ._rom_mbedtls_asn1_get_mpi = mbedtls_asn1_get_mpi,
    ._rom_mbedtls_asn1_get_alg = mbedtls_asn1_get_alg,
    ._rom_mbedtls_asn1_get_alg_null = mbedtls_asn1_get_alg_null,
    ._rom_mbedtls_asn1_write_len = mbedtls_asn1_write_len,
    ._rom_mbedtls_asn1_write_tag = mbedtls_asn1_write_tag,
    ._rom_mbedtls_asn1_write_mpi = mbedtls_asn1_write_mpi,
    /* base64 module */
    ._rom_mbedtls_base64_decode = mbedtls_base64_decode,
    /* bignum module */
    ._rom_mbedtls_mpi_init = mbedtls_mpi_init,
    ._rom_mbedtls_mpi_free = mbedtls_mpi_free,
    ._rom_mbedtls_mpi_grow = mbedtls_mpi_grow,
    ._rom_mbedtls_mpi_shrink = mbedtls_mpi_shrink,
    ._rom_mbedtls_mpi_copy = mbedtls_mpi_copy,
    ._rom_mbedtls_mpi_safe_cond_assign = mbedtls_mpi_safe_cond_assign,
    ._rom_mbedtls_mpi_safe_cond_swap = mbedtls_mpi_safe_cond_swap,
    ._rom_mbedtls_mpi_lset = mbedtls_mpi_lset,
    ._rom_mbedtls_mpi_get_bit = mbedtls_mpi_get_bit,
    ._rom_mbedtls_mpi_set_bit = mbedtls_mpi_set_bit,
    ._rom_mbedtls_mpi_lsb = mbedtls_mpi_lsb,
    ._rom_mbedtls_mpi_bitlen = mbedtls_mpi_bitlen,
    ._rom_mbedtls_mpi_size = mbedtls_mpi_size,
    ._rom_mbedtls_mpi_read_binary = mbedtls_mpi_read_binary,
    ._rom_mbedtls_mpi_write_binary = mbedtls_mpi_write_binary,
    ._rom_mbedtls_mpi_shift_l = mbedtls_mpi_shift_l,
    ._rom_mbedtls_mpi_shift_r = mbedtls_mpi_shift_r,
    ._rom_mbedtls_mpi_cmp_abs = mbedtls_mpi_cmp_abs,
    ._rom_mbedtls_mpi_cmp_mpi = mbedtls_mpi_cmp_mpi,
    ._rom_mbedtls_mpi_lt_mpi_ct = mbedtls_mpi_lt_mpi_ct,
    ._rom_mbedtls_mpi_cmp_int = mbedtls_mpi_cmp_int,
    ._rom_mbedtls_mpi_add_abs = mbedtls_mpi_add_abs,
    ._rom_mbedtls_mpi_sub_abs = mbedtls_mpi_sub_abs,
    ._rom_mbedtls_mpi_add_mpi = mbedtls_mpi_add_mpi,
    ._rom_mbedtls_mpi_sub_mpi = mbedtls_mpi_sub_mpi,
    ._rom_mbedtls_mpi_add_int = mbedtls_mpi_add_int,
    ._rom_mbedtls_mpi_sub_int = mbedtls_mpi_sub_int,
    ._rom_mbedtls_mpi_mul_mpi = mbedtls_mpi_mul_mpi,
    ._rom_mbedtls_mpi_mul_int = mbedtls_mpi_mul_int,
    ._rom_mbedtls_mpi_div_mpi = mbedtls_mpi_div_mpi,
    ._rom_mbedtls_mpi_div_int = mbedtls_mpi_div_int,
    ._rom_mbedtls_mpi_mod_mpi = mbedtls_mpi_mod_mpi,
    ._rom_mbedtls_mpi_mod_int = mbedtls_mpi_mod_int,
    ._rom_mbedtls_mpi_exp_mod = mbedtls_mpi_exp_mod,
    ._rom_mbedtls_mpi_fill_random = mbedtls_mpi_fill_random,
    ._rom_mbedtls_mpi_gcd = mbedtls_mpi_gcd,
    ._rom_mbedtls_mpi_inv_mod = mbedtls_mpi_inv_mod,
    ._rom_mbedtls_mpi_is_prime_ext = mbedtls_mpi_is_prime_ext,
    /* ccm module */
    ._rom_mbedtls_ccm_star_encrypt_and_tag = mbedtls_ccm_star_encrypt_and_tag,
    ._rom_mbedtls_ccm_star_auth_decrypt = mbedtls_ccm_star_auth_decrypt,
    /* cipher module */
    ._rom_mbedtls_cipher_init = mbedtls_cipher_init,
    ._rom_mbedtls_cipher_set_padding_mode = mbedtls_cipher_set_padding_mode,
    ._rom_mbedtls_cipher_reset = mbedtls_cipher_reset,
    ._rom_mbedtls_cipher_finish = mbedtls_cipher_finish,
    ._rom_mbedtls_cipher_crypt = mbedtls_cipher_crypt,
    ._rom_mbedtls_cipher_cmac_starts = mbedtls_cipher_cmac_starts,
    ._rom_mbedtls_cipher_cmac_update = mbedtls_cipher_cmac_update,
    ._rom_mbedtls_cipher_cmac_finish = mbedtls_cipher_cmac_finish,
    /* ctr drbg module */
    ._rom_mbedtls_ctr_drbg_init = mbedtls_ctr_drbg_init,
    ._rom_mbedtls_ctr_drbg_seed = mbedtls_ctr_drbg_seed,
    ._rom_mbedtls_ctr_drbg_free = mbedtls_ctr_drbg_free,
    ._rom_mbedtls_ctr_drbg_reseed = mbedtls_ctr_drbg_reseed,
    ._rom_mbedtls_ctr_drbg_random_with_add = mbedtls_ctr_drbg_random_with_add,
    ._rom_mbedtls_ctr_drbg_random = mbedtls_ctr_drbg_random,
    /* sha1 module */
    ._rom_mbedtls_sha1_init = mbedtls_sha1_init,
    ._rom_mbedtls_sha1_free = mbedtls_sha1_free,
    ._rom_mbedtls_sha1_clone = mbedtls_sha1_clone,
    ._rom_mbedtls_sha1_starts = mbedtls_sha1_starts,
    ._rom_mbedtls_sha1_finish = mbedtls_sha1_finish,
    /* sha256 module */
    ._rom_mbedtls_sha256_init = mbedtls_sha256_init,
    ._rom_mbedtls_sha256_free = mbedtls_sha256_free,
    ._rom_mbedtls_sha256_clone = mbedtls_sha256_clone,
    ._rom_mbedtls_sha256_starts = mbedtls_sha256_starts,
    ._rom_mbedtls_sha256_finish = mbedtls_sha256_finish,
    ._rom_mbedtls_sha256 = mbedtls_sha256,
    /* sha512 module */
    ._rom_mbedtls_sha512_init = mbedtls_sha512_init,
    ._rom_mbedtls_sha512_free = mbedtls_sha512_free,
    ._rom_mbedtls_sha512_clone = mbedtls_sha512_clone,
    ._rom_mbedtls_sha512_starts = mbedtls_sha512_starts,
    ._rom_mbedtls_sha512_update = mbedtls_sha512_update,
    ._rom_mbedtls_sha512_finish = mbedtls_sha512_finish,
    //._rom_mbedtls_internal_sha512_process = mbedtls_internal_sha512_process,
    ._rom_mbedtls_sha512 = mbedtls_sha512,

    ._rom_mbedtls_aes_xts_init = mbedtls_aes_xts_init,
    ._rom_mbedtls_aes_xts_free = mbedtls_aes_xts_free,
    ._rom_mbedtls_aes_xts_setkey_enc = mbedtls_aes_xts_setkey_enc,
    ._rom_mbedtls_aes_xts_setkey_dec = mbedtls_aes_xts_setkey_dec,
    ._rom_mbedtls_aes_crypt_xts = mbedtls_aes_crypt_xts,
    ._rom_mbedtls_aes_crypt_cfb128 = mbedtls_aes_crypt_cfb128,
    ._rom_mbedtls_aes_crypt_ofb = mbedtls_aes_crypt_ofb,
    ._rom_mbedtls_aes_crypt_ctr = mbedtls_aes_crypt_ctr,
    ._rom_mbedtls_ccm_init = mbedtls_ccm_init,
    ._rom_mbedtls_ccm_setkey = mbedtls_ccm_setkey,
    ._rom_mbedtls_ccm_free = mbedtls_ccm_free,
    ._rom_mbedtls_ccm_encrypt_and_tag = mbedtls_ccm_encrypt_and_tag,
    ._rom_mbedtls_ccm_auth_decrypt = mbedtls_ccm_auth_decrypt,
    ._rom_mbedtls_md5_init = mbedtls_md5_init,
    ._rom_mbedtls_md5_free = mbedtls_md5_free,
    ._rom_mbedtls_md5_clone = mbedtls_md5_clone,
    ._rom_mbedtls_md5_starts = mbedtls_md5_starts,
    ._rom_mbedtls_md5_update = mbedtls_md5_update,
    ._rom_mbedtls_md5_finish = mbedtls_md5_finish,
    ._rom_mbedtls_md5 = mbedtls_md5,
    ._rom_mbedtls_sha1 = mbedtls_sha1,

    // eco4 rom mbedtls functions
    ._rom_mbedtls_aes_crypt_cfb8 = mbedtls_aes_crypt_cfb8,
    ._rom_mbedtls_mpi_swap = mbedtls_mpi_swap,
    ._rom_mbedtls_mpi_read_string = mbedtls_mpi_read_string,
    ._rom_mbedtls_mpi_write_string = mbedtls_mpi_write_string,
    ._rom_mbedtls_mpi_read_binary_le = mbedtls_mpi_read_binary_le,
    ._rom_mbedtls_mpi_write_binary_le = mbedtls_mpi_write_binary_le,
    ._rom_mbedtls_mpi_random = mbedtls_mpi_random,
    ._rom_mbedtls_mpi_gen_prime = mbedtls_mpi_gen_prime,
    ._rom_mbedtls_ecp_check_budget = mbedtls_ecp_check_budget,
    ._rom_mbedtls_ecp_set_max_ops = mbedtls_ecp_set_max_ops,
    ._rom_mbedtls_ecp_restart_is_enabled = mbedtls_ecp_restart_is_enabled,
    ._rom_mbedtls_ecp_get_type = mbedtls_ecp_get_type,
    ._rom_mbedtls_ecp_curve_list = mbedtls_ecp_curve_list,
    ._rom_mbedtls_ecp_grp_id_list = mbedtls_ecp_grp_id_list,
    ._rom_mbedtls_ecp_curve_info_from_grp_id = mbedtls_ecp_curve_info_from_grp_id,
    ._rom_mbedtls_ecp_curve_info_from_tls_id = mbedtls_ecp_curve_info_from_tls_id,
    ._rom_mbedtls_ecp_curve_info_from_name = mbedtls_ecp_curve_info_from_name,
    ._rom_mbedtls_ecp_point_init = mbedtls_ecp_point_init,
    ._rom_mbedtls_ecp_group_init = mbedtls_ecp_group_init,
    ._rom_mbedtls_ecp_keypair_init = mbedtls_ecp_keypair_init,
    ._rom_mbedtls_ecp_point_free = mbedtls_ecp_point_free,
    ._rom_mbedtls_ecp_group_free = mbedtls_ecp_group_free,
    ._rom_mbedtls_ecp_keypair_free = mbedtls_ecp_keypair_free,
    ._rom_mbedtls_ecp_restart_init = mbedtls_ecp_restart_init,
    ._rom_mbedtls_ecp_restart_free = mbedtls_ecp_restart_free,
    ._rom_mbedtls_ecp_copy = mbedtls_ecp_copy,
    ._rom_mbedtls_ecp_group_copy = mbedtls_ecp_group_copy,
    ._rom_mbedtls_ecp_set_zero = mbedtls_ecp_set_zero,
    ._rom_mbedtls_ecp_is_zero = mbedtls_ecp_is_zero,
    ._rom_mbedtls_ecp_point_cmp = mbedtls_ecp_point_cmp,
    ._rom_mbedtls_ecp_point_read_string = mbedtls_ecp_point_read_string,
    ._rom_mbedtls_ecp_point_write_binary = mbedtls_ecp_point_write_binary,
    ._rom_mbedtls_ecp_point_read_binary = mbedtls_ecp_point_read_binary,
    ._rom_mbedtls_ecp_tls_read_point = mbedtls_ecp_tls_read_point,
    ._rom_mbedtls_ecp_tls_write_point = mbedtls_ecp_tls_write_point,
    ._rom_mbedtls_ecp_group_load = mbedtls_ecp_group_load,
    ._rom_mbedtls_ecp_tls_read_group = mbedtls_ecp_tls_read_group,
    ._rom_mbedtls_ecp_tls_read_group_id = mbedtls_ecp_tls_read_group_id,
    ._rom_mbedtls_ecp_tls_write_group = mbedtls_ecp_tls_write_group,
    ._rom_mbedtls_ecp_mul = mbedtls_ecp_mul,
    ._rom_mbedtls_ecp_mul_restartable = mbedtls_ecp_mul_restartable,
    ._rom_mbedtls_ecp_muladd = mbedtls_ecp_muladd,
    ._rom_mbedtls_ecp_muladd_restartable = mbedtls_ecp_muladd_restartable,
    ._rom_mbedtls_ecp_check_pubkey = mbedtls_ecp_check_pubkey,
    ._rom_mbedtls_ecp_check_privkey = mbedtls_ecp_check_privkey,
    ._rom_mbedtls_ecp_gen_privkey = mbedtls_ecp_gen_privkey,
    ._rom_mbedtls_ecp_gen_keypair_base = mbedtls_ecp_gen_keypair_base,
    ._rom_mbedtls_ecp_gen_keypair = mbedtls_ecp_gen_keypair,
    ._rom_mbedtls_ecp_gen_key = mbedtls_ecp_gen_key,
    ._rom_mbedtls_ecp_read_key = mbedtls_ecp_read_key,
    ._rom_mbedtls_ecp_write_key_ext = mbedtls_ecp_write_key_ext,
    ._rom_mbedtls_ecp_check_pub_priv = mbedtls_ecp_check_pub_priv,
    ._rom_mbedtls_ecp_export = mbedtls_ecp_export,
    ._rom_mbedtls_asn1_get_enum = mbedtls_asn1_get_enum,
    ._rom_mbedtls_asn1_sequence_free = mbedtls_asn1_sequence_free,
    ._rom_mbedtls_asn1_traverse_sequence_of = mbedtls_asn1_traverse_sequence_of,
    ._rom_mbedtls_asn1_find_named_data = mbedtls_asn1_find_named_data,
    ._rom_mbedtls_asn1_free_named_data_list = mbedtls_asn1_free_named_data_list,
    ._rom_mbedtls_asn1_free_named_data_list_shallow = mbedtls_asn1_free_named_data_list_shallow,
    ._rom_mbedtls_asn1_write_raw_buffer = mbedtls_asn1_write_raw_buffer,
    ._rom_mbedtls_asn1_write_null = mbedtls_asn1_write_null,
    ._rom_mbedtls_asn1_write_oid = mbedtls_asn1_write_oid,
    ._rom_mbedtls_asn1_write_algorithm_identifier = mbedtls_asn1_write_algorithm_identifier,
    ._rom_mbedtls_asn1_write_bool = mbedtls_asn1_write_bool,
    ._rom_mbedtls_asn1_write_int = mbedtls_asn1_write_int,
    ._rom_mbedtls_asn1_write_enum = mbedtls_asn1_write_enum,
    ._rom_mbedtls_asn1_write_tagged_string = mbedtls_asn1_write_tagged_string,
    ._rom_mbedtls_asn1_write_printable_string = mbedtls_asn1_write_printable_string,
    ._rom_mbedtls_asn1_write_utf8_string = mbedtls_asn1_write_utf8_string,
    ._rom_mbedtls_asn1_write_ia5_string = mbedtls_asn1_write_ia5_string,
    ._rom_mbedtls_asn1_write_bitstring = mbedtls_asn1_write_bitstring,
    ._rom_mbedtls_asn1_write_named_bitstring = mbedtls_asn1_write_named_bitstring,
    ._rom_mbedtls_asn1_write_octet_string = mbedtls_asn1_write_octet_string,
    ._rom_mbedtls_asn1_store_named_data = mbedtls_asn1_store_named_data,
    ._rom_mbedtls_ccm_starts = mbedtls_ccm_starts,
    ._rom_mbedtls_ccm_set_lengths = mbedtls_ccm_set_lengths,
    ._rom_mbedtls_ccm_update_ad = mbedtls_ccm_update_ad,
    ._rom_mbedtls_ccm_update = mbedtls_ccm_update,
    ._rom_mbedtls_ccm_finish = mbedtls_ccm_finish,
    ._rom_mbedtls_cipher_list = mbedtls_cipher_list,
    ._rom_mbedtls_cipher_info_from_string = mbedtls_cipher_info_from_string,
    ._rom_mbedtls_cipher_info_from_type = mbedtls_cipher_info_from_type,
    ._rom_mbedtls_cipher_info_from_values = mbedtls_cipher_info_from_values,
    ._rom_mbedtls_cipher_free = mbedtls_cipher_free,
    ._rom_mbedtls_cipher_setup = mbedtls_cipher_setup,
    ._rom_mbedtls_cipher_setkey = mbedtls_cipher_setkey,
    ._rom_mbedtls_cipher_set_iv = mbedtls_cipher_set_iv,
    ._rom_mbedtls_cipher_update_ad = mbedtls_cipher_update_ad,
    ._rom_mbedtls_cipher_update = mbedtls_cipher_update,
    ._rom_mbedtls_cipher_write_tag = mbedtls_cipher_write_tag,
    ._rom_mbedtls_cipher_check_tag = mbedtls_cipher_check_tag,
    ._rom_mbedtls_cipher_auth_encrypt_ext = mbedtls_cipher_auth_encrypt_ext,
    ._rom_mbedtls_cipher_auth_decrypt_ext = mbedtls_cipher_auth_decrypt_ext,
    ._rom_mbedtls_cipher_cmac_reset = mbedtls_cipher_cmac_reset,
    ._rom_mbedtls_cipher_cmac = mbedtls_cipher_cmac,
    ._rom_mbedtls_aes_cmac_prf_128 = mbedtls_aes_cmac_prf_128,
    ._rom_mbedtls_ctr_drbg_set_prediction_resistance = mbedtls_ctr_drbg_set_prediction_resistance,
    ._rom_mbedtls_ctr_drbg_set_entropy_len = mbedtls_ctr_drbg_set_entropy_len,
    ._rom_mbedtls_ctr_drbg_set_nonce_len = mbedtls_ctr_drbg_set_nonce_len,
    ._rom_mbedtls_ctr_drbg_set_reseed_interval = mbedtls_ctr_drbg_set_reseed_interval,
    ._rom_mbedtls_ctr_drbg_update = mbedtls_ctr_drbg_update,
    ._rom_mbedtls_base64_encode = mbedtls_base64_encode,

    /* Fill the SHA hardware functions into mbedtls rom function table */
    ._rom_mbedtls_sha1_update = mbedtls_sha1_update,
    ._rom_mbedtls_sha256_update = mbedtls_sha256_update,

    //memory calloc free
    ._rom_mbedtls_mem_calloc = MBEDTLS_PLATFORM_STD_CALLOC,
    ._rom_mbedtls_mem_free = MBEDTLS_PLATFORM_STD_FREE,
};

__attribute__((constructor)) void mbedtls_rom_osi_functions_init(void)
{
    /* Export the rom mbedtls functions table pointer */
    extern void *mbedtls_rom_osi_funcs_ptr;

#if defined(MBEDTLS_THREADING_ALT)
    mbedtls_threading_set_alt(mbedtls_rom_mutex_init, mbedtls_rom_mutex_free, mbedtls_rom_mutex_lock, mbedtls_rom_mutex_unlock);
#endif

    unsigned chip_version = efuse_hal_chip_revision();
    if ( ESP_CHIP_REV_ABOVE(chip_version, 200) ) {
        /* Initialize the rom function mbedtls_threading_set_alt on chip rev2.0 with rom eco4 */
        _rom_mbedtls_threading_set_alt_t rom_mbedtls_threading_set_alt = (_rom_mbedtls_threading_set_alt_t)0x40002c0c;
        rom_mbedtls_threading_set_alt(mbedtls_rom_mutex_init, mbedtls_rom_mutex_free, mbedtls_rom_mutex_lock, mbedtls_rom_mutex_unlock);

        /* Initialize the pointer of rom eco4 mbedtls functions table. */
        mbedtls_rom_osi_funcs_ptr = (mbedtls_rom_eco4_funcs_t *)&mbedtls_rom_eco4_funcs_table;
    } else {
        /* Initialize the pointer of rom mbedtls functions table. */
        mbedtls_rom_osi_funcs_ptr = (mbedtls_rom_funcs_t *)&mbedtls_rom_funcs_table;
    }
}
