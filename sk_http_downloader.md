# SK HTTP Downloader 模块

## 概述

SK HTTP Downloader 是一个简洁的HTTP文件下载模块，专门用于将文件下载到ESP32的PSRAM中，并使用静态环形缓冲区进行数据管理。

## 特性

- **PSRAM存储**：所有数据存储在PSRAM中，节省内部RAM
- **静态环形缓冲区**：使用`xRingbufferCreateStatic`创建环形缓冲区
- **一次性内存分配**：数据存储空间和结构体空间在一次分配中完成
- **简洁接口**：只有2个核心函数，使用简单
- **完整错误处理**：包含网络错误、内存分配失败等各种异常处理

## 文件结构

```
main/
├── include/
│   └── sk_http_downloader.h    # 头文件
└── network/
    └── sk_http_downloader.c    # 实现文件
```

## API 接口

### 数据结构

```c
typedef struct {
    RingbufHandle_t ringbuf;    // 环形缓冲区句柄
    uint8_t *storage;           // PSRAM存储指针
    size_t totalSize;           // 文件总大小
} SkHttpDownloadData;
```

### 核心函数

#### 1. SkHttpDownloadFile()

```c
sk_err_t SkHttpDownloadFile(const char *url, SkHttpDownloadData *downloadData);
```

**功能**：下载文件到PSRAM环形缓冲区

**参数**：
- `url`：文件URL地址
- `downloadData`：输出的下载数据结构

**返回值**：
- `SK_RET_SUCCESS`：下载成功
- `SK_RET_INVALID_PARAM`：参数无效
- `SK_RET_FAIL`：网络连接失败
- `SK_RET_NO_MEMORY`：内存分配失败

#### 2. SkHttpDownloadFree()

```c
void SkHttpDownloadFree(SkHttpDownloadData *downloadData);
```

**功能**：释放下载资源

**参数**：
- `downloadData`：要释放的下载数据结构

## 使用示例

### 基本使用

```c
#include "sk_http_downloader.h"

void example_download_audio(void) {
    SkHttpDownloadData downloadData;
    
    // 下载文件
    sk_err_t ret = SkHttpDownloadFile("http://example.com/music.opus", &downloadData);
    if (ret == SK_RET_SUCCESS) {
        SK_LOGI(TAG, "Downloaded %zu bytes to PSRAM", downloadData.totalSize);
        
        // 从环形缓冲区读取数据
        size_t itemSize;
        uint8_t *item = xRingbufferReceive(downloadData.ringbuf, &itemSize, portMAX_DELAY);
        if (item != NULL) {
            // 处理数据...
            SK_LOGI(TAG, "Received %zu bytes from ringbuffer", itemSize);
            
            // 返回数据项
            vRingbufferReturnItem(downloadData.ringbuf, item);
        }
        
        // 释放资源
        SkHttpDownloadFree(&downloadData);
    } else {
        SK_LOGE(TAG, "Download failed: %d", ret);
    }
}
```

### 流式读取示例

```c
void example_stream_read(SkHttpDownloadData *downloadData) {
    size_t totalRead = 0;
    
    while (totalRead < downloadData->totalSize) {
        size_t itemSize;
        uint8_t *item = xRingbufferReceive(downloadData->ringbuf, &itemSize, 
                                          pdMS_TO_TICKS(1000));
        if (item != NULL) {
            // 处理数据块
            process_audio_data(item, itemSize);
            
            totalRead += itemSize;
            vRingbufferReturnItem(downloadData->ringbuf, item);
        } else {
            SK_LOGW(TAG, "Ringbuffer receive timeout");
            break;
        }
    }
}
```

## 配置参数

在 `sk_http_downloader.c` 中可以调整以下参数：

```c
#define HTTP_BUFFER_SIZE        4096                    // HTTP读取缓冲区大小
#define HTTP_TIMEOUT_MS         30000                   // HTTP超时时间(30秒)
#define MAX_FILE_SIZE           (2 * 1024 * 1024)      // 最大文件大小(2MB)
#define RINGBUF_SIZE            (2 * 1024 * 1024)      // 环形缓冲区大小(2MB)
```

## 内存管理

### PSRAM分配策略

1. **一次性分配**：`RINGBUF_SIZE + sizeof(StaticRingbuffer_t)` 字节
2. **内存布局**：
   ```
   [数据存储空间: 2MB] [结构体空间: ~几百字节]
   ```
3. **自动清理**：调用 `SkHttpDownloadFree()` 自动释放所有PSRAM内存

### 环形缓冲区

- 使用 `xRingbufferCreateStatic()` 创建静态环形缓冲区
- 数据存储和结构体都在PSRAM中
- 支持多线程安全访问

## 错误处理

模块包含完整的错误处理机制：

1. **参数检查**：检查URL和输出参数有效性
2. **网络错误**：HTTP连接失败、读取错误等
3. **内存错误**：PSRAM分配失败、环形缓冲区创建失败
4. **文件大小检查**：超过最大文件大小限制
5. **资源清理**：任何错误情况下都会正确清理已分配的资源

## 日志输出

模块使用 `SK_LOG*` 宏输出日志：

- `SK_LOGI`：下载进度、完成状态
- `SK_LOGW`：警告信息
- `SK_LOGE`：错误信息

## 依赖项

- ESP-IDF HTTP客户端 (`esp_http_client`)
- FreeRTOS环形缓冲区 (`freertos/ringbuf`)
- ESP32 PSRAM支持 (`esp_heap_caps`)
- SK项目日志系统 (`sk_log`)

## 注意事项

1. **PSRAM要求**：需要ESP32配置并启用PSRAM
2. **网络连接**：需要WiFi连接正常
3. **文件大小限制**：默认最大2MB，可根据PSRAM大小调整
4. **线程安全**：环形缓冲区操作是线程安全的
5. **资源管理**：必须调用 `SkHttpDownloadFree()` 释放资源

## 代码统计

- **头文件**：42行
- **实现文件**：179行
- **总代码量**：221行
- **核心函数**：2个
- **配置宏**：4个

这是一个极简但功能完整的HTTP下载模块，专为ESP32 PSRAM环境优化。
