# Name,   Type, SubType, Offset,  Size, Flags
# Note: if you have increased the bootloader size, make sure to update the offsets to avoid overlap,,,,
nvs,        data,   nvs,        0x009000,   0x004000,
otadata,    data,   ota,        0x00d000,   0x002000,
phy_init,   data,   phy,        0x00f000,   0x001000,
audio,      data,   nvs,        0x010000,   0x0F0000,
app0,       app,    ota_0,      0x100000,   0x200000,
app1,       app,    ota_1,      0x300000,   0x200000,
model,      data,   spiffs,     0x500000,   0x600000,
coredump,   data,   coredump,   0xF00000,   0x010000,
