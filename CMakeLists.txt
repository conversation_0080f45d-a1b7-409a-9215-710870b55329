# The following lines of boilerplate have to be in your project's CMakeLists
# in this exact order for cmake to work correctly
cmake_minimum_required(VERSION 3.16)

# Read version from version.txt file
file(READ "${CMAKE_CURRENT_SOURCE_DIR}/version.txt" PROJECT_VER)
string(STRIP "${PROJECT_VER}" PROJECT_VER)

file(TO_NATIVE_PATH "$ENV{IDF_PATH}/tools/cmake/project.cmake" _project_path)
include(${_project_path})

project(sk_terminal VERSION ${PROJECT_VER})

file(TO_NATIVE_PATH "${CMAKE_CURRENT_BINARY_DIR}/coverage_report" _coverage_path)
idf_create_coverage_report(${_coverage_path})
idf_clean_coverage_report(${_coverage_path})
