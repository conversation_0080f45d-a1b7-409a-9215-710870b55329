#!/usr/bin/env python3
"""
WAV to Opus转换脚本
支持转换为纯Opus流（无OGG容器）或OGG容器格式
适用于ESP32音频播放项目

依赖安装：
pip install pydub

系统依赖：
- Ubuntu/Debian: sudo apt install ffmpeg
- macOS: brew install ffmpeg
- Windows: 下载ffmpeg并添加到PATH

使用方法：
python wav_to_opus.py input.wav output.opus --raw
python wav_to_opus.py input.wav output.opus --bitrate 64000
"""

import argparse
import os
import sys
from pathlib import Path

try:
    from pydub import AudioSegment
    from pydub.utils import which
except ImportError:
    print("错误: 请安装pydub库")
    print("运行: pip install pydub")
    sys.exit(1)

def check_ffmpeg():
    """检查ffmpeg是否可用"""
    if not which("ffmpeg"):
        print("错误: 未找到ffmpeg")
        print("请安装ffmpeg:")
        print("  Ubuntu/Debian: sudo apt install ffmpeg")
        print("  macOS: brew install ffmpeg")
        print("  Windows: 下载ffmpeg并添加到PATH")
        return False
    return True

def convert_wav_to_opus(input_file, output_file, bitrate=64000, sample_rate=16000, 
                       channels=1, raw_opus=False, frame_duration=60):
    """
    将WAV文件转换为Opus格式
    
    Args:
        input_file: 输入WAV文件路径
        output_file: 输出Opus文件路径
        bitrate: 比特率 (默认64kbps)
        sample_rate: 采样率 (默认16kHz，适合语音)
        channels: 声道数 (默认1，单声道)
        raw_opus: 是否输出纯Opus流（无OGG容器）
        frame_duration: 帧长度（毫秒，默认60ms）
    """
    
    if not os.path.exists(input_file):
        raise FileNotFoundError(f"输入文件不存在: {input_file}")
    
    print(f"正在转换: {input_file} -> {output_file}")
    print(f"参数: 采样率={sample_rate}Hz, 声道={channels}, 比特率={bitrate}bps")
    
    try:
        # 加载WAV文件
        audio = AudioSegment.from_wav(input_file)
        
        # 转换音频参数
        audio = audio.set_frame_rate(sample_rate)
        audio = audio.set_channels(channels)
        
        # 构建ffmpeg参数
        codec_params = [
            "-c:a", "libopus",
            "-b:a", str(bitrate),
            "-ar", str(sample_rate),
            "-ac", str(channels),
            "-frame_duration", str(frame_duration),
            "-application", "voip",  # 针对语音优化
        ]
        
        if raw_opus:
            # 输出纯Opus流（无OGG容器）
            codec_params.extend(["-f", "opus"])
            print("输出格式: 纯Opus流（无OGG容器）")
        else:
            # 输出OGG容器格式
            codec_params.extend(["-f", "ogg"])
            print("输出格式: OGG容器")
        
        # 执行转换
        audio.export(
            output_file,
            format="opus" if not raw_opus else "opus",
            codec="libopus",
            parameters=codec_params
        )
        
        # 显示文件信息
        input_size = os.path.getsize(input_file)
        output_size = os.path.getsize(output_file)
        compression_ratio = (1 - output_size / input_size) * 100
        
        print(f"转换完成!")
        print(f"输入文件大小: {input_size:,} 字节")
        print(f"输出文件大小: {output_size:,} 字节")
        print(f"压缩率: {compression_ratio:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"转换失败: {e}")
        return False

def analyze_audio_file(file_path):
    """分析音频文件信息"""
    try:
        audio = AudioSegment.from_file(file_path)
        
        print(f"\n音频文件信息: {file_path}")
        print(f"  时长: {len(audio) / 1000:.2f} 秒")
        print(f"  采样率: {audio.frame_rate} Hz")
        print(f"  声道数: {audio.channels}")
        print(f"  位深: {audio.sample_width * 8} bit")
        print(f"  文件大小: {os.path.getsize(file_path):,} 字节")
        
    except Exception as e:
        print(f"无法分析文件: {e}")

def main():
    parser = argparse.ArgumentParser(
        description="WAV to Opus转换工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 转换为纯Opus流（推荐用于ESP32）
  python wav_to_opus.py music.wav music.opus --raw
  
  # 转换为OGG容器格式
  python wav_to_opus.py music.wav music.opus
  
  # 自定义参数
  python wav_to_opus.py music.wav music.opus --bitrate 32000 --sample-rate 8000
  
  # 分析音频文件
  python wav_to_opus.py --analyze music.wav
        """
    )
    
    parser.add_argument("input", nargs="?", help="输入WAV文件路径")
    parser.add_argument("output", nargs="?", help="输出Opus文件路径")
    
    parser.add_argument("--raw", action="store_true", 
                       help="输出纯Opus流（无OGG容器，推荐用于ESP32）")
    parser.add_argument("--bitrate", type=int, default=32000,
                       help="比特率 (默认: 64000)")
    parser.add_argument("--sample-rate", type=int, default=16000,
                       help="采样率 (默认: 16000)")
    parser.add_argument("--channels", type=int, default=1,
                       help="声道数 (默认: 1)")
    parser.add_argument("--frame-duration", type=int, default=60,
                       help="帧长度毫秒 (默认: 60)")
    parser.add_argument("--analyze", action="store_true",
                       help="仅分析输入文件信息")
    
    args = parser.parse_args()
    
    # 检查ffmpeg
    if not check_ffmpeg():
        sys.exit(1)
    
    # 分析模式
    if args.analyze:
        if not args.input:
            print("错误: 请指定要分析的文件")
            sys.exit(1)
        analyze_audio_file(args.input)
        return
    
    # 转换模式
    if not args.input or not args.output:
        parser.print_help()
        sys.exit(1)
    
    # 验证输入文件
    if not os.path.exists(args.input):
        print(f"错误: 输入文件不存在: {args.input}")
        sys.exit(1)
    
    # 创建输出目录
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 分析输入文件
    analyze_audio_file(args.input)
    
    # 执行转换
    success = convert_wav_to_opus(
        args.input,
        args.output,
        bitrate=args.bitrate,
        sample_rate=args.sample_rate,
        channels=args.channels,
        raw_opus=args.raw,
        frame_duration=args.frame_duration
    )
    
    if success:
        print(f"\n✅ 转换成功: {args.output}")
        if args.raw:
            print("💡 提示: 生成的是纯Opus流，适合ESP32直接播放")
        else:
            print("💡 提示: 生成的是OGG容器格式，需要解析OGG头部")
    else:
        print("❌ 转换失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
